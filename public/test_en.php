<?php
// 测试en模块加载
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>测试en模块加载</h1>";

// 检查en模块目录是否存在
$en_module_path = __DIR__ . '/../application/en';
if (is_dir($en_module_path)) {
    echo "<p style='color: green;'>✓ en模块目录存在: " . $en_module_path . "</p>";
} else {
    echo "<p style='color: red;'>✗ en模块目录不存在: " . $en_module_path . "</p>";
}

// 检查en模块控制器目录
$en_controller_path = $en_module_path . '/controller';
if (is_dir($en_controller_path)) {
    echo "<p style='color: green;'>✓ en模块控制器目录存在</p>";
} else {
    echo "<p style='color: red;'>✗ en模块控制器目录不存在</p>";
}

// 检查Index控制器文件
$index_controller_path = $en_controller_path . '/Index.php';
if (file_exists($index_controller_path)) {
    echo "<p style='color: green;'>✓ Index控制器文件存在</p>";
} else {
    echo "<p style='color: red;'>✗ Index控制器文件不存在</p>";
}

// 尝试手动加载ThinkPHP并测试en模块
try {
    define('APP_PATH', __DIR__ . '/../application/');
    define('THINK_PATH', __DIR__ . '/../thinkphp/5.0.24/');
    
    require THINK_PATH . 'base.php';
    
    echo "<p style='color: green;'>✓ ThinkPHP base.php 加载成功</p>";
    
    // 检查模块列表
    $modules = \think\Loader::getModules();
    echo "<p>可用模块: " . implode(', ', $modules) . "</p>";
    
    // 检查en模块是否在列表中
    if (in_array('en', $modules)) {
        echo "<p style='color: green;'>✓ en模块在可用模块列表中</p>";
    } else {
        echo "<p style='color: red;'>✗ en模块不在可用模块列表中</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ ThinkPHP加载失败: " . $e->getMessage() . "</p>";
}

echo "<p><a href='/'>返回首页</a></p>";
?> 