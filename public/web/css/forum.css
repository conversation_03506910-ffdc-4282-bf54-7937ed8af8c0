/* 论坛日程页面样式 */
.forum-container {
    background-color: #fff;
    padding: 20px 0;
}

.forum-header {
    padding: 62px 0px;
}

.date-tabs {
    display: flex;
    gap: 30px;
}

.date-tab {
    padding: 18px 38px;
    border: 1px solid #082CA4;
    border-radius: 36px;
    background: white;
    color: #082CA4;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.date-tab.active {
    background: #082CA4;
    color: white;
    border-color:#082CA4;
}

.search-box {
    position: relative;
    width: 450px;
    height: 56px;
    margin-left: auto;
}

.search-input {
    width: 100%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 36px;
    outline: none;
    font-size: 16px;
    text-indent: 20px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #082CA4;
    box-shadow: 0 0 0 2px rgba(8, 44, 164, 0.1);
}

.search-btn {
    position: absolute;
    right: 30px;
    top: 20px;
    border: none;
    color: white;
    cursor: pointer;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon-search{
        width: 20px;
        height: 20px;
    }
}


.schedule-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.schedule-item {
    overflow: hidden;
    transition: all 0.3s;
}

.schedule-item:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.schedule-header {
    padding: 28px 40px;
    cursor: pointer;
    position: relative;
    background: linear-gradient(180deg, #EEF2FF -57%, #FFFFFF 100%);
    box-sizing: border-box;
    
}

.schedule-header.expanded {
    background: #082CA4;
    color: white;
}

.schedule-time {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.schedule-header.expanded .schedule-time {
    color: rgba(255,255,255,0.8);
}

.schedule-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.schedule-header.expanded .schedule-title {
    color: white;
}
.time-wrap{
    display: flex;align-items: center;justify-content: flex-start;margin-right: 30px;
}
.icon-clock{
    width:16px;height:16px;vertical-align:middle;margin-right:12px;
}
.time-text{
    color: #082CA4;font-size: 18px;font-weight: bold;
}
.map-wrap{
    display: flex;align-items: center;justify-content: flex-start;margin-right: 30px;
}
.icon-map{
    width:20px;height:20px;vertical-align:middle;margin-right:12px;
}
.map-text{
    color: #082CA4;font-size: 16px;
}
.schedule-header.expanded .time-text{
    color: white;
}
.schedule-header.expanded .map-text{
    color: white;
}
.icon-expand{
    width: 36px;
    height: 36px;
    border-radius: 36px;
    transform: rotate(180deg);
}
.icon-retract{
    width: 36px;
    height: 36px;
    border-radius: 36px;
    transition: transform 0.2s;
}
.schedule-location {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.schedule-header.expanded .schedule-location {
    color: rgba(255,255,255,0.8);
}

.expand-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #1890ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s;
}

.schedule-header.expanded .expand-icon {
    background: white;
    color: #1890ff;
    transform: translateY(-50%) rotate(180deg);
}

.schedule-content {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(-10px);
    will-change: max-height, opacity, transform;
}

.schedule-content.expanded {
    max-height: none;
    opacity: 1;
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .guests-grid {
        justify-content: center;
    }
    
    .guest-item {
        width: 240px;
        height: 320px;
    }
    
    .date-tabs {
        flex-wrap: wrap;
    }
    
    .schedule-header {
        padding: 15px;
    }
    
    .agenda-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .speakers {
        flex-direction: column;
    }
}

/* 更小屏幕的响应式设计 */
@media (max-width: 480px) {
    .guest-item {
        width: 200px;
        height: 280px;
    }
    
    .guest-avatar img {
        width: 100px;
        height: 100px;
        margin-top: 30px;
    }
    
    .guest-name {
        margin-top: 15px;
        font-size: 14px;
    }
    
    .guest-title {
        width: 150px;
        font-size: 12px;
    }
}

/* 论坛日程详情整体 */
.schedule-details {
    background: #fff;
    font-size: 16px;
    color: #3D3D3D;
    border: 1px solid rgba(0, 0, 0, 0.12);
}
.details-wrap{
    padding: 30px 40px 53px;
    background: #FAFBFF;
}
.location{
    margin-bottom: 24px;
}
/* 主办方/协办方/支持方 */
.organizers {
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding-top: 20px;
}
.organizer-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}
.organizer-label {
    color: #666;
    min-width: 60px;
}
.organizer-list {
    color: #333;
    margin-left: 8px;
    word-break: break-all;
}

/* 主题描述 */
.description {
    margin-top: 24px;
}
.description-title {
    font-weight: bold;
    color: #333333;
    margin-bottom: 16px;
}
.description-content {
    color: #666;
    line-height: 30px;
}

/* 议程安排 */
.agenda {
    margin-bottom: 18px;
}
.agenda-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
}
.agenda-time {
    color: #2a5bd7;
    font-weight: bold;
    min-width: 90px;
    text-align: right;
    margin-right: 16px;
    font-size: 15px;
}
.agenda-content {
    flex: 1;
}
.agenda-title {
    font-weight: bold;
    color: #222;
    margin-bottom: 4px;
}


/* 嘉宾展示 */
.guests-section {
    margin-top: 32px;
}
.guests-title {
    width: 100%;
    text-align: center;
    font-weight: bold;
   font-size: 28px;
    font-weight: bold;
    color: #3D3D3D;
    margin-bottom: 12px;
}
.guests-wrap{
    /* padding: 30px; */
    margin-top: 50px;
}
.guests-grid {
    display: flex;
    flex-wrap: wrap;
    /* 使用虚线边框创建点状效果 */
    border: 2px dashed #D9D9D9;
    border-bottom: none;
    border-radius: 4px;
    position: relative;
}
.guest-item {
    width: 270px;
    height: 310px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    border-bottom: 2px dashed #D9D9D9;
    /* 确保嘉宾卡片在网格中正确排列 */
}


/* 响应式设计 - 小屏幕时每行3个项目 */
/* @media (max-width: 1200px) {
    .guest-item:nth-child(5n+1),
    .guest-item:nth-child(5n+2),
    .guest-item:nth-child(5n+3),
    .guest-item:nth-child(5n+4) {
        border-bottom: none;
    }
    
    .guest-item:nth-child(3n+1),
    .guest-item:nth-child(3n+2),
    .guest-item:nth-child(3n+3) {
        border-bottom: 1px solid #E8E8E8;
    }
    
    .guest-item:nth-last-child(-n+3) {
        border-bottom: none;
    }
} */

/* 响应式设计 - 更小屏幕时每行2个项目 */
/* @media (max-width: 768px) {
    .guest-item:nth-child(3n+1),
    .guest-item:nth-child(3n+2),
    .guest-item:nth-child(3n+3) {
        border-bottom: none;
    }
    
    .guest-item:nth-child(2n+1),
    .guest-item:nth-child(2n+2) {
        border-bottom: 1px solid #E8E8E8;
    }
    
    .guest-item:nth-last-child(-n+2) {
        border-bottom: none;
    }
} */
.guest-avatar img {
    width: 120px;
    height: 120px;
    margin-top: 40px;
    border-radius: 50%;
}
.guest-name {
    margin-top: 18px;
    color: #333;
    margin-bottom: 4px;
}
.guest-title {
    width: 170px;
    margin: 0 auto;;
    font-size: 16px;
    color: #999;
    white-space: pre-line;
}

.timeline {
    padding: 40px;
    position: relative;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 103px;
    top: 4px;
    width: 1px;
    height: calc(100% + 20px);
    border-left: 2px dashed #082CA4;
    z-index: 0;
}

.timeline-item:last-child::before {
    display: none;
}

.timeline-time {
    width: 80px;
    font-size: 14px;
    color: #666;
    font-weight: bold;
    flex-shrink: 0;
}

.timeline-dot-out {
    width: 16px;
    height: 16px;
    border-radius: 10px;
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(8, 44, 164, 0.1);
    position: relative;
    z-index: 1;
    margin: 0 16px;
}

.timeline-dot {
    width: 8px;
    height: 8px;
    background: #082CA4;
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-size: 16px;
    color: #082CA4;
    margin-bottom: 5px;
}

.timeline-activity {
    font-size: 16px;
    color: #666;
}
.activity-item{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #333;
    margin-bottom: 8px;
}
.activity-name {
    font-weight: bold;
    margin-right: 20px;
}

.activity-info {
    color: #333;

}

/* 嘉宾墙样式 */
.guests-details {
    padding: 20px;
    border-radius: 5px;
    margin-top: 20px;
}







.guest-item.current-speaker {
    background: linear-gradient(180deg, #A1B7FF -107%, #FFFFFF 113%);

    box-sizing: border-box;
    border: 2px solid rgba(8, 44, 164, 0.08);
}

.guest-item.current-speaker::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #082CA4, #4A90E2);
    border-radius: 12px;
    z-index: -1;
}
