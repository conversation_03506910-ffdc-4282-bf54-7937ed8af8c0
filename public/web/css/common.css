*,
:after,
:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

html {
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

body,
button,
dd,
div,
dl,
dt,
form,
h1,
h2,
h3,
input,
li,
p,
select,
textarea,
ul {
	margin: 0;
	padding: 0;
}

article,
aside,
footer,
header,
nav,
section {
	display: block;
}

body {
	max-width: 100%;
	font: 14px Arial, "Microsoft YaHei";
	color: #333;
	background-color: #fff;
	--color: #082CA4;
	--btn-color: #082CA4;
	/* min-width: 1200px; */
	min-width: 1100px;
}

li {
	list-style: none;
}

img{
	border:0;
    image-rendering: -moz-crisp-edges; /* Firefox */
    image-rendering: -o-crisp-edges; /* Opera */
    image-rendering: -webkit-optimize-contrast; /* Webkit (non-standard naming) */
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
}
 img.high {
	-ms-interpolation-mode:bicubic; 
	image-rendering: optimizeQuality; 
}

a {
	color: #999;
}

a,
a:hover {
	text-decoration: none;
}

a:active,
a:hover {
	outline: 0;
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: inherit;
	outline: 0;
	border: none;
	background-color: transparent;
	/* appearance: none; */
}

input:read-only {
	cursor: pointer;
}

input:-webkit-autofill {
	box-shadow: inset 0 0 0 1000px #eff3f6 !important;
}

::-webkit-input-placeholder {
	color: #999;
}

:-ms-input-placeholder {
	color: #999;
}

textarea {
	resize: none;
}

table {
	width: 100%;
	border-collapse: collapse;
	border-spacing: 0;
	border: none;
}

table tr, table td, table th {
	border: none;
}

.scrollbar::-webkit-scrollbar {
	width: 3px;
	background: #f4f4f4;
}

.scrollbar::-webkit-scrollbar-button,
.scrollbar::-webkit-scrollbar-track {
	display: none;
}

.scrollbar::-webkit-scrollbar-track-piece {
	display: none;
}

.scrollbar::-webkit-scrollbar-thumb {
	background: #082CA4;
}

.scrollbar::-webkit-resizer,
.scrollbar::-webkit-scrollbar-corner {
	display: none;
}

.clear {
	zoom: 1;
	clear: both
}

.clear:after {
	content: "";
	display: block;
	clear: both;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.left {
	display: flex;
	flex-wrap: wrap;
}

.right {
	display: flex;
	justify-content: flex-end;
}

.between {
	display: flex;
	justify-content: space-between;
}

.around {
	display: flex;
	justify-content: space-around;
}

.center {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.centerT {
	display: flex;
	justify-content: center;
	align-items: center;
}

.cut {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.cutThree,
.cutTwo {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
}

.cutTwo {
	-webkit-line-clamp: 2;
}

.cutThree {
	-webkit-line-clamp: 3;
}

/* @font-face {
	font-family: AlimamaShuHeiTi-Bold;
	src: url(../fonts/Alimama_ShuHeiTi_Bold.ttf);
} */

.c1200 {
	/* width: 1200px; */
	width: 1100px;
}

.wrap {
	width: 81.25%;
}

.wrap1 {
	width: 92%;
}

.c1200, .wrap, .wrap1{
	margin: 0 auto;
	/* min-width: 1200px; */
	min-width: 1100px;
}

@media (max-width: 1920px) {
	.wrap {
		width: 1440px;
	}
}
@media (max-width: 1680px) {
	.wrap {
		width: 1200px;
	}
}
@media (max-width: 1480px){
    .wrap { 
		/* width: 1200px; */
		width: 1100px;
	}
}

/* 暂无数据 */
.noData {
	width: 100%;
	text-align: center;
	font-size: 28px;
	color: #333;
	line-height: 40px;
	font-weight: bold;
	padding: 50px 0;
}

/* 分页 */
.pagination {
	width: 100%;
	display: table;
	font-size: 0;
	text-align: center;
}

.pagination li {
	display: inline-block;
	vertical-align: top;
	border: 1px solid #EAEAEA;
	border-left: none;
	transition: all .6s ease;
}

.pagination li:first-child {
	border-radius: 0;
	border-left: 1px solid #EAEAEA;
}

.pagination li:last-child {
	border-radius: 0;
}

.pagination li a,
.pagination li span {
	display: block;
	font-size: 16px;
	line-height: 38px;
	color: #666;
	min-width: 38px;
	text-align: center;
	padding: 0 10px;
	transition: all .6s ease;
}

.pagination .active {
	cursor: default;
	background-color: #082CA4;
}

.pagination li:hover {
	cursor: pointer;
	background-color: #082CA4;
}

.pagination .active a,
.pagination .active a:focus,
.pagination .active a:hover,
.pagination .active span,
.pagination .active span:focus,
.pagination .active span:hover {
	/* cursor: default;
	background-color: #58b1f5; */
	color: #fff;
}

.pagination li a:focus,
.pagination li a:hover,
.pagination li span:focus,
.pagination li span:hover {
	/* cursor: pointer;
	background-color: #58b1f5; */
	color: #fff;
}

