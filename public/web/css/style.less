/* headerBox */
.headerBox {
    width: 100%;
    padding: 15px 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    background-color: rgb(29, 43, 87);
    transition: all 0.6s ease;

    .logo {
        display: block;
        width: 156px;
        height: 70px;
        background: url('../images/logo.png') center center no-repeat;
        background-size: cover;

        // img {
        //     height: 100%;
        //     display: block;
        // }
    }

    .box {
        width: calc(100% - 156px);
    }

    .navBox {
        // width: calc(100% - 472px);

        .nLi {
            // margin-left: 40px;
            margin-left: 70px;
            position: relative;

            h3 a {
                display: block;
                line-height: 40px;
                color: #fff;
                font-size: 18px;
                padding: 15px 0;
                font-weight: normal;
            }

            .sub {
                display: none;
                width: 130px;
                position: absolute;
                top: 70px;
                transform: translateX(-50%);
                left: 50%;
                z-index: 3;
                box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
                -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
                -moz-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);

                ul {
                    width: 100%;
                    padding: 10px 0;
                    background-color: #fff;
                    border-radius: 2px;
                    position: relative;

                    a {
                        display: block;
                        text-align: center;
                        font-size: 14px;
                        line-height: 20px;
                        color: #333;
                        padding: 10px 8px;
                        transition: all 0.6s ease;
                    }

                    li:hover a {
                        color: #082CA4;
                    }
                }
            }

            // &:hover h3 a,
            // &.on h3 a {
            //     font-weight: bold;
            // }
        }
    }

    .language {
        color: #fff;
        line-height: 70px;
        font-size: 18px;
        margin: 0 70px;
    }

    .login {
        width: 130px;
        height: 42px;
        border-radius: 2px;
        border: 1px solid rgba(255, 255, 255, 0.6);
        font-size: 16px;
        line-height: 42px;
        color: #ffffff;
        margin-top: 14px;
        transition: all 0.6s ease;

        .iconfont {
            font-size: 18px;
            margin-right: 10px;
        }

        &.login1 {
            width: 130px;
            font-size: 14px;

            .iconfont {
                font-size: 16px;
            }
        }

        &:hover {
            background-color: #fff;
            color: #082CA4;
        }
    }

    &.headerBox1 {
        background-color: #fff;

        .logo {
            background: url('../images/logo1.png') center center no-repeat;
            background-size: cover;
        }

        .navBox {
            .nLi {
                h3 a {
                    color: #333;
                }

                &:hover h3 a,
                &.on h3 a {
                    color: #082CA4;
                    // font-weight: bold;
                }
            }
        }
    
        .language {
            color: #333;
        }
    
        .login {
            border: 1px solid #082CA4;
            background-color: #082CA4; 
    
            &:hover {
                background-color: #fff;
                color: #082CA4;
            }
        }
    }
}
.is-fixed {
    padding: 10px 0;
    background-color: rgba(29, 43, 87, 0.8);
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
}
@media (max-width: 1680px) {
    .headerBox {
        .navBox {
            .nLi {
                h3 a {
                    font-size: 16px;
                }
            }
        }

        .language {
            font-size: 16px;
            margin: 0 40px;
        }
    
        .login {
            width: 110px;
            font-size: 14px;
            height: 40px;
            line-height: 40px;
            margin-top: 15px;
    
            .iconfont {
                font-size: 16px;
            }
        }
    }
}
@media (max-width: 1560px) {
    .headerBox {
        .navBox {
            .nLi {
                // margin-left: 30px;
                margin-left: 60px;
            }
        }
    }
}
@media (max-width: 1480px) {
    .headerBox {
        .logo {
            width: 124px;
            height: 56px;
            margin-top: 7px;
        }

        .box {
            width: calc(100% - 124px);
        }

        .navBox {
            .nLi {
                // margin-left: 28px;
                margin-left: 48px;
            }
        }

        .language {
            // margin: 0 30px;
            margin: 0 50px;
        }

        .login {
            width: 100px;

            .iconfont {
                margin-right: 6px;
            }

            &.login1 {
                width: 110px;
                font-size: 12px;

                .iconfont {
                    font-size: 14px;
                }
            }
        }
    }
}
@media (max-width: 1366px) {
    .headerBox {
        .navBox {
            .nLi {
                // margin-left: 20px;
                margin-left: 40px;
            }
        }
    }
}

/* indexBanner */
.indexBanner {
    width: 100%;
    overflow: hidden;
    margin-top: 50px;

    // a {
    //     display: block;
    //     width: 100%;
    //     height: 100vh;
    //     background-size: cover !important;
    // }

    img {
        width: 100%;
        display: block;
    }

    .swiper-button-next:after, .swiper-button-prev:after {
        color: #fff;
        opacity: 0;
    }

}

/* indexTitle */
.indexTitle {
    width: 100%;
    align-items: center;

    .title {
        font-size: 40px;
        line-height: 44px;
        color: #333333;
        font-weight: bold;
    }

    ul {
        li {
            font-size: 20px;
            font-weight: 300;
            line-height: 24px;
            color: rgba(51, 51, 51, 0.8);
            padding-bottom: 14px;
            margin-left: 50px;
            cursor: pointer;

            &.on {
                font-weight: bold;
                color: #082CA4;
                border-bottom: 2px solid #082CA4;
            }
        }
    }

    .more {
        font-size: 16px;
        line-height: 44px;
        color: #333333;

        .iconfont {
            font-size: 18px;
            color: #082CA4;
            transform: rotate(180deg);
            margin-left: 10px;
        }
    }
}
@media (max-width: 1680px) {
    .indexTitle {
    
        .title {
            font-size: 32px;
            line-height: 36px;
        }
    
        ul {
            li {
                font-size: 18px;
                line-height: 22px;
            }
        }
    
        .more {
            line-height: 36px;
        }
    }
}
@media (max-width: 1480px) {
    .indexTitle {
    
        .title {
            font-size: 30px;
            line-height: 34px;
        }

        ul {
            li {
                font-size: 16px;
                line-height: 20px;
            }
        }

        .more {
            line-height: 34px;
        }
    }
}

/* indexSchedule */
.indexSchedule {
    width: 100%;
    background: #fff url(../images/bg.png) top -600px right -770px no-repeat;
    background-size: 1209px auto;
    padding: 100px 0 70px;
    overflow: hidden;

    .timeBox {
        width: 100%;
        margin-top: 50px;

        li {
            width: 15%;
            height: 56px;
            border-radius: 28px;
            border: 1px solid #082CA4;
            font-size: 20px;
            line-height: 56px;
            color: #082CA4;
            text-align: center;
            cursor: pointer;
            margin: 0 4%;
            transition: all 0.6s ease;

            &.on, &:hover {
                background-color: #082CA4;
                color: #fff;
            }
        }
    }
}
.scheduleBox {
    width: 100%;
    margin-top: 40px;

    .liBox {
        width: 100%;
        margin-bottom: 20px;
    }

    .Box {
        width: 100%;
        min-height: 110px;
        background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
        border: 2px solid rgba(8, 44, 164, 0.08);
        align-items: center;
        padding: 1% 30px;
        transition: all 0.6s ease;

        .titBox {
            width: calc(100% - 200px);

            .tit {
                max-width: calc(100% - 130px);
                font-size: 18px;
                font-weight: bold;
                line-height: 26px;
                color: #333333;
            }

            .label {
                min-width: 78px;
                height: 28px;
                line-height: 28px;
                border-radius: 2px;
                font-size: 14px;
                margin-left: 20px;
                padding: 0 4px;

                &.label1 {
                    border: 1px solid #52A199;
                    color: #52A199;
                }

                &.label2 {
                    border: 1px solid #082CA4;
                    color: #082CA4;
                }

                &.label3 {
                    border: 1px solid #999999;
                    color: #999999;
                }

                .iconfont {
                    margin-right: 3px;
                    font-size: 16px;
                }
            }

            .address {
                font-size: 16px;
                line-height: 20px;
                color: #999999;
                margin-top: 18px;

                .iconfont {
                    font-size: 18px;
                    margin-right: 5px;
                }
            }
        }

        .btnBox {
            align-items: center;

            .btn {
                display: block;
                width: 130px;
                height: 40px;
                line-height: 40px;
                border-radius: 20px;
                font-size: 16px;
                color: #FFFFFF;
                text-align: center;

                &.btn1 {
                    background-color: #082CA4;
                }

                &.btn2 {
                    background-color: #CDCFD6;
                }
            }

            .iconfont {
                width: 40px;
                height: 40px;
                line-height: 40px;
                color: #fff;
                font-size: 12px;
                margin-left: 16px;
                background-color: #262626;
                text-align: center;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.6s ease;
            }
        }

        &.on {
            background: url(../images/titBg.jpg) center center no-repeat;
            background-size: cover;

            .titBox {

                .tit {
                    color: #fff;
                }

                .label {
                    &.label1 {
                        border: 1px solid #fff;
                        color: #fff;
                    }

                    &.label2 {
                        border: 1px solid #fff;
                        color: #fff;
                    }

                    &.label3 {
                        border: 1px solid #fff;
                        color: #fff;
                    }
                }

                .address {
                    color: #fff;
                }
            }

            .btnBox {
                .btn {

                    &.btn1 {
                        color: #082CA4;
                        background-color: #fff;
                    }

                    &.btn2 {
                        color: #CDCFD6;
                        background-color: #fff;
                    }
                }

                .iconfont {
                    background-color: #fff;
                    color: #333;
                    transform: rotate(90deg);
                }
            }
        }
    }

    .contBox {
        width: 100%;
        background-color: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-top: none;
        display: none;

        &.sel {
            display: block;
        }

        .cont {
            width: 100%;
            background-color: #FAFBFF;
            padding: 1% 30px 26px;

            .t {
                font-size: 16px;
                line-height: 24px;
                color: #3D3D3D;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                padding: 12px 0;

                p {
                    margin: 6px 0;

                    &:nth-child(1) {
                        min-width: 80px;
                    }

                    &:nth-child(2) {
                        min-width: calc(100% - 80px);
                    }

                    span {
                        &:last-child {
                            em {
                                display: none;
                            }
                        }
                    }
                }
            }

            .t1 {
                font-size: 16px;
                line-height: 20px;
                color: #3D3D3D;
                padding: 20px 0 14px;
            }

            .t2 {
                font-size: 14px;
                line-height: 28px;
                color: #666666;
                // height: 64px;
                overflow: hidden;

                &.on {
                    height: auto;
                }
            }

            .show {
                text-align: center;
                margin-top: 20px;
                font-size: 14px;
                line-height: 18px;
                color: #333333;
                cursor: pointer;

                p {
                    margin-bottom: 5px;
                }

                .iconfont {
                    width: 36px;
                    height: 36px;
                    line-height: 36px;
                    border-radius: 18px;
                    background-color: #262626;
                    color: #fff;
                    transform: rotate(90deg);
                    margin: 0 auto;
                    text-align: center;
                    font-size: 16px;
                }
            }
        }

        .dl {
            width: 100%;
            padding: 50px 30px 20px;
            position: relative;

            &::after {
                content: '';
                width: 1px;
                height: calc(100% - 156px);
                border-left: 1px dashed #082CA4;
                position: absolute;
                top: 79px;
                left: 190px;
            }

            .dt {
                width: 100%;
                margin-bottom: 30px;

                .time {
                    width: 170px;
                    font-size: 16px;
                    line-height: 22px;
                    color: #333333;
                    margin-right: 60px;
                    padding-right: 70px;
                    text-align: right;
                    position: relative;
                    padding-top: 20px;

                    &::after {
                        content: '';
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        background-color: rgba(8, 44, 164, 0.1);
                        position: absolute;
                        top: 23px;
                        right: 2px;
                    }

                    &::before {
                        content: '';
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background-color: #082CA4;
                        position: absolute;
                        top: 27px;
                        right: 6px;
                    }
                }

                .dd {
                    width: calc(100% - 230px);
                    background-color: #FAFBFF;
                    padding: 20px 2% 0;

                    .t3 {
                        font-size: 16px;
                        line-height: 22px;
                        color: #333333;
                        margin-bottom: 20px;
                        font-weight: bold;
                    }

                    .li {
                        width: 31.3333%;
                        margin: 0 3% 30px 0;
                        align-items: center;

                        &:nth-child(3n) {
                            margin: 0 0 30px 0;
                        }

                        .img {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            overflow: hidden;
                            margin-right: 10px;

                            img {
                                width: 100%;
                                display: block;
                            }
                        }

                        .t4 {
                            width: calc(100% - 60px);

                            .name {
                                font-size: 14px;
                                line-height: 18px;
                                color: #333333;

                                span {
                                    font-size: 12px;
                                    line-height: 16px;
                                    color: #999;
                                }
                            }

                            .t5 {
                                font-size: 12px;
                                line-height: 18px;
                                color: #999;
                                margin-top: 4px;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .indexSchedule {
        .timeBox {
            li {
                height: 46px;
                line-height: 46px;
                border-radius: 23px;
                font-size: 16px;
            }
        }
    }
    
    .scheduleBox {
        .Box {
            .titBox {
                width: calc(100% - 190px);
                .tit {
                    max-width: calc(100% - 110px);
                    font-size: 16px;
                    line-height: 24px;
                }

                .label {
                    min-width: 70px;
                    height: 26px;
                    line-height: 26px;
                    font-size: 12px;

                    .iconfont {
                        font-size: 14px;
                    }
                }

                .address {
                    font-size: 14px;
                    line-height: 18px;

                    .iconfont {
                        font-size: 16px;
                    }
                }
            }

            .btnBox {
                .btn {
                    width: 120px;
                    height: 36px;
                    line-height: 36px;
                    border-radius: 18px;
                    font-size: 14px;
                }

                .iconfont {
                    width: 36px;
                    height: 36px;
                    line-height: 36px;
                }
            }
        }
    }
}

/* indexNews */
.indexNews {
    width: 100%;
    overflow: hidden;
    padding: 120px 0;
    background: url(../images/newsBg.jpg) center center no-repeat;
    background-size: cover;

    .box {
        width: 100%;
        margin-top: 50px;
    }

    .contBox {
        display: block;
        width: 32%;
        background-color: #fff;

        .img {
            width: 100%;
            height: 322px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                display: block;
                transition: all 0.6s ease;
            }
        }

        .cont {
            padding: 6%;

            .time {
                display: inline-block;
                height: 36px;
                line-height: 36px;
                border-radius: 2px;
                background-color: #082CA4;
                padding: 0 12px;
                font-size: 14px;
                color: #FFFFFF;
            }

            .tit {
                font-size: 20px;
                line-height: 24px;
                color: #333333;
                margin-top: 20px;
                transition: all 0.6s ease;
            }

            .txt {
                font-size: 16px;
                line-height: 28px;
                color: #666666;
                margin-top: 16px;
                height: 56px;
            }
        }

        &:hover {
            .img img {
                transform: scale(1.1);
            }

            .cont .tit {
                color: #082CA4;
            }
        }
    }

    .newsBox {
        width: 32%;
        background: url(../images/newsImgBg.jpg) center center no-repeat;
        background-size: cover;

        a {
            display: block;
            width: 100%;
            height: 100%;
            padding: 13% 9%;

            .time {
                display: inline-block;
                height: 36px;
                line-height: 36px;
                border-radius: 2px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 0 12px;
                font-size: 14px;
                color: #FFFFFF;
            }

            .tit {
                font-size: 20px;
                line-height: 36px;
                color: #FFFFFF;
                margin-top: 20px;
            }

            .txt {
                font-size: 16px;
                line-height: 32px;
                color: #FFFFFF;
                margin-top: 20px;
            }
        }

        .swiper-pagination {
            padding: 0 9%;
            bottom: 10%;
            display: flex;
            justify-content: flex-start;

            .swiper-pagination-bullet {
                opacity: 0.5;
                background-color: #fff;
                margin: 0 15px 0 0;

                &.swiper-pagination-bullet-active {
                    opacity: 1;
                }
            }
        }
    }

    ul {
        width: 32%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        li {
            width: 100%;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            padding-bottom: 2.8%;
            align-items: center;

            &:last-child {
                border-bottom: none;
                padding-bottom: 0;
            }

            a {
                align-items: center;
            }

            .img {
                width: 49%;
                height: 158px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                    transition: all 0.6s ease;
                }
            }

            .cont {
                width: 45%;

                .tit {
                    font-size: 18px;
                    line-height: 22px;
                    color: #333333;
                    transition: all 0.6s ease;
                }

                .txt {
                    font-size: 14px;
                    line-height: 24px;
                    color: #666666;
                    margin-top: 14px;
                }

                .time {
                    font-size: 14px;
                    line-height: 18px;
                    color: #999999;
                    margin-top: 14px;
                }
            }

            &:hover {
                .img img {
                    transform: scale(1.1);
                }
    
                .cont .tit {
                    color: #082CA4;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .indexNews {
        .contBox {
            .img {
                height: 268px;
            }
        }

        ul {
            li {
                padding-bottom: 4.7%;

                .img {
                    height: 131px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .indexNews {
        .contBox {
            .img {
                height: 246px;
            }

            .cont {
                .time {
                    height: 32px;
                    line-height: 32px;
                }
    
                .tit {
                    font-size: 18px;
                    line-height: 22px;
                }
    
                .txt {
                    font-size: 14px;
                    line-height: 28px;
                }
            }
        }
    
        .newsBox {
            a {
                .time {
                    height: 32px;
                    line-height: 32px;
                    font-size: 14px;
                }
    
                .tit {
                    font-size: 18px;
                    line-height: 30px;
                }
    
                .txt {
                    font-size: 14px;
                    line-height: 28px;
                }
            }
        }
    
        ul {
            li {
                padding-bottom: 5.4%;

                .img {
                    height: 120px;
                }

                .cont {
                    .tit {
                        font-size: 16px;
                        line-height: 20px;
                    }
    
                    .txt {
                        font-size: 12px;
                        line-height: 22px;
                        margin-top: 10px;
                    }
    
                    .time {
                        font-size: 12px;
                        line-height: 16px;
                        margin-top: 10px;
                    }
                }
            }
        }
    }
}

/* indexReport */
.indexReport {
    width: 100%;
    padding: 126px 0;
    background: url(../images/reportBg.jpg) center center no-repeat;
    background-size: cover;

    .tabList {
        width: 100%;
        position: relative;

        li {
            padding-bottom: 12px;
            margin-right: 40px;
            cursor: pointer;

            .p {
                font-size: 40px;
                font-weight: 300;
                line-height: 44px;
                color: rgba(255, 255, 255, 0.8);
            }

            .more {
                font-size: 16px;
                line-height: 30px;
                color: #fff;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 0;
                display: none;
                
                .iconfont {
                    font-size: 18px;
                    color: #fff;
                    transform: rotate(180deg);
                    margin-left: 10px;
                }
            }

            &.on {
                border-bottom: 2px solid #fff;

                .p {
                    font-weight: bold;
                    color: #FFFFFF;
                }

                .more {
                    display: block;
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin-top: 36px;
        position: relative;
    }

    .reportBox {
        width: 100%;
        padding: 0 calc((100% - 1440px) / 2) 110px;

        .swiper-slide {

            a {
                display: block;
                width: 100%;
                height: 340px;
                background-color: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.1);
                padding: 11% 7% 5%;
                transition: all 0.6s ease;

                .label {
                    display: inline-block;
                    height: 34px;
                    line-height: 34px;
                    border-radius: 2px;
                    background-color: #082CA4;
                    font-size: 14px;
                    color: #FFFFFF;
                    padding: 0 14px;
                    transition: all 0.6s ease;
                }

                .tit {
                    font-size: 20px;
                    line-height: 32px;
                    color: #FFFFFF;
                    margin-top: 30px;
                }

                .txt {
                    font-size: 16px;
                    line-height: 30px;
                    color: #FFFFFF;
                    margin-top: 16px;
                }

                .more {
                    font-size: 16px;
                    line-height: 18px;
                    color: #FFFFFF;
                    margin-top: 40px;

                    .iconfont {
                        width: 18px;
                        height: 18px;
                        line-height: 18px;
                        border-radius: 50%;
                        background-color: #fff;
                        color: #333;
                        font-size: 10px;
                        font-weight: bold;
                        text-align: center;
                        margin-left: 12px;
                    }
                }

                .down {
                    width: 150px;
                    height: 40px;
                    line-height: 40px;
                    border-radius: 20px;
                    background-color: #082CA4;
                    font-size: 14px;
                    color: #FFFFFF;
                    text-align: center;
                    margin-top: 50px;
                    transition: all 0.6s ease;
                }

                &:hover {
                    background-color: #082CA4;
                    border: 2px solid #082CA4;

                    .label {
                        background-color: #fff;
                        color: #082CA4;
                    }

                    .down {
                        background-color: #fff;
                        color: #082CA4;
                    }
                }
            }
        }

        .arrow {
            width: 156px;
            height: 48px;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);

            .swiper-button-next, .swiper-button-prev {
                width: 48px;
                height: 48px;
                background-color: #fff;
                border-radius: 50%;
                top: 0;
                margin-top: 0;
                transition: all 0.6s ease;

                &::after {
                    color: #000;
                    font-size: 14px;
                }

                &:hover {
                    background-color: #082CA4;
                    
                    &::after {
                        color: #fff;
                    }
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .indexReport {
        .tabList {
            li {
                .p {
                    font-size: 32px;
                    line-height: 36px;
                }
            }
        }

        .reportBox {
            padding: 0 calc((100% - 1200px) / 2) 110px;
        }
    }
}
@media (max-width: 1480px) {
    .indexReport {
        .tabList {
            li {
                .p {
                    font-size: 30px;
                    line-height: 34px;
                }
            }
        }

        .reportBox {
            width: 100%;
            padding: 0 calc((100% - 1100px) / 2) 110px;
    
            .swiper-slide {
    
                a {
                    height: 310px;
    
                    .label {
                        height: 32px;
                        line-height: 32px;
                        font-size: 12px;
                    }
    
                    .tit {
                        font-size: 18px;
                        line-height: 30px;
                    }
    
                    .txt {
                        font-size: 14px;
                        line-height: 28px;
                    }
    
                    .more {
                        font-size: 14px;
                        line-height: 16px;
    
                        .iconfont {
                            width: 16px;
                            height: 16px;
                            line-height: 16px;
                            font-size: 8px;
                            margin-left: 10px;
                        }
                    }
    
                    .down {
                        width: 130px;
                        height: 36px;
                        line-height: 36px;
                        border-radius: 18px;
                        font-size: 12px;
                    }
                }
            }
    
            .arrow {
                width: 136px;
                height: 40px;
    
                .swiper-button-next, .swiper-button-prev {
                    width: 40px;
                    height: 40px;
    
                    &::after {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

/* indexActivity */
.indexActivity {
    width: 100%;
    padding: 100px 0 140px;
    background: #efefef url(../images/activityBg.png) center center no-repeat;
    background-size: cover;

    .activityBox {
        width: 100%;
        margin-top: 50px;
        padding-bottom: 60px;
        
        .swiper-slide {
            width: 560px;
            height: 450px;
            transition: all 0.3s ease;

            .activity-card {
                width: 100%;
                height: 100%;
                display: block;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-8px);
                    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
                }

                .card-bg {
                    width: 100%;
                    height: 100%;
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    position: relative;

                    .card-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.7) 100%);
                        display: flex;
                        align-items: flex-end;
                        padding: 30px;

                        .card-content {
                            width: 100%;

                            .label {
                                display: inline-block;
                                height: 32px;
                                line-height: 32px;
                                border-radius: 4px;
                                background-color: #082CA4;
                                padding: 0 16px;
                                font-size: 14px;
                                color: #FFFFFF;
                                font-weight: 500;
                                margin-bottom: 16px;
                            }

                            .tit {
                                font-size: 20px;
                                line-height: 28px;
                                color: #FFFFFF;
                                font-weight: 600;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }
            }
        }

        .swiper-pagination {
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .swiper-pagination-bullet {
                width: 14px;
                height: 14px;
                opacity: 0.4;
                background-color: #082CA4;
                margin: 0 10px;

                &.swiper-pagination-bullet-active {
                    width: 18px;
                    height: 18px;
                    opacity: 1;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .indexActivity {
        .activityBox {
            .swiper-slide {
                width: 480px;
                height: 380px;

                .activity-card {
                    .card-overlay {
                        padding: 24px;

                        .card-content {
                            .label {
                                height: 28px;
                                line-height: 28px;
                                font-size: 12px;
                                padding: 0 12px;
                                margin-bottom: 12px;
                            }

                            .tit {
                                font-size: 18px;
                                line-height: 26px;
                            }
                        }
                    }
                }
            }

            .swiper-pagination {
                .swiper-pagination-bullet {
                    width: 10px;
                    height: 10px;

                    &.swiper-pagination-bullet-active {
                        width: 14px;
                        height: 14px;
                    }
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .indexActivity {
        .activityBox {
            .swiper-slide {
                width: 420px;
                height: 340px;

                .activity-card {
                    .card-overlay {
                        padding: 20px;

                        .card-content {
                            .label {
                                height: 26px;
                                line-height: 26px;
                                font-size: 11px;
                                padding: 0 10px;
                                margin-bottom: 10px;
                            }

                            .tit {
                                font-size: 16px;
                                line-height: 24px;
                            }
                        }
                    }
                }
            }

            .swiper-pagination {
                .swiper-pagination-bullet {
                    width: 8px;
                    height: 8px;

                    &.swiper-pagination-bullet-active {
                        width: 12px;
                        height: 12px;
                    }
                }
            }
        }
    }
}

/* indexAd */
.indexAd {
    width: 100%;
    padding: 85px 0;
    background: url(../images/adBg.jpg) center center no-repeat;
    background-size: cover;

    .t1 {
        font-size: 20px;
        line-height: 24px;
        color: #FFFFFF;
    }

    .t2 {
        font-size: 40px;
        font-weight: bold;
        line-height: 44px;
        color: #FFFFFF;
        margin-top: 26px;
    }
}
@media (max-width: 1480px) {
    .indexAd {

        .t1 {
            font-size: 18px;
            line-height: 26px;
        }
    
        .t2 {
            font-size: 32px;
            line-height: 36px;
        }
    }
}

/* indexVideo */
.indexVideo {
    width: 100%;
    padding: 100px 0 80px;
    // background: #fff url(../images/videoBg.png) bottom -600px left -370px no-repeat;
    // background-size: 1209px auto;
    background: url(../images/videoBg.jpg) center center no-repeat;
    background-size: cover;

    .indexTitle {
        .title {
            color: #fff;
        }
    
        ul {
            li {
                color: rgba(255, 255, 255, 0.8);
    
                &.on {
                    color: #fff;
                    border-bottom: 2px solid #fff;
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin-top: 50px;
    }

    .videoBox {
        width: 100%;
        padding: 0 calc((100% - 1440px) / 2) 130px;

        .swiper-slide {

            a {
                display: block;
                width: 100%;

                .img {
                    width: 100%;
                    height: 486px;
                    overflow: hidden;
                    position: relative;

                    img {
                        width: 100%;
                        height: 100%;
                        display: block;
                        transition: all 0.6s ease;
                    }

                    .iconfont {
                        position: absolute;
                        bottom: 30px;
                        right: 30px;
                        color: #fff;
                        font-size: 40px;
                        line-height: 44px;
                    }
                }

                .cont {
                    width: 100%;
                    position: relative;
                    padding-top: 60px;

                    .label {
                        display: inline-block;
                        height: 32px;
                        line-height: 32px;
                        border-radius: 2px;
                        background-color: #082CA4;
                        font-size: 14px;
                        color: #FFFFFF;
                        padding: 0 14px;
                        position: absolute;
                        top: -18px;
                        left: 20px;
                    }

                    .tit {
                        font-size: 20px;
                        line-height: 24px;
                        color: #fff;
                        transition: all 0.6s ease;
                    }

                    .txt {
                        font-size: 16px;
                        line-height: 20px;
                        color: #999999;
                        margin-top: 30px;
                    }  
                }

                &:hover {
                    .img img {
                        transform: scale(1.1);
                    }
        
                    // .cont .tit {
                    //     color: #082CA4;
                    // }
                }
            }
        }

        .arrowBox {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            align-items: center;

            .arrow {
                width: 130px;
                height: 48px;
                position: relative;

                .swiper-button-next {
                    right: 0;
                }

                .swiper-button-prev {
                    left: 0;
                }

                .swiper-button-next, .swiper-button-prev {
                    width: 48px;
                    height: 48px;
                    background-color: #262626;
                    border-radius: 50%;
                    top: 0;
                    margin-top: 0;
                    transition: all 0.6s ease;

                    &::after {
                        color: #fff;
                        font-size: 14px;
                    }

                    &:hover {
                        background-color: #082CA4;
                    }
                }
            }

            .more {
                font-size: 16px;
                line-height: 32px;
                color: #fff;
        
                .iconfont {
                    font-size: 18px;
                    color: #fff;
                    transform: rotate(180deg);
                    margin-left: 10px;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .indexVideo {
        .videoBox {
            padding: 0 calc((100% - 1200px) / 2) 130px;

            .swiper-slide {
                a {
                    .img {
                        height: 402px;
                    }
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .indexVideo {
        .videoBox {
            padding: 0 calc((100% - 1100px) / 2) 130px;
    
            .swiper-slide {
                a {
                    .img {
                        height: 367px;

                        .iconfont {
                            bottom: 20px;
                            right: 20px;
                            font-size: 34px;
                            line-height: 38px;
                        }
                    }
    
                    .cont {
                        padding-top: 50px;
    
                        .label {
                            height: 30px;
                            line-height: 30px;
                            font-size: 12px;
                        }
    
                        .tit {
                            font-size: 18px;
                            line-height: 22px;
                        }
    
                        .txt {
                            font-size: 14px;
                            line-height: 18px;
                            margin-top: 20px;
                        }  
                    }
                }
            }
    
            .arrowBox {
    
                .arrow {
                    width: 120px;
                    height: 40px;
    
                    .swiper-button-next, .swiper-button-prev {
                        width: 40px;
                        height: 40px;
    
                        &::after {
                            font-size: 12px;
                        }
                    }
                }
            }
        }
    }
}

/* indexPartner */
.indexPartner {
    width: 100%;
    background-color: #fff;
    padding: 120px 0 90px;

    .box {
        width: 100%;

        .tit {
            width: 192px;
            height: 105px;
            background: url(../images/partnerImgBg.png) center center no-repeat;
            background-size: 100% 100%;
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
            text-align: center;
            color: #333;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 20px 0 10px;
            margin-bottom: 16px;
        }

        ul {
            width: calc(100% - 192px);

            li {
                width: 192px;
                height: 105px;
                // background-color: rgba(255, 255, 255, 0.5);
                // border: 2px solid rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(51, 51, 51, 0.2);
                background-color: #fff;
                margin-left: 16px;
                margin-bottom: 16px;
                transition: all 0.6s ease;

                a {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                img {
                    // width: 100%;
                    // display: block;
                    max-width: 100%;
                    max-height: 100%;
                }

                // &:hover {
                //     background-color: #082CA4;
                //     border: 2px solid #082CA4;
                // }
            }
        }
    }
}
@media (max-width: 1680px) {
    .indexPartner {
        .box {
            .tit {
                width: 162px;
                height: 88px;
                margin-bottom: 11px;
                font-size: 16px;
                line-height: 22px;
                padding: 0 20px 0 10px;
            }
    
            ul {
                width: calc(100% - 162px);
    
                li {
                    width: 162px;
                    height: 88px;
                    margin-left: 11px;
                    margin-bottom: 11px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .indexPartner {
        .box {
            .tit {
                width: 146px;
                height: 80px;
                margin-bottom: 13px;
                font-size: 14px;
                line-height: 20px;
                padding: 0 20px 0 10px;
            }
    
            ul {
                width: calc(100% - 146px);
    
                li {
                    width: 146px;
                    height: 80px;
                    margin-left: 13px;
                    margin-bottom: 13px;
                }
            }
        }
    }
}

/* footer */
.footer {
    width: 100%;
    background-color: #262626;
    padding-top: 80px;

    dl {
        padding-right: 2%;
        
        dt {
            font-size: 20px;
            line-height: 24px;
            color: #FFFFFF;
            margin-bottom: 20px;
        }

        dd {
            font-size: 16px;
            line-height: 30px;
            color: rgba(255, 255, 255, 0.8);
        }
    }

    .codeBox {
        .p {
            font-size: 20px;
            line-height: 24px;
            color: #FFFFFF;
            margin-bottom: 20px;
            margin-left: 20px;
        }

        .code {
            width: 100px;
            margin-left: 20px;

            img {
                width: 100px;
                display: block;
                background-color: #ffffff;
            }
    
            p {
                font-size: 14px;
                line-height: 18px;
                color: rgba(255, 255, 255, 0.8);
                text-align: center;
                margin-top: 12px;
            }
        }
    }

    .copyRight {
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 60px;
        padding: 30px 0;
        font-size: 14px;
        line-height: 18px;
        text-align: center;

        span {
            color: rgba(255, 255, 255, 0.6);
        }

        a {
            color: rgba(255, 255, 255, 0.6);

            &:hover {
                color: #fff;
            }
        }
    }
}
@media (max-width: 1680px) {
    .footer {
        dl {
            dt {
                font-size: 18px;
                line-height: 22px;
            }
    
            dd {
                font-size: 14px;
                line-height: 28px;
            }
        }
    
        .codeBox {
            .p {
                font-size: 18px;
                line-height: 22px;
            }
        }
    }
}

/* sidebarBox */
.sidebarBox {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    z-index: 99;

    li {
        width: 90px;
        height: 72px;
        background-color: #666;
        border-bottom: 1px solid #fff;
        text-align: center;
        position: relative;

        &:last-child {
            border-bottom: none;
        }

        a {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .iconfont {
                color: #fff;
                font-size: 26px;
                line-height: 30px;
            }
    
            .t {
                font-size: 14px;
                line-height: 18px;
                color: #fff;
                margin-top: 6px;
            }
        }

        .img {
            width: 100px;
            overflow: hidden;
            background-color: #082CA4;
            padding: 5px;
            position: absolute;
            right: -100px;
            top: 0;
            z-index: 3;
            transition: all 0.6s ease;

            img {
                width: 100%;
                display: block;
            }
        }

        &:hover {
            background-color: #082CA4;

            .img {
                right: 90px;
            }
        }
    }

    &.sidebarBox1 {
        li {
            a {
                .iconfont {
                    font-size: 24px;
                    line-height: 28px;
                }
        
                .t {
                    font-size: 12px;
                    line-height: 16px;
                    margin-top: 0;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .sidebarBox {
        li {
            width: 80px;
            height: 62px;
    
            a {
                .iconfont {
                    font-size: 22px;
                    line-height: 26px;
                }
        
                .t {
                    font-size: 12px;
                    line-height: 16px;
                    margin-top: 6px;
                }
            }
    
            .img {
                width: 90px;
            }
    
            &:hover {
                .img {
                    right: 80px;
                }
            }
        }
    
        &.sidebarBox1 {
            li {
                a {
                    .iconfont {
                        font-size: 20px;
                        line-height: 24px;
                    }
            
                    .t {
                        font-size: 12px;
                        line-height: 16px;
                        margin-top: 0;
                    }
                }
            }
        }
    }
}

/* pageBanner */
.pageBanner {
    width: 100%;
    height: 400px;
    position: relative;
    background-size: cover !important;

    .box {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);

        .wrap {
            min-height: 68px;
            align-items: center;
        }

        .location {
            width: 25%;
            align-items: center;
            line-height: 24px;

            .sel {
                color: #082CA4;
                font-size: 22px;
                margin-right: 8px;
            }

            .icon-jiantou {
                color: #999999;
                font-size: 12px;
                margin: 0 6px;
            }

            p {
                font-size: 16px;
                color: #999999;
            }

            a {
                font-size: 16px;
                color: #999999;

                &:hover, &.on {
                    color: #082CA4;
                }
            }
        }

        .tabBox {
            width: 75%;

            li {
                margin-left: 6%;
                border-bottom: 3px solid #fff;
                position: relative;

                a {
                    display: block;
                    font-size: 18px;
                    line-height: 22px;
                    color: #333333;
                    padding: 24px 0 23px;
                }

                &.on {
                    border-bottom: 3px solid #082CA4;

                    a {
                        color: #082CA4;
                        font-weight: bold;
                    }
                }

                .ul {
                    width: 200px;
                    position: absolute;
                    top: 72px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: #FAFBFF;
                    padding: 10px;
                    display: none;

                    &.ul1 {
                        top: 93px;
                    }

                    .li {
                        width: 100%;
                        display: block;
                        font-size: 16px;
                        line-height: 20px;
                        color: #666;
                        padding: 12px 0;
                        text-align: center;
                        font-weight: normal;

                        &:hover {
                            color: #082CA4;
                        }
                    }
                }
            }

            &.tabBox1 {
                width: 100%;
                flex-wrap: nowrap;

                li {
                    margin-left: 0;
                    margin-right: 3%;

                    &:last-child {
                        margin-right: 0;
                    }
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageBanner {
        .box {
            .tabBox {
                li {
                    margin-left: 5%;
    
                    a {
                        font-size: 16px;
                        line-height: 20px;
                    }
    
                    .ul {
                        top: 70px;

                        &.ul1 {
                            top: 91px;
                        }
    
                        .li {
                            font-size: 14px;
                            line-height: 18px;
                        }
                    }
                }

                &.tabBox1 {
                    li {
                        margin-left: 0;
                        margin-right: 3%;
                    }
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageBanner {
        .box {
            .location {
                .sel {
                    font-size: 18px;
                    margin-right: 6px;
                }
    
                .icon-jiantou {
                    font-size: 10px;
                    margin: 0 5px;
                }
    
                p {
                    font-size: 14px;
                }
    
                a {
                    font-size: 14px;
                }
            }
    
            .tabBox {
                li {
    
                    a {
                        font-size: 14px;
                        line-height: 18px;
                    }

                    .ul {
                        top: 68px;

                        &.ul1 {
                            top: 86px;
                        }
                    }
                }            

                &.tabBox1 {
    
                    li {
                        margin-left: 0;
                        margin-right: 3%;
                    }
                }
            }
        }
    }
}

/* pageTitle */
.pageTitle {
    font-size: 40px;
    line-height: 44px;
    color: #333333;
    position: relative;
    padding-bottom: 24px;
    font-weight: bold;

    &::after {
        content: '';
        width: 40px;
        height: 6px;
        background-color: #082CA4;
        position: absolute;
        bottom: 0;
        left: 0;
    }
}
@media (max-width: 1680px) {
    .pageTitle {
        font-size: 32px;
        line-height: 36px;
    
        &::after {
            width: 32px;
            height: 5px;
        }
    }
}
@media (max-width: 1480px) {
    .pageTitle {
        font-size: 30px;
        line-height: 34px;
        padding-bottom: 20px;
    
        &::after {
            width: 30px;
            height: 4px;
        }
    }
}

/* pageTab */
.pageTab {
    width: 100%;
    padding-top: 50px;

    ul {

        li {
            min-width: 14.6666%;
            height: 56px;
            border-radius: 28px;
            background-color: #FFFFFF;
            border: 1px solid rgba(0, 0, 0, 0.1);
            margin: 0 1%;
            transition: all 0.6s ease;

            a {
                display: block;
                width: 100%;
                height: 100%;
                font-size: 18px;
                line-height: 56px;
                text-align: center;
                color: #333333;
                padding: 0 10px;
                transition: all 0.6s ease;
            }

            &.on, &:hover {
                background-color: #082CA4;

                a {
                    color: #fff;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageTab {
        ul {
    
            li {
                height: 52px;
                border-radius: 26px;
    
                a {
                    font-size: 16px;
                    line-height: 52px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageTab {
        ul {
            li {
                height: 46px;
                border-radius: 23px;
    
                a {
                    font-size: 14px;
                    line-height: 46px;
                }
            }
        }
    }
}

/* pageAbout */
.pageAbout {
    width: 100%;
    padding: 80px 0 120px;

    .text {
        font-size: 16px;
        line-height: 36px;
        color: #666666;
        text-align: justify;
        margin-top: 40px;

        img {
            max-width: 100%;
            height: auto;
            margin: auto;
        }
    }

    .img {
        width: 100%;
        overflow: hidden;
        margin-top: 30px;

        img {
            width: 100%;
            display: block;
        }
    }
}

/* pageOrganization */
.pageOrganization {
    width: 100%;
    padding: 80px 0 120px;

    ul {
        width: 100%;
        margin-top: 40px;

        li {
            width: 100%;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-left: 3px solid #082CA4;
            margin-bottom: 30px;
            padding: 30px 40px;

            .t1 {
                font-size: 20px;
                line-height: 30px;
                color: #666666;
                font-weight: bold;
            }

            .t2 {
                font-size: 16px;
                line-height: 28px;
                color: #666666;
                margin-top: 10px;

                p {
                    margin-right: 20px;
                }
            }
        }
    }
}

/* pageNews */
.pageNews {
    width: 100%;
    padding: 80px 0 60px;

    .newsBox {
        width: 100%;
        padding-bottom: 70px;

        .swiper-slide {
            background-color: #FAFBFF;
        }

        a {
            width: 100%;
            align-items: center;

            .img {
                width: 50%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                    transition: all 0.6s ease;
                }
            }

            .cont {
                width: 50%;
                padding: 0 3.5%;

                .tit {
                    font-size: 20px;
                    line-height: 36px;
                    color: #333;
                    transition: all 0.6s ease;
                }
    
                .txt {
                    font-size: 16px;
                    line-height: 30px;
                    color: #999;
                    margin-top: 30px;
                }
    
                .time {
                    font-size: 16px;
                    line-height: 30px;
                    color: #666;
                    margin-top: 60px;
                }
            }

            &:hover {
                .img img {
                    transform: scale(1.1);
                }
    
                .cont .tit {
                    color: #082CA4;
                }
            }
        }

        .swiper-pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            bottom: 0 !important;

            .swiper-pagination-bullet {
                width: 14px;
                height: 14px;
                opacity: 0.5;
                background-color: #082CA4;
                margin: 0 10px;

                &.swiper-pagination-bullet-active {
                    width: 18px;
                    height: 18px;
                    opacity: 1;
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin: 80px 0 60px;

        li {
            width: 100%;
            border-top: 1px solid rgba(0, 0, 0, 0.1);

            &:last-child {
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }

            a {
                background-color: #ffffff;
                padding: 80px 0;
                align-items: center;
            }

            .time {
                font-size: 22px;
                line-height: 44px;
                color: #999999;
                transition: all 0.6s ease;

                p {
                    font-size: 48px;
                    color: #333333;
                    transition: all 0.6s ease;
                }
            }

            .img {
                width: 320px;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                    transition: all 0.6s ease;
                }
            }

            .cont {
                width: calc(100% - 200px);

                &.cont1 {
                    width: calc(100% - 370px);
                }

                .tit {
                    font-size: 20px;
                    line-height: 30px;
                    color: #333333;
                    transition: all 0.6s ease;
                }

                .txt {
                    font-size: 16px;
                    line-height: 30px;
                    color: #999999;
                    margin-top: 20px;
                }

                .time1 {
                    font-size: 16px;
                    line-height: 20px;
                    color: #999999;
                    margin-top: 20px;
                }

                .a {
                    align-items: center;
                    line-height: 20px;
                    margin-top: 30px;
                    
                    p {
                        font-size: 16px;
                        color: #999999;
                    }

                    .iconfont {
                        color: #082CA4;
                        font-size: 20px;
                        transform: rotate(180deg);
                        margin-left: 10px;
                    }
                }
            }

            &:hover {
                .time {
                    color: #082CA4;

                    p {
                        color: #082CA4;
                    }
                }

                .img img {
                    transform: scale(1.1);
                }
    
                .cont .tit {
                    color: #082CA4;
                }
            }
        }
    }

    .release {
        width: 100%;
        position: relative;

        &::after {
            content: '';
            width: 1px;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.1);
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        li {
            width: 46%;

            &:nth-child(2n-1) {
                margin-right: 8%;
            }

            .img {
                width: 100%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                    transition: all 0.6s ease;
                }
            }

            .cont {
                width: 100%;
                padding: 30px 20px;

                .tit {
                    font-size: 20px;
                    line-height: 30px;
                    color: #333333;
                    transition: all 0.6s ease;
                }

                .txt {
                    font-size: 16px;
                    line-height: 30px;
                    color: #999999;
                    margin-top: 20px;
                }   
                
                .time {
                    font-size: 16px;
                    line-height: 20px;
                    color: #999999;
                    margin-top: 20px;
                }
            }

            
            &:hover {
                .img img {
                    transform: scale(1.1);
                }
    
                .cont .tit {
                    color: #082CA4;
                }
            }
        }
    }
}

/* pageActivity */
.pageActivity {
    width: 100%;
    padding: 80px 0 60px;

    .activityBox {
        width: 100%;
        position: relative;

        .gallery-top {
            width: calc(100% - 250px);
            margin: 0;

            a {
                width: 100%;

                .img {
                    width: 700px;
                    height: 488px;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                        display: block;
                        transition: all 0.6s ease;
                    }
                }

                .cont {
                    width: calc(100% - 700px);
                    padding: 30px 0 0 30px;

                    .label {
                        display: inline-block;
                        // width: 120px;
                        height: 40px;
                        border-radius: 3px;
                        background-color: #082CA4;
                        font-size: 16px;
                        line-height: 40px;
                        color: #FFFFFF;
                        text-align: center;
                        padding: 0 10px;
                    }

                    .tit {
                        font-size: 20px;
                        line-height: 32px;
                        color: #333333;
                        margin-top: 30px;
                        transition: all 0.6s ease;
                    }

                    .txt {
                        font-size: 16px;
                        line-height: 30px;
                        color: #666;
                        margin-top: 50px;
                    }

                    .time {
                        margin-top: 50px;

                        .p {
                            font-size: 16px;
                            line-height: 20px;
                            color: #999999;
                            margin-top: 20px;

                            .iconfont {
                                font-size: 20px;
                                margin-right: 10px;
                            }
                        }
                    }
                }
                
                &:hover {
                    .img img {
                        transform: scale(1.1);
                    }
        
                    .cont .tit {
                        color: #082CA4;
                    }
                }
            }

            .arrow {
                width: 148px;
                height: 48px;
                position: absolute;
                bottom: 30px;
                left: 730px;

                .swiper-button-next {
                    right: 0;
                }

                .swiper-button-prev {
                    left: 0;
                }

                .swiper-button-next, .swiper-button-prev {
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    background-color: #252525;
                    margin: 0;
                    top: 0;
                    transition: all 0.6s ease;

                    &::after {
                        color: #fff;
                        font-size: 14px;
                    }

                    &:hover {
                        background-color: #082CA4;
                    }
                }
            }
        }

        .gallery-thumbs {
            width: 216px;
            height: 100%;
            position: absolute;
            top: 0;
            right: 0;

            .swiper-wrapper {
                justify-content: space-between;
                transform: none !important;

                .swiper-slide {
                    height: auto !important;
                    background-color: #000;
                    cursor: pointer;

                    img {
                        width: 100%;
                        height: 150px;
                        display: block;
                        opacity: 0.6;
                        transition: all 0.6s ease;
                    }

                    &.swiper-slide-thumb-active img {
                        opacity: 1;
                    }
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin: 80px 0 30px;

        li {
            width: 32%;
            margin: 0 2% 2% 0;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.6s ease;

            &:nth-child(3n) {
                margin: 0 0 2% 0;
            }

            .img {
                width: 100%;
                height: 320px;
                overflow: hidden;
                position: relative;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                }

                .label {
                    height: 40px;
                    background-color: #082CA4;
                    font-size: 14px;
                    line-height: 40px;
                    color: #FFFFFF;
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding: 0 12px;
                }
            }

            .cont {
                width: 100%;
                padding: 6%;
                border-top: none;

                .tit {
                    font-size: 20px;
                    line-height: 32px;
                    color: #333333;
                    transition: all 0.6s ease;
                }

                .txt {
                    font-size: 16px;
                    line-height: 26px;
                    color: #666;
                    margin-top: 20px;
                    transition: all 0.6s ease;
                }

                .time {
                    margin-top: 20px;

                    .p {
                        font-size: 16px;
                        line-height: 20px;
                        color: #999999;
                        margin-top: 20px;
                        transition: all 0.6s ease;

                        .iconfont {
                            font-size: 20px;
                            margin-right: 10px;
                        }
                    }
                }
            }

            &:hover {
                background-color: #082CA4;

                .cont {
                    .tit, .txt {
                        color: #fff;
                    }
    
                    .time {
                        .p {
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageActivity {
        .activityBox {
            .gallery-top {
                width: calc(100% - 210px);

                a {
                    .img {
                        width: 580px;
                        height: 405px;
                    }
    
                    .cont {
                        width: calc(100% - 580px);

                        .txt {
                            margin-top: 30px;
                        }

                        .time {
                            margin-top: 30px;
                        }
                    }
                }
    
                .arrow {
                    left: 610px;
                }
            }
    
            .gallery-thumbs {
                width: 180px;

                .swiper-wrapper {
                    .swiper-slide {
                        img {
                            height: 125px;
                        }
                    }
                }
            }
        }
    
        .lists {
            margin: 60px 0 30px;

            li .img {
                height: 266px;
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageActivity {
        .activityBox {
            .gallery-top {
                width: calc(100% - 192px);
    
                a {
                    .img {
                        width: 530px;
                        height: 370px;
                    }
    
                    .cont {
                        width: calc(100% - 530px);
    
                        .label {
                            height: 32px;
                            font-size: 14px;
                            line-height: 32px;
                        }
    
                        .tit {
                            font-size: 18px;
                            line-height: 30px;
                        }
    
                        .txt {
                            font-size: 14px;
                            line-height: 28px;
                        }
    
                        .time {
    
                            .p {
                                font-size: 14px;
                                line-height: 18px;
                                margin-top: 15px;
    
                                .iconfont {
                                    font-size: 16px;
                                    margin-right: 6px;
                                }
                            }
                        }
                    }
                }
    
                .arrow {
                    width: 120px;
                    height: 40px;
                    left: 560px;
    
    
                    .swiper-button-next, .swiper-button-prev {
                        width: 40px;
                        height: 40px;
                    }
                }
            }
    
            .gallery-thumbs {
                width: 162px;

                .swiper-wrapper {
                    .swiper-slide {
                        img {
                            height: 113px;
                        }
                    }
                }
            }
        }
    
        .lists {
            li {
                .img {
                    height: 244px;

                    .label {
                        height: 36px;
                        line-height: 36px;
                    }
                }
    
                .cont {
                    .tit {
                        font-size: 16px;
                        line-height: 28px;
                    }
    
                    .txt {
                        font-size: 14px;
                        line-height: 28px;
                    }
    
                    .time {
                        .p {
                            font-size: 14px;
                            line-height: 18px;
                            margin-top: 15px;
    
                            .iconfont {
                                font-size: 16px;
                                margin-right: 6px;
                            }
                        }
                    }
                }
            }
        }
    }
}

/* pageVideo */
.pageVideo {
    width: 100%;
    padding: 80px 0 60px;

    .videoBox {
        width: 100%;
        position: relative;

        .gallery-top {
            width: 58%;
            margin: 0;

            .img {
                width: 100%;
                height: 584px;
                display: block;
                overflow: hidden;
                background-color: #000;
                position: relative;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                    opacity: 0.6;
                }

                .iconfont {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #fff;
                    font-size: 70px;
                }
            }
        }

        .gallery-thumbs {
            width: 42%;
            height: 100%;
            position: absolute;
            top: 0;
            right: 0;
            background-color: #FAFBFF;
            overflow: visible;

            .swiper-wrapper {
                justify-content: space-between;
                transform: none !important;

                .swiper-slide {
                    height: 33.3333% !important;
                    font-size: 20px;
                    line-height: 38px;
                    color: #333333;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-bottom: 1px solid rgba(8, 44, 164, 0.2);
                    cursor: pointer;

                    &::after {
                        content: '';
                        width: calc(100% + 30px);
                        height: 100%;
                        background-color: #082CA4;
                        position: absolute;
                        top: 0;
                        right: 0;
                        z-index: 1;
                        opacity: 0;
                        transition: all 0.6s ease;
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    p {
                        width: 100%;
                        position: relative;
                        z-index: 2;
                        padding: 0 5%;
                        transition: all 0.6s ease;
                    }

                    .iconfont {
                        position: relative;
                        z-index: 2;
                        color: #fff;
                        font-size: 32px;
                        position: absolute;
                        bottom: 5%;
                        right: 5%;
                        opacity: 0;
                        transition: all 0.6s ease;
                    }

                    &.swiper-slide-thumb-active {
                        color: #fff;

                        p {
                            padding: 0 5% 0 0;
                        }

                        &::after {
                            opacity: 1;
                        }

                        .iconfont {
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin: 80px 0 30px;

        li {
            width: 48.5%;
            margin: 0 3% 3% 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);

            &:nth-child(2n) {
                margin: 0 0 3% 0;
            }

            a {
                display: block;
                padding-bottom: 40px;
            }

            .img {
                width: 100%;
                height: 488px;
                overflow: hidden;
                background-color: #000;
                position: relative;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                    opacity: 0.6;
                }

                .iconfont {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #fff;
                    font-size: 60px;
                }
            }

            .tit {
                font-size: 20px;
                line-height: 30px;
                color: #333333;
                margin-top: 30px;
                transition: all 0.6s ease;
            }

            .t {
                font-size: 16px;
                line-height: 20px;
                color: #999999;
                margin-top: 20px;
            }

            &:hover {
                .tit {
                    color: #082CA4;
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageVideo {
        .videoBox {
            .gallery-top {
                .img {
                    height: 487px;
                }
            }
        }
    
        .lists {
            li {
                .img {
                    height: 407px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageVideo {
        .videoBox {
            .gallery-top {
                .img {
                    height: 446px;

                    .iconfont {
                        font-size: 60px;
                    }
                }
            }
    
            .gallery-thumbs {
                .swiper-wrapper {
                    .swiper-slide {
                        font-size: 18px;
                        line-height: 32px;
                    }
                }
            }
        }
    
        .lists {
            margin: 60px 0 30px;
    
            li {

                a {
                    padding-bottom: 30px;
                }

                .img {
                    height: 373px;

                    .iconfont {
                        font-size: 50px;
                    }
                }
    
                .tit {
                    font-size: 18px;
                    line-height: 28px;
                    margin-top: 30px;
                }
    
                .t {
                    font-size: 14px;
                    line-height: 18px;
                    margin-top: 20px;
                }
            }
        }
    }
}

/* pageNewsD */
.pageNewsD {
    width: 100%;
    padding: 80px 0 120px;

    .title {
        text-align: center;
        font-size: 24px;
        line-height: 36px;
        color: #333;
        font-weight: bold;
    }

    .date {
        color: #999;
        font-size: 16px;
        text-align: center;
        border-bottom: 1px dashed #ddd;
        padding: 30px 0;
        margin-bottom: 40px;

        span {
            margin: 0 20px;
        }
    }

    .text {
        font-size: 16px;
        line-height: 36px;
        color: #333;

        img {
            max-width: 100%;
            height: auto;
        }
        
        video {
            max-width: 80%;
            margin: auto;
            display: block;
        }

        iframe {
            width: 767px;
            height: 443px;
            margin: 0 auto;
            display: block;
        }
    }

    .box {
        width: 100%;
        border-top: 1px dashed #ddd;
        padding: 40px 0 0;
        margin-top: 40px;

        a {
            color: #999;
            font-size: 16px;
            line-height: 26px;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.6s ease;
            
            &:hover {
                color: #082CA4;
            }
        }
    }
}

/* pageForum */
.pageForum {
    width: 100%;
    padding: 50px 0 100px;
    margin: 0;

    // .liBox {
    //     width: 100%;
    //     margin-bottom: 20px;
    // }

    .Box {
        // width: 100%;
        // min-height: 140px;
        // background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
        // border: 2px solid rgba(8, 44, 164, 0.08);
        // align-items: center;
        // padding: 1% 30px;
        // transition: all 0.6s ease;

        // .titBox {
        //     width: calc(100% - 200px);

        //     .tit {
        //         max-width: calc(100% - 134px);
        //         font-size: 24px;
        //         font-weight: bold;
        //         line-height: 32px;
        //         color: #333333;
        //     }

        //     .label {
        //         min-width: 84px;
        //         height: 32px;
        //         border-radius: 3px;
        //         font-size: 16px;
        //         line-height: 32px;
        //         margin-left: 20px;
        //         padding: 0 5px;

        //         &.label1 {
        //             border: 1px solid #52A199;
        //             color: #52A199;
        //         }

        //         &.label2 {
        //             border: 1px solid #082CA4;
        //             color: #082CA4;
        //         }

        //         &.label3 {
        //             border: 1px solid #999999;
        //             color: #999999;
        //         }

        //         .iconfont {
        //             margin-right: 3px;
        //             font-size: 18px;
        //         }
        //     }

        //     .address {
        //         font-size: 16px;
        //         line-height: 22px;
        //         color: #999999;
        //         margin-top: 18px;

        //         .iconfont {
        //             font-size: 20px;
        //             margin-right: 6px;
        //         }
        //     }
        // }

        // .btnBox {
        //     align-items: center;

        //     .btn {
        //         display: block;
        //         width: 136px;
        //         height: 42px;
        //         border-radius: 22px;
        //         font-size: 18px;
        //         line-height: 42px;
        //         color: #FFFFFF;
        //         text-align: center;

        //         &.btn1 {
        //             background-color: #082CA4;
        //         }

        //         &.btn2 {
        //             background-color: #CDCFD6;
        //         }
        //     }

        //     .iconfont {
        //         width: 42px;
        //         height: 42px;
        //         line-height: 42px;
        //         color: #fff;
        //         font-size: 14px;
        //         margin-left: 16px;
        //         background-color: #262626;
        //         text-align: center;
        //         border-radius: 50%;
        //         cursor: pointer;
        //         transition: all 0.6s ease;

        //         // &:hover {
        //         //     background-color: #082CA4;
        //         //     color: #fff;
        //         // }
        //     }
        // }

        &.on {
            background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);

            // .titBox {

            //     .tit {
            //         color: #fff;
            //     }

            //     .label {
            //         &.label1 {
            //             border: 1px solid #fff;
            //             color: #fff;
            //         }

            //         &.label2 {
            //             border: 1px solid #fff;
            //             color: #fff;
            //         }

            //         &.label3 {
            //             border: 1px solid #fff;
            //             color: #fff;
            //         }
            //     }

            //     .address {
            //         color: #fff;
            //     }
            // }

            // .btnBox {
            //     .iconfont {
            //         background-color: #fff;
            //         color: #333;
            //         transform: rotate(90deg);
            //     }
            // }
        }
    }

    // .contBox {
    //     width: 100%;
    //     background-color: #FFFFFF;
    //     border: 1px solid rgba(0, 0, 0, 0.12);
    //     border-top: none;
    //     display: none;

    //     &.sel {
    //         display: block;
    //     }

    //     .cont {
    //         width: 100%;
    //         background-color: #FAFBFF;
    //         padding: 1% 30px 26px;

    //         .t {
    //             font-size: 18px;
    //             line-height: 22px;
    //             color: #3D3D3D;
    //             border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    //             padding: 12px 0;

    //             p {
    //                 margin: 8px 0;

    //                 span {
    //                     &:last-child {
    //                         em {
    //                             display: none;
    //                         }
    //                     }
    //                 }
    //             }
    //         }

    //         .t1 {
    //             font-size: 18px;
    //             line-height: 22px;
    //             color: #3D3D3D;
    //             padding: 20px 0 14px;
    //         }

    //         .t2 {
    //             font-size: 16px;
    //             line-height: 34px;
    //             color: #666666;
    //             // height: 64px;
    //             overflow: hidden;

    //             &.on {
    //                 height: auto;
    //             }
    //         }

    //         .show {
    //             text-align: center;
    //             margin-top: 20px;
    //             font-size: 16px;
    //             line-height: 20px;
    //             color: #333333;
    //             cursor: pointer;

    //             .iconfont {
    //                 width: 36px;
    //                 height: 36px;
    //                 line-height: 36px;
    //                 border-radius: 18px;
    //                 background-color: #262626;
    //                 color: #fff;
    //                 transform: rotate(90deg);
    //                 margin: 6px auto 0;
    //                 text-align: center;
    //                 font-size: 16px;
    //             }
    //         }
    //     }

    //     .dl {
    //         width: 100%;
    //         padding: 50px 30px 20px;
    //         position: relative;

    //         &::after {
    //             content: '';
    //             width: 1px;
    //             height: calc(100% - 156px);
    //             border-left: 1px dashed #082CA4;
    //             position: absolute;
    //             top: 56px;
    //             left: 190px;
    //         }

    //         .dt {
    //             width: 100%;
    //             margin-bottom: 30px;

    //             .time {
    //                 width: 170px;
    //                 font-size: 16px;
    //                 line-height: 20px;
    //                 color: #333333;
    //                 margin-right: 60px;
    //                 padding-right: 70px;
    //                 text-align: right;
    //                 position: relative;

    //                 &::after {
    //                     content: '';
    //                     width: 16px;
    //                     height: 16px;
    //                     border-radius: 50%;
    //                     background-color: rgba(8, 44, 164, 0.1);
    //                     position: absolute;
    //                     top: 0;
    //                     right: 2px;
    //                 }

    //                 &::before {
    //                     content: '';
    //                     width: 8px;
    //                     height: 8px;
    //                     border-radius: 50%;
    //                     background-color: #082CA4;
    //                     position: absolute;
    //                     top: 4px;
    //                     right: 6px;
    //                 }
    //             }

    //             .dd {
    //                 width: calc(100% - 230px);
    //                 background-color: #FAFBFF;
    //                 padding: 20px 2% 0;

    //                 .t3 {
    //                     font-size: 18px;
    //                     line-height: 22px;
    //                     color: #333333;
    //                     margin-bottom: 20px;
    //                 }

    //                 .li {
    //                     width: 31.3333%;
    //                     margin: 0 3% 30px 0;
    //                     align-items: center;

    //                     &:nth-child(3n) {
    //                         margin: 0 0 30px 0;
    //                     }

    //                     .img {
    //                         width: 50px;
    //                         height: 50px;
    //                         border-radius: 50%;
    //                         overflow: hidden;
    //                         margin-right: 10px;

    //                         img {
    //                             width: 100%;
    //                             display: block;
    //                         }
    //                     }

    //                     .t4 {
    //                         width: calc(100% - 60px);

    //                         .name {
    //                             font-size: 16px;
    //                             line-height: 20px;
    //                             color: #333333;
    //                         }

    //                         .t5 {
    //                             font-size: 14px;
    //                             line-height: 20px;
    //                             color: #3D3D3D;
    //                             margin-top: 8px;
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }
}

/* pageCharacter */
.pageCharacter {
    width: 100%;
    padding: 80px 0 60px;

    .lists {
        width: 100%;
        margin-bottom: 30px;

        li {
            width: 48.5%;
            margin: 0 3% 3% 0;
            border: 1px solid rgba(0, 0, 0, 0.12);

            &:nth-child(2n) {
                margin: 0 0 3% 0;
            }

            a {
                align-items: center;
            }

            .img {
                width: 40%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: 60%;
                padding: 3% 5%;

                .name {
                    font-size: 20px;
                    font-weight: bold;
                    line-height: 28px;
                    color: #333;
                }

                .p {
                    font-size: 16px;
                    line-height: 26px;
                    color: #999999;
                    margin-top: 14px;
                }

                .a {
                    font-size: 16px;
                    line-height: 22px;
                    color: #333333;
                    align-items: center;
                    margin-top: 50px;

                    .iconfont {
                        font-size: 20px;
                        color: #082CA4;
                        transform: rotate(180deg);
                        margin-left: 8px;
                    }
                }
            }
        }
    }
}

/* pageCharacterD */
.pageCharacterD {
    width: 100%;
    padding: 80px 0;

    .gallery-top {
        width: 100%;

        .between {
            width: 100%;
            background-color: #FAFBFF;

            .img {
                width: 400px;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: calc(100% - 400px);
                padding: 80px 40px 0 40px;

                .name {
                    font-size: 20px;
                    font-weight: bold;
                    line-height: 28px;
                    color: #333333;
                }

                .t {
                    font-size: 16px;
                    line-height: 26px;
                    color: #999999;
                    margin-top: 20px;
                }
            }
        }

        .arrow {
            width: 138px;
            height: 48px;
            position: absolute;
            bottom: 40px;
            left: 440px;

            .swiper-button-next {
                right: 0;
            }

            .swiper-button-prev {
                left: 0;
            }

            .swiper-button-next, .swiper-button-prev {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background-color: #252525;
                margin: 0;
                top: 0;
                transition: all 0.6s ease;

                &::after {
                    color: #fff;
                    font-size: 14px;
                }

                &:hover {
                    background-color: #082CA4;
                }
            }
        }
    }

    .gallery-thumbs {
        width: 100%;
        margin-top: 40px;

        .swiper-slide {
            background-color: #000;
            cursor: pointer;

            img {
                width: 100%;
                display: block;
                opacity: 0.6;
                transition: all 0.6s ease;
            }

            &.swiper-slide-thumb-active img {
                opacity: 1;
            }
        }
    }
}

/* pageHotel */
.pageHotel {
    width: 100%;
    padding: 60px 0 120px;

    // .tit {
    //     font-size: 44px;
    //     line-height: 48px;
    //     color: #333333;
    // }

    .t {
        font-size: 16px;
        line-height: 32px;
        color: #666666;
        margin-top: 20px;
    }

    .cont {
        align-items: center;
        background-color: #FAFBFF;
        margin-top: 30px;

        .img {
            width: 50%;
            overflow: hidden;
    
            img {
                width: 100%;
                display: block;
            }
        }

        .txt {
            width: 50%;
            font-size: 16px;
            line-height: 28px;
            color: #333333;
            padding: 0 50px;
            
            p {
                margin: 10px 0;
            }
        }
    }

    .t1 {
        font-size: 20px;
        line-height: 24px;
        color: #333333;
        margin-top: 40px;
    }

    table {
        width: 100%;
        margin-top: 20px;
        text-align: center;

        tr {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: #082CA4;
            color: #fff;
            font-weight: normal;
            font-size: 16px;
            line-height: 60px;
            color: #FFFFFF;
        }

        td {
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 16px;
            line-height: 26px;
            color: #333333;
            padding: 17px 0;
            width: 20%;

            &:nth-child(2) {
                width: 25%;
            }

            &:nth-child(4) {
                width: 35%;
                border-right: none;
            }
        }
    }

    .tips {
        font-size: 16px;
        line-height: 20px;
        color: #999999;
        margin-top: 30px;
    }

    .p {
        width: 110px;
        height: 36px;
        line-height: 36px;
        background-color: #082CA4;
        text-align: center;
        font-size: 16px;
        color: #FFFFFF;
        margin-top: 20px;
    }

    .box {
        width: 1090px;
        height: 120px;
        background-color: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.1);
        align-items: center;
        padding: 0 40px;

        .t2 {
            width: calc(100% - 160px);
            font-size: 16px;
            line-height: 40px;
            color: #333333;
        }

        a {
            display: block;
            width: 140px;
            height: 40px;
            line-height: 40px;
            border-radius: 20px;
            background: #082CA4;
            font-size: 14px;
            color: #FFFFFF;
            text-align: center;
        }
    }

    .map {
        width: 100%;
        margin-top: 40px;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
        }
    }
}

/* pageLive */
.pageLive {
    width: 100%;
    padding: 60px 0 120px;

    .lists {
        width: 100%;
        margin-bottom: 30px;

        li {
            width: 48.5%;
            margin: 0 3% 3% 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);

            &:nth-child(2n) {
                margin: 0 0 3% 0;
            }

            a {
                display: block;
                padding-bottom: 30px;
            }

            .img {
                width: 100%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .tit {
                font-size: 20px;
                line-height: 30px;
                color: #333333;
                margin-top: 30px;
                transition: all 0.6s ease;
            }

            .time {
                font-size: 16px;
                line-height: 20px;
                color: #999999;
                margin-top: 20px;
            }

            &:hover {
                .tit {
                    color: #082CA4;
                }
            }
        }
        
        &.lists1 li {
            width: 23.5%;
            margin: 0 2% 2% 0;

            &:nth-child(2n) {
                margin: 0 2% 2% 0;
            }

            &:nth-child(4n) {
                margin: 0 0 2% 0;
            }

            .tit {
                font-size: 16px;
                line-height: 24px;
            }  
            
            .time {
                font-size: 14px;
                line-height: 18px;
            }
        }
    }
}

/* pageReview */
.pageReview {
    width: 100%;
    padding: 60px 0 120px;

    ul {
        width: 100%;
        
        li {
            width: 32%;
            margin: 0 2% 2% 0;
            background-color: #FAFBFF;

            &:nth-child(3n) {
                margin: 0 0 2% 0;
            }

            .img {
                width: 100%;
                height: 270px;
                background-color: #000;
                overflow: hidden;
                position: relative;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                    transition: all 0.6s ease;
                }

                .btn {
                    width: 68px;
                    height: 68px;
                    border-radius: 50%;
                    background-color: #082CA4;
                    text-align: center;
                    font-size: 14px;
                    line-height: 18px;
                    color: #FFFFFF;
                    padding: 15px 0;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0);
                    z-index: 1;
                    transition: all 0.6s ease;

                    &::after {
                        content: '';
                        width: 90px;
                        height: 90px;
                        border-radius: 50%;
                        background-color: rgba(8, 44, 164, 0.6);
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        z-index: -1;
                    }
                }
            }

            .t {
                width: 100%;
                text-align: center;
                background-color: #FAFBFF;
                padding: 20px 15px;
                font-size: 18px;
                line-height: 22px;
                color: #333333;
            }

            &:hover .img {
                img {
                    opacity: 0.5;
                }

                .btn {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageReview {
        ul {
            li {
                .img {
                    height: 225px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageReview {
        ul {
            li {
                .img {
                    height: 206px;
                }
    
                .t {
                    font-size: 16px;
                    line-height: 20px;
                }
            }
        }
    }
}

/* pageTheme */
.pageTheme {
    width: 100%;
    padding: 60px 0 120px;

    .text {
        font-size: 16px;
        line-height: 36px;
        color: #666666;
        text-align: justify;

        img {
            max-width: 100%;
            height: auto;
            margin: auto;
        }
    }

    .img {
        width: 100%;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
        }
    }

    .cont {
        width: 100%;
        background-color: #FAFBFF;
        padding: 40px;

        .tit {
            font-size: 36px;
            line-height: 40px;
            color: #333333;
        }

        .txt {
            font-size: 18px;
            line-height: 36px;
            color: #666666;
            margin-top: 20px;
        }
    }
}

/* pageSchedule */
.pageSchedule {
    width: 100%;
    padding: 60px 0 120px;

    .title {
        font-size: 30px;
        line-height: 40px;
        color: #333333;
        text-align: center;
    }

    .p {
        font-size: 16px;
        line-height: 24px;
        color: #666666;
        text-align: center;
        margin: 20px 0 40px;

        span {
            margin: 0 6px;
        }
    }

    .time {
        width: 100%;
        height: 60px;
        line-height: 60px;
        background-color: #082CA4;
        color: #fff;
        font-size: 16px;
        text-align: center;

        &.time1 {
            background-color: #52A199;
        }
    }

    table {
        width: 100%;
        text-align: center;

        tr {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: #FAFBFF;
            font-size: 16px;
            line-height: 20px;
            font-weight: normal;
            color: #666666;
            padding: 20px 0;
            border-right: 1px solid rgba(0, 0, 0, 0.1);

            &:last-child {
                border-right: none;
            }
        }

        td {
            font-size: 16px;
            line-height: 20px;
            color: #333333;
            padding: 20px 0;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            width: 53%;

            &:nth-child(1) {
                width: 20%;
            }
            
            &:nth-child(3) {
                width: 27%;
                border-right: none;
            }
        }
    }

    .tips {
        font-size: 16px;
        line-height: 20px;
        color: #999999;
        margin-top: 30px;
    }
}

/* pageGuest */
.pageGuest {
    width: 100%;
    padding: 100px 0 150px; 

    .lists {
        width: 100%;

        li {
            width: 32%;
            margin: 0 2% 2% 0;
            border: 1px solid rgba(0, 0, 0, 0.12);

            &:nth-child(3n) {
                margin: 0 0 2% 0;
            }

            a {
                padding: 5%;
                align-items: center;

                .img {
                    width: 150px;
                    height: 150px;
                    border-radius: 50%;
                    overflow: hidden;

                    img {
                        width: 100%;
                        display: block;
                    }
                }

                .cont {
                    width: calc(100% - 170px);

                    .name {
                        font-size: 20px;
                        line-height: 30px;
                        color: #3D3D3D;
                    }

                    .t {
                        font-size: 14px;
                        line-height: 20px;
                        color: #999999;
                        margin-top: 10px;
                    }
                }
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageGuest {
        .lists {
            li {
                a {
                    padding: 4%;
    
                    .img {
                        width: 120px;
                        height: 120px;
                    }
    
                    .cont {
                        width: calc(100% - 140px);
    
                        .name {
                            font-size: 18px;
                            line-height: 28px;
                        }
    
                        .t {
                            font-size: 12px;
                            line-height: 18px;
                        }
                    }
                }
            }
        }
    }
}

/* pageAtlas */
.pageAtlas {
    width: 100%;
    padding: 100px 0 150px; 

    ul {
        width: 100%;

        li {
            float: left;
            width: 48.5%;
            height: 466px;
            margin: 0 3% 3% 0;

            &:nth-child(2n-1) {
                margin: 0 0 3% 0;
            }

            &:nth-child(1) {
                width: 66.2%;
                height: 636px;
            }

            &:nth-child(2) {
                width: 30.8%;
                height: 296px;
                margin: 0 0 3% 3%;
            }

            &:nth-child(3) {
                width: 30.8%;
                height: 296px;
                margin: 0 0 0 3%;
            }

            img {
                width: 100%;
                height: 100%;
                display: block;
            }
        }
    }
}
@media (max-width: 1680px) {
    .pageAtlas {
        ul {
            li {
                height: 388px;
    
                &:nth-child(1) {
                    height: 530px;
                }
    
                &:nth-child(2) {
                    height: 246px;
                }
    
                &:nth-child(3) {
                    height: 246px;
                }
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageAtlas {
        ul {
            li {
                height: 356px;
    
                &:nth-child(1) {
                    height: 486px;
                }
    
                &:nth-child(2) {
                    height: 226px;
                }
    
                &:nth-child(3) {
                    height: 226px;
                }
            }
        }
    }
}

/* pageReport */
.pageReport {
    width: 100%;
    padding: 80px 0 120px;

    .lists {
        width: 100%;

        li {
            width: 48.5%;
            margin: 0 3% 3% 0;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 3%;

            &:nth-child(2n) {
                margin: 0 0 3% 0;
            }

            .tit {
                font-size: 20px;
                line-height: 30px;
                color: #333333;
                transition: all 0.6s ease;
            }

            .time {
                font-size: 16px;
                line-height: 20px;
                color: #999999;
                margin-top: 14px;
            }

            .down {
                display: block;
                width: 140px;
                height: 40px;
                line-height: 40px;
                border-radius: 20px;
                background-color: #082CA4;
                font-size: 14px;
                color: #FFFFFF;
                text-align: center;
                margin-top: 30px;
            }

            &:hover .tit {
                color: #082CA4;
            }
        }
    }
}
@media (max-width: 1480px) {
    .pageReport {
        .lists {
            li {

                .tit {
                    font-size: 18px;
                    line-height: 28px;
                }
            }
        }
    }
}

/* pagePrint */
.pagePrint {
    width: 100%;
    padding: 100px 0 200px;

    ul {
        width: 100%;

        li {
            width: 100%;
            background: url(../images/printBg.jpg) center center no-repeat;
            background-size: cover;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 4% 4.5%;
            align-items: center;
            cursor: pointer;

            .img {
                width: 240px;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: 520px;
                margin-left: 50px;

                .t {
                    font-size: 20px;
                    line-height: 30px;
                    color: #333333;
                }

                .p {
                    font-size: 18px;
                    line-height: 22px;
                    color: #333333;
                    margin-top: 30px;
                }

                .open {
                    display: block;
                    width: 160px;
                    height: 42px;
                    line-height: 42px;
                    border-radius: 21px;
                    background-color: #082CA4;
                    text-align: center;
                    font-size: 14px;
                    color: #FFFFFF;
                    margin-top: 60px;
                }
            }
        }
    }
}

/* pagePartner */
.pagePartner {
    width: 100%;
    padding: 80px 0 120px;

    .title {
        font-size: 20px;
        font-weight: bold;
        line-height: 20px;
        margin-bottom: 30px;
        border-left: 4px solid #082CA4;
        padding-left: 10px;
    }

    ul {
        width: 100%;

        li {
            width: 18.4%;
            margin: 0 2% 2% 0;
            border: 1px solid rgba(51, 51, 51, 0.2);
            background-color: #fff;
            overflow: hidden;
            transition: all 0.6s ease;

            &:nth-child(5n) {
                margin: 0 0 2% 0;
            }

            a {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            img {
                // width: 100%;
                // display: block;
                max-width: 100%;
                max-height: 100%;
            }

            // &:hover {
            //     background-color: rgba(8, 44, 164, 1);
            //     border-color: #082CA4;
            // }
        }
    }
}

/* popupBox */
.popupBox {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    display: none;
}
.popupForm {
    width: 500px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    box-sizing: content-box;
    background-color: #fff;
    border-radius: 2px;
    padding: 40px 20px;
    display: none;

    .close {
        width: 30px;
        height: 30px;
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;

        &::after {
            content: '';
            width: 30px;
            height: 2px;
            background-color: #999;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            transition: all 0.6s ease;
        }

        &::before {
            content: '';
            width: 30px;
            height: 2px;
            background-color: #999;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            transition: all 0.6s ease;
        }

        &:hover {
            &::after {
                background-color: #082CA4;
            }
    
            &::before {
                background-color: #082CA4;
            }
        }
    }

    .title {
        text-align: center;
        color: #333;
        font-size: 18px;
        font-weight: bold;
        line-height: 22px;
    }

    .tit {
        text-align: center;
        color: #666;
        font-size: 14px;
        line-height: 18px;
        margin: 10px 0 20px;
    }

    .row {
        width: 100%;
        margin-bottom: 20px;

        .p {
            color: #666;
            font-size: 14px;
            line-height: 18px;
            font-weight: bold;
        }

        .txt {
            width: 100%;
            border: 1px solid #ddd;
            color: #333;
            font-size: 14px;
            padding: 10px 15px;
            line-height: 20px;
            border-radius: 2px;
            margin-top: 6px;
        }
    }

    .ajaxformbtn {
        display: block;
        width: 100%;
        line-height: 50px;
        background-color: #082CA4;
        border-radius: 2px;
        color: #fff;
        font-size: 16px;
        cursor: pointer;
    }
}

