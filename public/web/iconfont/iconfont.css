@font-face {
  font-family: "iconfont"; /* Project id 4592529 */
  src: url('iconfont.eot?t=1721093189870'); /* IE9 */
  src: url('iconfont.eot?t=1721093189870#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1721093189870') format('woff2'),
       url('iconfont.woff?t=1721093189870') format('woff'),
       url('iconfont.ttf?t=1721093189870') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-jiantou2:before {
  content: "\eb94";
}

.icon-jiantou1:before {
  content: "\e64a";
}

.icon-shouye:before {
  content: "\e626";
}

.icon-denglu:before {
  content: "\e63a";
}

.icon-weizhi1:before {
  content: "\e8f5";
}

.icon-zhibo:before {
  content: "\e64c";
}

.icon-shouji:before {
  content: "\e608";
}

.icon-yanzhengma:before {
  content: "\e60a";
}

.icon-dianhua:before {
  content: "\e611";
}

.icon-dingbu:before {
  content: "\e65f";
}

.icon-hezuo:before {
  content: "\e622";
}

.icon-weixin:before {
  content: "\e603";
}

.icon-duihao:before {
  content: "\e6ed";
}

.icon-dingyue:before {
  content: "\e6bf";
}

.icon-jiantou:before {
  content: "\e612";
}

.icon-shijian:before {
  content: "\e609";
}

.icon-arrow:before {
  content: "\e687";
}

.icon-caidan:before {
  content: "\e60e";
}

.icon-bofang:before {
  content: "\e610";
}

.icon-weizhi:before {
  content: "\e638";
}

.icon-login:before {
  content: "\e620";
}

