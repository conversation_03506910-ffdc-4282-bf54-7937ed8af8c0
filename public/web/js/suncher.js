﻿$(function () {
    $(window).scroll(function () {
        var h = $("body").height() - window.getHeight();
        if ($(window).scrollTop() > 1 && h > 10) {
            $(".headerBox").addClass("is-fixed");
        } else if ($(window).scrollTop() < 1) {
            $(".headerBox").removeClass("is-fixed");
        }
    });
    
    window.getHeight = function () {
        if (window.innerHeight != undefined) {
            return window.innerHeight;
        } else {
            var B = document.body,
                D = document.documentElement;
            return Math.min(D.clientHeight, B.clientHeight);
        }
    }
    
    var urlstr = location.pathname;
    $("#headerNav .nLi h3 a").each(function () {
        if (urlstr.indexOf($(this).data('href')) > -1 && $(this).attr('href')!='') {
            $(this).parent().parent().addClass('on');
            $(this).parent().parent().siblings().removeClass('on');
        }
    });    
    if(urlstr == '/' || urlstr == '/web/index/index' || urlstr == '/web/index/index.html' || urlstr == '/en/index/index' || urlstr == '/en/index/index.html') {
        $(".headerBox").removeClass("headerBox1");
    }
    // if(urlstr == '/web/index/signup.html' || urlstr == '/web/index/login.html' || urlstr == '/web/index/choose.html' || urlstr == '/web/index/company.html' || urlstr == '/web/index/school.html' || urlstr == '/web/index/finish.html') {
    //     $(".headerBox").hide();
    //     $(".footer").hide();
    // }

    // headerNav
    jQuery("#headerNav").slide({
        type: "menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
        titCell: ".nLi", //鼠标触发对象
        targetCell: ".sub", //titCell里面包含的要显示/消失的对象
        effect: "slideDown", //targetCell下拉效果
        delayTime: 300, //效果时间
        triggerTime: 0, //鼠标延迟触发时间（默认150）
        returnDefault: true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回（默认false）
    });

    // goTop
	// $(window).scroll(function(){
	//     if($(window).scrollTop()>100){  //距顶部多少像素时，出现返回顶部按钮
	//         $(".goTop").fadeIn();
	//     } else{
	//         $(".goTop").fadeOut();
	//     }
	// });
	$(".goTop").click(function(){
		$('html,body').animate({'scrollTop':0},900); //返回顶部动画 数值越小时间越短
	});
});



