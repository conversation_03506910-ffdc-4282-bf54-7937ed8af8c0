<?php
// 检查PHP扩展
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP扩展检查</h1>";

// 检查必要的扩展
$required_extensions = [
    'pdo',
    'pdo_mysql',
    'mbstring',
    'json',
    'openssl'
];

echo "<h2>必要扩展检查</h2>";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ " . $ext . " 已加载</p>";
    } else {
        echo "<p style='color: red;'>✗ " . $ext . " 未加载</p>";
    }
}

// 检查PDO驱动
echo "<h2>PDO驱动检查</h2>";
$pdo_drivers = PDO::getAvailableDrivers();
if (in_array('mysql', $pdo_drivers)) {
    echo "<p style='color: green;'>✓ PDO MySQL驱动可用</p>";
} else {
    echo "<p style='color: red;'>✗ PDO MySQL驱动不可用</p>";
    echo "<p>可用的PDO驱动: " . implode(', ', $pdo_drivers) . "</p>";
}

// 检查PHP配置
echo "<h2>PHP配置信息</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>扩展目录: " . ini_get('extension_dir') . "</p>";
echo "<p>已加载扩展: " . implode(', ', get_loaded_extensions()) . "</p>";

echo "<h2>解决方案</h2>";
echo "<p>如果PDO MySQL扩展未加载，请：</p>";
echo "<ol>";
echo "<li>在php.ini中启用extension=pdo_mysql</li>";
echo "<li>重启Web服务器</li>";
echo "<li>或者安装MySQL扩展</li>";
echo "</ol>";

echo "<p><a href='home.php'>返回首页</a></p>";
?> 