<?php
// 简单的测试页面
echo "PHP环境正常！<br>";
echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "服务器信息: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// 测试数据库连接
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "数据库连接成功！<br>";
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "<br>";
    echo "请确保MySQL服务已启动，并创建了pujiangforum数据库<br>";
}
?> 