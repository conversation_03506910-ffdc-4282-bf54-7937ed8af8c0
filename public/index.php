<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// [ 应用入口文件 ]

// 启用错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 启用调试模式
define('APP_DEBUG', true);

// 检查PDO扩展
if (!extension_loaded('pdo_mysql')) {
    header('Location: check_extensions.php');
    exit;
}

// 为PHP内置开发服务器添加URL重写支持
if (php_sapi_name() === 'cli-server') {
    $url = parse_url($_SERVER['REQUEST_URI']);
    $file = __DIR__ . $url['path'];
    
    // 如果请求的是静态文件且文件存在，直接返回
    if (is_file($file)) {
        return false;
    }
    
    // 设置PATH_INFO用于路由解析
    if (!isset($_SERVER['PATH_INFO'])) {
        $_SERVER['PATH_INFO'] = $url['path'];
    }
}

       // 定义应用目录 - 必须在加载ThinkPHP之前定义
       define('APP_PATH', realpath(__DIR__ . '/../application/') . '/');
       define('THINK_PATH', realpath(__DIR__ . '/../thinkphp/5.0.24/') . '/');

// 检查必要文件是否存在
if (!file_exists(APP_PATH . 'config.php')) {
    header('Location: home.php');
    exit;
}

if (!file_exists(THINK_PATH . 'start.php')) {
    header('Location: home.php');
    exit;
}

// 创建必要的运行时目录
$runtime_dirs = [
    __DIR__ . '/../runtime/cache',
    __DIR__ . '/../runtime/temp',
    __DIR__ . '/../runtime/log'
];

foreach ($runtime_dirs as $dir) {
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            header('Location: home.php');
            exit;
        }
    }
}

// 设置默认模块为web
define('BIND_MODULE', 'web');

try {
    // 加载框架引导文件
    require THINK_PATH . 'start.php';
} catch (Throwable $e) {
    // 如果是调试模式，显示错误信息
    if (defined('APP_DEBUG') && APP_DEBUG) {
        // 显示详细的错误信息
        echo '<h1>ThinkPHP 启动错误</h1>';
        echo '<p><strong>错误类型：</strong>' . get_class($e) . '</p>';
        echo '<p><strong>错误信息：</strong>' . $e->getMessage() . '</p>';
        echo '<p><strong>错误文件：</strong>' . $e->getFile() . '</p>';
        echo '<p><strong>错误行号：</strong>' . $e->getLine() . '</p>';
        echo '<h2>调试信息</h2>';
        echo '<p>APP_PATH: ' . APP_PATH . '</p>';
        echo '<p>THINK_PATH: ' . THINK_PATH . '</p>';
        echo '<p>当前目录: ' . __DIR__ . '</p>';
                       echo '<p>PHP版本: ' . PHP_VERSION . '</p>';
               echo '<p>请求URI: ' . $_SERVER['REQUEST_URI'] . '</p>';

               // 检查模块目录
               $modules = ['web', 'en', 'm', 'admin', 'adminen'];
               echo '<h3>模块检查:</h3>';
               foreach ($modules as $module) {
                   $module_path = APP_PATH . $module;
                   if (is_dir($module_path)) {
                       echo '<p style="color: green;">✓ ' . $module . ' 模块目录存在</p>';
                       if (is_dir($module_path . '/controller')) {
                           echo '<p style="color: green;">  - ' . $module . ' 控制器目录存在</p>';
                       } else {
                           echo '<p style="color: red;">  - ' . $module . ' 控制器目录不存在</p>';
                       }
                   } else {
                       echo '<p style="color: red;">✗ ' . $module . ' 模块目录不存在</p>';
                   }
               }

               // 详细检查en模块
               echo '<h3>en模块详细检查:</h3>';
               $en_path = APP_PATH . 'en';
               echo '<p>en模块路径: ' . $en_path . '</p>';
               echo '<p>路径是否存在: ' . (is_dir($en_path) ? '是' : '否') . '</p>';
               echo '<p>路径是否可读: ' . (is_readable($en_path) ? '是' : '否') . '</p>';
               echo '<p>APP_PATH: ' . APP_PATH . '</p>';
               echo '<p>APP_PATH是否以斜杠结尾: ' . (substr(APP_PATH, -1) === '/' ? '是' : '否') . '</p>';
               
               // 检查deny_module_list
               echo '<h3>配置检查:</h3>';
               echo '<p>deny_module_list: ' . json_encode(['common']) . '</p>';
               echo '<p>en是否在deny_module_list中: ' . (in_array('en', ['common']) ? '是' : '否') . '</p>';
               
               // 模拟ThinkPHP的模块检查逻辑
               echo '<h3>ThinkPHP模块检查逻辑:</h3>';
               $module = 'en';
               $module_path = APP_PATH . $module;
               echo '<p>模块名: ' . $module . '</p>';
               echo '<p>完整模块路径: ' . $module_path . '</p>';
               echo '<p>is_dir() 结果: ' . (is_dir($module_path) ? 'true' : 'false') . '</p>';
               echo '<p>!in_array() 结果: ' . (!in_array($module, ['common']) ? 'true' : 'false') . '</p>';
               echo '<p>最终available结果: ' . (!in_array($module, ['common']) && is_dir($module_path) ? 'true' : 'false') . '</p>';

               // 检查数据库连接
               try {
                   $pdo = new PDO('mysql:host=rm-cn-ioy4bnmu00003eeo.rwlb.rds.aliyuncs.com;dbname=pujiangforum;charset=utf8mb4', 'huiyi_31', 'huiyi_31@Ali4Launch');
                   echo '<p style="color: green;">✓ 数据库连接正常</p>';
               } catch (PDOException $db_error) {
                   echo '<p style="color: red;">✗ 数据库连接失败: ' . $db_error->getMessage() . '</p>';
               }
        
        // 显示错误堆栈
        echo '<h3>错误堆栈:</h3>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
        
        echo '<p><a href="home.php">访问简单首页</a></p>';
        echo '<p><a href="check_extensions.php">检查PHP扩展</a></p>';
    } else {
        // 生产环境重定向到简单首页
        header('Location: home.php');
    }
    
    exit;
}
