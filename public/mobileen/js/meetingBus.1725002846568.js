"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[124],{158:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},l=[],n=a(3397),s={name:"meetingBus",data(){return{detail:""}},created(){n.Ht({language:2}).then((e=>{if(200==e.data.code){let t=e.data.data.detail,a=t.replaceAll("&nbsp;"," ");this.detail=a}}))},methods:{}},r=s,c=a(1656),d=(0,c.A)(r,i,l,!1,null,"2eacb6e4",null),u=d.exports}}]);