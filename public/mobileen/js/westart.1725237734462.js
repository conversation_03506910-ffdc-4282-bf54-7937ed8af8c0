"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[217],{3038:function(t,e,n){n.r(e),n.d(e,{default:function(){return l}});var r=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("img",{staticClass:"bg",attrs:{src:n(6650)}})])}],a={name:"WeStart",data(){return{}},created(){},mounted(){const t=window.innerHeight;let e=document.querySelector("html");var n=document.defaultView.getComputedStyle(e,null);let r=n.fontSize.replace("px","");const s=248/75*r;document.querySelector(".main").style.height=t-s+"px"},methods:{}},c=a,u=n(1656),i=(0,u.A)(c,r,s,!1,null,"53536598",null),l=i.exports},6650:function(t,e,n){t.exports=n.p+"assets/img/westart.a3cad85e.jpg"}}]);