"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[324],{8432:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},n=[],i=a(3397),r={name:"meetingHotel",data(){return{detail:""}},created(){i.JX({language:2}).then((e=>{if(200==e.data.code){let t=e.data.data.detail,a=t.replaceAll("&nbsp;"," ");this.detail=a}}))},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let l=a.fontSize.replace("px","");const n=308/75*l;document.querySelector(".article-detail").style.height=e-n+"px"},methods:{}},c=r,d=a(1656),s=(0,d.A)(c,l,n,!1,null,"a9947864",null),u=s.exports}}]);