"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[461],{4137:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},i=[],n=a(3397),c={name:"meetingTraffic",data(){return{detail:""}},created(){n.cv({language:2}).then((e=>{if(200==e.data.code){let t=e.data.data.detail,a=t.replaceAll("&nbsp;"," ");this.detail=a}}))},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let l=a.fontSize.replace("px","");const i=308/75*l;document.querySelector(".article-detail").style.height=e-i+"px"},methods:{}},r=c,d=a(1656),s=(0,d.A)(r,l,i,!1,null,"18e858f6",null),u=s.exports}}]);