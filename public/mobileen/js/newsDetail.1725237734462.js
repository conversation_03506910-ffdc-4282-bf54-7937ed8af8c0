"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[589],{3204:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article-tit"},[e._v(e._s(e.news.title))]),t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.news.detail)}})])])},s=[],l=a(3397),n={name:"newsDetail",data(){return{news:{}}},created(){let e=this.$route.query.id;e?l.PI({id:e}).then((e=>{if(200==e.data.code){this.news=e.data.data;let t=e.data.data.detail,a=t.replaceAll("&nbsp;"," ");this.news.detail=a,this.news.release_time=e.data.data.release_time.substring(0,10)}})):this.$router.replace({path:"/News"})},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let i=a.fontSize.replace("px","");const s=308/75*i;document.querySelector(".article-detail").style.height=e-s+"px"},methods:{}},r=n,d=a(1656),c=(0,d.A)(r,i,s,!1,null,"7fc79841",null),u=c.exports}}]);