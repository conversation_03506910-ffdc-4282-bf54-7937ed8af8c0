"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[324],{9464:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("div",{staticClass:"article-detail"},[e("div",{staticClass:"article",domProps:{innerHTML:t._s(t.detail)}})])])},l=[],n=a(3397),s={name:"meetingHotel",data(){return{detail:""}},created(){n.JX({language:2}).then((t=>{if(200==t.data.code){let e=t.data.data.detail,a=e.replaceAll("&nbsp;"," ");this.detail=a}}))},methods:{}},c=s,d=a(1656),r=(0,d.A)(c,i,l,!1,null,"0c8b88dc",null),u=r.exports}}]);