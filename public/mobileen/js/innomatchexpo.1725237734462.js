"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[670],{7710:function(e,t,n){n.r(t),n.d(t,{default:function(){return a}});var c=function(){var e=this;e._self._c;return e._m(0)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("img",{staticClass:"bg",attrs:{src:n(257)}})])}],s={name:"InnoMatchExpo",data(){return{}},created(){},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var n=document.defaultView.getComputedStyle(t,null);let c=n.fontSize.replace("px","");const r=248/75*c;document.querySelector(".main").style.height=e-r+"px"},methods:{}},o=s,u=n(1656),i=(0,u.A)(o,c,r,!1,null,"e3f9fbe6",null),a=i.exports},257:function(e,t,n){e.exports=n.p+"assets/img/innomatchexpo.3fc25150.png"}}]);