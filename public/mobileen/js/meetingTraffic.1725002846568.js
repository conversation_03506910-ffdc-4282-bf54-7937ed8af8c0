"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[461],{2098:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("div",{staticClass:"article-detail"},[e("div",{staticClass:"article",domProps:{innerHTML:t._s(t.detail)}})])])},l=[],n=a(3397),s={name:"meetingTraffic",data(){return{detail:""}},created(){n.cv({language:2}).then((t=>{if(200==t.data.code){let e=t.data.data.detail,a=e.replaceAll("&nbsp;"," ");this.detail=a}}))},methods:{}},c=s,r=a(1656),d=(0,r.A)(c,i,l,!1,null,"3c10a4d3",null),u=d.exports}}]);