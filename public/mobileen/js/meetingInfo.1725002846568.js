"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[222],{6339:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("div",{staticClass:"article-detail"},[e("div",{staticClass:"article",domProps:{innerHTML:t._s(t.detail)}})])])},n=[],l=a(3397),s={name:"meetingInfo",data(){return{detail:""}},created(){l.XK({language:2}).then((t=>{if(200==t.data.code){let e=t.data.data.detail,a=e.replaceAll("&nbsp;"," ");this.detail=a}}))},methods:{}},r=s,c=a(1656),d=(0,c.A)(r,i,n,!1,null,"673542ac",null),u=d.exports}}]);