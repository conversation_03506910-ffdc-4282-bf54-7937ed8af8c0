var oidcByt=function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/",r(r.s=431)}([function(t,e,r){var n=r(2),i=r(17).f,o=r(14),s=r(18),a=r(89),u=r(120),c=r(60);t.exports=function(t,e){var r,f,h,l,p,g=t.target,d=t.global,v=t.stat;if(r=d?n:v?n[g]||a(g,{}):(n[g]||{}).prototype)for(f in e){if(l=e[f],h=t.noTargetGet?(p=i(r,f))&&p.value:r[f],!c(d?f:g+(v?".":"#")+f,t.forced)&&void 0!==h){if(typeof l==typeof h)continue;u(l,h)}(t.sham||h&&h.sham)&&o(l,"sham",!0),s(r,f,l,t)}}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e,r){(function(e){var r=function(t){return t&&t.Math==Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||function(){return this}()||Function("return this")()}).call(this,r(177))},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,r){var n=r(3);t.exports=function(t){if(!n(t))throw TypeError(String(t)+" is not an object");return t}},function(t,e,r){var n=r(1);t.exports=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(t,e,r){var n=r(2),i=r(71),o=r(12),s=r(57),a=r(96),u=r(122),c=i("wks"),f=n.Symbol,h=u?f:f&&f.withoutSetter||s;t.exports=function(t){return o(c,t)&&(a||"string"==typeof c[t])||(a&&o(f,t)?c[t]=f[t]:c[t]=h("Symbol."+t)),c[t]}},function(t,e,r){var n=r(20),i=Math.min;t.exports=function(t){return t>0?i(n(t),9007199254740991):0}},function(t,e,r){"use strict";var n,i=r(105),o=r(5),s=r(2),a=r(3),u=r(12),c=r(65),f=r(14),h=r(18),l=r(9).f,p=r(29),g=r(36),d=r(6),v=r(57),y=s.Int8Array,m=y&&y.prototype,b=s.Uint8ClampedArray,S=b&&b.prototype,w=y&&p(y),_=m&&p(m),E=Object.prototype,x=E.isPrototypeOf,F=d("toStringTag"),A=v("TYPED_ARRAY_TAG"),T=i&&!!g&&"Opera"!==c(s.opera),k=!1,P={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},R={BigInt64Array:8,BigUint64Array:8},I=function(t){if(!a(t))return!1;var e=c(t);return u(P,e)||u(R,e)};for(n in P)s[n]||(T=!1);if((!T||"function"!=typeof w||w===Function.prototype)&&(w=function(){throw TypeError("Incorrect invocation")},T))for(n in P)s[n]&&g(s[n],w);if((!T||!_||_===E)&&(_=w.prototype,T))for(n in P)s[n]&&g(s[n].prototype,_);if(T&&p(S)!==_&&g(S,_),o&&!u(_,F))for(n in k=!0,l(_,F,{get:function(){return a(this)?this[A]:void 0}}),P)s[n]&&f(s[n],A,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:T,TYPED_ARRAY_TAG:k&&A,aTypedArray:function(t){if(I(t))return t;throw TypeError("Target is not a typed array")},aTypedArrayConstructor:function(t){if(g){if(x.call(w,t))return t}else for(var e in P)if(u(P,n)){var r=s[e];if(r&&(t===r||x.call(r,t)))return t}throw TypeError("Target is not a typed array constructor")},exportTypedArrayMethod:function(t,e,r){if(o){if(r)for(var n in P){var i=s[n];if(i&&u(i.prototype,t))try{delete i.prototype[t]}catch(t){}}_[t]&&!r||h(_,t,r?e:T&&m[t]||e)}},exportTypedArrayStaticMethod:function(t,e,r){var n,i;if(o){if(g){if(r)for(n in P)if((i=s[n])&&u(i,t))try{delete i[t]}catch(t){}if(w[t]&&!r)return;try{return h(w,t,r?e:T&&w[t]||e)}catch(t){}}for(n in P)!(i=s[n])||i[t]&&!r||h(i,t,e)}},isView:function(t){if(!a(t))return!1;var e=c(t);return"DataView"===e||u(P,e)||u(R,e)},isTypedArray:I,TypedArray:w,TypedArrayPrototype:_}},function(t,e,r){var n=r(5),i=r(118),o=r(4),s=r(31),a=Object.defineProperty;e.f=n?a:function(t,e,r){if(o(t),e=s(e,!0),o(r),i)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},function(t,e,r){var n=r(13);t.exports=function(t){return Object(n(t))}},function(t,e,r){t.exports=r(117)},function(t,e,r){var n=r(10),i={}.hasOwnProperty;t.exports=Object.hasOwn||function(t,e){return i.call(n(t),e)}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t}},function(t,e,r){var n=r(5),i=r(9),o=r(34);t.exports=n?function(t,e,r){return i.f(t,e,o(1,r))}:function(t,e,r){return t[e]=r,t}},function(t,e,r){var n,i,o,s=r(119),a=r(2),u=r(3),c=r(14),f=r(12),h=r(91),l=r(70),p=r(58),g=a.WeakMap;if(s||h.state){var d=h.state||(h.state=new g),v=d.get,y=d.has,m=d.set;n=function(t,e){if(y.call(d,t))throw new TypeError("Object already initialized");return e.facade=t,m.call(d,t,e),e},i=function(t){return v.call(d,t)||{}},o=function(t){return y.call(d,t)}}else{var b=l("state");p[b]=!0,n=function(t,e){if(f(t,b))throw new TypeError("Object already initialized");return e.facade=t,c(t,b,e),e},i=function(t){return f(t,b)?t[b]:{}},o=function(t){return f(t,b)}}t.exports={set:n,get:i,has:o,enforce:function(t){return o(t)?i(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=i(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}}},function(t,e,r){var n=r(43),i=r(56),o=r(10),s=r(7),a=r(62),u=[].push,c=function(t){var e=1==t,r=2==t,c=3==t,f=4==t,h=6==t,l=7==t,p=5==t||h;return function(g,d,v,y){for(var m,b,S=o(g),w=i(S),_=n(d,v,3),E=s(w.length),x=0,F=y||a,A=e?F(g,E):r||l?F(g,0):void 0;E>x;x++)if((p||x in w)&&(b=_(m=w[x],x,S),t))if(e)A[x]=b;else if(b)switch(t){case 3:return!0;case 5:return m;case 6:return x;case 2:u.call(A,m)}else switch(t){case 4:return!1;case 7:u.call(A,m)}return h?-1:c||f?f:A}};t.exports={forEach:c(0),map:c(1),filter:c(2),some:c(3),every:c(4),find:c(5),findIndex:c(6),filterOut:c(7)}},function(t,e,r){var n=r(5),i=r(69),o=r(34),s=r(22),a=r(31),u=r(12),c=r(118),f=Object.getOwnPropertyDescriptor;e.f=n?f:function(t,e){if(t=s(t),e=a(e,!0),c)try{return f(t,e)}catch(t){}if(u(t,e))return o(!i.f.call(t,e),t[e])}},function(t,e,r){var n=r(2),i=r(14),o=r(12),s=r(89),a=r(90),u=r(15),c=u.get,f=u.enforce,h=String(String).split("String");(t.exports=function(t,e,r,a){var u,c=!!a&&!!a.unsafe,l=!!a&&!!a.enumerable,p=!!a&&!!a.noTargetGet;"function"==typeof r&&("string"!=typeof e||o(r,"name")||i(r,"name",e),(u=f(r)).source||(u.source=h.join("string"==typeof e?e:""))),t!==n?(c?!p&&t[e]&&(l=!0):delete t[e],l?t[e]=r:i(t,e,r)):l?t[e]=r:s(e,r)})(Function.prototype,"toString",(function(){return"function"==typeof this&&c(this).source||a(this)}))},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t}},function(t,e){var r=Math.ceil,n=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?n:r)(t)}},function(t,e,r){var n=r(93),i=r(12),o=r(125),s=r(9).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});i(e,t)||s(e,t,{value:o.f(t)})}},function(t,e,r){var n=r(56),i=r(13);t.exports=function(t){return n(i(t))}},function(t,e,r){var n=r(93),i=r(2),o=function(t){return"function"==typeof t?t:void 0};t.exports=function(t,e){return arguments.length<2?o(n[t])||o(i[t]):n[t]&&n[t][e]||i[t]&&i[t][e]}},function(t,e,r){var n=r(13),i=/"/g;t.exports=function(t,e,r,o){var s=String(n(t)),a="<"+e;return""!==r&&(a+=" "+r+'="'+String(o).replace(i,"&quot;")+'"'),a+">"+s+"</"+e+">"}},function(t,e,r){var n=r(1);t.exports=function(t){return n((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},function(t,e,r){"use strict";var n=r(162),i=Object.prototype.toString;function o(t){return"[object Array]"===i.call(t)}function s(t){return void 0===t}function a(t){return null!==t&&"object"==typeof t}function u(t){if("[object Object]"!==i.call(t))return!1;var e=Object.getPrototypeOf(t);return null===e||e===Object.prototype}function c(t){return"[object Function]"===i.call(t)}function f(t,e){if(null!=t)if("object"!=typeof t&&(t=[t]),o(t))for(var r=0,n=t.length;r<n;r++)e.call(null,t[r],r,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.call(null,t[i],i,t)}t.exports={isArray:o,isArrayBuffer:function(t){return"[object ArrayBuffer]"===i.call(t)},isBuffer:function(t){return null!==t&&!s(t)&&null!==t.constructor&&!s(t.constructor)&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)},isFormData:function(t){return"undefined"!=typeof FormData&&t instanceof FormData},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&t.buffer instanceof ArrayBuffer},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:a,isPlainObject:u,isUndefined:s,isDate:function(t){return"[object Date]"===i.call(t)},isFile:function(t){return"[object File]"===i.call(t)},isBlob:function(t){return"[object Blob]"===i.call(t)},isFunction:c,isStream:function(t){return a(t)&&c(t.pipe)},isURLSearchParams:function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:f,merge:function t(){var e={};function r(r,n){u(e[n])&&u(r)?e[n]=t(e[n],r):u(r)?e[n]=t({},r):o(r)?e[n]=r.slice():e[n]=r}for(var n=0,i=arguments.length;n<i;n++)f(arguments[n],r);return e},extend:function(t,e,r){return f(e,(function(e,i){t[i]=r&&"function"==typeof e?n(e,r):e})),t},trim:function(t){return t.replace(/^\s*/,"").replace(/\s*$/,"")},stripBOM:function(t){return 65279===t.charCodeAt(0)&&(t=t.slice(1)),t}}},function(t,e){function r(t,e,r,n,i,o,s){try{var a=t[o](s),u=a.value}catch(t){return void r(t)}a.done?e(u):Promise.resolve(u).then(n,i)}t.exports=function(t){return function(){var e=this,n=arguments;return new Promise((function(i,o){var s=t.apply(e,n);function a(t){r(s,i,o,a,u,"next",t)}function u(t){r(s,i,o,a,u,"throw",t)}a(void 0)}))}},t.exports.default=t.exports,t.exports.__esModule=!0},function(t,e){t.exports=!1},function(t,e,r){var n=r(12),i=r(10),o=r(70),s=r(98),a=o("IE_PROTO"),u=Object.prototype;t.exports=s?Object.getPrototypeOf:function(t){return t=i(t),n(t,a)?t[a]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?u:null}},function(t,e,r){var n;n=function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=22)}([function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i={debug:function(){},info:function(){},warn:function(){},error:function(){}},o=void 0,s=void 0;(e.Log=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.reset=function(){s=3,o=i},t.debug=function(){if(s>=4){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];o.debug.apply(o,Array.from(e))}},t.info=function(){if(s>=3){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];o.info.apply(o,Array.from(e))}},t.warn=function(){if(s>=2){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];o.warn.apply(o,Array.from(e))}},t.error=function(){if(s>=1){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];o.error.apply(o,Array.from(e))}},n(t,null,[{key:"NONE",get:function(){return 0}},{key:"ERROR",get:function(){return 1}},{key:"WARN",get:function(){return 2}},{key:"INFO",get:function(){return 3}},{key:"DEBUG",get:function(){return 4}},{key:"level",get:function(){return s},set:function(t){if(!(0<=t&&t<=4))throw new Error("Invalid log level");s=t}},{key:"logger",get:function(){return o},set:function(t){if(!t.debug&&t.info&&(t.debug=t.info),!(t.debug&&t.info&&t.warn&&t.error))throw new Error("Invalid logger");o=t}}]),t}()).reset()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i={setInterval:function(t){function e(e,r){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t,e){return setInterval(t,e)})),clearInterval:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){return clearInterval(t)}))},o=!1,s=null;e.Global=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t._testing=function(){o=!0},t.setXMLHttpRequest=function(t){s=t},n(t,null,[{key:"location",get:function(){if(!o)return location}},{key:"localStorage",get:function(){if(!o&&"undefined"!=typeof window)return localStorage}},{key:"sessionStorage",get:function(){if(!o&&"undefined"!=typeof window)return sessionStorage}},{key:"XMLHttpRequest",get:function(){if(!o&&"undefined"!=typeof window)return s||XMLHttpRequest}},{key:"timer",get:function(){if(!o)return i}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MetadataService=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(7);function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var a=".well-known/openid-configuration";e.MetadataService=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.JsonService;if(s(this,t),!e)throw i.Log.error("MetadataService: No settings passed to MetadataService"),new Error("settings");this._settings=e,this._jsonService=new r(["application/jwk-set+json"])}return t.prototype.resetSigningKeys=function(){this._settings=this._settings||{},this._settings.signingKeys=void 0},t.prototype.getMetadata=function(){var t=this;return this._settings.metadata?(i.Log.debug("MetadataService.getMetadata: Returning metadata from settings"),Promise.resolve(this._settings.metadata)):this.metadataUrl?(i.Log.debug("MetadataService.getMetadata: getting metadata from",this.metadataUrl),this._jsonService.getJson(this.metadataUrl).then((function(e){i.Log.debug("MetadataService.getMetadata: json received");var r=t._settings.metadataSeed||{};return t._settings.metadata=Object.assign({},r,e),t._settings.metadata}))):(i.Log.error("MetadataService.getMetadata: No authority or metadataUrl configured on settings"),Promise.reject(new Error("No authority or metadataUrl configured on settings")))},t.prototype.getIssuer=function(){return this._getMetadataProperty("issuer")},t.prototype.getAuthorizationEndpoint=function(){return this._getMetadataProperty("authorization_endpoint")},t.prototype.getUserInfoEndpoint=function(){return this._getMetadataProperty("userinfo_endpoint")},t.prototype.getTokenEndpoint=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this._getMetadataProperty("token_endpoint",t)},t.prototype.getCheckSessionIframe=function(){return this._getMetadataProperty("check_session_iframe",!0)},t.prototype.getEndSessionEndpoint=function(){return this._getMetadataProperty("end_session_endpoint",!0)},t.prototype.getRevocationEndpoint=function(){return this._getMetadataProperty("revocation_endpoint",!0)},t.prototype.getKeysEndpoint=function(){return this._getMetadataProperty("jwks_uri",!0)},t.prototype._getMetadataProperty=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return i.Log.debug("MetadataService.getMetadataProperty for: "+t),this.getMetadata().then((function(r){if(i.Log.debug("MetadataService.getMetadataProperty: metadata recieved"),void 0===r[t]){if(!0===e)return void i.Log.warn("MetadataService.getMetadataProperty: Metadata does not contain optional property "+t);throw i.Log.error("MetadataService.getMetadataProperty: Metadata does not contain property "+t),new Error("Metadata does not contain property "+t)}return r[t]}))},t.prototype.getSigningKeys=function(){var t=this;return this._settings.signingKeys?(i.Log.debug("MetadataService.getSigningKeys: Returning signingKeys from settings"),Promise.resolve(this._settings.signingKeys)):this._getMetadataProperty("jwks_uri").then((function(e){return i.Log.debug("MetadataService.getSigningKeys: jwks_uri received",e),t._jsonService.getJson(e).then((function(e){if(i.Log.debug("MetadataService.getSigningKeys: key set received",e),!e.keys)throw i.Log.error("MetadataService.getSigningKeys: Missing keys on keyset"),new Error("Missing keys on keyset");return t._settings.signingKeys=e.keys,t._settings.signingKeys}))}))},n(t,[{key:"metadataUrl",get:function(){return this._metadataUrl||(this._settings.metadataUrl?this._metadataUrl=this._settings.metadataUrl:(this._metadataUrl=this._settings.authority,this._metadataUrl&&this._metadataUrl.indexOf(a)<0&&("/"!==this._metadataUrl[this._metadataUrl.length-1]&&(this._metadataUrl+="/"),this._metadataUrl+=a))),this._metadataUrl}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UrlUtility=void 0;var n=r(0),i=r(1);e.UrlUtility=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.addQueryParam=function(t,e,r){return t.indexOf("?")<0&&(t+="?"),"?"!==t[t.length-1]&&(t+="&"),t+=encodeURIComponent(e),(t+="=")+encodeURIComponent(r)},t.parseUrlFragment=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:i.Global;"string"!=typeof t&&(t=r.location.href);var o=t.lastIndexOf(e);o>=0&&(t=t.substr(o+1)),"?"===e&&(o=t.indexOf("#"))>=0&&(t=t.substr(0,o));for(var s,a={},u=/([^&=]+)=([^&]*)/g,c=0;s=u.exec(t);)if(a[decodeURIComponent(s[1])]=decodeURIComponent(s[2].replace(/\+/g," ")),c++>50)return n.Log.error("UrlUtility.parseUrlFragment: response exceeded expected number of parameters",t),{error:"Response exceeded expected number of parameters"};for(var f in a)return a;return{}},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.JoseUtil=void 0;var n=r(26),i=function(t){return t&&t.__esModule?t:{default:t}}(r(33));e.JoseUtil=(0,i.default)({jws:n.jws,KeyUtil:n.KeyUtil,X509:n.X509,crypto:n.crypto,hextob64u:n.hextob64u,b64tohex:n.b64tohex,AllowedSigningAlgs:n.AllowedSigningAlgs})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.OidcClientSettings=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),o=r(0),s=r(23),a=r(6),u=r(24),c=r(2);function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var h=".well-known/openid-configuration",l="id_token",p="openid",g="client_secret_post";e.OidcClientSettings=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.authority,i=e.metadataUrl,o=e.metadata,h=e.signingKeys,d=e.metadataSeed,v=e.client_id,y=e.client_secret,m=e.response_type,b=void 0===m?l:m,S=e.scope,w=void 0===S?p:S,_=e.redirect_uri,E=e.post_logout_redirect_uri,x=e.client_authentication,F=void 0===x?g:x,A=e.prompt,T=e.display,k=e.max_age,P=e.ui_locales,R=e.acr_values,I=e.resource,C=e.response_mode,O=e.filterProtocolClaims,D=void 0===O||O,N=e.loadUserInfo,L=void 0===N||N,j=e.staleStateAge,M=void 0===j?900:j,B=e.clockSkew,U=void 0===B?300:B,H=e.clockService,V=void 0===H?new s.ClockService:H,q=e.userInfoJwtIssuer,K=void 0===q?"OP":q,W=e.mergeClaims,J=void 0!==W&&W,z=e.stateStore,Y=void 0===z?new a.WebStorageStateStore:z,G=e.ResponseValidatorCtor,$=void 0===G?u.ResponseValidator:G,X=e.MetadataServiceCtor,Z=void 0===X?c.MetadataService:X,Q=e.extraQueryParams,tt=void 0===Q?{}:Q,et=e.extraTokenParams,rt=void 0===et?{}:et;f(this,t),this._authority=r,this._metadataUrl=i,this._metadata=o,this._metadataSeed=d,this._signingKeys=h,this._client_id=v,this._client_secret=y,this._response_type=b,this._scope=w,this._redirect_uri=_,this._post_logout_redirect_uri=E,this._client_authentication=F,this._prompt=A,this._display=T,this._max_age=k,this._ui_locales=P,this._acr_values=R,this._resource=I,this._response_mode=C,this._filterProtocolClaims=!!D,this._loadUserInfo=!!L,this._staleStateAge=M,this._clockSkew=U,this._clockService=V,this._userInfoJwtIssuer=K,this._mergeClaims=!!J,this._stateStore=Y,this._validator=new $(this),this._metadataService=new Z(this),this._extraQueryParams="object"===(void 0===tt?"undefined":n(tt))?tt:{},this._extraTokenParams="object"===(void 0===rt?"undefined":n(rt))?rt:{}}return t.prototype.getEpochTime=function(){return this._clockService.getEpochTime()},i(t,[{key:"client_id",get:function(){return this._client_id},set:function(t){if(this._client_id)throw o.Log.error("OidcClientSettings.set_client_id: client_id has already been assigned."),new Error("client_id has already been assigned.");this._client_id=t}},{key:"client_secret",get:function(){return this._client_secret}},{key:"response_type",get:function(){return this._response_type}},{key:"scope",get:function(){return this._scope}},{key:"redirect_uri",get:function(){return this._redirect_uri}},{key:"post_logout_redirect_uri",get:function(){return this._post_logout_redirect_uri}},{key:"client_authentication",get:function(){return this._client_authentication}},{key:"prompt",get:function(){return this._prompt}},{key:"display",get:function(){return this._display}},{key:"max_age",get:function(){return this._max_age}},{key:"ui_locales",get:function(){return this._ui_locales}},{key:"acr_values",get:function(){return this._acr_values}},{key:"resource",get:function(){return this._resource}},{key:"response_mode",get:function(){return this._response_mode}},{key:"authority",get:function(){return this._authority},set:function(t){if(this._authority)throw o.Log.error("OidcClientSettings.set_authority: authority has already been assigned."),new Error("authority has already been assigned.");this._authority=t}},{key:"metadataUrl",get:function(){return this._metadataUrl||(this._metadataUrl=this.authority,this._metadataUrl&&this._metadataUrl.indexOf(h)<0&&("/"!==this._metadataUrl[this._metadataUrl.length-1]&&(this._metadataUrl+="/"),this._metadataUrl+=h)),this._metadataUrl}},{key:"metadata",get:function(){return this._metadata},set:function(t){this._metadata=t}},{key:"metadataSeed",get:function(){return this._metadataSeed},set:function(t){this._metadataSeed=t}},{key:"signingKeys",get:function(){return this._signingKeys},set:function(t){this._signingKeys=t}},{key:"filterProtocolClaims",get:function(){return this._filterProtocolClaims}},{key:"loadUserInfo",get:function(){return this._loadUserInfo}},{key:"staleStateAge",get:function(){return this._staleStateAge}},{key:"clockSkew",get:function(){return this._clockSkew}},{key:"userInfoJwtIssuer",get:function(){return this._userInfoJwtIssuer}},{key:"mergeClaims",get:function(){return this._mergeClaims}},{key:"stateStore",get:function(){return this._stateStore}},{key:"validator",get:function(){return this._validator}},{key:"metadataService",get:function(){return this._metadataService}},{key:"extraQueryParams",get:function(){return this._extraQueryParams},set:function(t){"object"===(void 0===t?"undefined":n(t))?this._extraQueryParams=t:this._extraQueryParams={}}},{key:"extraTokenParams",get:function(){return this._extraTokenParams},set:function(t){"object"===(void 0===t?"undefined":n(t))?this._extraTokenParams=t:this._extraTokenParams={}}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WebStorageStateStore=void 0;var n=r(0),i=r(1);function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.WebStorageStateStore=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.prefix,n=void 0===r?"oidc.":r,s=e.store,a=void 0===s?i.Global.localStorage:s;o(this,t),this._store=a,this._prefix=n}return t.prototype.set=function(t,e){return n.Log.debug("WebStorageStateStore.set",t),t=this._prefix+t,this._store.setItem(t,e),Promise.resolve()},t.prototype.get=function(t){n.Log.debug("WebStorageStateStore.get",t),t=this._prefix+t;var e=this._store.getItem(t);return Promise.resolve(e)},t.prototype.remove=function(t){n.Log.debug("WebStorageStateStore.remove",t),t=this._prefix+t;var e=this._store.getItem(t);return this._store.removeItem(t),Promise.resolve(e)},t.prototype.getAllKeys=function(){n.Log.debug("WebStorageStateStore.getAllKeys");for(var t=[],e=0;e<this._store.length;e++){var r=this._store.key(e);0===r.indexOf(this._prefix)&&t.push(r.substr(this._prefix.length))}return Promise.resolve(t)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.JsonService=void 0;var n=r(0),i=r(1);function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.JsonService=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Global.XMLHttpRequest,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;o(this,t),e&&Array.isArray(e)?this._contentTypes=e.slice():this._contentTypes=[],this._contentTypes.push("application/json"),n&&this._contentTypes.push("application/jwt"),this._XMLHttpRequest=r,this._jwtHandler=n}return t.prototype.getJson=function(t,e){var r=this;if(!t)throw n.Log.error("JsonService.getJson: No url passed"),new Error("url");return n.Log.debug("JsonService.getJson, url: ",t),new Promise((function(i,o){var s=new r._XMLHttpRequest;s.open("GET",t);var a=r._contentTypes,u=r._jwtHandler;s.onload=function(){if(n.Log.debug("JsonService.getJson: HTTP response received, status",s.status),200===s.status){var e=s.getResponseHeader("Content-Type");if(e){var r=a.find((function(t){if(e.startsWith(t))return!0}));if("application/jwt"==r)return void u(s).then(i,o);if(r)try{return void i(JSON.parse(s.responseText))}catch(t){return n.Log.error("JsonService.getJson: Error parsing JSON response",t.message),void o(t)}}o(Error("Invalid response Content-Type: "+e+", from URL: "+t))}else o(Error(s.statusText+" ("+s.status+")"))},s.onerror=function(){n.Log.error("JsonService.getJson: network error"),o(Error("Network Error"))},e&&(n.Log.debug("JsonService.getJson: token passed, setting Authorization header"),s.setRequestHeader("Authorization","Bearer "+e)),s.send()}))},t.prototype.postForm=function(t,e,r){var i=this;if(!t)throw n.Log.error("JsonService.postForm: No url passed"),new Error("url");return n.Log.debug("JsonService.postForm, url: ",t),new Promise((function(o,s){var a=new i._XMLHttpRequest;a.open("POST",t);var u=i._contentTypes;a.onload=function(){if(n.Log.debug("JsonService.postForm: HTTP response received, status",a.status),200!==a.status){if(400===a.status&&(r=a.getResponseHeader("Content-Type"))&&u.find((function(t){if(r.startsWith(t))return!0})))try{var e=JSON.parse(a.responseText);if(e&&e.error)return n.Log.error("JsonService.postForm: Error from server: ",e.error),void s(new Error(e.error))}catch(t){return n.Log.error("JsonService.postForm: Error parsing JSON response",t.message),void s(t)}s(Error(a.statusText+" ("+a.status+")"))}else{var r;if((r=a.getResponseHeader("Content-Type"))&&u.find((function(t){if(r.startsWith(t))return!0})))try{return void o(JSON.parse(a.responseText))}catch(t){return n.Log.error("JsonService.postForm: Error parsing JSON response",t.message),void s(t)}s(Error("Invalid response Content-Type: "+r+", from URL: "+t))}},a.onerror=function(){n.Log.error("JsonService.postForm: network error"),s(Error("Network Error"))};var c="";for(var f in e){var h=e[f];h&&(c.length>0&&(c+="&"),c+=encodeURIComponent(f),c+="=",c+=encodeURIComponent(h))}a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),void 0!==r&&a.setRequestHeader("Authorization","Basic "+btoa(r)),a.send(c)}))},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SigninRequest=void 0;var n=r(0),i=r(3),o=r(13);e.SigninRequest=function(){function t(e){var r=e.url,s=e.client_id,a=e.redirect_uri,u=e.response_type,c=e.scope,f=e.authority,h=e.data,l=e.prompt,p=e.display,g=e.max_age,d=e.ui_locales,v=e.id_token_hint,y=e.login_hint,m=e.acr_values,b=e.resource,S=e.response_mode,w=e.request,_=e.request_uri,E=e.extraQueryParams,x=e.request_type,F=e.client_secret,A=e.extraTokenParams,T=e.skipUserInfo;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!r)throw n.Log.error("SigninRequest.ctor: No url passed"),new Error("url");if(!s)throw n.Log.error("SigninRequest.ctor: No client_id passed"),new Error("client_id");if(!a)throw n.Log.error("SigninRequest.ctor: No redirect_uri passed"),new Error("redirect_uri");if(!u)throw n.Log.error("SigninRequest.ctor: No response_type passed"),new Error("response_type");if(!c)throw n.Log.error("SigninRequest.ctor: No scope passed"),new Error("scope");if(!f)throw n.Log.error("SigninRequest.ctor: No authority passed"),new Error("authority");var k=t.isOidc(u),P=t.isCode(u);S||(S=t.isCode(u)?"query":null),this.state=new o.SigninState({nonce:k,data:h,client_id:s,authority:f,redirect_uri:a,code_verifier:P,request_type:x,response_mode:S,client_secret:F,scope:c,extraTokenParams:A,skipUserInfo:T}),r=i.UrlUtility.addQueryParam(r,"client_id",s),r=i.UrlUtility.addQueryParam(r,"redirect_uri",a),r=i.UrlUtility.addQueryParam(r,"response_type",u),r=i.UrlUtility.addQueryParam(r,"scope",c),r=i.UrlUtility.addQueryParam(r,"state",this.state.id),k&&(r=i.UrlUtility.addQueryParam(r,"nonce",this.state.nonce)),P&&(r=i.UrlUtility.addQueryParam(r,"code_challenge",this.state.code_challenge),r=i.UrlUtility.addQueryParam(r,"code_challenge_method","S256"));var R={prompt:l,display:p,max_age:g,ui_locales:d,id_token_hint:v,login_hint:y,acr_values:m,resource:b,request:w,request_uri:_,response_mode:S};for(var I in R)R[I]&&(r=i.UrlUtility.addQueryParam(r,I,R[I]));for(var C in E)r=i.UrlUtility.addQueryParam(r,C,E[C]);this.url=r}return t.isOidc=function(t){return!!t.split(/\s+/g).filter((function(t){return"id_token"===t}))[0]},t.isOAuth=function(t){return!!t.split(/\s+/g).filter((function(t){return"token"===t}))[0]},t.isCode=function(t){return!!t.split(/\s+/g).filter((function(t){return"code"===t}))[0]},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.State=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=function(t){return t&&t.__esModule?t:{default:t}}(r(14));function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.State=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.id,n=e.data,i=e.created,a=e.request_type;s(this,t),this._id=r||(0,o.default)(),this._data=n,this._created="number"==typeof i&&i>0?i:parseInt(Date.now()/1e3),this._request_type=a}return t.prototype.toStorageString=function(){return i.Log.debug("State.toStorageString"),JSON.stringify({id:this.id,data:this.data,created:this.created,request_type:this.request_type})},t.fromStorageString=function(e){return i.Log.debug("State.fromStorageString"),new t(JSON.parse(e))},t.clearStaleState=function(e,r){var n=Date.now()/1e3-r;return e.getAllKeys().then((function(r){i.Log.debug("State.clearStaleState: got keys",r);for(var o=[],s=function(s){var a=r[s];u=e.get(a).then((function(r){var o=!1;if(r)try{var s=t.fromStorageString(r);i.Log.debug("State.clearStaleState: got item from key: ",a,s.created),s.created<=n&&(o=!0)}catch(t){i.Log.error("State.clearStaleState: Error parsing state for key",a,t.message),o=!0}else i.Log.debug("State.clearStaleState: no item in storage for key: ",a),o=!0;if(o)return i.Log.debug("State.clearStaleState: removed item for key: ",a),e.remove(a)})),o.push(u)},a=0;a<r.length;a++){var u;s(a)}return i.Log.debug("State.clearStaleState: waiting on promise count:",o.length),Promise.all(o)}))},n(t,[{key:"id",get:function(){return this._id}},{key:"data",get:function(){return this._data}},{key:"created",get:function(){return this._created}},{key:"request_type",get:function(){return this._request_type}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.OidcClient=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(5),s=r(12),a=r(8),u=r(34),c=r(35),f=r(36),h=r(13),l=r(9);function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.OidcClient=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};p(this,t),e instanceof o.OidcClientSettings?this._settings=e:this._settings=new o.OidcClientSettings(e)}return t.prototype.createSigninRequest=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.response_type,n=e.scope,o=e.redirect_uri,s=e.data,u=e.state,c=e.prompt,f=e.display,h=e.max_age,l=e.ui_locales,p=e.id_token_hint,g=e.login_hint,d=e.acr_values,v=e.resource,y=e.request,m=e.request_uri,b=e.response_mode,S=e.extraQueryParams,w=e.extraTokenParams,_=e.request_type,E=e.skipUserInfo,x=arguments[1];i.Log.debug("OidcClient.createSigninRequest");var F=this._settings.client_id;r=r||this._settings.response_type,n=n||this._settings.scope,o=o||this._settings.redirect_uri,c=c||this._settings.prompt,f=f||this._settings.display,h=h||this._settings.max_age,l=l||this._settings.ui_locales,d=d||this._settings.acr_values,v=v||this._settings.resource,b=b||this._settings.response_mode,S=S||this._settings.extraQueryParams,w=w||this._settings.extraTokenParams;var A=this._settings.authority;return a.SigninRequest.isCode(r)&&"code"!==r?Promise.reject(new Error("OpenID Connect hybrid flow is not supported")):this._metadataService.getAuthorizationEndpoint().then((function(e){i.Log.debug("OidcClient.createSigninRequest: Received authorization endpoint",e);var T=new a.SigninRequest({url:e,client_id:F,redirect_uri:o,response_type:r,scope:n,data:s||u,authority:A,prompt:c,display:f,max_age:h,ui_locales:l,id_token_hint:p,login_hint:g,acr_values:d,resource:v,request:y,request_uri:m,extraQueryParams:S,extraTokenParams:w,request_type:_,response_mode:b,client_secret:t._settings.client_secret,skipUserInfo:E}),k=T.state;return(x=x||t._stateStore).set(k.id,k.toStorageString()).then((function(){return T}))}))},t.prototype.readSigninResponseState=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];i.Log.debug("OidcClient.readSigninResponseState");var n="query"===this._settings.response_mode||!this._settings.response_mode&&a.SigninRequest.isCode(this._settings.response_type),o=n?"?":"#",s=new u.SigninResponse(t,o);if(!s.state)return i.Log.error("OidcClient.readSigninResponseState: No state in response"),Promise.reject(new Error("No state in response"));e=e||this._stateStore;var c=r?e.remove.bind(e):e.get.bind(e);return c(s.state).then((function(t){if(!t)throw i.Log.error("OidcClient.readSigninResponseState: No matching state found in storage"),new Error("No matching state found in storage");return{state:h.SigninState.fromStorageString(t),response:s}}))},t.prototype.processSigninResponse=function(t,e){var r=this;return i.Log.debug("OidcClient.processSigninResponse"),this.readSigninResponseState(t,e,!0).then((function(t){var e=t.state,n=t.response;return i.Log.debug("OidcClient.processSigninResponse: Received state from storage; validating response"),r._validator.validateSigninResponse(e,n)}))},t.prototype.createSignoutRequest=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.id_token_hint,n=e.data,o=e.state,s=e.post_logout_redirect_uri,a=e.extraQueryParams,u=e.request_type,f=arguments[1];return i.Log.debug("OidcClient.createSignoutRequest"),s=s||this._settings.post_logout_redirect_uri,a=a||this._settings.extraQueryParams,this._metadataService.getEndSessionEndpoint().then((function(e){if(!e)throw i.Log.error("OidcClient.createSignoutRequest: No end session endpoint url returned"),new Error("no end session endpoint");i.Log.debug("OidcClient.createSignoutRequest: Received end session endpoint",e);var h=new c.SignoutRequest({url:e,id_token_hint:r,post_logout_redirect_uri:s,data:n||o,extraQueryParams:a,request_type:u}),l=h.state;return l&&(i.Log.debug("OidcClient.createSignoutRequest: Signout request has state to persist"),(f=f||t._stateStore).set(l.id,l.toStorageString())),h}))},t.prototype.readSignoutResponseState=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];i.Log.debug("OidcClient.readSignoutResponseState");var n=new f.SignoutResponse(t);if(!n.state)return i.Log.debug("OidcClient.readSignoutResponseState: No state in response"),n.error?(i.Log.warn("OidcClient.readSignoutResponseState: Response was error: ",n.error),Promise.reject(new s.ErrorResponse(n))):Promise.resolve({state:void 0,response:n});var o=n.state;e=e||this._stateStore;var a=r?e.remove.bind(e):e.get.bind(e);return a(o).then((function(t){if(!t)throw i.Log.error("OidcClient.readSignoutResponseState: No matching state found in storage"),new Error("No matching state found in storage");return{state:l.State.fromStorageString(t),response:n}}))},t.prototype.processSignoutResponse=function(t,e){var r=this;return i.Log.debug("OidcClient.processSignoutResponse"),this.readSignoutResponseState(t,e,!0).then((function(t){var e=t.state,n=t.response;return e?(i.Log.debug("OidcClient.processSignoutResponse: Received state from storage; validating response"),r._validator.validateSignoutResponse(e,n)):(i.Log.debug("OidcClient.processSignoutResponse: No state from storage; skipping validating response"),n)}))},t.prototype.clearStaleState=function(t){return i.Log.debug("OidcClient.clearStaleState"),t=t||this._stateStore,l.State.clearStaleState(t,this.settings.staleStateAge)},n(t,[{key:"_stateStore",get:function(){return this.settings.stateStore}},{key:"_validator",get:function(){return this.settings.validator}},{key:"_metadataService",get:function(){return this.settings.metadataService}},{key:"settings",get:function(){return this._settings}},{key:"metadataService",get:function(){return this._metadataService}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TokenClient=void 0;var n=r(7),i=r(2),o=r(0);function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.TokenClient=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.JsonService,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:i.MetadataService;if(s(this,t),!e)throw o.Log.error("TokenClient.ctor: No settings passed"),new Error("settings");this._settings=e,this._jsonService=new r,this._metadataService=new a(this._settings)}return t.prototype.exchangeCode=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=Object.assign({},e)).grant_type=e.grant_type||"authorization_code",e.client_id=e.client_id||this._settings.client_id,e.client_secret=e.client_secret||this._settings.client_secret,e.redirect_uri=e.redirect_uri||this._settings.redirect_uri;var r=void 0,n=e._client_authentication||this._settings._client_authentication;return delete e._client_authentication,e.code?e.redirect_uri?e.code_verifier?e.client_id?e.client_secret||"client_secret_basic"!=n?("client_secret_basic"==n&&(r=e.client_id+":"+e.client_secret,delete e.client_id,delete e.client_secret),this._metadataService.getTokenEndpoint(!1).then((function(n){return o.Log.debug("TokenClient.exchangeCode: Received token endpoint"),t._jsonService.postForm(n,e,r).then((function(t){return o.Log.debug("TokenClient.exchangeCode: response received"),t}))}))):(o.Log.error("TokenClient.exchangeCode: No client_secret passed"),Promise.reject(new Error("A client_secret is required"))):(o.Log.error("TokenClient.exchangeCode: No client_id passed"),Promise.reject(new Error("A client_id is required"))):(o.Log.error("TokenClient.exchangeCode: No code_verifier passed"),Promise.reject(new Error("A code_verifier is required"))):(o.Log.error("TokenClient.exchangeCode: No redirect_uri passed"),Promise.reject(new Error("A redirect_uri is required"))):(o.Log.error("TokenClient.exchangeCode: No code passed"),Promise.reject(new Error("A code is required")))},t.prototype.exchangeRefreshToken=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=Object.assign({},e)).grant_type=e.grant_type||"refresh_token",e.client_id=e.client_id||this._settings.client_id,e.client_secret=e.client_secret||this._settings.client_secret;var r=void 0,n=e._client_authentication||this._settings._client_authentication;return delete e._client_authentication,e.refresh_token?e.client_id?("client_secret_basic"==n&&(r=e.client_id+":"+e.client_secret,delete e.client_id,delete e.client_secret),this._metadataService.getTokenEndpoint(!1).then((function(n){return o.Log.debug("TokenClient.exchangeRefreshToken: Received token endpoint"),t._jsonService.postForm(n,e,r).then((function(t){return o.Log.debug("TokenClient.exchangeRefreshToken: response received"),t}))}))):(o.Log.error("TokenClient.exchangeRefreshToken: No client_id passed"),Promise.reject(new Error("A client_id is required"))):(o.Log.error("TokenClient.exchangeRefreshToken: No refresh_token passed"),Promise.reject(new Error("A refresh_token is required")))},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ErrorResponse=void 0;var n=r(0);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}e.ErrorResponse=function(t){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=r.error,a=r.error_description,u=r.error_uri,c=r.state,f=r.session_state;if(i(this,e),!s)throw n.Log.error("No error passed to ErrorResponse"),new Error("error");var h=o(this,t.call(this,a||s));return h.name="ErrorResponse",h.error=s,h.error_description=a,h.error_uri=u,h.state=c,h.session_state=f,h}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(Error)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SigninState=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(9),s=r(4),a=function(t){return t&&t.__esModule?t:{default:t}}(r(14));function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}e.SigninState=function(t){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=r.nonce,i=r.authority,o=r.client_id,f=r.redirect_uri,h=r.code_verifier,l=r.response_mode,p=r.client_secret,g=r.scope,d=r.extraTokenParams,v=r.skipUserInfo;u(this,e);var y=c(this,t.call(this,arguments[0]));if(!0===n?y._nonce=(0,a.default)():n&&(y._nonce=n),!0===h?y._code_verifier=(0,a.default)()+(0,a.default)()+(0,a.default)():h&&(y._code_verifier=h),y.code_verifier){var m=s.JoseUtil.hashString(y.code_verifier,"SHA256");y._code_challenge=s.JoseUtil.hexToBase64Url(m)}return y._redirect_uri=f,y._authority=i,y._client_id=o,y._response_mode=l,y._client_secret=p,y._scope=g,y._extraTokenParams=d,y._skipUserInfo=v,y}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.toStorageString=function(){return i.Log.debug("SigninState.toStorageString"),JSON.stringify({id:this.id,data:this.data,created:this.created,request_type:this.request_type,nonce:this.nonce,code_verifier:this.code_verifier,redirect_uri:this.redirect_uri,authority:this.authority,client_id:this.client_id,response_mode:this.response_mode,client_secret:this.client_secret,scope:this.scope,extraTokenParams:this.extraTokenParams,skipUserInfo:this.skipUserInfo})},e.fromStorageString=function(t){return i.Log.debug("SigninState.fromStorageString"),new e(JSON.parse(t))},n(e,[{key:"nonce",get:function(){return this._nonce}},{key:"authority",get:function(){return this._authority}},{key:"client_id",get:function(){return this._client_id}},{key:"redirect_uri",get:function(){return this._redirect_uri}},{key:"code_verifier",get:function(){return this._code_verifier}},{key:"code_challenge",get:function(){return this._code_challenge}},{key:"response_mode",get:function(){return this._response_mode}},{key:"client_secret",get:function(){return this._client_secret}},{key:"scope",get:function(){return this._scope}},{key:"extraTokenParams",get:function(){return this._extraTokenParams}},{key:"skipUserInfo",get:function(){return this._skipUserInfo}}]),e}(o.State)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){return("undefined"!=n&&null!==n&&void 0!==n.getRandomValues?i:o)().replace(/-/g,"")};var n="undefined"!=typeof window?window.crypto||window.msCrypto:null;function i(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^n.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16)}))}function o(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(t){return(t^16*Math.random()>>t/4).toString(16)}))}t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.User=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0);e.User=function(){function t(e){var r=e.id_token,n=e.session_state,i=e.access_token,o=e.refresh_token,s=e.token_type,a=e.scope,u=e.profile,c=e.expires_at,f=e.state;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.id_token=r,this.session_state=n,this.access_token=i,this.refresh_token=o,this.token_type=s,this.scope=a,this.profile=u,this.expires_at=c,this.state=f}return t.prototype.toStorageString=function(){return i.Log.debug("User.toStorageString"),JSON.stringify({id_token:this.id_token,session_state:this.session_state,access_token:this.access_token,refresh_token:this.refresh_token,token_type:this.token_type,scope:this.scope,profile:this.profile,expires_at:this.expires_at})},t.fromStorageString=function(e){return i.Log.debug("User.fromStorageString"),new t(JSON.parse(e))},n(t,[{key:"expires_in",get:function(){if(this.expires_at){var t=parseInt(Date.now()/1e3);return this.expires_at-t}},set:function(t){var e=parseInt(t);if("number"==typeof e&&e>0){var r=parseInt(Date.now()/1e3);this.expires_at=r+e}}},{key:"expired",get:function(){var t=this.expires_in;if(void 0!==t)return t<=0}},{key:"scopes",get:function(){return(this.scope||"").split(" ")}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AccessTokenEvents=void 0;var n=r(0),i=r(46);function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.AccessTokenEvents=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.accessTokenExpiringNotificationTime,n=void 0===r?60:r,s=e.accessTokenExpiringTimer,a=void 0===s?new i.Timer("Access token expiring"):s,u=e.accessTokenExpiredTimer,c=void 0===u?new i.Timer("Access token expired"):u;o(this,t),this._accessTokenExpiringNotificationTime=n,this._accessTokenExpiring=a,this._accessTokenExpired=c}return t.prototype.load=function(t){if(t.access_token&&void 0!==t.expires_in){var e=t.expires_in;if(n.Log.debug("AccessTokenEvents.load: access token present, remaining duration:",e),e>0){var r=e-this._accessTokenExpiringNotificationTime;r<=0&&(r=1),n.Log.debug("AccessTokenEvents.load: registering expiring timer in:",r),this._accessTokenExpiring.init(r)}else n.Log.debug("AccessTokenEvents.load: canceling existing expiring timer becase we're past expiration."),this._accessTokenExpiring.cancel();var i=e+1;n.Log.debug("AccessTokenEvents.load: registering expired timer in:",i),this._accessTokenExpired.init(i)}else this._accessTokenExpiring.cancel(),this._accessTokenExpired.cancel()},t.prototype.unload=function(){n.Log.debug("AccessTokenEvents.unload: canceling existing access token timers"),this._accessTokenExpiring.cancel(),this._accessTokenExpired.cancel()},t.prototype.addAccessTokenExpiring=function(t){this._accessTokenExpiring.addHandler(t)},t.prototype.removeAccessTokenExpiring=function(t){this._accessTokenExpiring.removeHandler(t)},t.prototype.addAccessTokenExpired=function(t){this._accessTokenExpired.addHandler(t)},t.prototype.removeAccessTokenExpired=function(t){this._accessTokenExpired.removeHandler(t)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Event=void 0;var n=r(0);e.Event=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._name=e,this._callbacks=[]}return t.prototype.addHandler=function(t){this._callbacks.push(t)},t.prototype.removeHandler=function(t){var e=this._callbacks.findIndex((function(e){return e===t}));e>=0&&this._callbacks.splice(e,1)},t.prototype.raise=function(){n.Log.debug("Event: Raising event: "+this._name);for(var t=0;t<this._callbacks.length;t++){var e;(e=this._callbacks)[t].apply(e,arguments)}},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SessionMonitor=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(19),s=r(1);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.SessionMonitor=function(){function t(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.CheckSessionIFrame,u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.Global.timer;if(a(this,t),!e)throw i.Log.error("SessionMonitor.ctor: No user manager passed to SessionMonitor"),new Error("userManager");this._userManager=e,this._CheckSessionIFrameCtor=n,this._timer=u,this._userManager.events.addUserLoaded(this._start.bind(this)),this._userManager.events.addUserUnloaded(this._stop.bind(this)),Promise.resolve(this._userManager.getUser().then((function(t){t?r._start(t):r._settings.monitorAnonymousSession&&r._userManager.querySessionStatus().then((function(t){var e={session_state:t.session_state};t.sub&&t.sid&&(e.profile={sub:t.sub,sid:t.sid}),r._start(e)})).catch((function(t){i.Log.error("SessionMonitor ctor: error from querySessionStatus:",t.message)}))})).catch((function(t){i.Log.error("SessionMonitor ctor: error from getUser:",t.message)})))}return t.prototype._start=function(t){var e=this,r=t.session_state;r&&(t.profile?(this._sub=t.profile.sub,this._sid=t.profile.sid,i.Log.debug("SessionMonitor._start: session_state:",r,", sub:",this._sub)):(this._sub=void 0,this._sid=void 0,i.Log.debug("SessionMonitor._start: session_state:",r,", anonymous user")),this._checkSessionIFrame?this._checkSessionIFrame.start(r):this._metadataService.getCheckSessionIframe().then((function(t){if(t){i.Log.debug("SessionMonitor._start: Initializing check session iframe");var n=e._client_id,o=e._checkSessionInterval,s=e._stopCheckSessionOnError;e._checkSessionIFrame=new e._CheckSessionIFrameCtor(e._callback.bind(e),n,t,o,s),e._checkSessionIFrame.load().then((function(){e._checkSessionIFrame.start(r)}))}else i.Log.warn("SessionMonitor._start: No check session iframe found in the metadata")})).catch((function(t){i.Log.error("SessionMonitor._start: Error from getCheckSessionIframe:",t.message)})))},t.prototype._stop=function(){var t=this;if(this._sub=void 0,this._sid=void 0,this._checkSessionIFrame&&(i.Log.debug("SessionMonitor._stop"),this._checkSessionIFrame.stop()),this._settings.monitorAnonymousSession)var e=this._timer.setInterval((function(){t._timer.clearInterval(e),t._userManager.querySessionStatus().then((function(e){var r={session_state:e.session_state};e.sub&&e.sid&&(r.profile={sub:e.sub,sid:e.sid}),t._start(r)})).catch((function(t){i.Log.error("SessionMonitor: error from querySessionStatus:",t.message)}))}),1e3)},t.prototype._callback=function(){var t=this;this._userManager.querySessionStatus().then((function(e){var r=!0;e?e.sub===t._sub?(r=!1,t._checkSessionIFrame.start(e.session_state),e.sid===t._sid?i.Log.debug("SessionMonitor._callback: Same sub still logged in at OP, restarting check session iframe; session_state:",e.session_state):(i.Log.debug("SessionMonitor._callback: Same sub still logged in at OP, session state has changed, restarting check session iframe; session_state:",e.session_state),t._userManager.events._raiseUserSessionChanged())):i.Log.debug("SessionMonitor._callback: Different subject signed into OP:",e.sub):i.Log.debug("SessionMonitor._callback: Subject no longer signed into OP"),r&&(t._sub?(i.Log.debug("SessionMonitor._callback: SessionMonitor._callback; raising signed out event"),t._userManager.events._raiseUserSignedOut()):(i.Log.debug("SessionMonitor._callback: SessionMonitor._callback; raising signed in event"),t._userManager.events._raiseUserSignedIn()))})).catch((function(e){t._sub&&(i.Log.debug("SessionMonitor._callback: Error calling queryCurrentSigninSession; raising signed out event",e.message),t._userManager.events._raiseUserSignedOut())}))},n(t,[{key:"_settings",get:function(){return this._userManager.settings}},{key:"_metadataService",get:function(){return this._userManager.metadataService}},{key:"_client_id",get:function(){return this._settings.client_id}},{key:"_checkSessionInterval",get:function(){return this._settings.checkSessionInterval}},{key:"_stopCheckSessionOnError",get:function(){return this._settings.stopCheckSessionOnError}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CheckSessionIFrame=void 0;var n=r(0);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.CheckSessionIFrame=function(){function t(e,r,n,o){var s=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];i(this,t),this._callback=e,this._client_id=r,this._url=n,this._interval=o||2e3,this._stopOnError=s;var a=n.indexOf("/",n.indexOf("//")+2);this._frame_origin=n.substr(0,a),this._frame=window.document.createElement("iframe"),this._frame.style.visibility="hidden",this._frame.style.position="absolute",this._frame.style.display="none",this._frame.width=0,this._frame.height=0,this._frame.src=n}return t.prototype.load=function(){var t=this;return new Promise((function(e){t._frame.onload=function(){e()},window.document.body.appendChild(t._frame),t._boundMessageEvent=t._message.bind(t),window.addEventListener("message",t._boundMessageEvent,!1)}))},t.prototype._message=function(t){t.origin===this._frame_origin&&t.source===this._frame.contentWindow&&("error"===t.data?(n.Log.error("CheckSessionIFrame: error message from check session op iframe"),this._stopOnError&&this.stop()):"changed"===t.data?(n.Log.debug("CheckSessionIFrame: changed message from check session op iframe"),this.stop(),this._callback()):n.Log.debug("CheckSessionIFrame: "+t.data+" message from check session op iframe"))},t.prototype.start=function(t){var e=this;if(this._session_state!==t){n.Log.debug("CheckSessionIFrame.start"),this.stop(),this._session_state=t;var r=function(){e._frame.contentWindow.postMessage(e._client_id+" "+e._session_state,e._frame_origin)};r(),this._timer=window.setInterval(r,this._interval)}},t.prototype.stop=function(){this._session_state=null,this._timer&&(n.Log.debug("CheckSessionIFrame.stop"),window.clearInterval(this._timer),this._timer=null)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TokenRevocationClient=void 0;var n=r(0),i=r(2),o=r(1);function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var a="access_token",u="refresh_token";e.TokenRevocationClient=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.Global.XMLHttpRequest,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:i.MetadataService;if(s(this,t),!e)throw n.Log.error("TokenRevocationClient.ctor: No settings provided"),new Error("No settings provided.");this._settings=e,this._XMLHttpRequestCtor=r,this._metadataService=new a(this._settings)}return t.prototype.revoke=function(t,e){var r=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"access_token";if(!t)throw n.Log.error("TokenRevocationClient.revoke: No token provided"),new Error("No token provided.");if(i!==a&&i!=u)throw n.Log.error("TokenRevocationClient.revoke: Invalid token type"),new Error("Invalid token type.");return this._metadataService.getRevocationEndpoint().then((function(o){if(o){n.Log.debug("TokenRevocationClient.revoke: Revoking "+i);var s=r._settings.client_id,a=r._settings.client_secret;return r._revoke(o,s,a,t,i)}if(e)throw n.Log.error("TokenRevocationClient.revoke: Revocation not supported"),new Error("Revocation not supported")}))},t.prototype._revoke=function(t,e,r,i,o){var s=this;return new Promise((function(a,u){var c=new s._XMLHttpRequestCtor;c.open("POST",t),c.onload=function(){n.Log.debug("TokenRevocationClient.revoke: HTTP response received, status",c.status),200===c.status?a():u(Error(c.statusText+" ("+c.status+")"))},c.onerror=function(){n.Log.debug("TokenRevocationClient.revoke: Network Error."),u("Network Error")};var f="client_id="+encodeURIComponent(e);r&&(f+="&client_secret="+encodeURIComponent(r)),f+="&token_type_hint="+encodeURIComponent(o),f+="&token="+encodeURIComponent(i),c.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),c.send(f)}))},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CordovaPopupWindow=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0);e.CordovaPopupWindow=function(){function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._promise=new Promise((function(t,e){r._resolve=t,r._reject=e})),this.features=e.popupWindowFeatures||"location=no,toolbar=no,zoom=no",this.target=e.popupWindowTarget||"_blank",this.redirect_uri=e.startUrl,i.Log.debug("CordovaPopupWindow.ctor: redirect_uri: "+this.redirect_uri)}return t.prototype._isInAppBrowserInstalled=function(t){return["cordova-plugin-inappbrowser","cordova-plugin-inappbrowser.inappbrowser","org.apache.cordova.inappbrowser"].some((function(e){return t.hasOwnProperty(e)}))},t.prototype.navigate=function(t){if(t&&t.url){if(!window.cordova)return this._error("cordova is undefined");var e=window.cordova.require("cordova/plugin_list").metadata;if(!1===this._isInAppBrowserInstalled(e))return this._error("InAppBrowser plugin not found");this._popup=cordova.InAppBrowser.open(t.url,this.target,this.features),this._popup?(i.Log.debug("CordovaPopupWindow.navigate: popup successfully created"),this._exitCallbackEvent=this._exitCallback.bind(this),this._loadStartCallbackEvent=this._loadStartCallback.bind(this),this._popup.addEventListener("exit",this._exitCallbackEvent,!1),this._popup.addEventListener("loadstart",this._loadStartCallbackEvent,!1)):this._error("Error opening popup window")}else this._error("No url provided");return this.promise},t.prototype._loadStartCallback=function(t){0===t.url.indexOf(this.redirect_uri)&&this._success({url:t.url})},t.prototype._exitCallback=function(t){this._error(t)},t.prototype._success=function(t){this._cleanup(),i.Log.debug("CordovaPopupWindow: Successful response from cordova popup window"),this._resolve(t)},t.prototype._error=function(t){this._cleanup(),i.Log.error(t),this._reject(new Error(t))},t.prototype.close=function(){this._cleanup()},t.prototype._cleanup=function(){this._popup&&(i.Log.debug("CordovaPopupWindow: cleaning up popup"),this._popup.removeEventListener("exit",this._exitCallbackEvent,!1),this._popup.removeEventListener("loadstart",this._loadStartCallbackEvent,!1),this._popup.close()),this._popup=null},n(t,[{key:"promise",get:function(){return this._promise}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(0),i=r(10),o=r(5),s=r(6),a=r(37),u=r(38),c=r(16),f=r(2),h=r(48),l=r(49),p=r(19),g=r(20),d=r(18),v=r(1),y=r(15),m=r(50);e.default={Version:m.Version,Log:n.Log,OidcClient:i.OidcClient,OidcClientSettings:o.OidcClientSettings,WebStorageStateStore:s.WebStorageStateStore,InMemoryWebStorage:a.InMemoryWebStorage,UserManager:u.UserManager,AccessTokenEvents:c.AccessTokenEvents,MetadataService:f.MetadataService,CordovaPopupNavigator:h.CordovaPopupNavigator,CordovaIFrameNavigator:l.CordovaIFrameNavigator,CheckSessionIFrame:p.CheckSessionIFrame,TokenRevocationClient:g.TokenRevocationClient,SessionMonitor:d.SessionMonitor,Global:v.Global,User:y.User},t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ClockService=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.getEpochTime=function(){return Promise.resolve(Date.now()/1e3|0)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ResponseValidator=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i=r(0),o=r(2),s=r(25),a=r(11),u=r(12),c=r(4);function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var h=["nonce","at_hash","iat","nbf","exp","aud","iss","c_hash"];e.ResponseValidator=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.MetadataService,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.UserInfoService,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:c.JoseUtil,h=arguments.length>4&&void 0!==arguments[4]?arguments[4]:a.TokenClient;if(f(this,t),!e)throw i.Log.error("ResponseValidator.ctor: No settings passed to ResponseValidator"),new Error("settings");this._settings=e,this._metadataService=new r(this._settings),this._userInfoService=new n(this._settings),this._joseUtil=u,this._tokenClient=new h(this._settings)}return t.prototype.validateSigninResponse=function(t,e){var r=this;return i.Log.debug("ResponseValidator.validateSigninResponse"),this._processSigninParams(t,e).then((function(e){return i.Log.debug("ResponseValidator.validateSigninResponse: state processed"),r._validateTokens(t,e).then((function(e){return i.Log.debug("ResponseValidator.validateSigninResponse: tokens validated"),r._processClaims(t,e).then((function(t){return i.Log.debug("ResponseValidator.validateSigninResponse: claims processed"),t}))}))}))},t.prototype.validateSignoutResponse=function(t,e){return t.id!==e.state?(i.Log.error("ResponseValidator.validateSignoutResponse: State does not match"),Promise.reject(new Error("State does not match"))):(i.Log.debug("ResponseValidator.validateSignoutResponse: state validated"),e.state=t.data,e.error?(i.Log.warn("ResponseValidator.validateSignoutResponse: Response was error",e.error),Promise.reject(new u.ErrorResponse(e))):Promise.resolve(e))},t.prototype._processSigninParams=function(t,e){if(t.id!==e.state)return i.Log.error("ResponseValidator._processSigninParams: State does not match"),Promise.reject(new Error("State does not match"));if(!t.client_id)return i.Log.error("ResponseValidator._processSigninParams: No client_id on state"),Promise.reject(new Error("No client_id on state"));if(!t.authority)return i.Log.error("ResponseValidator._processSigninParams: No authority on state"),Promise.reject(new Error("No authority on state"));if(this._settings.authority){if(this._settings.authority&&this._settings.authority!==t.authority)return i.Log.error("ResponseValidator._processSigninParams: authority mismatch on settings vs. signin state"),Promise.reject(new Error("authority mismatch on settings vs. signin state"))}else this._settings.authority=t.authority;if(this._settings.client_id){if(this._settings.client_id&&this._settings.client_id!==t.client_id)return i.Log.error("ResponseValidator._processSigninParams: client_id mismatch on settings vs. signin state"),Promise.reject(new Error("client_id mismatch on settings vs. signin state"))}else this._settings.client_id=t.client_id;return i.Log.debug("ResponseValidator._processSigninParams: state validated"),e.state=t.data,e.error?(i.Log.warn("ResponseValidator._processSigninParams: Response was error",e.error),Promise.reject(new u.ErrorResponse(e))):t.nonce&&!e.id_token?(i.Log.error("ResponseValidator._processSigninParams: Expecting id_token in response"),Promise.reject(new Error("No id_token in response"))):!t.nonce&&e.id_token?(i.Log.error("ResponseValidator._processSigninParams: Not expecting id_token in response"),Promise.reject(new Error("Unexpected id_token in response"))):t.code_verifier&&!e.code?(i.Log.error("ResponseValidator._processSigninParams: Expecting code in response"),Promise.reject(new Error("No code in response"))):!t.code_verifier&&e.code?(i.Log.error("ResponseValidator._processSigninParams: Not expecting code in response"),Promise.reject(new Error("Unexpected code in response"))):(e.scope||(e.scope=t.scope),Promise.resolve(e))},t.prototype._processClaims=function(t,e){var r=this;if(e.isOpenIdConnect){if(i.Log.debug("ResponseValidator._processClaims: response is OIDC, processing claims"),e.profile=this._filterProtocolClaims(e.profile),!0!==t.skipUserInfo&&this._settings.loadUserInfo&&e.access_token)return i.Log.debug("ResponseValidator._processClaims: loading user info"),this._userInfoService.getClaims(e.access_token).then((function(t){return i.Log.debug("ResponseValidator._processClaims: user info claims received from user info endpoint"),t.sub!==e.profile.sub?(i.Log.error("ResponseValidator._processClaims: sub from user info endpoint does not match sub in id_token"),Promise.reject(new Error("sub from user info endpoint does not match sub in id_token"))):(e.profile=r._mergeClaims(e.profile,t),i.Log.debug("ResponseValidator._processClaims: user info claims received, updated profile:",e.profile),e)}));i.Log.debug("ResponseValidator._processClaims: not loading user info")}else i.Log.debug("ResponseValidator._processClaims: response is not OIDC, not processing claims");return Promise.resolve(e)},t.prototype._mergeClaims=function(t,e){var r=Object.assign({},t);for(var i in e){var o=e[i];Array.isArray(o)||(o=[o]);for(var s=0;s<o.length;s++){var a=o[s];r[i]?Array.isArray(r[i])?r[i].indexOf(a)<0&&r[i].push(a):r[i]!==a&&("object"===(void 0===a?"undefined":n(a))&&this._settings.mergeClaims?r[i]=this._mergeClaims(r[i],a):r[i]=[r[i],a]):r[i]=a}}return r},t.prototype._filterProtocolClaims=function(t){i.Log.debug("ResponseValidator._filterProtocolClaims, incoming claims:",t);var e=Object.assign({},t);return this._settings._filterProtocolClaims?(h.forEach((function(t){delete e[t]})),i.Log.debug("ResponseValidator._filterProtocolClaims: protocol claims filtered",e)):i.Log.debug("ResponseValidator._filterProtocolClaims: protocol claims not filtered"),e},t.prototype._validateTokens=function(t,e){return e.code?(i.Log.debug("ResponseValidator._validateTokens: Validating code"),this._processCode(t,e)):e.id_token?e.access_token?(i.Log.debug("ResponseValidator._validateTokens: Validating id_token and access_token"),this._validateIdTokenAndAccessToken(t,e)):(i.Log.debug("ResponseValidator._validateTokens: Validating id_token"),this._validateIdToken(t,e)):(i.Log.debug("ResponseValidator._validateTokens: No code to process or id_token to validate"),Promise.resolve(e))},t.prototype._processCode=function(t,e){var r=this,o={client_id:t.client_id,client_secret:t.client_secret,code:e.code,redirect_uri:t.redirect_uri,code_verifier:t.code_verifier};return t.extraTokenParams&&"object"===n(t.extraTokenParams)&&Object.assign(o,t.extraTokenParams),this._tokenClient.exchangeCode(o).then((function(n){for(var o in n)e[o]=n[o];return e.id_token?(i.Log.debug("ResponseValidator._processCode: token response successful, processing id_token"),r._validateIdTokenAttributes(t,e)):(i.Log.debug("ResponseValidator._processCode: token response successful, returning response"),e)}))},t.prototype._validateIdTokenAttributes=function(t,e){var r=this;return this._metadataService.getIssuer().then((function(n){var o=t.client_id,s=r._settings.clockSkew;return i.Log.debug("ResponseValidator._validateIdTokenAttributes: Validaing JWT attributes; using clock skew (in seconds) of: ",s),r._settings.getEpochTime().then((function(a){return r._joseUtil.validateJwtAttributes(e.id_token,n,o,s,a).then((function(r){return t.nonce&&t.nonce!==r.nonce?(i.Log.error("ResponseValidator._validateIdTokenAttributes: Invalid nonce in id_token"),Promise.reject(new Error("Invalid nonce in id_token"))):r.sub?(e.profile=r,e):(i.Log.error("ResponseValidator._validateIdTokenAttributes: No sub present in id_token"),Promise.reject(new Error("No sub present in id_token")))}))}))}))},t.prototype._validateIdTokenAndAccessToken=function(t,e){var r=this;return this._validateIdToken(t,e).then((function(t){return r._validateAccessToken(t)}))},t.prototype._getSigningKeyForJwt=function(t){var e=this;return this._metadataService.getSigningKeys().then((function(r){var n=t.header.kid;if(!r)return i.Log.error("ResponseValidator._validateIdToken: No signing keys from metadata"),Promise.reject(new Error("No signing keys from metadata"));i.Log.debug("ResponseValidator._validateIdToken: Received signing keys");var o=void 0;if(n)o=r.filter((function(t){return t.kid===n}))[0];else{if((r=e._filterByAlg(r,t.header.alg)).length>1)return i.Log.error("ResponseValidator._validateIdToken: No kid found in id_token and more than one key found in metadata"),Promise.reject(new Error("No kid found in id_token and more than one key found in metadata"));o=r[0]}return Promise.resolve(o)}))},t.prototype._getSigningKeyForJwtWithSingleRetry=function(t){var e=this;return this._getSigningKeyForJwt(t).then((function(r){return r?Promise.resolve(r):(e._metadataService.resetSigningKeys(),e._getSigningKeyForJwt(t))}))},t.prototype._validateIdToken=function(t,e){var r=this;if(!t.nonce)return i.Log.error("ResponseValidator._validateIdToken: No nonce on state"),Promise.reject(new Error("No nonce on state"));var n=this._joseUtil.parseJwt(e.id_token);return n&&n.header&&n.payload?t.nonce!==n.payload.nonce?(i.Log.error("ResponseValidator._validateIdToken: Invalid nonce in id_token"),Promise.reject(new Error("Invalid nonce in id_token"))):this._metadataService.getIssuer().then((function(o){return i.Log.debug("ResponseValidator._validateIdToken: Received issuer"),r._getSigningKeyForJwtWithSingleRetry(n).then((function(s){if(!s)return i.Log.error("ResponseValidator._validateIdToken: No key matching kid or alg found in signing keys"),Promise.reject(new Error("No key matching kid or alg found in signing keys"));var a=t.client_id,u=r._settings.clockSkew;return i.Log.debug("ResponseValidator._validateIdToken: Validaing JWT; using clock skew (in seconds) of: ",u),r._joseUtil.validateJwt(e.id_token,s,o,a,u).then((function(){return i.Log.debug("ResponseValidator._validateIdToken: JWT validation successful"),n.payload.sub?(e.profile=n.payload,e):(i.Log.error("ResponseValidator._validateIdToken: No sub present in id_token"),Promise.reject(new Error("No sub present in id_token")))}))}))})):(i.Log.error("ResponseValidator._validateIdToken: Failed to parse id_token",n),Promise.reject(new Error("Failed to parse id_token")))},t.prototype._filterByAlg=function(t,e){var r=null;if(e.startsWith("RS"))r="RSA";else if(e.startsWith("PS"))r="PS";else{if(!e.startsWith("ES"))return i.Log.debug("ResponseValidator._filterByAlg: alg not supported: ",e),[];r="EC"}return i.Log.debug("ResponseValidator._filterByAlg: Looking for keys that match kty: ",r),t=t.filter((function(t){return t.kty===r})),i.Log.debug("ResponseValidator._filterByAlg: Number of keys that match kty: ",r,t.length),t},t.prototype._validateAccessToken=function(t){if(!t.profile)return i.Log.error("ResponseValidator._validateAccessToken: No profile loaded from id_token"),Promise.reject(new Error("No profile loaded from id_token"));if(!t.profile.at_hash)return i.Log.error("ResponseValidator._validateAccessToken: No at_hash in id_token"),Promise.reject(new Error("No at_hash in id_token"));if(!t.id_token)return i.Log.error("ResponseValidator._validateAccessToken: No id_token"),Promise.reject(new Error("No id_token"));var e=this._joseUtil.parseJwt(t.id_token);if(!e||!e.header)return i.Log.error("ResponseValidator._validateAccessToken: Failed to parse id_token",e),Promise.reject(new Error("Failed to parse id_token"));var r=e.header.alg;if(!r||5!==r.length)return i.Log.error("ResponseValidator._validateAccessToken: Unsupported alg:",r),Promise.reject(new Error("Unsupported alg: "+r));var n=r.substr(2,3);if(!n)return i.Log.error("ResponseValidator._validateAccessToken: Unsupported alg:",r,n),Promise.reject(new Error("Unsupported alg: "+r));if(256!==(n=parseInt(n))&&384!==n&&512!==n)return i.Log.error("ResponseValidator._validateAccessToken: Unsupported alg:",r,n),Promise.reject(new Error("Unsupported alg: "+r));var o="sha"+n,s=this._joseUtil.hashString(t.access_token,o);if(!s)return i.Log.error("ResponseValidator._validateAccessToken: access_token hash failed:",o),Promise.reject(new Error("Failed to validate at_hash"));var a=s.substr(0,s.length/2),u=this._joseUtil.hexToBase64Url(a);return u!==t.profile.at_hash?(i.Log.error("ResponseValidator._validateAccessToken: Failed to validate at_hash",u,t.profile.at_hash),Promise.reject(new Error("Failed to validate at_hash"))):(i.Log.debug("ResponseValidator._validateAccessToken: success"),Promise.resolve(t))},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UserInfoService=void 0;var n=r(7),i=r(2),o=r(0),s=r(4);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.UserInfoService=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.JsonService,u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:i.MetadataService,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:s.JoseUtil;if(a(this,t),!e)throw o.Log.error("UserInfoService.ctor: No settings passed"),new Error("settings");this._settings=e,this._jsonService=new r(void 0,void 0,this._getClaimsFromJwt.bind(this)),this._metadataService=new u(this._settings),this._joseUtil=c}return t.prototype.getClaims=function(t){var e=this;return t?this._metadataService.getUserInfoEndpoint().then((function(r){return o.Log.debug("UserInfoService.getClaims: received userinfo url",r),e._jsonService.getJson(r,t).then((function(t){return o.Log.debug("UserInfoService.getClaims: claims received",t),t}))})):(o.Log.error("UserInfoService.getClaims: No token passed"),Promise.reject(new Error("A token is required")))},t.prototype._getClaimsFromJwt=function t(e){var r=this;try{var n=this._joseUtil.parseJwt(e.responseText);if(!n||!n.header||!n.payload)return o.Log.error("UserInfoService._getClaimsFromJwt: Failed to parse JWT",n),Promise.reject(new Error("Failed to parse id_token"));var i=n.header.kid,s=void 0;switch(this._settings.userInfoJwtIssuer){case"OP":s=this._metadataService.getIssuer();break;case"ANY":s=Promise.resolve(n.payload.iss);break;default:s=Promise.resolve(this._settings.userInfoJwtIssuer)}return s.then((function(t){return o.Log.debug("UserInfoService._getClaimsFromJwt: Received issuer:"+t),r._metadataService.getSigningKeys().then((function(s){if(!s)return o.Log.error("UserInfoService._getClaimsFromJwt: No signing keys from metadata"),Promise.reject(new Error("No signing keys from metadata"));o.Log.debug("UserInfoService._getClaimsFromJwt: Received signing keys");var a=void 0;if(i)a=s.filter((function(t){return t.kid===i}))[0];else{if((s=r._filterByAlg(s,n.header.alg)).length>1)return o.Log.error("UserInfoService._getClaimsFromJwt: No kid found in id_token and more than one key found in metadata"),Promise.reject(new Error("No kid found in id_token and more than one key found in metadata"));a=s[0]}if(!a)return o.Log.error("UserInfoService._getClaimsFromJwt: No key matching kid or alg found in signing keys"),Promise.reject(new Error("No key matching kid or alg found in signing keys"));var u=r._settings.client_id,c=r._settings.clockSkew;return o.Log.debug("UserInfoService._getClaimsFromJwt: Validaing JWT; using clock skew (in seconds) of: ",c),r._joseUtil.validateJwt(e.responseText,a,t,u,c,void 0,!0).then((function(){return o.Log.debug("UserInfoService._getClaimsFromJwt: JWT validation successful"),n.payload}))}))}))}catch(t){return o.Log.error("UserInfoService._getClaimsFromJwt: Error parsing JWT response",t.message),void reject(t)}},t.prototype._filterByAlg=function(t,e){var r=null;if(e.startsWith("RS"))r="RSA";else if(e.startsWith("PS"))r="PS";else{if(!e.startsWith("ES"))return o.Log.debug("UserInfoService._filterByAlg: alg not supported: ",e),[];r="EC"}return o.Log.debug("UserInfoService._filterByAlg: Looking for keys that match kty: ",r),t=t.filter((function(t){return t.kty===r})),o.Log.debug("UserInfoService._filterByAlg: Number of keys that match kty: ",r,t.length),t},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AllowedSigningAlgs=e.b64tohex=e.hextob64u=e.crypto=e.X509=e.KeyUtil=e.jws=void 0;var n=r(27);e.jws=n.jws,e.KeyUtil=n.KEYUTIL,e.X509=n.X509,e.crypto=n.crypto,e.hextob64u=n.hextob64u,e.b64tohex=n.b64tohex,e.AllowedSigningAlgs=["RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"]},function(t,e,r){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n={userAgent:!1},i={};
/*!
      Copyright (c) 2011, Yahoo! Inc. All rights reserved.
      Code licensed under the BSD License:
      http://developer.yahoo.com/yui/license.html
      version: 2.9.0
      */if(void 0===o)var o={};o.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var o;for(o in r)t.prototype[o]=r[o];var s=function(){},a=["toString","valueOf"];try{/MSIE/.test(n.userAgent)&&(s=function(t,e){for(o=0;o<a.length;o+=1){var r=a[o],n=e[r];"function"==typeof n&&n!=Object.prototype[r]&&(t[r]=n)}})}catch(t){}s(t.prototype,r)}}};
/*! CryptoJS v3.1.2 core-fix.js
       * code.google.com/p/crypto-js
       * (c) 2009-2013 by Jeff Mott. All rights reserved.
       * code.google.com/p/crypto-js/wiki/License
       * THIS IS FIX of 'core.js' to fix Hmac issue.
       * https://code.google.com/p/crypto-js/issues/detail?id=84
       * https://crypto-js.googlecode.com/svn-history/r667/branches/3.x/src/core.js
       */
var s,a,u,c,f,h,l,p,g,d,v,y=y||(s=Math,u=(a={}).lib={},c=u.Base=function(){function t(){}return{extend:function(e){t.prototype=this;var r=new t;return e&&r.mixIn(e),r.hasOwnProperty("init")||(r.init=function(){r.$super.init.apply(this,arguments)}),r.init.prototype=r,r.$super=this,r},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),f=u.WordArray=c.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||l).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,i=t.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var s=r[o>>>2]>>>24-o%4*8&255;e[n+o>>>2]|=s<<24-(n+o)%4*8}else for(o=0;o<i;o+=4)e[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=s.ceil(e/4)},clone:function(){var t=c.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(4294967296*s.random()|0);return new f.init(e,t)}}),h=a.enc={},l=h.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new f.init(r,e/2)}},p=h.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new f.init(r,e)}},g=h.Utf8={stringify:function(t){try{return decodeURIComponent(escape(p.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return p.parse(unescape(encodeURIComponent(t)))}},d=u.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new f.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=g.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e=this._data,r=e.words,n=e.sigBytes,i=this.blockSize,o=n/(4*i),a=(o=t?s.ceil(o):s.max((0|o)-this._minBufferSize,0))*i,u=s.min(4*a,n);if(a){for(var c=0;c<a;c+=i)this._doProcessBlock(r,c);var h=r.splice(0,a);e.sigBytes-=u}return new f.init(h,u)},clone:function(){var t=c.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),u.Hasher=d.extend({cfg:c.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new v.HMAC.init(t,r).finalize(e)}}}),v=a.algo={},a);!function(t){var e,r=(e=y).lib,n=r.Base,i=r.WordArray;(e=e.x64={}).Word=n.extend({init:function(t,e){this.high=t,this.low=e}}),e.WordArray=n.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var o=t[n];r.push(o.high),r.push(o.low)}return i.create(r,this.sigBytes)},clone:function(){for(var t=n.clone.call(this),e=t.words=this.words.slice(0),r=e.length,i=0;i<r;i++)e[i]=e[i].clone();return t}})}(),function(){var t=y,e=t.lib.WordArray;t.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp(),t=[];for(var i=0;i<r;i+=3)for(var o=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;4>s&&i+.75*s<r;s++)t.push(n.charAt(o>>>6*(3-s)&63));if(e=n.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var r=t.length,n=this._map;(i=n.charAt(64))&&-1!=(i=t.indexOf(i))&&(r=i);for(var i=[],o=0,s=0;s<r;s++)if(s%4){var a=n.indexOf(t.charAt(s-1))<<s%4*2,u=n.indexOf(t.charAt(s))>>>6-s%4*2;i[o>>>2]|=(a|u)<<24-o%4*8,o++}return e.create(i,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){for(var e=y,r=(i=e.lib).WordArray,n=i.Hasher,i=e.algo,o=[],s=[],a=function(t){return 4294967296*(t-(0|t))|0},u=2,c=0;64>c;){var f;t:{f=u;for(var h=t.sqrt(f),l=2;l<=h;l++)if(!(f%l)){f=!1;break t}f=!0}f&&(8>c&&(o[c]=a(t.pow(u,.5))),s[c]=a(t.pow(u,1/3)),c++),u++}var p=[];i=i.SHA256=n.extend({_doReset:function(){this._hash=new r.init(o.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],u=r[4],c=r[5],f=r[6],h=r[7],l=0;64>l;l++){if(16>l)p[l]=0|t[e+l];else{var g=p[l-15],d=p[l-2];p[l]=((g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3)+p[l-7]+((d<<15|d>>>17)^(d<<13|d>>>19)^d>>>10)+p[l-16]}g=h+((u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25))+(u&c^~u&f)+s[l]+p[l],d=((n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22))+(n&i^n&o^i&o),h=f,f=c,c=u,u=a+g|0,a=o,o=i,i=n,n=g+d|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+u|0,r[5]=r[5]+c|0,r[6]=r[6]+f|0,r[7]=r[7]+h|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}}),e.SHA256=n._createHelper(i),e.HmacSHA256=n._createHmacHelper(i)}(Math),function(){function t(){return n.create.apply(n,arguments)}for(var e=y,r=e.lib.Hasher,n=(o=e.x64).Word,i=o.WordArray,o=e.algo,s=[t(1116352408,3609767458),t(1899447441,602891725),t(3049323471,3964484399),t(3921009573,2173295548),t(961987163,4081628472),t(1508970993,3053834265),t(2453635748,2937671579),t(2870763221,3664609560),t(3624381080,2734883394),t(310598401,1164996542),t(607225278,1323610764),t(1426881987,3590304994),t(1925078388,4068182383),t(2162078206,991336113),t(2614888103,633803317),t(3248222580,3479774868),t(3835390401,2666613458),t(4022224774,944711139),t(264347078,2341262773),t(604807628,2007800933),t(770255983,1495990901),t(1249150122,1856431235),t(1555081692,3175218132),t(1996064986,2198950837),t(2554220882,3999719339),t(2821834349,766784016),t(2952996808,2566594879),t(3210313671,3203337956),t(3336571891,1034457026),t(3584528711,2466948901),t(113926993,3758326383),t(338241895,168717936),t(666307205,1188179964),t(773529912,1546045734),t(1294757372,1522805485),t(1396182291,2643833823),t(1695183700,2343527390),t(1986661051,1014477480),t(2177026350,1206759142),t(2456956037,344077627),t(2730485921,1290863460),t(2820302411,3158454273),t(3259730800,3505952657),t(3345764771,106217008),t(3516065817,3606008344),t(3600352804,1432725776),t(4094571909,1467031594),t(275423344,851169720),t(430227734,3100823752),t(506948616,1363258195),t(659060556,3750685593),t(883997877,3785050280),t(958139571,3318307427),t(1322822218,3812723403),t(1537002063,2003034995),t(1747873779,3602036899),t(1955562222,1575990012),t(2024104815,1125592928),t(2227730452,2716904306),t(2361852424,442776044),t(2428436474,593698344),t(2756734187,3733110249),t(3204031479,2999351573),t(3329325298,3815920427),t(3391569614,3928383900),t(3515267271,566280711),t(3940187606,3454069534),t(4118630271,4000239992),t(116418474,1914138554),t(174292421,2731055270),t(289380356,3203993006),t(460393269,320620315),t(685471733,587496836),t(852142971,1086792851),t(1017036298,365543100),t(1126000580,2618297676),t(1288033470,3409855158),t(1501505948,4234509866),t(1607167915,987167468),t(1816402316,1246189591)],a=[],u=0;80>u;u++)a[u]=t();o=o.SHA512=r.extend({_doReset:function(){this._hash=new i.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=(h=this._hash.words)[0],n=h[1],i=h[2],o=h[3],u=h[4],c=h[5],f=h[6],h=h[7],l=r.high,p=r.low,g=n.high,d=n.low,v=i.high,y=i.low,m=o.high,b=o.low,S=u.high,w=u.low,_=c.high,E=c.low,x=f.high,F=f.low,A=h.high,T=h.low,k=l,P=p,R=g,I=d,C=v,O=y,D=m,N=b,L=S,j=w,M=_,B=E,U=x,H=F,V=A,q=T,K=0;80>K;K++){var W=a[K];if(16>K)var J=W.high=0|t[e+2*K],z=W.low=0|t[e+2*K+1];else{J=((z=(J=a[K-15]).high)>>>1|(Y=J.low)<<31)^(z>>>8|Y<<24)^z>>>7;var Y=(Y>>>1|z<<31)^(Y>>>8|z<<24)^(Y>>>7|z<<25),G=((z=(G=a[K-2]).high)>>>19|($=G.low)<<13)^(z<<3|$>>>29)^z>>>6,$=($>>>19|z<<13)^($<<3|z>>>29)^($>>>6|z<<26),X=(z=a[K-7]).high,Z=(Q=a[K-16]).high,Q=Q.low;J=(J=(J=J+X+((z=Y+z.low)>>>0<Y>>>0?1:0))+G+((z+=$)>>>0<$>>>0?1:0))+Z+((z+=Q)>>>0<Q>>>0?1:0),W.high=J,W.low=z}X=L&M^~L&U,Q=j&B^~j&H,W=k&R^k&C^R&C;var tt=P&I^P&O^I&O,et=(Y=(k>>>28|P<<4)^(k<<30|P>>>2)^(k<<25|P>>>7),G=(P>>>28|k<<4)^(P<<30|k>>>2)^(P<<25|k>>>7),($=s[K]).high),rt=$.low;Z=V+((L>>>14|j<<18)^(L>>>18|j<<14)^(L<<23|j>>>9))+(($=q+((j>>>14|L<<18)^(j>>>18|L<<14)^(j<<23|L>>>9)))>>>0<q>>>0?1:0),V=U,q=H,U=M,H=B,M=L,B=j,L=D+(Z=(Z=(Z=Z+X+(($+=Q)>>>0<Q>>>0?1:0))+et+(($+=rt)>>>0<rt>>>0?1:0))+J+(($+=z)>>>0<z>>>0?1:0))+((j=N+$|0)>>>0<N>>>0?1:0)|0,D=C,N=O,C=R,O=I,R=k,I=P,k=Z+(W=Y+W+((z=G+tt)>>>0<G>>>0?1:0))+((P=$+z|0)>>>0<$>>>0?1:0)|0}p=r.low=p+P,r.high=l+k+(p>>>0<P>>>0?1:0),d=n.low=d+I,n.high=g+R+(d>>>0<I>>>0?1:0),y=i.low=y+O,i.high=v+C+(y>>>0<O>>>0?1:0),b=o.low=b+N,o.high=m+D+(b>>>0<N>>>0?1:0),w=u.low=w+j,u.high=S+L+(w>>>0<j>>>0?1:0),E=c.low=E+B,c.high=_+M+(E>>>0<B>>>0?1:0),F=f.low=F+H,f.high=x+U+(F>>>0<H>>>0?1:0),T=h.low=T+q,h.high=A+V+(T>>>0<q>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32}),e.SHA512=r._createHelper(o),e.HmacSHA512=r._createHmacHelper(o)}(),function(){var t=y,e=(i=t.x64).Word,r=i.WordArray,n=(i=t.algo).SHA512,i=i.SHA384=n.extend({_doReset:function(){this._hash=new r.init([new e.init(3418070365,3238371032),new e.init(1654270250,914150663),new e.init(2438529370,812702999),new e.init(355462360,4144912697),new e.init(1731405415,4290775857),new e.init(2394180231,1750603025),new e.init(3675008525,1694076839),new e.init(1203062813,3204075428)])},_doFinalize:function(){var t=n._doFinalize.call(this);return t.sigBytes-=16,t}});t.SHA384=n._createHelper(i),t.HmacSHA384=n._createHmacHelper(i)}();
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */
var m,b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function S(t){var e,r,n="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),n+=b.charAt(r>>6)+b.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),n+=b.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),n+=b.charAt(r>>2)+b.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function w(t){var e,r,n,i="",o=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e)(n=b.indexOf(t.charAt(e)))<0||(0==o?(i+=k(n>>2),r=3&n,o=1):1==o?(i+=k(r<<2|n>>4),r=15&n,o=2):2==o?(i+=k(r),i+=k(n>>2),r=3&n,o=3):(i+=k(r<<2|n>>4),i+=k(15&n),o=0));return 1==o&&(i+=k(r<<2)),i}function _(t){var e,r=w(t),n=new Array;for(e=0;2*e<r.length;++e)n[e]=parseInt(r.substring(2*e,2*e+2),16);return n}function E(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function x(){return new E(null)}"Microsoft Internet Explorer"==n.appName?(E.prototype.am=function(t,e,r,n,i,o){for(var s=32767&e,a=e>>15;--o>=0;){var u=32767&this[t],c=this[t++]>>15,f=a*u+c*s;i=((u=s*u+((32767&f)<<15)+r[n]+(**********&i))>>>30)+(f>>>15)+a*c+(i>>>30),r[n++]=**********&u}return i},m=30):"Netscape"!=n.appName?(E.prototype.am=function(t,e,r,n,i,o){for(;--o>=0;){var s=e*this[t++]+r[n]+i;i=Math.floor(s/67108864),r[n++]=67108863&s}return i},m=26):(E.prototype.am=function(t,e,r,n,i,o){for(var s=16383&e,a=e>>14;--o>=0;){var u=16383&this[t],c=this[t++]>>14,f=a*u+c*s;i=((u=s*u+((16383&f)<<14)+r[n]+i)>>28)+(f>>14)+a*c,r[n++]=268435455&u}return i},m=28),E.prototype.DB=m,E.prototype.DM=(1<<m)-1,E.prototype.DV=1<<m,E.prototype.FV=Math.pow(2,52),E.prototype.F1=52-m,E.prototype.F2=2*m-52;var F,A,T=new Array;for(F="0".charCodeAt(0),A=0;A<=9;++A)T[F++]=A;for(F="a".charCodeAt(0),A=10;A<36;++A)T[F++]=A;for(F="A".charCodeAt(0),A=10;A<36;++A)T[F++]=A;function k(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function P(t,e){var r=T[t.charCodeAt(e)];return null==r?-1:r}function R(t){var e=x();return e.fromInt(t),e}function I(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function C(t){this.m=t}function O(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function D(t,e){return t&e}function N(t,e){return t|e}function L(t,e){return t^e}function j(t,e){return t&~e}function M(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function B(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function U(){}function H(t){return t}function V(t){this.r2=x(),this.q3=x(),E.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}C.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},C.prototype.revert=function(t){return t},C.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},C.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},C.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},O.prototype.convert=function(t){var e=x();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(E.ZERO)>0&&this.m.subTo(e,e),e},O.prototype.revert=function(t){var e=x();return t.copyTo(e),this.reduce(e),e},O.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},O.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},O.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},E.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},E.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},E.prototype.fromString=function(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,i=!1,o=0;--n>=0;){var s=8==r?255&t[n]:P(t,n);s<0?"-"==t.charAt(n)&&(i=!0):(i=!1,0==o?this[this.t++]=s:o+r>this.DB?(this[this.t-1]|=(s&(1<<this.DB-o)-1)<<o,this[this.t++]=s>>this.DB-o):this[this.t-1]|=s<<o,(o+=r)>=this.DB&&(o-=this.DB))}8==r&&0!=(128&t[0])&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),i&&E.ZERO.subTo(this,this)},E.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},E.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},E.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},E.prototype.lShiftTo=function(t,e){var r,n=t%this.DB,i=this.DB-n,o=(1<<i)-1,s=Math.floor(t/this.DB),a=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e[r+s+1]=this[r]>>i|a,a=(this[r]&o)<<n;for(r=s-1;r>=0;--r)e[r]=0;e[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()},E.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&o)<<i,e[s-r]=this[s]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<i),e.t=this.t-r,e.clamp()}},E.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},E.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),i=r.t;for(e.t=i+n.t;--i>=0;)e[i]=0;for(i=0;i<n.t;++i)e[i+r.t]=r.am(0,n[i],e,i,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&E.ZERO.subTo(e,e)},E.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},E.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var i=this.abs();if(i.t<n.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=x());var o=x(),s=this.s,a=t.s,u=this.DB-I(n[n.t-1]);u>0?(n.lShiftTo(u,o),i.lShiftTo(u,r)):(n.copyTo(o),i.copyTo(r));var c=o.t,f=o[c-1];if(0!=f){var h=f*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),l=this.FV/h,p=(1<<this.F1)/h,g=1<<this.F2,d=r.t,v=d-c,y=null==e?x():e;for(o.dlShiftTo(v,y),r.compareTo(y)>=0&&(r[r.t++]=1,r.subTo(y,r)),E.ONE.dlShiftTo(c,y),y.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--v>=0;){var m=r[--d]==f?this.DM:Math.floor(r[d]*l+(r[d-1]+g)*p);if((r[d]+=o.am(0,m,r,v,0,c))<m)for(o.dlShiftTo(v,y),r.subTo(y,r);r[d]<--m;)r.subTo(y,r)}null!=e&&(r.drShiftTo(c,e),s!=a&&E.ZERO.subTo(e,e)),r.t=c,r.clamp(),u>0&&r.rShiftTo(u,r),s<0&&E.ZERO.subTo(r,r)}}},E.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},E.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},E.prototype.exp=function(t,e){if(t>4294967295||t<1)return E.ONE;var r=x(),n=x(),i=e.convert(this),o=I(t)-1;for(i.copyTo(r);--o>=0;)if(e.sqrTo(r,n),(t&1<<o)>0)e.mulTo(n,i,r);else{var s=r;r=n,n=s}return e.revert(r)},E.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(r=this[s]>>a)>0&&(i=!0,o=k(r));s>=0;)a<e?(r=(this[s]&(1<<a)-1)<<e-a,r|=this[--s]>>(a+=this.DB-e)):(r=this[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),r>0&&(i=!0),i&&(o+=k(r));return i?o:"0"},E.prototype.negate=function(){var t=x();return E.ZERO.subTo(this,t),t},E.prototype.abs=function(){return this.s<0?this.negate():this},E.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},E.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+I(this[this.t-1]^this.s&this.DM)},E.prototype.mod=function(t){var e=x();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(E.ZERO)>0&&t.subTo(e,e),e},E.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new C(e):new O(e),this.exp(t,r)},E.ZERO=R(0),E.ONE=R(1),U.prototype.convert=H,U.prototype.revert=H,U.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},U.prototype.sqrTo=function(t,e){t.squareTo(e)},V.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=x();return t.copyTo(e),this.reduce(e),e},V.prototype.revert=function(t){return t},V.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},V.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},V.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var q,K,W,J=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],z=(1<<26)/J[J.length-1];
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */function Y(){this.i=0,this.j=0,this.S=new Array}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */
function G(){!function(t){K[W++]^=255&t,K[W++]^=t>>8&255,K[W++]^=t>>16&255,K[W++]^=t>>24&255,W>=256&&(W-=256)}((new Date).getTime())}if(E.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},E.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=R(r),i=x(),o=x(),s="";for(this.divRemTo(n,i,o);i.signum()>0;)s=(r+o.intValue()).toString(t).substr(1)+s,i.divRemTo(n,i,o);return o.intValue().toString(t)+s},E.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),i=!1,o=0,s=0,a=0;a<t.length;++a){var u=P(t,a);u<0?"-"==t.charAt(a)&&0==this.signum()&&(i=!0):(s=e*s+u,++o>=r&&(this.dMultiply(n),this.dAddOffset(s,0),o=0,s=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(s,0)),i&&E.ZERO.subTo(this,this)},E.prototype.fromNumber=function(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(E.ONE.shiftLeft(t-1),N,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(E.ONE.shiftLeft(t-1),this);else{var n=new Array,i=7&t;n.length=1+(t>>3),e.nextBytes(n),i>0?n[0]&=(1<<i)-1:n[0]=0,this.fromString(n,256)}},E.prototype.bitwiseTo=function(t,e,r){var n,i,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},E.prototype.changeBit=function(t,e){var r=E.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},E.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},E.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},E.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},E.prototype.multiplyLowerTo=function(t,e,r){var n,i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},E.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},E.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},E.prototype.millerRabin=function(t){var e=this.subtract(E.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>J.length&&(t=J.length);for(var i=x(),o=0;o<t;++o){i.fromInt(J[Math.floor(Math.random()*J.length)]);var s=i.modPow(n,this);if(0!=s.compareTo(E.ONE)&&0!=s.compareTo(e)){for(var a=1;a++<r&&0!=s.compareTo(e);)if(0==(s=s.modPowInt(2,this)).compareTo(E.ONE))return!1;if(0!=s.compareTo(e))return!1}}return!0},E.prototype.clone=
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */
function(){var t=x();return this.copyTo(t),t},E.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},E.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},E.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},E.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},E.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},E.prototype.equals=function(t){return 0==this.compareTo(t)},E.prototype.min=function(t){return this.compareTo(t)<0?this:t},E.prototype.max=function(t){return this.compareTo(t)>0?this:t},E.prototype.and=function(t){var e=x();return this.bitwiseTo(t,D,e),e},E.prototype.or=function(t){var e=x();return this.bitwiseTo(t,N,e),e},E.prototype.xor=function(t){var e=x();return this.bitwiseTo(t,L,e),e},E.prototype.andNot=function(t){var e=x();return this.bitwiseTo(t,j,e),e},E.prototype.not=function(){for(var t=x(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},E.prototype.shiftLeft=function(t){var e=x();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},E.prototype.shiftRight=function(t){var e=x();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},E.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+M(this[t]);return this.s<0?this.t*this.DB:-1},E.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=B(this[r]^e);return t},E.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},E.prototype.setBit=function(t){return this.changeBit(t,N)},E.prototype.clearBit=function(t){return this.changeBit(t,j)},E.prototype.flipBit=function(t){return this.changeBit(t,L)},E.prototype.add=function(t){var e=x();return this.addTo(t,e),e},E.prototype.subtract=function(t){var e=x();return this.subTo(t,e),e},E.prototype.multiply=function(t){var e=x();return this.multiplyTo(t,e),e},E.prototype.divide=function(t){var e=x();return this.divRemTo(t,e,null),e},E.prototype.remainder=function(t){var e=x();return this.divRemTo(t,null,e),e},E.prototype.divideAndRemainder=function(t){var e=x(),r=x();return this.divRemTo(t,e,r),new Array(e,r)},E.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),o=R(1);if(i<=0)return o;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new C(e):e.isEven()?new V(e):new O(e);var s=new Array,a=3,u=r-1,c=(1<<r)-1;if(s[1]=n.convert(this),r>1){var f=x();for(n.sqrTo(s[1],f);a<=c;)s[a]=x(),n.mulTo(f,s[a-2],s[a]),a+=2}var h,l,p=t.t-1,g=!0,d=x();for(i=I(t[p])-1;p>=0;){for(i>=u?h=t[p]>>i-u&c:(h=(t[p]&(1<<i+1)-1)<<u-i,p>0&&(h|=t[p-1]>>this.DB+i-u)),a=r;0==(1&h);)h>>=1,--a;if((i-=a)<0&&(i+=this.DB,--p),g)s[h].copyTo(o),g=!1;else{for(;a>1;)n.sqrTo(o,d),n.sqrTo(d,o),a-=2;a>0?n.sqrTo(o,d):(l=o,o=d,d=l),n.mulTo(d,s[h],o)}for(;p>=0&&0==(t[p]&1<<i);)n.sqrTo(o,d),l=o,o=d,d=l,--i<0&&(i=this.DB-1,--p)}return n.revert(o)},E.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return E.ZERO;for(var r=t.clone(),n=this.clone(),i=R(1),o=R(0),s=R(0),a=R(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),e?(i.isEven()&&o.isEven()||(i.addTo(this,i),o.subTo(t,o)),i.rShiftTo(1,i)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),e?(s.isEven()&&a.isEven()||(s.addTo(this,s),a.subTo(t,a)),s.rShiftTo(1,s)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);r.compareTo(n)>=0?(r.subTo(n,r),e&&i.subTo(s,i),o.subTo(a,o)):(n.subTo(r,n),e&&s.subTo(i,s),a.subTo(o,a))}return 0!=n.compareTo(E.ONE)?E.ZERO:a.compareTo(t)>=0?a.subtract(t):a.signum()<0?(a.addTo(t,a),a.signum()<0?a.add(t):a):a},E.prototype.pow=function(t){return this.exp(t,new U)},E.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(i<o&&(o=i),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},E.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=J[J.length-1]){for(e=0;e<J.length;++e)if(r[0]==J[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<J.length;){for(var n=J[e],i=e+1;i<J.length&&n<z;)n*=J[i++];for(n=r.modInt(n);e<i;)if(n%J[e++]==0)return!1}return r.millerRabin(t)},E.prototype.square=function(){var t=x();return this.squareTo(t),t},Y.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},Y.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},null==K){var $;if(K=new Array,W=0,void 0!==i&&(void 0!==i.crypto||void 0!==i.msCrypto)){var X=i.crypto||i.msCrypto;if(X.getRandomValues){var Z=new Uint8Array(32);for(X.getRandomValues(Z),$=0;$<32;++$)K[W++]=Z[$]}else if("Netscape"==n.appName&&n.appVersion<"5"){var Q=i.crypto.random(32);for($=0;$<Q.length;++$)K[W++]=255&Q.charCodeAt($)}}for(;W<256;)$=Math.floor(65536*Math.random()),K[W++]=$>>>8,K[W++]=255&$;W=0,G()}function tt(){if(null==q){for(G(),(q=new Y).init(K),W=0;W<K.length;++W)K[W]=0;W=0}return q.next()}function et(){}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */function rt(t,e){return new E(t,e)}function nt(t,e,r){for(var n="",i=0;n.length<e;)n+=r(String.fromCharCode.apply(String,t.concat([(4278190080&i)>>24,(16711680&i)>>16,(65280&i)>>8,255&i]))),i+=1;return n}function it(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
       */}function ot(t,e){this.x=e,this.q=t}function st(t,e,r,n){this.curve=t,this.x=e,this.y=r,this.z=null==n?E.ONE:n,this.zinv=null}function at(t,e,r){this.q=t,this.a=this.fromBigInteger(e),this.b=this.fromBigInteger(r),this.infinity=new st(this,null,null)}et.prototype.nextBytes=function(t){var e;for(e=0;e<t.length;++e)t[e]=tt()},it.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},it.prototype.setPublic=function(t,e){if(this.isPublic=!0,this.isPrivate=!1,"string"!=typeof t)this.n=t,this.e=e;else{if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA public key";this.n=rt(t,16),this.e=parseInt(e,16)}},it.prototype.encrypt=function(t){var e=function(t,e){if(e<t.length+11)throw"Message too long for RSA";for(var r=new Array,n=t.length-1;n>=0&&e>0;){var i=t.charCodeAt(n--);i<128?r[--e]=i:i>127&&i<2048?(r[--e]=63&i|128,r[--e]=i>>6|192):(r[--e]=63&i|128,r[--e]=i>>6&63|128,r[--e]=i>>12|224)}r[--e]=0;for(var o=new et,s=new Array;e>2;){for(s[0]=0;0==s[0];)o.nextBytes(s);r[--e]=s[0]}return r[--e]=2,r[--e]=0,new E(r)}(t,this.n.bitLength()+7>>3);if(null==e)return null;var r=this.doPublic(e);if(null==r)return null;var n=r.toString(16);return 0==(1&n.length)?n:"0"+n},it.prototype.encryptOAEP=function(t,e,r){var n=function(t,e,r,n){var i=ct.crypto.MessageDigest,o=ct.crypto.Util,s=null;if(r||(r="sha1"),"string"==typeof r&&(s=i.getCanonicalAlgName(r),n=i.getHashLength(s),r=function(t){return Et(o.hashHex(xt(t),s))}),t.length+2*n+2>e)throw"Message too long for RSA";var a,u="";for(a=0;a<e-t.length-2*n-2;a+=1)u+="\0";var c=r("")+u+""+t,f=new Array(n);(new et).nextBytes(f);var h=nt(f,c.length,r),l=[];for(a=0;a<c.length;a+=1)l[a]=c.charCodeAt(a)^h.charCodeAt(a);var p=nt(l,f.length,r),g=[0];for(a=0;a<f.length;a+=1)g[a+1]=f[a]^p.charCodeAt(a);return new E(g.concat(l))}(t,this.n.bitLength()+7>>3,e,r);if(null==n)return null;var i=this.doPublic(n);if(null==i)return null;var o=i.toString(16);return 0==(1&o.length)?o:"0"+o},it.prototype.type="RSA",ot.prototype.equals=function(t){return t==this||this.q.equals(t.q)&&this.x.equals(t.x)},ot.prototype.toBigInteger=function(){return this.x},ot.prototype.negate=function(){return new ot(this.q,this.x.negate().mod(this.q))},ot.prototype.add=function(t){return new ot(this.q,this.x.add(t.toBigInteger()).mod(this.q))},ot.prototype.subtract=function(t){return new ot(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))},ot.prototype.multiply=function(t){return new ot(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))},ot.prototype.square=function(){return new ot(this.q,this.x.square().mod(this.q))},ot.prototype.divide=function(t){return new ot(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))},st.prototype.getX=function(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))},st.prototype.getY=function(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))},st.prototype.equals=function(t){return t==this||(this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():!!t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(E.ZERO)&&t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(E.ZERO))},st.prototype.isInfinity=function(){return null==this.x&&null==this.y||this.z.equals(E.ZERO)&&!this.y.toBigInteger().equals(E.ZERO)},st.prototype.negate=function(){return new st(this.curve,this.x,this.y.negate(),this.z)},st.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q),r=t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q);if(E.ZERO.equals(r))return E.ZERO.equals(e)?this.twice():this.curve.getInfinity();var n=new E("3"),i=this.x.toBigInteger(),o=this.y.toBigInteger(),s=(t.x.toBigInteger(),t.y.toBigInteger(),r.square()),a=s.multiply(r),u=i.multiply(s),c=e.square().multiply(this.z),f=c.subtract(u.shiftLeft(1)).multiply(t.z).subtract(a).multiply(r).mod(this.curve.q),h=u.multiply(n).multiply(e).subtract(o.multiply(a)).subtract(c.multiply(e)).multiply(t.z).add(e.multiply(a)).mod(this.curve.q),l=a.multiply(this.z).multiply(t.z).mod(this.curve.q);return new st(this.curve,this.curve.fromBigInteger(f),this.curve.fromBigInteger(h),l)},st.prototype.twice=function(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=new E("3"),e=this.x.toBigInteger(),r=this.y.toBigInteger(),n=r.multiply(this.z),i=n.multiply(r).mod(this.curve.q),o=this.curve.a.toBigInteger(),s=e.square().multiply(t);E.ZERO.equals(o)||(s=s.add(this.z.square().multiply(o)));var a=(s=s.mod(this.curve.q)).square().subtract(e.shiftLeft(3).multiply(i)).shiftLeft(1).multiply(n).mod(this.curve.q),u=s.multiply(t).multiply(e).subtract(i.shiftLeft(1)).shiftLeft(2).multiply(i).subtract(s.square().multiply(s)).mod(this.curve.q),c=n.square().multiply(n).shiftLeft(3).mod(this.curve.q);return new st(this.curve,this.curve.fromBigInteger(a),this.curve.fromBigInteger(u),c)},st.prototype.multiply=function(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,n=r.multiply(new E("3")),i=this.negate(),o=this,s=this.curve.q.subtract(t),a=s.multiply(new E("3")),u=new st(this.curve,this.x,this.y),c=u.negate();for(e=n.bitLength()-2;e>0;--e){o=o.twice();var f=n.testBit(e);f!=r.testBit(e)&&(o=o.add(f?this:i))}for(e=a.bitLength()-2;e>0;--e){u=u.twice();var h=a.testBit(e);h!=s.testBit(e)&&(u=u.add(h?u:c))}return o},st.prototype.multiplyTwo=function(t,e,r){var n;n=t.bitLength()>r.bitLength()?t.bitLength()-1:r.bitLength()-1;for(var i=this.curve.getInfinity(),o=this.add(e);n>=0;)i=i.twice(),t.testBit(n)?i=r.testBit(n)?i.add(o):i.add(this):r.testBit(n)&&(i=i.add(e)),--n;return i},at.prototype.getQ=function(){return this.q},at.prototype.getA=function(){return this.a},at.prototype.getB=function(){return this.b},at.prototype.equals=function(t){return t==this||this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)},at.prototype.getInfinity=function(){return this.infinity},at.prototype.fromBigInteger=function(t){return new ot(this.q,t)},at.prototype.decodePointHex=function(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:return null;case 4:case 6:case 7:var e=(t.length-2)/2,r=t.substr(2,e),n=t.substr(e+2,e);return new st(this,this.fromBigInteger(new E(r,16)),this.fromBigInteger(new E(n,16)));default:return null}},
/*! (c) Stefan Thomas | https://github.com/bitcoinjs/bitcoinjs-lib
       */
ot.prototype.getByteLength=function(){return Math.floor((this.toBigInteger().bitLength()+7)/8)},st.prototype.getEncoded=function(t){var e=function(t,e){var r=t.toByteArrayUnsigned();if(e<r.length)r=r.slice(r.length-e);else for(;e>r.length;)r.unshift(0);return r},r=this.getX().toBigInteger(),n=this.getY().toBigInteger(),i=e(r,32);return t?n.isEven()?i.unshift(2):i.unshift(3):(i.unshift(4),i=i.concat(e(n,32))),i},st.decodeFrom=function(t,e){e[0];var r=e.length-1,n=e.slice(1,1+r/2),i=e.slice(1+r/2,1+r);n.unshift(0),i.unshift(0);var o=new E(n),s=new E(i);return new st(t,t.fromBigInteger(o),t.fromBigInteger(s))},st.decodeFromHex=function(t,e){e.substr(0,2);var r=e.length-2,n=e.substr(2,r/2),i=e.substr(2+r/2,r/2),o=new E(n,16),s=new E(i,16);return new st(t,t.fromBigInteger(o),t.fromBigInteger(s))},st.prototype.add2D=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;if(this.x.equals(t.x))return this.y.equals(t.y)?this.twice():this.curve.getInfinity();var e=t.x.subtract(this.x),r=t.y.subtract(this.y).divide(e),n=r.square().subtract(this.x).subtract(t.x),i=r.multiply(this.x.subtract(n)).subtract(this.y);return new st(this.curve,n,i)},st.prototype.twice2D=function(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=this.curve.fromBigInteger(E.valueOf(2)),e=this.curve.fromBigInteger(E.valueOf(3)),r=this.x.square().multiply(e).add(this.curve.a).divide(this.y.multiply(t)),n=r.square().subtract(this.x.multiply(t)),i=r.multiply(this.x.subtract(n)).subtract(this.y);return new st(this.curve,n,i)},st.prototype.multiply2D=function(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,n=r.multiply(new E("3")),i=this.negate(),o=this;for(e=n.bitLength()-2;e>0;--e){o=o.twice();var s=n.testBit(e);s!=r.testBit(e)&&(o=o.add2D(s?this:i))}return o},st.prototype.isOnCurve=function(){var t=this.getX().toBigInteger(),e=this.getY().toBigInteger(),r=this.curve.getA().toBigInteger(),n=this.curve.getB().toBigInteger(),i=this.curve.getQ(),o=e.multiply(e).mod(i),s=t.multiply(t).multiply(t).add(r.multiply(t)).add(n).mod(i);return o.equals(s)},st.prototype.toString=function(){return"("+this.getX().toBigInteger().toString()+","+this.getY().toBigInteger().toString()+")"},st.prototype.validate=function(){var t=this.curve.getQ();if(this.isInfinity())throw new Error("Point is at infinity.");var e=this.getX().toBigInteger(),r=this.getY().toBigInteger();if(e.compareTo(E.ONE)<0||e.compareTo(t.subtract(E.ONE))>0)throw new Error("x coordinate out of bounds");if(r.compareTo(E.ONE)<0||r.compareTo(t.subtract(E.ONE))>0)throw new Error("y coordinate out of bounds");if(!this.isOnCurve())throw new Error("Point is not on the curve.");if(this.multiply(t).isInfinity())throw new Error("Point is not a scalar multiple of G.");return!0};
/*! Mike Samuel (c) 2009 | code.google.com/p/json-sans-eval
       */
var ut=function(){var t=new RegExp('(?:false|true|null|[\\{\\}\\[\\]]|(?:-?\\b(?:0|[1-9][0-9]*)(?:\\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\\b)|(?:"(?:[^\\0-\\x08\\x0a-\\x1f"\\\\]|\\\\(?:["/\\\\bfnrt]|u[0-9A-Fa-f]{4}))*"))',"g"),e=new RegExp("\\\\(?:([^u])|u(.{4}))","g"),n={'"':'"',"/":"/","\\":"\\",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"};function i(t,e,r){return e?n[e]:String.fromCharCode(parseInt(r,16))}var o=new String(""),s=Object.hasOwnProperty;return function(n,a){var u,c,f=n.match(t),h=f[0],l=!1;"{"===h?u={}:"["===h?u=[]:(u=[],l=!0);for(var p=[u],g=1-l,d=f.length;g<d;++g){var v;switch((h=f[g]).charCodeAt(0)){default:(v=p[0])[c||v.length]=+h,c=void 0;break;case 34:if(-1!==(h=h.substring(1,h.length-1)).indexOf("\\")&&(h=h.replace(e,i)),v=p[0],!c){if(!(v instanceof Array)){c=h||o;break}c=v.length}v[c]=h,c=void 0;break;case 91:v=p[0],p.unshift(v[c||v.length]=[]),c=void 0;break;case 93:p.shift();break;case 102:(v=p[0])[c||v.length]=!1,c=void 0;break;case 110:(v=p[0])[c||v.length]=null,c=void 0;break;case 116:(v=p[0])[c||v.length]=!0,c=void 0;break;case 123:v=p[0],p.unshift(v[c||v.length]={}),c=void 0;break;case 125:p.shift()}}if(l){if(1!==p.length)throw new Error;u=u[0]}else if(p.length)throw new Error;return a&&(u=function t(e,n){var i=e[n];if(i&&"object"===(void 0===i?"undefined":r(i))){var o=null;for(var u in i)if(s.call(i,u)&&i!==e){var c=t(i,u);void 0!==c?i[u]=c:(o||(o=[]),o.push(u))}if(o)for(var f=o.length;--f>=0;)delete i[o[f]]}return a.call(e,n,i)}({"":u},"")),u}}();void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.asn1&&ct.asn1||(ct.asn1={}),ct.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var n="",i=0;i<r;i++)n+="f";e=new E(n,16).xor(t).add(E.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return kt(t,e)},this.newObject=function(t){var e=ct.asn1,r=e.ASN1Object,n=e.DERBoolean,i=e.DERInteger,o=e.DERBitString,s=e.DEROctetString,a=e.DERNull,u=e.DERObjectIdentifier,c=e.DEREnumerated,f=e.DERUTF8String,h=e.DERNumericString,l=e.DERPrintableString,p=e.DERTeletexString,g=e.DERIA5String,d=e.DERUTCTime,v=e.DERGeneralizedTime,y=e.DERVisibleString,m=e.DERBMPString,b=e.DERSequence,S=e.DERSet,w=e.DERTaggedObject,_=e.ASN1Util.newObject;if(t instanceof e.ASN1Object)return t;var E=Object.keys(t);if(1!=E.length)throw new Error("key of param shall be only one.");var x=E[0];if(-1==":asn1:bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:visstr:bmpstr:seq:set:tag:".indexOf(":"+x+":"))throw new Error("undefined key: "+x);if("bool"==x)return new n(t[x]);if("int"==x)return new i(t[x]);if("bitstr"==x)return new o(t[x]);if("octstr"==x)return new s(t[x]);if("null"==x)return new a(t[x]);if("oid"==x)return new u(t[x]);if("enum"==x)return new c(t[x]);if("utf8str"==x)return new f(t[x]);if("numstr"==x)return new h(t[x]);if("prnstr"==x)return new l(t[x]);if("telstr"==x)return new p(t[x]);if("ia5str"==x)return new g(t[x]);if("utctime"==x)return new d(t[x]);if("gentime"==x)return new v(t[x]);if("visstr"==x)return new y(t[x]);if("bmpstr"==x)return new m(t[x]);if("asn1"==x)return new r(t[x]);if("seq"==x){for(var F=t[x],A=[],T=0;T<F.length;T++){var k=_(F[T]);A.push(k)}return new b({array:A})}if("set"==x){for(F=t[x],A=[],T=0;T<F.length;T++)k=_(F[T]),A.push(k);return new S({array:A})}if("tag"==x){var P=t[x];if("[object Array]"===Object.prototype.toString.call(P)&&3==P.length){var R=_(P[2]);return new w({tag:P[0],explicit:P[1],obj:R})}return new w(P)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},ct.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),n=(e=Math.floor(r/40)+"."+r%40,""),i=2;i<t.length;i+=2){var o=("00000000"+parseInt(t.substr(i,2),16).toString(2)).slice(-8);n+=o.substr(1,7),"0"==o.substr(0,1)&&(e=e+"."+new E(n,2).toString(10),n="")}return e},ct.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new E(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var o="",s=0;s<i;s++)o+="0";for(n=o+n,s=0;s<n.length-1;s+=7){var a=n.substr(s,7);s!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),o=40*parseInt(i[0])+parseInt(i[1]);n+=e(o),i.splice(0,2);for(var s=0;s<i.length;s++)n+=r(i[s]);return n},ct.asn1.ASN1Object=function(t){this.params=null,this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw new Error("this.hV is null or undefined");if(this.hV.length%2==1)throw new Error("value hex must be even length: n="+"".length+",v="+this.hV);var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""},this.setByParam=function(t){this.params=t},null!=t&&null!=t.tlv&&(this.hTLV=t.tlv,this.isModified=!1)},ct.asn1.DERAbstractString=function(t){ct.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=wt(this.s).toLowerCase()},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},o.lang.extend(ct.asn1.DERAbstractString,ct.asn1.ASN1Object),ct.asn1.DERAbstractTime=function(t){ct.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){var e=t.getTime()+6e4*t.getTimezoneOffset();return new Date(e)},this.formatDate=function(t,e,r){var n=this.zeroPadding,i=this.localDateToUTC(t),o=String(i.getFullYear());"utc"==e&&(o=o.substr(2,2));var s=o+n(String(i.getMonth()+1),2)+n(String(i.getDate()),2)+n(String(i.getHours()),2)+n(String(i.getMinutes()),2)+n(String(i.getSeconds()),2);if(!0===r){var a=i.getMilliseconds();if(0!=a){var u=n(String(a),3);s=s+"."+(u=u.replace(/[0]+$/,""))}}return s+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=vt(t)},this.setByDateValue=function(t,e,r,n,i,o){var s=new Date(Date.UTC(t,e-1,r,n,i,o,0));this.setByDate(s)},this.getFreshValueHex=function(){return this.hV}},o.lang.extend(ct.asn1.DERAbstractTime,ct.asn1.ASN1Object),ct.asn1.DERAbstractStructured=function(t){ct.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},o.lang.extend(ct.asn1.DERAbstractStructured,ct.asn1.ASN1Object),ct.asn1.DERBoolean=function(t){ct.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV=0==t?"010100":"0101ff"},o.lang.extend(ct.asn1.DERBoolean,ct.asn1.ASN1Object),ct.asn1.DERInteger=function(t){ct.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=ct.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new E(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},o.lang.extend(ct.asn1.DERInteger,ct.asn1.ASN1Object),ct.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=ct.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}ct.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var n="";for(r=0;r<t.length-1;r+=8){var i=t.substr(r,8),o=parseInt(i,2).toString(16);1==o.length&&(o="0"+o),n+=o}this.hTLV=null,this.isModified=!0,this.hV="0"+e+n},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},o.lang.extend(ct.asn1.DERBitString,ct.asn1.ASN1Object),ct.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=ct.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}ct.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},o.lang.extend(ct.asn1.DEROctetString,ct.asn1.DERAbstractString),ct.asn1.DERNull=function(){ct.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},o.lang.extend(ct.asn1.DERNull,ct.asn1.ASN1Object),ct.asn1.DERObjectIdentifier=function(t){ct.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){var e=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=parseInt(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var o="",s=0;s<i;s++)o+="0";for(n=o+n,s=0;s<n.length-1;s+=7){var a=n.substr(s,7);s!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};try{if(!t.match(/^[0-9.]+$/))return null;var n="",i=t.split("."),o=40*parseInt(i[0],10)+parseInt(i[1],10);n+=e(o),i.splice(0,2);for(var s=0;s<i.length;s++)n+=r(i[s]);return n}catch(t){return null}}(t);if(null==e)throw new Error("malformed oid string: "+t);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=e},this.setValueName=function(t){var e=ct.asn1.x509.OID.name2oid(t);if(""===e)throw new Error("DERObjectIdentifier oidName undefined: "+t);this.setValueOidString(e)},this.setValueNameOrOid=function(t){t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t)},this.getFreshValueHex=function(){return this.hV},this.setByParam=function(t){"string"==typeof t?this.setValueNameOrOid(t):void 0!==t.oid?this.setValueNameOrOid(t.oid):void 0!==t.name?this.setValueNameOrOid(t.name):void 0!==t.hex&&this.setValueHex(t.hex)},void 0!==t&&this.setByParam(t)},o.lang.extend(ct.asn1.DERObjectIdentifier,ct.asn1.ASN1Object),ct.asn1.DEREnumerated=function(t){ct.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=ct.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new E(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},o.lang.extend(ct.asn1.DEREnumerated,ct.asn1.ASN1Object),ct.asn1.DERUTF8String=function(t){ct.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},o.lang.extend(ct.asn1.DERUTF8String,ct.asn1.DERAbstractString),ct.asn1.DERNumericString=function(t){ct.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},o.lang.extend(ct.asn1.DERNumericString,ct.asn1.DERAbstractString),ct.asn1.DERPrintableString=function(t){ct.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},o.lang.extend(ct.asn1.DERPrintableString,ct.asn1.DERAbstractString),ct.asn1.DERTeletexString=function(t){ct.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},o.lang.extend(ct.asn1.DERTeletexString,ct.asn1.DERAbstractString),ct.asn1.DERIA5String=function(t){ct.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},o.lang.extend(ct.asn1.DERIA5String,ct.asn1.DERAbstractString),ct.asn1.DERVisibleString=function(t){ct.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="1a"},o.lang.extend(ct.asn1.DERVisibleString,ct.asn1.DERAbstractString),ct.asn1.DERBMPString=function(t){ct.asn1.DERBMPString.superclass.constructor.call(this,t),this.hT="1e"},o.lang.extend(ct.asn1.DERBMPString,ct.asn1.DERAbstractString),ct.asn1.DERUTCTime=function(t){ct.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=vt(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=vt(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},o.lang.extend(ct.asn1.DERUTCTime,ct.asn1.DERAbstractTime),ct.asn1.DERGeneralizedTime=function(t){ct.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=vt(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=vt(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},o.lang.extend(ct.asn1.DERGeneralizedTime,ct.asn1.DERAbstractTime),ct.asn1.DERSequence=function(t){ct.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},o.lang.extend(ct.asn1.DERSequence,ct.asn1.DERAbstractStructured),ct.asn1.DERSet=function(t){ct.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},o.lang.extend(ct.asn1.DERSet,ct.asn1.DERAbstractStructured),ct.asn1.DERTaggedObject=function(t){ct.asn1.DERTaggedObject.superclass.constructor.call(this);var e=ct.asn1;this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},this.setByParam=function(t){null!=t.tag&&(this.hT=t.tag),null!=t.explicit&&(this.isExplicit=t.explicit),null!=t.tage&&(this.hT=t.tage,this.isExplicit=!0),null!=t.tagi&&(this.hT=t.tagi,this.isExplicit=!1),null!=t.obj&&(t.obj instanceof e.ASN1Object?(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)):"object"==r(t.obj)&&(this.asn1Object=e.ASN1Util.newObject(t.obj),this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},null!=t&&this.setByParam(t)},o.lang.extend(ct.asn1.DERTaggedObject,ct.asn1.ASN1Object);var ct,ft,ht,lt=new function(){};function pt(t){for(var e=new Array,r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}function gt(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}function dt(t){for(var e="",r=0;r<t.length;r++){var n=t[r].toString(16);1==n.length&&(n="0"+n),e+=n}return e}function vt(t){return dt(pt(t))}function yt(t){return(t=(t=t.replace(/\=/g,"")).replace(/\+/g,"-")).replace(/\//g,"_")}function mt(t){return t.length%4==2?t+="==":t.length%4==3&&(t+="="),(t=t.replace(/-/g,"+")).replace(/_/g,"/")}function bt(t){return t.length%2==1&&(t="0"+t),yt(S(t))}function St(t){return w(mt(t))}function wt(t){return Ct(jt(t))}function _t(t){return decodeURIComponent(Ot(t))}function Et(t){for(var e="",r=0;r<t.length-1;r+=2)e+=String.fromCharCode(parseInt(t.substr(r,2),16));return e}function xt(t){for(var e="",r=0;r<t.length;r++)e+=("0"+t.charCodeAt(r).toString(16)).slice(-2);return e}function Ft(t){return S(t)}function At(t){var e=Ft(t).replace(/(.{64})/g,"$1\r\n");return e.replace(/\r\n$/,"")}function Tt(t){return w(t.replace(/[^0-9A-Za-z\/+=]*/g,""))}function kt(t,e){return"-----BEGIN "+e+"-----\r\n"+At(t)+"\r\n-----END "+e+"-----\r\n"}function Pt(t,e){if(-1==t.indexOf("-----BEGIN "))throw"can't find PEM header: "+e;return Tt(t=void 0!==e?(t=t.replace(new RegExp("^[^]*-----BEGIN "+e+"-----"),"")).replace(new RegExp("-----END "+e+"-----[^]*$"),""):(t=t.replace(/^[^]*-----BEGIN [^-]+-----/,"")).replace(/-----END [^-]+-----[^]*$/,""))}function Rt(t){var e,r,n,i,o,s,a,u,c,f,h;if(h=t.match(/^(\d{2}|\d{4})(\d\d)(\d\d)(\d\d)(\d\d)(\d\d)(|\.\d+)Z$/))return u=h[1],e=parseInt(u),2===u.length&&(50<=e&&e<100?e=1900+e:0<=e&&e<50&&(e=2e3+e)),r=parseInt(h[2])-1,n=parseInt(h[3]),i=parseInt(h[4]),o=parseInt(h[5]),s=parseInt(h[6]),a=0,""!==(c=h[7])&&(f=(c.substr(1)+"00").substr(0,3),a=parseInt(f)),Date.UTC(e,r,n,i,o,s,a);throw"unsupported zulu format: "+t}function It(t){return~~(Rt(t)/1e3)}function Ct(t){return t.replace(/%/g,"")}function Ot(t){return t.replace(/(..)/g,"%$1")}function Dt(t){var e="malformed IPv6 address";if(!t.match(/^[0-9A-Fa-f:]+$/))throw e;var r=(t=t.toLowerCase()).split(":").length-1;if(r<2)throw e;var n=":".repeat(7-r+2),i=(t=t.replace("::",n)).split(":");if(8!=i.length)throw e;for(var o=0;o<8;o++)i[o]=("0000"+i[o]).slice(-4);return i.join("")}function Nt(t){if(!t.match(/^[0-9A-Fa-f]{32}$/))throw"malformed IPv6 address octet";for(var e=(t=t.toLowerCase()).match(/.{1,4}/g),r=0;r<8;r++)e[r]=e[r].replace(/^0+/,""),""==e[r]&&(e[r]="0");var n=(t=":"+e.join(":")+":").match(/:(0:){2,}/g);if(null===n)return t.slice(1,-1);var i="";for(r=0;r<n.length;r++)n[r].length>i.length&&(i=n[r]);return(t=t.replace(i,"::")).slice(1,-1)}function Lt(t){var e="malformed hex value";if(!t.match(/^([0-9A-Fa-f][0-9A-Fa-f]){1,}$/))throw e;if(8!=t.length)return 32==t.length?Nt(t):t;try{return parseInt(t.substr(0,2),16)+"."+parseInt(t.substr(2,2),16)+"."+parseInt(t.substr(4,2),16)+"."+parseInt(t.substr(6,2),16)}catch(t){throw e}}function jt(t){for(var e=encodeURIComponent(t),r="",n=0;n<e.length;n++)"%"==e[n]?(r+=e.substr(n,3),n+=2):r=r+"%"+vt(e[n]);return r}function Mt(t){return!(t.length%2!=0||!t.match(/^[0-9a-f]+$/)&&!t.match(/^[0-9A-F]+$/))}function Bt(t){return t.length%2==1?"0"+t:t.substr(0,1)>"7"?"00"+t:t}lt.getLblen=function(t,e){if("8"!=t.substr(e+2,1))return 1;var r=parseInt(t.substr(e+3,1));return 0==r?-1:0<r&&r<10?r+1:-2},lt.getL=function(t,e){var r=lt.getLblen(t,e);return r<1?"":t.substr(e+2,2*r)},lt.getVblen=function(t,e){var r;return""==(r=lt.getL(t,e))?-1:("8"===r.substr(0,1)?new E(r.substr(2),16):new E(r,16)).intValue()},lt.getVidx=function(t,e){var r=lt.getLblen(t,e);return r<0?r:e+2*(r+1)},lt.getV=function(t,e){var r=lt.getVidx(t,e),n=lt.getVblen(t,e);return t.substr(r,2*n)},lt.getTLV=function(t,e){return t.substr(e,2)+lt.getL(t,e)+lt.getV(t,e)},lt.getTLVblen=function(t,e){return 2+2*lt.getLblen(t,e)+2*lt.getVblen(t,e)},lt.getNextSiblingIdx=function(t,e){return lt.getVidx(t,e)+2*lt.getVblen(t,e)},lt.getChildIdx=function(t,e){var r,n,i,o=lt,s=[];r=o.getVidx(t,e),n=2*o.getVblen(t,e),"03"==t.substr(e,2)&&(r+=2,n-=2),i=0;for(var a=r;i<=n;){var u=o.getTLVblen(t,a);if((i+=u)<=n&&s.push(a),a+=u,i>=n)break}return s},lt.getNthChildIdx=function(t,e,r){return lt.getChildIdx(t,e)[r]},lt.getIdxbyList=function(t,e,r,n){var i,o,s=lt;return 0==r.length?void 0!==n&&t.substr(e,2)!==n?-1:e:(i=r.shift())>=(o=s.getChildIdx(t,e)).length?-1:s.getIdxbyList(t,o[i],r,n)},lt.getIdxbyListEx=function(t,e,r,n){var i,o,s=lt;if(0==r.length)return void 0!==n&&t.substr(e,2)!==n?-1:e;i=r.shift(),o=s.getChildIdx(t,e);for(var a=0,u=0;u<o.length;u++){var c=t.substr(o[u],2);if("number"==typeof i&&!s.isContextTag(c)&&a==i||"string"==typeof i&&s.isContextTag(c,i))return s.getIdxbyListEx(t,o[u],r,n);s.isContextTag(c)||a++}return-1},lt.getTLVbyList=function(t,e,r,n){var i=lt,o=i.getIdxbyList(t,e,r,n);return-1==o||o>=t.length?null:i.getTLV(t,o)},lt.getTLVbyListEx=function(t,e,r,n){var i=lt,o=i.getIdxbyListEx(t,e,r,n);return-1==o?null:i.getTLV(t,o)},lt.getVbyList=function(t,e,r,n,i){var o,s,a=lt;return-1==(o=a.getIdxbyList(t,e,r,n))||o>=t.length?null:(s=a.getV(t,o),!0===i&&(s=s.substr(2)),s)},lt.getVbyListEx=function(t,e,r,n,i){var o,s,a=lt;return-1==(o=a.getIdxbyListEx(t,e,r,n))?null:(s=a.getV(t,o),"03"==t.substr(o,2)&&!1!==i&&(s=s.substr(2)),s)},lt.getInt=function(t,e,r){null==r&&(r=-1);try{var n=t.substr(e,2);if("02"!=n&&"03"!=n)return r;var i=lt.getV(t,e);return"02"==n?parseInt(i,16):function(t){try{var e=t.substr(0,2);if("00"==e)return parseInt(t.substr(2),16);var r=parseInt(e,16),n=t.substr(2),i=parseInt(n,16).toString(2);return"0"==i&&(i="00000000"),i=i.slice(0,0-r),parseInt(i,2)}catch(t){return-1}}(i)}catch(t){return r}},lt.getOID=function(t,e,r){null==r&&(r=null);try{return"06"!=t.substr(e,2)?r:function(t){if(!Mt(t))return null;try{var e=[],r=t.substr(0,2),n=parseInt(r,16);e[0]=new String(Math.floor(n/40)),e[1]=new String(n%40);for(var i=t.substr(2),o=[],s=0;s<i.length/2;s++)o.push(parseInt(i.substr(2*s,2),16));var a=[],u="";for(s=0;s<o.length;s++)128&o[s]?u+=Ut((127&o[s]).toString(2),7):(u+=Ut((127&o[s]).toString(2),7),a.push(new String(parseInt(u,2))),u="");var c=e.join(".");return a.length>0&&(c=c+"."+a.join(".")),c}catch(t){return null}}(lt.getV(t,e))}catch(t){return r}},lt.getOIDName=function(t,e,r){null==r&&(r=null);try{var n=lt.getOID(t,e,r);if(n==r)return r;var i=ct.asn1.x509.OID.oid2name(n);return""==i?n:i}catch(t){return r}},lt.getString=function(t,e,r){null==r&&(r=null);try{return Et(lt.getV(t,e))}catch(t){return r}},lt.hextooidstr=function(t){var e=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},r=[],n=t.substr(0,2),i=parseInt(n,16);r[0]=new String(Math.floor(i/40)),r[1]=new String(i%40);for(var o=t.substr(2),s=[],a=0;a<o.length/2;a++)s.push(parseInt(o.substr(2*a,2),16));var u=[],c="";for(a=0;a<s.length;a++)128&s[a]?c+=e((127&s[a]).toString(2),7):(c+=e((127&s[a]).toString(2),7),u.push(new String(parseInt(c,2))),c="");var f=r.join(".");return u.length>0&&(f=f+"."+u.join(".")),f},lt.dump=function(t,e,r,n){var i=lt,o=i.getV,s=i.dump,a=i.getChildIdx,u=t;t instanceof ct.asn1.ASN1Object&&(u=t.getEncodedHex());var c=function(t,e){return t.length<=2*e?t:t.substr(0,e)+"..(total "+t.length/2+"bytes).."+t.substr(t.length-e,e)};void 0===e&&(e={ommit_long_octet:32}),void 0===r&&(r=0),void 0===n&&(n="");var f,h=e.ommit_long_octet;if("01"==(f=u.substr(r,2)))return"00"==(l=o(u,r))?n+"BOOLEAN FALSE\n":n+"BOOLEAN TRUE\n";if("02"==f)return n+"INTEGER "+c(l=o(u,r),h)+"\n";if("03"==f){var l=o(u,r);return i.isASN1HEX(l.substr(2))?(w=n+"BITSTRING, encapsulates\n")+s(l.substr(2),e,0,n+"  "):n+"BITSTRING "+c(l,h)+"\n"}if("04"==f)return l=o(u,r),i.isASN1HEX(l)?(w=n+"OCTETSTRING, encapsulates\n")+s(l,e,0,n+"  "):n+"OCTETSTRING "+c(l,h)+"\n";if("05"==f)return n+"NULL\n";if("06"==f){var p=o(u,r),g=ct.asn1.ASN1Util.oidHexToInt(p),d=ct.asn1.x509.OID.oid2name(g),v=g.replace(/\./g," ");return""!=d?n+"ObjectIdentifier "+d+" ("+v+")\n":n+"ObjectIdentifier ("+v+")\n"}if("0a"==f)return n+"ENUMERATED "+parseInt(o(u,r))+"\n";if("0c"==f)return n+"UTF8String '"+_t(o(u,r))+"'\n";if("13"==f)return n+"PrintableString '"+_t(o(u,r))+"'\n";if("14"==f)return n+"TeletexString '"+_t(o(u,r))+"'\n";if("16"==f)return n+"IA5String '"+_t(o(u,r))+"'\n";if("17"==f)return n+"UTCTime "+_t(o(u,r))+"\n";if("18"==f)return n+"GeneralizedTime "+_t(o(u,r))+"\n";if("1a"==f)return n+"VisualString '"+_t(o(u,r))+"'\n";if("1e"==f)return n+"BMPString '"+_t(o(u,r))+"'\n";if("30"==f){if("3000"==u.substr(r,4))return n+"SEQUENCE {}\n";w=n+"SEQUENCE\n";var y=e;if((2==(S=a(u,r)).length||3==S.length)&&"06"==u.substr(S[0],2)&&"04"==u.substr(S[S.length-1],2)){d=i.oidname(o(u,S[0]));var m=JSON.parse(JSON.stringify(e));m.x509ExtName=d,y=m}for(var b=0;b<S.length;b++)w+=s(u,y,S[b],n+"  ");return w}if("31"==f){w=n+"SET\n";var S=a(u,r);for(b=0;b<S.length;b++)w+=s(u,e,S[b],n+"  ");return w}if(0!=(128&(f=parseInt(f,16)))){var w,_=31&f;if(0!=(32&f)){for(w=n+"["+_+"]\n",S=a(u,r),b=0;b<S.length;b++)w+=s(u,e,S[b],n+"  ");return w}return l=o(u,r),lt.isASN1HEX(l)?(w=n+"["+_+"]\n")+s(l,e,0,n+"  "):(("68747470"==l.substr(0,8)||"subjectAltName"===e.x509ExtName&&2==_)&&(l=_t(l)),n+"["+_+"] "+l+"\n")}return n+"UNKNOWN("+f+") "+o(u,r)+"\n"},lt.isContextTag=function(t,e){var r,n;t=t.toLowerCase();try{r=parseInt(t,16)}catch(t){return-1}if(void 0===e)return 128==(192&r);try{return null!=e.match(/^\[[0-9]+\]$/)&&!((n=parseInt(e.substr(1,e.length-1),10))>31)&&128==(192&r)&&(31&r)==n}catch(t){return!1}},lt.isASN1HEX=function(t){var e=lt;if(t.length%2==1)return!1;var r=e.getVblen(t,0),n=t.substr(0,2),i=e.getL(t,0);return t.length-n.length-i.length==2*r},lt.checkStrictDER=function(t,e,r,n,i){var o=lt;if(void 0===r){if("string"!=typeof t)throw new Error("not hex string");if(t=t.toLowerCase(),!ct.lang.String.isHex(t))throw new Error("not hex string");r=t.length,i=(n=t.length/2)<128?1:Math.ceil(n.toString(16))+1}if(o.getL(t,e).length>2*i)throw new Error("L of TLV too long: idx="+e);var s=o.getVblen(t,e);if(s>n)throw new Error("value of L too long than hex: idx="+e);var a=o.getTLV(t,e),u=a.length-2-o.getL(t,e).length;if(u!==2*s)throw new Error("V string length and L's value not the same:"+u+"/"+2*s);if(0===e&&t.length!=a.length)throw new Error("total length and TLV length unmatch:"+t.length+"!="+a.length);var c=t.substr(e,2);if("02"===c){var f=o.getVidx(t,e);if("00"==t.substr(f,2)&&t.charCodeAt(f+2)<56)throw new Error("not least zeros for DER INTEGER")}if(32&parseInt(c,16)){for(var h=o.getVblen(t,e),l=0,p=o.getChildIdx(t,e),g=0;g<p.length;g++)l+=o.getTLV(t,p[g]).length,o.checkStrictDER(t,p[g],r,n,i);if(2*h!=l)throw new Error("sum of children's TLV length and L unmatch: "+2*h+"!="+l)}},lt.oidname=function(t){var e=ct.asn1;ct.lang.String.isHex(t)&&(t=e.ASN1Util.oidHexToInt(t));var r=e.x509.OID.oid2name(t);return""===r&&(r=t),r},void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.lang&&ct.lang||(ct.lang={}),ct.lang.String=function(){},"function"==typeof t?(e.utf8tob64u=ft=function(e){return yt(t.from(e,"utf8").toString("base64"))},e.b64utoutf8=ht=function(e){return t.from(mt(e),"base64").toString("utf8")}):(e.utf8tob64u=ft=function(t){return bt(Ct(jt(t)))},e.b64utoutf8=ht=function(t){return decodeURIComponent(Ot(St(t)))}),ct.lang.String.isInteger=function(t){return!!t.match(/^[0-9]+$/)||!!t.match(/^-[0-9]+$/)},ct.lang.String.isHex=function(t){return Mt(t)},ct.lang.String.isBase64=function(t){return!(!(t=t.replace(/\s+/g,"")).match(/^[0-9A-Za-z+\/]+={0,3}$/)||t.length%4!=0)},ct.lang.String.isBase64URL=function(t){return!t.match(/[+/=]/)&&(t=mt(t),ct.lang.String.isBase64(t))},ct.lang.String.isIntegerArray=function(t){return!!(t=t.replace(/\s+/g,"")).match(/^\[[0-9,]+\]$/)},ct.lang.String.isPrintable=function(t){return null!==t.match(/^[0-9A-Za-z '()+,-./:=?]*$/)},ct.lang.String.isIA5=function(t){return null!==t.match(/^[\x20-\x21\x23-\x7f]*$/)},ct.lang.String.isMail=function(t){return null!==t.match(/^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/)};var Ut=function(t,e,r){return null==r&&(r="0"),t.length>=e?t:new Array(e-t.length+1).join(r)+t};void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.crypto&&ct.crypto||(ct.crypto={}),ct.crypto.Util=new function(){this.DIGESTINFOHEAD={sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",ripemd160:"3021300906052b2403020105000414"},this.DEFAULTPROVIDER={md5:"cryptojs",sha1:"cryptojs",sha224:"cryptojs",sha256:"cryptojs",sha384:"cryptojs",sha512:"cryptojs",ripemd160:"cryptojs",hmacmd5:"cryptojs",hmacsha1:"cryptojs",hmacsha224:"cryptojs",hmacsha256:"cryptojs",hmacsha384:"cryptojs",hmacsha512:"cryptojs",hmacripemd160:"cryptojs",MD5withRSA:"cryptojs/jsrsa",SHA1withRSA:"cryptojs/jsrsa",SHA224withRSA:"cryptojs/jsrsa",SHA256withRSA:"cryptojs/jsrsa",SHA384withRSA:"cryptojs/jsrsa",SHA512withRSA:"cryptojs/jsrsa",RIPEMD160withRSA:"cryptojs/jsrsa",MD5withECDSA:"cryptojs/jsrsa",SHA1withECDSA:"cryptojs/jsrsa",SHA224withECDSA:"cryptojs/jsrsa",SHA256withECDSA:"cryptojs/jsrsa",SHA384withECDSA:"cryptojs/jsrsa",SHA512withECDSA:"cryptojs/jsrsa",RIPEMD160withECDSA:"cryptojs/jsrsa",SHA1withDSA:"cryptojs/jsrsa",SHA224withDSA:"cryptojs/jsrsa",SHA256withDSA:"cryptojs/jsrsa",MD5withRSAandMGF1:"cryptojs/jsrsa",SHAwithRSAandMGF1:"cryptojs/jsrsa",SHA1withRSAandMGF1:"cryptojs/jsrsa",SHA224withRSAandMGF1:"cryptojs/jsrsa",SHA256withRSAandMGF1:"cryptojs/jsrsa",SHA384withRSAandMGF1:"cryptojs/jsrsa",SHA512withRSAandMGF1:"cryptojs/jsrsa",RIPEMD160withRSAandMGF1:"cryptojs/jsrsa"},this.CRYPTOJSMESSAGEDIGESTNAME={md5:y.algo.MD5,sha1:y.algo.SHA1,sha224:y.algo.SHA224,sha256:y.algo.SHA256,sha384:y.algo.SHA384,sha512:y.algo.SHA512,ripemd160:y.algo.RIPEMD160},this.getDigestInfoHex=function(t,e){if(void 0===this.DIGESTINFOHEAD[e])throw"alg not supported in Util.DIGESTINFOHEAD: "+e;return this.DIGESTINFOHEAD[e]+t},this.getPaddedDigestInfoHex=function(t,e,r){var n=this.getDigestInfoHex(t,e),i=r/4;if(n.length+22>i)throw"key is too short for SigAlg: keylen="+r+","+e;for(var o="0001",s="00"+n,a="",u=i-o.length-s.length,c=0;c<u;c+=2)a+="ff";return o+a+s},this.hashString=function(t,e){return new ct.crypto.MessageDigest({alg:e}).digestString(t)},this.hashHex=function(t,e){return new ct.crypto.MessageDigest({alg:e}).digestHex(t)},this.sha1=function(t){return this.hashString(t,"sha1")},this.sha256=function(t){return this.hashString(t,"sha256")},this.sha256Hex=function(t){return this.hashHex(t,"sha256")},this.sha512=function(t){return this.hashString(t,"sha512")},this.sha512Hex=function(t){return this.hashHex(t,"sha512")},this.isKey=function(t){return t instanceof it||t instanceof ct.crypto.DSA||t instanceof ct.crypto.ECDSA}},ct.crypto.Util.md5=function(t){return new ct.crypto.MessageDigest({alg:"md5",prov:"cryptojs"}).digestString(t)},ct.crypto.Util.ripemd160=function(t){return new ct.crypto.MessageDigest({alg:"ripemd160",prov:"cryptojs"}).digestString(t)},ct.crypto.Util.SECURERANDOMGEN=new et,ct.crypto.Util.getRandomHexOfNbytes=function(t){var e=new Array(t);return ct.crypto.Util.SECURERANDOMGEN.nextBytes(e),dt(e)},ct.crypto.Util.getRandomBigIntegerOfNbytes=function(t){return new E(ct.crypto.Util.getRandomHexOfNbytes(t),16)},ct.crypto.Util.getRandomHexOfNbits=function(t){var e=t%8,r=new Array((t-e)/8+1);return ct.crypto.Util.SECURERANDOMGEN.nextBytes(r),r[0]=(255<<e&255^255)&r[0],dt(r)},ct.crypto.Util.getRandomBigIntegerOfNbits=function(t){return new E(ct.crypto.Util.getRandomHexOfNbits(t),16)},ct.crypto.Util.getRandomBigIntegerZeroToMax=function(t){for(var e=t.bitLength();;){var r=ct.crypto.Util.getRandomBigIntegerOfNbits(e);if(-1!=t.compareTo(r))return r}},ct.crypto.Util.getRandomBigIntegerMinToMax=function(t,e){var r=t.compareTo(e);if(1==r)throw"biMin is greater than biMax";if(0==r)return t;var n=e.subtract(t);return ct.crypto.Util.getRandomBigIntegerZeroToMax(n).add(t)},ct.crypto.MessageDigest=function(t){this.setAlgAndProvider=function(t,e){if(null!==(t=ct.crypto.MessageDigest.getCanonicalAlgName(t))&&void 0===e&&(e=ct.crypto.Util.DEFAULTPROVIDER[t]),-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(t)&&"cryptojs"==e){try{this.md=ct.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[t].create()}catch(e){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+e}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=y.enc.Hex.parse(t);this.md.update(e)},this.digest=function(){return this.md.finalize().toString(y.enc.Hex)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}if(-1!=":sha256:".indexOf(t)&&"sjcl"==e){try{this.md=new sjcl.hash.sha256}catch(e){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+e}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=sjcl.codec.hex.toBits(t);this.md.update(e)},this.digest=function(){var t=this.md.finalize();return sjcl.codec.hex.fromBits(t)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digest=function(){throw"digest() not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},void 0!==t&&void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=ct.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName))},ct.crypto.MessageDigest.getCanonicalAlgName=function(t){return"string"==typeof t&&(t=(t=t.toLowerCase()).replace(/-/,"")),t},ct.crypto.MessageDigest.getHashLength=function(t){var e=ct.crypto.MessageDigest,r=e.getCanonicalAlgName(t);if(void 0===e.HASHLENGTH[r])throw"not supported algorithm: "+t;return e.HASHLENGTH[r]},ct.crypto.MessageDigest.HASHLENGTH={md5:16,sha1:20,sha224:28,sha256:32,sha384:48,sha512:64,ripemd160:20},ct.crypto.Mac=function(t){this.setAlgAndProvider=function(t,e){if(null==(t=t.toLowerCase())&&(t="hmacsha1"),"hmac"!=(t=t.toLowerCase()).substr(0,4))throw"setAlgAndProvider unsupported HMAC alg: "+t;void 0===e&&(e=ct.crypto.Util.DEFAULTPROVIDER[t]),this.algProv=t+"/"+e;var r=t.substr(4);if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(r)&&"cryptojs"==e){try{var n=ct.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[r];this.mac=y.algo.HMAC.create(n,this.pass)}catch(t){throw"setAlgAndProvider hash alg set fail hashAlg="+r+"/"+t}this.updateString=function(t){this.mac.update(t)},this.updateHex=function(t){var e=y.enc.Hex.parse(t);this.mac.update(e)},this.doFinal=function(){return this.mac.finalize().toString(y.enc.Hex)},this.doFinalString=function(t){return this.updateString(t),this.doFinal()},this.doFinalHex=function(t){return this.updateHex(t),this.doFinal()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algProv},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algProv},this.doFinal=function(){throw"digest() not supported for this alg/prov: "+this.algProv},this.doFinalString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algProv},this.doFinalHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algProv},this.setPassword=function(t){if("string"==typeof t){var e=t;return t.length%2!=1&&t.match(/^[0-9A-Fa-f]+$/)||(e=xt(t)),void(this.pass=y.enc.Hex.parse(e))}if("object"!=(void 0===t?"undefined":r(t)))throw"KJUR.crypto.Mac unsupported password type: "+t;if(e=null,void 0!==t.hex){if(t.hex.length%2!=0||!t.hex.match(/^[0-9A-Fa-f]+$/))throw"Mac: wrong hex password: "+t.hex;e=t.hex}if(void 0!==t.utf8&&(e=wt(t.utf8)),void 0!==t.rstr&&(e=xt(t.rstr)),void 0!==t.b64&&(e=w(t.b64)),void 0!==t.b64u&&(e=St(t.b64u)),null==e)throw"KJUR.crypto.Mac unsupported password type: "+t;this.pass=y.enc.Hex.parse(e)},void 0!==t&&(void 0!==t.pass&&this.setPassword(t.pass),void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=ct.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName)))},ct.crypto.Signature=function(t){var e=null;if(this._setAlgNames=function(){var t=this.algName.match(/^(.+)with(.+)$/);t&&(this.mdAlgName=t[1].toLowerCase(),this.pubkeyAlgName=t[2].toLowerCase(),"rsaandmgf1"==this.pubkeyAlgName&&"sha"==this.mdAlgName&&(this.mdAlgName="sha1"))},this._zeroPaddingOfSignature=function(t,e){for(var r="",n=e/4-t.length,i=0;i<n;i++)r+="0";return r+t},this.setAlgAndProvider=function(t,e){if(this._setAlgNames(),"cryptojs/jsrsa"!=e)throw new Error("provider not supported: "+e);if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(this.mdAlgName)){try{this.md=new ct.crypto.MessageDigest({alg:this.mdAlgName})}catch(t){throw new Error("setAlgAndProvider hash alg set fail alg="+this.mdAlgName+"/"+t)}this.init=function(t,e){var r=null;try{r=void 0===e?Ht.getKey(t):Ht.getKey(t,e)}catch(t){throw"init failed:"+t}if(!0===r.isPrivate)this.prvKey=r,this.state="SIGN";else{if(!0!==r.isPublic)throw"init failed.:"+r;this.pubKey=r,this.state="VERIFY"}},this.updateString=function(t){this.md.updateString(t)},this.updateHex=function(t){this.md.updateHex(t)},this.sign=function(){if(this.sHashHex=this.md.digest(),void 0===this.prvKey&&void 0!==this.ecprvhex&&void 0!==this.eccurvename&&void 0!==ct.crypto.ECDSA&&(this.prvKey=new ct.crypto.ECDSA({curve:this.eccurvename,prv:this.ecprvhex})),this.prvKey instanceof it&&"rsaandmgf1"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHashPSS(this.sHashHex,this.mdAlgName,this.pssSaltLen);else if(this.prvKey instanceof it&&"rsa"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex,this.mdAlgName);else if(this.prvKey instanceof ct.crypto.ECDSA)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex);else{if(!(this.prvKey instanceof ct.crypto.DSA))throw"Signature: unsupported private key alg: "+this.pubkeyAlgName;this.hSign=this.prvKey.signWithMessageHash(this.sHashHex)}return this.hSign},this.signString=function(t){return this.updateString(t),this.sign()},this.signHex=function(t){return this.updateHex(t),this.sign()},this.verify=function(t){if(this.sHashHex=this.md.digest(),void 0===this.pubKey&&void 0!==this.ecpubhex&&void 0!==this.eccurvename&&void 0!==ct.crypto.ECDSA&&(this.pubKey=new ct.crypto.ECDSA({curve:this.eccurvename,pub:this.ecpubhex})),this.pubKey instanceof it&&"rsaandmgf1"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHashPSS(this.sHashHex,t,this.mdAlgName,this.pssSaltLen);if(this.pubKey instanceof it&&"rsa"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==ct.crypto.ECDSA&&this.pubKey instanceof ct.crypto.ECDSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==ct.crypto.DSA&&this.pubKey instanceof ct.crypto.DSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);throw"Signature: unsupported public key alg: "+this.pubkeyAlgName}}},this.init=function(t,e){throw"init(key, pass) not supported for this alg:prov="+this.algProvName},this.updateString=function(t){throw"updateString(str) not supported for this alg:prov="+this.algProvName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg:prov="+this.algProvName},this.sign=function(){throw"sign() not supported for this alg:prov="+this.algProvName},this.signString=function(t){throw"digestString(str) not supported for this alg:prov="+this.algProvName},this.signHex=function(t){throw"digestHex(hex) not supported for this alg:prov="+this.algProvName},this.verify=function(t){throw"verify(hSigVal) not supported for this alg:prov="+this.algProvName},this.initParams=t,void 0!==t&&(void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov?this.provName=ct.crypto.Util.DEFAULTPROVIDER[this.algName]:this.provName=t.prov,this.algProvName=this.algName+":"+this.provName,this.setAlgAndProvider(this.algName,this.provName),this._setAlgNames()),void 0!==t.psssaltlen&&(this.pssSaltLen=t.psssaltlen),void 0!==t.prvkeypem)){if(void 0!==t.prvkeypas)throw"both prvkeypem and prvkeypas parameters not supported";try{e=Ht.getKey(t.prvkeypem),this.init(e)}catch(t){throw"fatal error to load pem private key: "+t}}},ct.crypto.Cipher=function(t){},ct.crypto.Cipher.encrypt=function(t,e,r){if(e instanceof it&&e.isPublic){var n=ct.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===n)return e.encrypt(t);if("RSAOAEP"===n)return e.encryptOAEP(t,"sha1");var i=n.match(/^RSAOAEP(\d+)$/);if(null!==i)return e.encryptOAEP(t,"sha"+i[1]);throw"Cipher.encrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.encrypt: unsupported key or algorithm"},ct.crypto.Cipher.decrypt=function(t,e,r){if(e instanceof it&&e.isPrivate){var n=ct.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===n)return e.decrypt(t);if("RSAOAEP"===n)return e.decryptOAEP(t,"sha1");var i=n.match(/^RSAOAEP(\d+)$/);if(null!==i)return e.decryptOAEP(t,"sha"+i[1]);throw"Cipher.decrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.decrypt: unsupported key or algorithm"},ct.crypto.Cipher.getAlgByKeyAndName=function(t,e){if(t instanceof it){if(-1!=":RSA:RSAOAEP:RSAOAEP224:RSAOAEP256:RSAOAEP384:RSAOAEP512:".indexOf(e))return e;if(null==e)return"RSA";throw"getAlgByKeyAndName: not supported algorithm name for RSAKey: "+e}throw"getAlgByKeyAndName: not supported algorithm name: "+e},ct.crypto.OID=new function(){this.oidhex2name={"2a864886f70d010101":"rsaEncryption","2a8648ce3d0201":"ecPublicKey","2a8648ce380401":"dsa","2a8648ce3d030107":"secp256r1","2b8104001f":"secp192k1","2b81040021":"secp224r1","2b8104000a":"secp256k1","2b81040023":"secp521r1","2b81040022":"secp384r1","2a8648ce380403":"SHA1withDSA","608648016503040301":"SHA224withDSA","608648016503040302":"SHA256withDSA"}},void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.crypto&&ct.crypto||(ct.crypto={}),ct.crypto.ECDSA=function(t){var e=Error,n=E,i=st,o=ct.crypto.ECDSA,s=ct.crypto.ECParameterDB,a=o.getName,u=lt,c=u.getVbyListEx,f=u.isASN1HEX,h=new et;this.type="EC",this.isPrivate=!1,this.isPublic=!1,this.getBigRandom=function(t){return new n(t.bitLength(),h).mod(t.subtract(n.ONE)).add(n.ONE)},this.setNamedCurve=function(t){this.ecparams=s.getByName(t),this.prvKeyHex=null,this.pubKeyHex=null,this.curveName=t},this.setPrivateKeyHex=function(t){this.isPrivate=!0,this.prvKeyHex=t},this.setPublicKeyHex=function(t){this.isPublic=!0,this.pubKeyHex=t},this.getPublicKeyXYHex=function(){var t=this.pubKeyHex;if("04"!==t.substr(0,2))throw"this method supports uncompressed format(04) only";var e=this.ecparams.keylen/4;if(t.length!==2+2*e)throw"malformed public key hex length";var r={};return r.x=t.substr(2,e),r.y=t.substr(2+e),r},this.getShortNISTPCurveName=function(){var t=this.curveName;return"secp256r1"===t||"NIST P-256"===t||"P-256"===t||"prime256v1"===t?"P-256":"secp384r1"===t||"NIST P-384"===t||"P-384"===t?"P-384":null},this.generateKeyPairHex=function(){var t=this.ecparams.n,e=this.getBigRandom(t),r=this.ecparams.G.multiply(e),n=r.getX().toBigInteger(),i=r.getY().toBigInteger(),o=this.ecparams.keylen/4,s=("0000000000"+e.toString(16)).slice(-o),a="04"+("0000000000"+n.toString(16)).slice(-o)+("0000000000"+i.toString(16)).slice(-o);return this.setPrivateKeyHex(s),this.setPublicKeyHex(a),{ecprvhex:s,ecpubhex:a}},this.signWithMessageHash=function(t){return this.signHex(t,this.prvKeyHex)},this.signHex=function(t,e){var r=new n(e,16),i=this.ecparams.n,s=new n(t.substring(0,this.ecparams.keylen/4),16);do{var a=this.getBigRandom(i),u=this.ecparams.G.multiply(a).getX().toBigInteger().mod(i)}while(u.compareTo(n.ZERO)<=0);var c=a.modInverse(i).multiply(s.add(r.multiply(u))).mod(i);return o.biRSSigToASN1Sig(u,c)},this.sign=function(t,e){var r=e,i=this.ecparams.n,o=n.fromByteArrayUnsigned(t);do{var s=this.getBigRandom(i),a=this.ecparams.G.multiply(s).getX().toBigInteger().mod(i)}while(a.compareTo(E.ZERO)<=0);var u=s.modInverse(i).multiply(o.add(r.multiply(a))).mod(i);return this.serializeSig(a,u)},this.verifyWithMessageHash=function(t,e){return this.verifyHex(t,e,this.pubKeyHex)},this.verifyHex=function(t,e,r){try{var s,a,u=o.parseSigHex(e);s=u.r,a=u.s;var c=i.decodeFromHex(this.ecparams.curve,r),f=new n(t.substring(0,this.ecparams.keylen/4),16);return this.verifyRaw(f,s,a,c)}catch(t){return!1}},this.verify=function(t,e,o){var s,a,u;if(Bitcoin.Util.isArray(e)){var c=this.parseSig(e);s=c.r,a=c.s}else{if("object"!==(void 0===e?"undefined":r(e))||!e.r||!e.s)throw"Invalid value for signature";s=e.r,a=e.s}if(o instanceof st)u=o;else{if(!Bitcoin.Util.isArray(o))throw"Invalid format for pubkey value, must be byte array or ECPointFp";u=i.decodeFrom(this.ecparams.curve,o)}var f=n.fromByteArrayUnsigned(t);return this.verifyRaw(f,s,a,u)},this.verifyRaw=function(t,e,r,i){var o=this.ecparams.n,s=this.ecparams.G;if(e.compareTo(n.ONE)<0||e.compareTo(o)>=0)return!1;if(r.compareTo(n.ONE)<0||r.compareTo(o)>=0)return!1;var a=r.modInverse(o),u=t.multiply(a).mod(o),c=e.multiply(a).mod(o);return s.multiply(u).add(i.multiply(c)).getX().toBigInteger().mod(o).equals(e)},this.serializeSig=function(t,e){var r=t.toByteArraySigned(),n=e.toByteArraySigned(),i=[];return i.push(2),i.push(r.length),(i=i.concat(r)).push(2),i.push(n.length),(i=i.concat(n)).unshift(i.length),i.unshift(48),i},this.parseSig=function(t){var e;if(48!=t[0])throw new Error("Signature not a valid DERSequence");if(2!=t[e=2])throw new Error("First element in signature must be a DERInteger");var r=t.slice(e+2,e+2+t[e+1]);if(2!=t[e+=2+t[e+1]])throw new Error("Second element in signature must be a DERInteger");var i=t.slice(e+2,e+2+t[e+1]);return e+=2+t[e+1],{r:n.fromByteArrayUnsigned(r),s:n.fromByteArrayUnsigned(i)}},this.parseSigCompact=function(t){if(65!==t.length)throw"Signature has the wrong length";var e=t[0]-27;if(e<0||e>7)throw"Invalid signature type";var r=this.ecparams.n;return{r:n.fromByteArrayUnsigned(t.slice(1,33)).mod(r),s:n.fromByteArrayUnsigned(t.slice(33,65)).mod(r),i:e}},this.readPKCS5PrvKeyHex=function(t){if(!1===f(t))throw new Error("not ASN.1 hex string");var e,r,n;try{e=c(t,0,["[0]",0],"06"),r=c(t,0,[1],"04");try{n=c(t,0,["[1]",0],"03")}catch(t){}}catch(t){throw new Error("malformed PKCS#1/5 plain ECC private key")}if(this.curveName=a(e),void 0===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(n),this.setPrivateKeyHex(r),this.isPublic=!1},this.readPKCS8PrvKeyHex=function(t){if(!1===f(t))throw new e("not ASN.1 hex string");var r,n,i;try{c(t,0,[1,0],"06"),r=c(t,0,[1,1],"06"),n=c(t,0,[2,0,1],"04");try{i=c(t,0,[2,0,"[1]",0],"03")}catch(t){}}catch(t){throw new e("malformed PKCS#8 plain ECC private key")}if(this.curveName=a(r),void 0===this.curveName)throw new e("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(i),this.setPrivateKeyHex(n),this.isPublic=!1},this.readPKCS8PubKeyHex=function(t){if(!1===f(t))throw new e("not ASN.1 hex string");var r,n;try{c(t,0,[0,0],"06"),r=c(t,0,[0,1],"06"),n=c(t,0,[1],"03")}catch(t){throw new e("malformed PKCS#8 ECC public key")}if(this.curveName=a(r),null===this.curveName)throw new e("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(n)},this.readCertPubKeyHex=function(t,r){if(!1===f(t))throw new e("not ASN.1 hex string");var n,i;try{n=c(t,0,[0,5,0,1],"06"),i=c(t,0,[0,5,1],"03")}catch(t){throw new e("malformed X.509 certificate ECC public key")}if(this.curveName=a(n),null===this.curveName)throw new e("unsupported curve name");this.setNamedCurve(this.curveName),this.setPublicKeyHex(i)},void 0!==t&&void 0!==t.curve&&(this.curveName=t.curve),void 0===this.curveName&&(this.curveName="secp256r1"),this.setNamedCurve(this.curveName),void 0!==t&&(void 0!==t.prv&&this.setPrivateKeyHex(t.prv),void 0!==t.pub&&this.setPublicKeyHex(t.pub))},ct.crypto.ECDSA.parseSigHex=function(t){var e=ct.crypto.ECDSA.parseSigHexInHexRS(t);return{r:new E(e.r,16),s:new E(e.s,16)}},ct.crypto.ECDSA.parseSigHexInHexRS=function(t){var e=lt,r=e.getChildIdx,n=e.getV;if(e.checkStrictDER(t,0),"30"!=t.substr(0,2))throw new Error("signature is not a ASN.1 sequence");var i=r(t,0);if(2!=i.length)throw new Error("signature shall have two elements");var o=i[0],s=i[1];if("02"!=t.substr(o,2))throw new Error("1st item not ASN.1 integer");if("02"!=t.substr(s,2))throw new Error("2nd item not ASN.1 integer");return{r:n(t,o),s:n(t,s)}},ct.crypto.ECDSA.asn1SigToConcatSig=function(t){var e=ct.crypto.ECDSA.parseSigHexInHexRS(t),r=e.r,n=e.s;if("00"==r.substr(0,2)&&r.length%32==2&&(r=r.substr(2)),"00"==n.substr(0,2)&&n.length%32==2&&(n=n.substr(2)),r.length%32==30&&(r="00"+r),n.length%32==30&&(n="00"+n),r.length%32!=0)throw"unknown ECDSA sig r length error";if(n.length%32!=0)throw"unknown ECDSA sig s length error";return r+n},ct.crypto.ECDSA.concatSigToASN1Sig=function(t){if(t.length/2*8%128!=0)throw"unknown ECDSA concatinated r-s sig  length error";var e=t.substr(0,t.length/2),r=t.substr(t.length/2);return ct.crypto.ECDSA.hexRSSigToASN1Sig(e,r)},ct.crypto.ECDSA.hexRSSigToASN1Sig=function(t,e){var r=new E(t,16),n=new E(e,16);return ct.crypto.ECDSA.biRSSigToASN1Sig(r,n)},ct.crypto.ECDSA.biRSSigToASN1Sig=function(t,e){var r=ct.asn1,n=new r.DERInteger({bigint:t}),i=new r.DERInteger({bigint:e});return new r.DERSequence({array:[n,i]}).getEncodedHex()},ct.crypto.ECDSA.getName=function(t){return"2b8104001f"===t?"secp192k1":"2a8648ce3d030107"===t?"secp256r1":"2b8104000a"===t?"secp256k1":"2b81040021"===t?"secp224r1":"2b81040022"===t?"secp384r1":-1!=="|secp256r1|NIST P-256|P-256|prime256v1|".indexOf(t)?"secp256r1":-1!=="|secp256k1|".indexOf(t)?"secp256k1":-1!=="|secp224r1|NIST P-224|P-224|".indexOf(t)?"secp224r1":-1!=="|secp384r1|NIST P-384|P-384|".indexOf(t)?"secp384r1":null},void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.crypto&&ct.crypto||(ct.crypto={}),ct.crypto.ECParameterDB=new function(){var t={},e={};function r(t){return new E(t,16)}this.getByName=function(r){var n=r;if(void 0!==e[n]&&(n=e[r]),void 0!==t[n])return t[n];throw"unregistered EC curve name: "+n},this.regist=function(n,i,o,s,a,u,c,f,h,l,p,g){t[n]={};var d=r(o),v=r(s),y=r(a),m=r(u),b=r(c),S=new at(d,v,y),w=S.decodePointHex("04"+f+h);t[n].name=n,t[n].keylen=i,t[n].curve=S,t[n].G=w,t[n].n=m,t[n].h=b,t[n].oid=p,t[n].info=g;for(var _=0;_<l.length;_++)e[l[_]]=n}},ct.crypto.ECParameterDB.regist("secp128r1",128,"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC","E87579C11079F43DD824993C2CEE5ED3","FFFFFFFE0000000075A30D1B9038A115","1","161FF7528B899B2D0C28607CA52C5B86","CF5AC8395BAFEB13C02DA292DDED7A83",[],"","secp128r1 : SECG curve over a 128 bit prime field"),ct.crypto.ECParameterDB.regist("secp160k1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73","0","7","0100000000000000000001B8FA16DFAB9ACA16B6B3","1","3B4C382CE37AA192A4019E763036F4F5DD4D7EBB","938CF935318FDCED6BC28286531733C3F03C4FEE",[],"","secp160k1 : SECG curve over a 160 bit prime field"),ct.crypto.ECParameterDB.regist("secp160r1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC","****************************************","0100000000000000000001F4C8F927AED3CA752257","1","4A96B5688EF573284664698968C38BB913CBFC82","23A628553168947D59DCC912042351377AC5FB32",[],"","secp160r1 : SECG curve over a 160 bit prime field"),ct.crypto.ECParameterDB.regist("secp192k1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37","0","3","FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D","1","DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D","9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D",[]),ct.crypto.ECParameterDB.regist("secp192r1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC","64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1","FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831","1","188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012","07192B95FFC8DA78631011ED6B24CDD573F977A11E794811",[]),ct.crypto.ECParameterDB.regist("secp224r1",224,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE","B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4","FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D","1","B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21","BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34",[]),ct.crypto.ECParameterDB.regist("secp256k1",256,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F","0","7","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141","1","79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798","483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8",[]),ct.crypto.ECParameterDB.regist("secp256r1",256,"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC","5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B","FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551","1","6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296","4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5",["NIST P-256","P-256","prime256v1"]),ct.crypto.ECParameterDB.regist("secp384r1",384,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC","B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973","1","AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB7","3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f",["NIST P-384","P-384"]),ct.crypto.ECParameterDB.regist("secp521r1",521,"1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC","051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409","1","C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66","011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650",["NIST P-521","P-521"]);var Ht=function(){var t=function(t,r,n){return e(y.AES,t,r,n)},e=function(t,e,r,n){var i=y.enc.Hex.parse(e),o=y.enc.Hex.parse(r),s=y.enc.Hex.parse(n),a={};a.key=o,a.iv=s,a.ciphertext=i;var u=t.decrypt(a,o,{iv:s});return y.enc.Hex.stringify(u)},r=function(t,e,r){return n(y.AES,t,e,r)},n=function(t,e,r,n){var i=y.enc.Hex.parse(e),o=y.enc.Hex.parse(r),s=y.enc.Hex.parse(n),a=t.encrypt(i,o,{iv:s}),u=y.enc.Hex.parse(a.toString());return y.enc.Base64.stringify(u)},i={"AES-256-CBC":{proc:t,eproc:r,keylen:32,ivlen:16},"AES-192-CBC":{proc:t,eproc:r,keylen:24,ivlen:16},"AES-128-CBC":{proc:t,eproc:r,keylen:16,ivlen:16},"DES-EDE3-CBC":{proc:function(t,r,n){return e(y.TripleDES,t,r,n)},eproc:function(t,e,r){return n(y.TripleDES,t,e,r)},keylen:24,ivlen:8},"DES-CBC":{proc:function(t,r,n){return e(y.DES,t,r,n)},eproc:function(t,e,r){return n(y.DES,t,e,r)},keylen:8,ivlen:8}},o=function(t){var e={},r=t.match(new RegExp("DEK-Info: ([^,]+),([0-9A-Fa-f]+)","m"));r&&(e.cipher=r[1],e.ivsalt=r[2]);var n=t.match(new RegExp("-----BEGIN ([A-Z]+) PRIVATE KEY-----"));n&&(e.type=n[1]);var i=-1,o=0;-1!=t.indexOf("\r\n\r\n")&&(i=t.indexOf("\r\n\r\n"),o=2),-1!=t.indexOf("\n\n")&&(i=t.indexOf("\n\n"),o=1);var s=t.indexOf("-----END");if(-1!=i&&-1!=s){var a=t.substring(i+2*o,s-o);a=a.replace(/\s+/g,""),e.data=a}return e},s=function(t,e,r){for(var n=r.substring(0,16),o=y.enc.Hex.parse(n),s=y.enc.Utf8.parse(e),a=i[t].keylen+i[t].ivlen,u="",c=null;;){var f=y.algo.MD5.create();if(null!=c&&f.update(c),f.update(s),f.update(o),c=f.finalize(),(u+=y.enc.Hex.stringify(c)).length>=2*a)break}var h={};return h.keyhex=u.substr(0,2*i[t].keylen),h.ivhex=u.substr(2*i[t].keylen,2*i[t].ivlen),h},a=function(t,e,r,n){var o=y.enc.Base64.parse(t),s=y.enc.Hex.stringify(o);return(0,i[e].proc)(s,r,n)};return{version:"1.0.0",parsePKCS5PEM:function(t){return o(t)},getKeyAndUnusedIvByPasscodeAndIvsalt:function(t,e,r){return s(t,e,r)},decryptKeyB64:function(t,e,r,n){return a(t,e,r,n)},getDecryptedKeyHex:function(t,e){var r=o(t),n=(r.type,r.cipher),i=r.ivsalt,u=r.data,c=s(n,e,i).keyhex;return a(u,n,c,i)},getEncryptedPKCS5PEMFromPrvKeyHex:function(t,e,r,n,o){var a="";if(void 0!==n&&null!=n||(n="AES-256-CBC"),void 0===i[n])throw"KEYUTIL unsupported algorithm: "+n;return void 0!==o&&null!=o||(o=function(t){var e=y.lib.WordArray.random(t);return y.enc.Hex.stringify(e)}(i[n].ivlen).toUpperCase()),a="-----BEGIN "+t+" PRIVATE KEY-----\r\n",a+="Proc-Type: 4,ENCRYPTED\r\n",a+="DEK-Info: "+n+","+o+"\r\n",a+="\r\n",(a+=function(t,e,r,n){return(0,i[e].eproc)(t,r,n)}(e,n,s(n,r,o).keyhex,o).replace(/(.{64})/g,"$1\r\n"))+"\r\n-----END "+t+" PRIVATE KEY-----\r\n"},parseHexOfEncryptedPKCS8:function(t){var e=lt,r=e.getChildIdx,n=e.getV,i={},o=r(t,0);if(2!=o.length)throw"malformed format: SEQUENCE(0).items != 2: "+o.length;i.ciphertext=n(t,o[1]);var s=r(t,o[0]);if(2!=s.length)throw"malformed format: SEQUENCE(0.0).items != 2: "+s.length;if("2a864886f70d01050d"!=n(t,s[0]))throw"this only supports pkcs5PBES2";var a=r(t,s[1]);if(2!=s.length)throw"malformed format: SEQUENCE(0.0.1).items != 2: "+a.length;var u=r(t,a[1]);if(2!=u.length)throw"malformed format: SEQUENCE(*******).items != 2: "+u.length;if("2a864886f70d0307"!=n(t,u[0]))throw"this only supports TripleDES";i.encryptionSchemeAlg="TripleDES",i.encryptionSchemeIV=n(t,u[1]);var c=r(t,a[0]);if(2!=c.length)throw"malformed format: SEQUENCE(*******).items != 2: "+c.length;if("2a864886f70d01050c"!=n(t,c[0]))throw"this only supports pkcs5PBKDF2";var f=r(t,c[1]);if(f.length<2)throw"malformed format: SEQUENCE(*******.1).items < 2: "+f.length;i.pbkdf2Salt=n(t,f[0]);var h=n(t,f[1]);try{i.pbkdf2Iter=parseInt(h,16)}catch(t){throw"malformed format pbkdf2Iter: "+h}return i},getPBKDF2KeyHexFromParam:function(t,e){var r=y.enc.Hex.parse(t.pbkdf2Salt),n=t.pbkdf2Iter,i=y.PBKDF2(e,r,{keySize:6,iterations:n});return y.enc.Hex.stringify(i)},_getPlainPKCS8HexFromEncryptedPKCS8PEM:function(t,e){var r=Pt(t,"ENCRYPTED PRIVATE KEY"),n=this.parseHexOfEncryptedPKCS8(r),i=Ht.getPBKDF2KeyHexFromParam(n,e),o={};o.ciphertext=y.enc.Hex.parse(n.ciphertext);var s=y.enc.Hex.parse(i),a=y.enc.Hex.parse(n.encryptionSchemeIV),u=y.TripleDES.decrypt(o,s,{iv:a});return y.enc.Hex.stringify(u)},getKeyFromEncryptedPKCS8PEM:function(t,e){var r=this._getPlainPKCS8HexFromEncryptedPKCS8PEM(t,e);return this.getKeyFromPlainPrivatePKCS8Hex(r)},parsePlainPrivatePKCS8Hex:function(t){var e=lt,r=e.getChildIdx,n=e.getV,i={algparam:null};if("30"!=t.substr(0,2))throw"malformed plain PKCS8 private key(code:001)";var o=r(t,0);if(3!=o.length)throw"malformed plain PKCS8 private key(code:002)";if("30"!=t.substr(o[1],2))throw"malformed PKCS8 private key(code:003)";var s=r(t,o[1]);if(2!=s.length)throw"malformed PKCS8 private key(code:004)";if("06"!=t.substr(s[0],2))throw"malformed PKCS8 private key(code:005)";if(i.algoid=n(t,s[0]),"06"==t.substr(s[1],2)&&(i.algparam=n(t,s[1])),"04"!=t.substr(o[2],2))throw"malformed PKCS8 private key(code:006)";return i.keyidx=e.getVidx(t,o[2]),i},getKeyFromPlainPrivatePKCS8PEM:function(t){var e=Pt(t,"PRIVATE KEY");return this.getKeyFromPlainPrivatePKCS8Hex(e)},getKeyFromPlainPrivatePKCS8Hex:function(t){var e,r=this.parsePlainPrivatePKCS8Hex(t);if("2a864886f70d010101"==r.algoid)e=new it;else if("2a8648ce380401"==r.algoid)e=new ct.crypto.DSA;else{if("2a8648ce3d0201"!=r.algoid)throw"unsupported private key algorithm";e=new ct.crypto.ECDSA}return e.readPKCS8PrvKeyHex(t),e},_getKeyFromPublicPKCS8Hex:function(t){var e,r=lt.getVbyList(t,0,[0,0],"06");if("2a864886f70d010101"===r)e=new it;else if("2a8648ce380401"===r)e=new ct.crypto.DSA;else{if("2a8648ce3d0201"!==r)throw"unsupported PKCS#8 public key hex";e=new ct.crypto.ECDSA}return e.readPKCS8PubKeyHex(t),e},parsePublicRawRSAKeyHex:function(t){var e=lt,r=e.getChildIdx,n=e.getV,i={};if("30"!=t.substr(0,2))throw"malformed RSA key(code:001)";var o=r(t,0);if(2!=o.length)throw"malformed RSA key(code:002)";if("02"!=t.substr(o[0],2))throw"malformed RSA key(code:003)";if(i.n=n(t,o[0]),"02"!=t.substr(o[1],2))throw"malformed RSA key(code:004)";return i.e=n(t,o[1]),i},parsePublicPKCS8Hex:function(t){var e=lt,r=e.getChildIdx,n=e.getV,i={algparam:null},o=r(t,0);if(2!=o.length)throw"outer DERSequence shall have 2 elements: "+o.length;var s=o[0];if("30"!=t.substr(s,2))throw"malformed PKCS8 public key(code:001)";var a=r(t,s);if(2!=a.length)throw"malformed PKCS8 public key(code:002)";if("06"!=t.substr(a[0],2))throw"malformed PKCS8 public key(code:003)";if(i.algoid=n(t,a[0]),"06"==t.substr(a[1],2)?i.algparam=n(t,a[1]):"30"==t.substr(a[1],2)&&(i.algparam={},i.algparam.p=e.getVbyList(t,a[1],[0],"02"),i.algparam.q=e.getVbyList(t,a[1],[1],"02"),i.algparam.g=e.getVbyList(t,a[1],[2],"02")),"03"!=t.substr(o[1],2))throw"malformed PKCS8 public key(code:004)";return i.key=n(t,o[1]).substr(2),i}}}();Ht.getKey=function(t,e,r){var n,i=(y=lt).getChildIdx,o=(y.getV,y.getVbyList),s=ct.crypto,a=s.ECDSA,u=s.DSA,c=it,f=Pt,h=Ht;if(void 0!==c&&t instanceof c)return t;if(void 0!==a&&t instanceof a)return t;if(void 0!==u&&t instanceof u)return t;if(void 0!==t.curve&&void 0!==t.xy&&void 0===t.d)return new a({pub:t.xy,curve:t.curve});if(void 0!==t.curve&&void 0!==t.d)return new a({prv:t.d,curve:t.curve});if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d)return(P=new c).setPublic(t.n,t.e),P;if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.co&&void 0===t.qi)return(P=new c).setPrivateEx(t.n,t.e,t.d,t.p,t.q,t.dp,t.dq,t.co),P;if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0===t.p)return(P=new c).setPrivate(t.n,t.e,t.d),P;if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0===t.x)return(P=new u).setPublic(t.p,t.q,t.g,t.y),P;if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0!==t.x)return(P=new u).setPrivate(t.p,t.q,t.g,t.y,t.x),P;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d)return(P=new c).setPublic(St(t.n),St(t.e)),P;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.qi)return(P=new c).setPrivateEx(St(t.n),St(t.e),St(t.d),St(t.p),St(t.q),St(t.dp),St(t.dq),St(t.qi)),P;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d)return(P=new c).setPrivate(St(t.n),St(t.e),St(t.d)),P;if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0===t.d){var l=(k=new a({curve:t.crv})).ecparams.keylen/4,p="04"+("0000000000"+St(t.x)).slice(-l)+("0000000000"+St(t.y)).slice(-l);return k.setPublicKeyHex(p),k}if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0!==t.d){l=(k=new a({curve:t.crv})).ecparams.keylen/4,p="04"+("0000000000"+St(t.x)).slice(-l)+("0000000000"+St(t.y)).slice(-l);var g=("0000000000"+St(t.d)).slice(-l);return k.setPublicKeyHex(p),k.setPrivateKeyHex(g),k}if("pkcs5prv"===r){var d,v=t,y=lt;if(9===(d=i(v,0)).length)(P=new c).readPKCS5PrvKeyHex(v);else if(6===d.length)(P=new u).readPKCS5PrvKeyHex(v);else{if(!(d.length>2&&"04"===v.substr(d[1],2)))throw"unsupported PKCS#1/5 hexadecimal key";(P=new a).readPKCS5PrvKeyHex(v)}return P}if("pkcs8prv"===r)return h.getKeyFromPlainPrivatePKCS8Hex(t);if("pkcs8pub"===r)return h._getKeyFromPublicPKCS8Hex(t);if("x509pub"===r)return Jt.getPublicKeyFromCertHex(t);if(-1!=t.indexOf("-END CERTIFICATE-",0)||-1!=t.indexOf("-END X509 CERTIFICATE-",0)||-1!=t.indexOf("-END TRUSTED CERTIFICATE-",0))return Jt.getPublicKeyFromCertPEM(t);if(-1!=t.indexOf("-END PUBLIC KEY-")){var m=Pt(t,"PUBLIC KEY");return h._getKeyFromPublicPKCS8Hex(m)}if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var b=f(t,"RSA PRIVATE KEY");return h.getKey(b,null,"pkcs5prv")}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var S=o(n=f(t,"DSA PRIVATE KEY"),0,[1],"02"),w=o(n,0,[2],"02"),_=o(n,0,[3],"02"),x=o(n,0,[4],"02"),F=o(n,0,[5],"02");return(P=new u).setPrivate(new E(S,16),new E(w,16),new E(_,16),new E(x,16),new E(F,16)),P}if(-1!=t.indexOf("-END EC PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED"))return b=f(t,"EC PRIVATE KEY"),h.getKey(b,null,"pkcs5prv");if(-1!=t.indexOf("-END PRIVATE KEY-"))return h.getKeyFromPlainPrivatePKCS8PEM(t);if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var A=h.getDecryptedKeyHex(t,e),T=new it;return T.readPKCS5PrvKeyHex(A),T}if(-1!=t.indexOf("-END EC PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var k,P=o(n=h.getDecryptedKeyHex(t,e),0,[1],"04"),R=o(n,0,[2,0],"06"),I=o(n,0,[3,0],"03").substr(2);if(void 0===ct.crypto.OID.oidhex2name[R])throw"undefined OID(hex) in KJUR.crypto.OID: "+R;return(k=new a({curve:ct.crypto.OID.oidhex2name[R]})).setPublicKeyHex(I),k.setPrivateKeyHex(P),k.isPublic=!1,k}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED"))return S=o(n=h.getDecryptedKeyHex(t,e),0,[1],"02"),w=o(n,0,[2],"02"),_=o(n,0,[3],"02"),x=o(n,0,[4],"02"),F=o(n,0,[5],"02"),(P=new u).setPrivate(new E(S,16),new E(w,16),new E(_,16),new E(x,16),new E(F,16)),P;if(-1!=t.indexOf("-END ENCRYPTED PRIVATE KEY-"))return h.getKeyFromEncryptedPKCS8PEM(t,e);throw new Error("not supported argument")},Ht.generateKeypair=function(t,e){if("RSA"==t){var r=e;(s=new it).generate(r,"10001"),s.isPrivate=!0,s.isPublic=!0;var n=new it,i=s.n.toString(16),o=s.e.toString(16);return n.setPublic(i,o),n.isPrivate=!1,n.isPublic=!0,(a={}).prvKeyObj=s,a.pubKeyObj=n,a}if("EC"==t){var s,a,u=e,c=new ct.crypto.ECDSA({curve:u}).generateKeyPairHex();return(s=new ct.crypto.ECDSA({curve:u})).setPublicKeyHex(c.ecpubhex),s.setPrivateKeyHex(c.ecprvhex),s.isPrivate=!0,s.isPublic=!1,(n=new ct.crypto.ECDSA({curve:u})).setPublicKeyHex(c.ecpubhex),n.isPrivate=!1,n.isPublic=!0,(a={}).prvKeyObj=s,a.pubKeyObj=n,a}throw"unknown algorithm: "+t},Ht.getPEM=function(t,e,r,n,i,o){var s=ct,a=s.asn1,u=a.DERObjectIdentifier,c=a.DERInteger,f=a.ASN1Util.newObject,h=a.x509.SubjectPublicKeyInfo,l=s.crypto,p=l.DSA,g=l.ECDSA,d=it;function v(t){return f({seq:[{int:0},{int:{bigint:t.n}},{int:t.e},{int:{bigint:t.d}},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.dmp1}},{int:{bigint:t.dmq1}},{int:{bigint:t.coeff}}]})}function m(t){return f({seq:[{int:1},{octstr:{hex:t.prvKeyHex}},{tag:["a0",!0,{oid:{name:t.curveName}}]},{tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]}]})}function b(t){return f({seq:[{int:0},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}},{int:{bigint:t.y}},{int:{bigint:t.x}}]})}if((void 0!==d&&t instanceof d||void 0!==p&&t instanceof p||void 0!==g&&t instanceof g)&&1==t.isPublic&&(void 0===e||"PKCS8PUB"==e))return kt(E=new h(t).getEncodedHex(),"PUBLIC KEY");if("PKCS1PRV"==e&&void 0!==d&&t instanceof d&&(void 0===r||null==r)&&1==t.isPrivate)return kt(E=v(t).getEncodedHex(),"RSA PRIVATE KEY");if("PKCS1PRV"==e&&void 0!==g&&t instanceof g&&(void 0===r||null==r)&&1==t.isPrivate){var S=new u({name:t.curveName}).getEncodedHex(),w=m(t).getEncodedHex(),_="";return(_+=kt(S,"EC PARAMETERS"))+kt(w,"EC PRIVATE KEY")}if("PKCS1PRV"==e&&void 0!==p&&t instanceof p&&(void 0===r||null==r)&&1==t.isPrivate)return kt(E=b(t).getEncodedHex(),"DSA PRIVATE KEY");if("PKCS5PRV"==e&&void 0!==d&&t instanceof d&&void 0!==r&&null!=r&&1==t.isPrivate){var E=v(t).getEncodedHex();return void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("RSA",E,r,n,o)}if("PKCS5PRV"==e&&void 0!==g&&t instanceof g&&void 0!==r&&null!=r&&1==t.isPrivate)return E=m(t).getEncodedHex(),void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("EC",E,r,n,o);if("PKCS5PRV"==e&&void 0!==p&&t instanceof p&&void 0!==r&&null!=r&&1==t.isPrivate)return E=b(t).getEncodedHex(),void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("DSA",E,r,n,o);var x=function(t,e){var r=F(t,e);return new f({seq:[{seq:[{oid:{name:"pkcs5PBES2"}},{seq:[{seq:[{oid:{name:"pkcs5PBKDF2"}},{seq:[{octstr:{hex:r.pbkdf2Salt}},{int:r.pbkdf2Iter}]}]},{seq:[{oid:{name:"des-EDE3-CBC"}},{octstr:{hex:r.encryptionSchemeIV}}]}]}]},{octstr:{hex:r.ciphertext}}]}).getEncodedHex()},F=function(t,e){var r=y.lib.WordArray.random(8),n=y.lib.WordArray.random(8),i=y.PBKDF2(e,r,{keySize:6,iterations:100}),o=y.enc.Hex.parse(t),s=y.TripleDES.encrypt(o,i,{iv:n})+"",a={};return a.ciphertext=s,a.pbkdf2Salt=y.enc.Hex.stringify(r),a.pbkdf2Iter=100,a.encryptionSchemeAlg="DES-EDE3-CBC",a.encryptionSchemeIV=y.enc.Hex.stringify(n),a};if("PKCS8PRV"==e&&null!=d&&t instanceof d&&1==t.isPrivate){var A=v(t).getEncodedHex();return E=f({seq:[{int:0},{seq:[{oid:{name:"rsaEncryption"}},{null:!0}]},{octstr:{hex:A}}]}).getEncodedHex(),void 0===r||null==r?kt(E,"PRIVATE KEY"):kt(w=x(E,r),"ENCRYPTED PRIVATE KEY")}if("PKCS8PRV"==e&&void 0!==g&&t instanceof g&&1==t.isPrivate)return A=new f({seq:[{int:1},{octstr:{hex:t.prvKeyHex}},{tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]}]}).getEncodedHex(),E=f({seq:[{int:0},{seq:[{oid:{name:"ecPublicKey"}},{oid:{name:t.curveName}}]},{octstr:{hex:A}}]}).getEncodedHex(),void 0===r||null==r?kt(E,"PRIVATE KEY"):kt(w=x(E,r),"ENCRYPTED PRIVATE KEY");if("PKCS8PRV"==e&&void 0!==p&&t instanceof p&&1==t.isPrivate)return A=new c({bigint:t.x}).getEncodedHex(),E=f({seq:[{int:0},{seq:[{oid:{name:"dsa"}},{seq:[{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}}]}]},{octstr:{hex:A}}]}).getEncodedHex(),void 0===r||null==r?kt(E,"PRIVATE KEY"):kt(w=x(E,r),"ENCRYPTED PRIVATE KEY");throw new Error("unsupported object nor format")},Ht.getKeyFromCSRPEM=function(t){var e=Pt(t,"CERTIFICATE REQUEST");return Ht.getKeyFromCSRHex(e)},Ht.getKeyFromCSRHex=function(t){var e=Ht.parseCSRHex(t);return Ht.getKey(e.p8pubkeyhex,null,"pkcs8pub")},Ht.parseCSRHex=function(t){var e=lt,r=e.getChildIdx,n=e.getTLV,i={},o=t;if("30"!=o.substr(0,2))throw"malformed CSR(code:001)";var s=r(o,0);if(s.length<1)throw"malformed CSR(code:002)";if("30"!=o.substr(s[0],2))throw"malformed CSR(code:003)";var a=r(o,s[0]);if(a.length<3)throw"malformed CSR(code:004)";return i.p8pubkeyhex=n(o,a[2]),i},Ht.getKeyID=function(t){var e=Ht,r=lt;"string"==typeof t&&-1!=t.indexOf("BEGIN ")&&(t=e.getKey(t));var n=Pt(e.getPEM(t)),i=r.getIdxbyList(n,0,[1]),o=r.getV(n,i).substring(2);return ct.crypto.Util.hashHex(o,"sha1")},Ht.getJWKFromKey=function(t){var e={};if(t instanceof it&&t.isPrivate)return e.kty="RSA",e.n=bt(t.n.toString(16)),e.e=bt(t.e.toString(16)),e.d=bt(t.d.toString(16)),e.p=bt(t.p.toString(16)),e.q=bt(t.q.toString(16)),e.dp=bt(t.dmp1.toString(16)),e.dq=bt(t.dmq1.toString(16)),e.qi=bt(t.coeff.toString(16)),e;if(t instanceof it&&t.isPublic)return e.kty="RSA",e.n=bt(t.n.toString(16)),e.e=bt(t.e.toString(16)),e;if(t instanceof ct.crypto.ECDSA&&t.isPrivate){if("P-256"!==(n=t.getShortNISTPCurveName())&&"P-384"!==n)throw"unsupported curve name for JWT: "+n;var r=t.getPublicKeyXYHex();return e.kty="EC",e.crv=n,e.x=bt(r.x),e.y=bt(r.y),e.d=bt(t.prvKeyHex),e}if(t instanceof ct.crypto.ECDSA&&t.isPublic){var n;if("P-256"!==(n=t.getShortNISTPCurveName())&&"P-384"!==n)throw"unsupported curve name for JWT: "+n;return r=t.getPublicKeyXYHex(),e.kty="EC",e.crv=n,e.x=bt(r.x),e.y=bt(r.y),e}throw"not supported key object"},it.getPosArrayOfChildrenFromHex=function(t){return lt.getChildIdx(t,0)},it.getHexValueArrayOfChildrenFromHex=function(t){var e,r=lt.getV,n=r(t,(e=it.getPosArrayOfChildrenFromHex(t))[0]),i=r(t,e[1]),o=r(t,e[2]),s=r(t,e[3]),a=r(t,e[4]),u=r(t,e[5]),c=r(t,e[6]),f=r(t,e[7]),h=r(t,e[8]);return(e=new Array).push(n,i,o,s,a,u,c,f,h),e},it.prototype.readPrivateKeyFromPEMString=function(t){var e=Pt(t),r=it.getHexValueArrayOfChildrenFromHex(e);this.setPrivateEx(r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8])},it.prototype.readPKCS5PrvKeyHex=function(t){var e=it.getHexValueArrayOfChildrenFromHex(t);this.setPrivateEx(e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},it.prototype.readPKCS8PrvKeyHex=function(t){var e,r,n,i,o,s,a,u,c=lt,f=c.getVbyListEx;if(!1===c.isASN1HEX(t))throw new Error("not ASN.1 hex string");try{e=f(t,0,[2,0,1],"02"),r=f(t,0,[2,0,2],"02"),n=f(t,0,[2,0,3],"02"),i=f(t,0,[2,0,4],"02"),o=f(t,0,[2,0,5],"02"),s=f(t,0,[2,0,6],"02"),a=f(t,0,[2,0,7],"02"),u=f(t,0,[2,0,8],"02")}catch(t){throw new Error("malformed PKCS#8 plain RSA private key")}this.setPrivateEx(e,r,n,i,o,s,a,u)},it.prototype.readPKCS5PubKeyHex=function(t){var e=lt,r=e.getV;if(!1===e.isASN1HEX(t))throw new Error("keyHex is not ASN.1 hex string");var n=e.getChildIdx(t,0);if(2!==n.length||"02"!==t.substr(n[0],2)||"02"!==t.substr(n[1],2))throw new Error("wrong hex for PKCS#5 public key");var i=r(t,n[0]),o=r(t,n[1]);this.setPublic(i,o)},it.prototype.readPKCS8PubKeyHex=function(t){var e=lt;if(!1===e.isASN1HEX(t))throw new Error("not ASN.1 hex string");if("06092a864886f70d010101"!==e.getTLVbyListEx(t,0,[0,0]))throw new Error("not PKCS8 RSA public key");var r=e.getTLVbyListEx(t,0,[1,0]);this.readPKCS5PubKeyHex(r)},it.prototype.readCertPubKeyHex=function(t,e){var r,n;(r=new Jt).readCertHex(t),n=r.getPublicKeyHex(),this.readPKCS8PubKeyHex(n)};var Vt=new RegExp("[^0-9a-f]","gi");function qt(t,e){for(var r="",n=e/4-t.length,i=0;i<n;i++)r+="0";return r+t}function Kt(t,e,r){for(var n="",i=0;n.length<e;)n+=Et(r(xt(t+String.fromCharCode.apply(String,[(4278190080&i)>>24,(16711680&i)>>16,(65280&i)>>8,255&i])))),i+=1;return n}function Wt(t){for(var e in ct.crypto.Util.DIGESTINFOHEAD){var r=ct.crypto.Util.DIGESTINFOHEAD[e],n=r.length;if(t.substring(0,n)==r)return[e,t.substring(n)]}return[]}function Jt(t){var e,r=lt,n=r.getChildIdx,i=r.getV,o=r.getTLV,s=r.getVbyList,a=r.getVbyListEx,u=r.getTLVbyList,c=r.getTLVbyListEx,f=r.getIdxbyList,h=r.getIdxbyListEx,l=r.getVidx,p=r.oidname,g=r.hextooidstr,d=Jt,v=Pt;try{e=ct.asn1.x509.AlgorithmIdentifier.PSSNAME2ASN1TLV}catch(t){}this.HEX2STAG={"0c":"utf8",13:"prn",16:"ia5","1a":"vis","1e":"bmp"},this.hex=null,this.version=0,this.foffset=0,this.aExtInfo=null,this.getVersion=function(){return null===this.hex||0!==this.version?this.version:"a003020102"!==u(this.hex,0,[0,0])?(this.version=1,this.foffset=-1,1):(this.version=3,3)},this.getSerialNumberHex=function(){return a(this.hex,0,[0,0],"02")},this.getSignatureAlgorithmField=function(){var t=c(this.hex,0,[0,1]);return this.getAlgorithmIdentifierName(t)},this.getAlgorithmIdentifierName=function(t){for(var r in e)if(t===e[r])return r;return p(a(t,0,[0],"06"))},this.getIssuer=function(){return this.getX500Name(this.getIssuerHex())},this.getIssuerHex=function(){return u(this.hex,0,[0,3+this.foffset],"30")},this.getIssuerString=function(){return d.hex2dn(this.getIssuerHex())},this.getSubject=function(){return this.getX500Name(this.getSubjectHex())},this.getSubjectHex=function(){return u(this.hex,0,[0,5+this.foffset],"30")},this.getSubjectString=function(){return d.hex2dn(this.getSubjectHex())},this.getNotBefore=function(){var t=s(this.hex,0,[0,4+this.foffset,0]);return t=t.replace(/(..)/g,"%$1"),decodeURIComponent(t)},this.getNotAfter=function(){var t=s(this.hex,0,[0,4+this.foffset,1]);return t=t.replace(/(..)/g,"%$1"),decodeURIComponent(t)},this.getPublicKeyHex=function(){return r.getTLVbyList(this.hex,0,[0,6+this.foffset],"30")},this.getPublicKeyIdx=function(){return f(this.hex,0,[0,6+this.foffset],"30")},this.getPublicKeyContentIdx=function(){var t=this.getPublicKeyIdx();return f(this.hex,t,[1,0],"30")},this.getPublicKey=function(){return Ht.getKey(this.getPublicKeyHex(),null,"pkcs8pub")},this.getSignatureAlgorithmName=function(){var t=u(this.hex,0,[1],"30");return this.getAlgorithmIdentifierName(t)},this.getSignatureValueHex=function(){return s(this.hex,0,[2],"03",!0)},this.verifySignature=function(t){var e=this.getSignatureAlgorithmField(),r=this.getSignatureValueHex(),n=u(this.hex,0,[0],"30"),i=new ct.crypto.Signature({alg:e});return i.init(t),i.updateHex(n),i.verify(r)},this.parseExt=function(t){var e,o,a;if(void 0===t){if(a=this.hex,3!==this.version)return-1;e=f(a,0,[0,7,0],"30"),o=n(a,e)}else{a=Pt(t);var u=f(a,0,[0,3,0,0],"06");if("2a864886f70d01090e"!=i(a,u))return void(this.aExtInfo=new Array);e=f(a,0,[0,3,0,1,0],"30"),o=n(a,e),this.hex=a}this.aExtInfo=new Array;for(var c=0;c<o.length;c++){var h={critical:!1},p=0;3===n(a,o[c]).length&&(h.critical=!0,p=1),h.oid=r.hextooidstr(s(a,o[c],[0],"06"));var g=f(a,o[c],[1+p]);h.vidx=l(a,g),this.aExtInfo.push(h)}},this.getExtInfo=function(t){var e=this.aExtInfo,r=t;if(t.match(/^[0-9.]+$/)||(r=ct.asn1.x509.OID.name2oid(t)),""!==r)for(var n=0;n<e.length;n++)if(e[n].oid===r)return e[n]},this.getExtBasicConstraints=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("basicConstraints");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var n={extname:"basicConstraints"};if(e&&(n.critical=!0),"3000"===t)return n;if("30030101ff"===t)return n.cA=!0,n;if("30060101ff02"===t.substr(0,12)){var s=i(t,10),a=parseInt(s,16);return n.cA=!0,n.pathLen=a,n}throw new Error("hExtV parse error: "+t)},this.getExtKeyUsage=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("keyUsage");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var n={extname:"keyUsage"};return e&&(n.critical=!0),n.names=this.getExtKeyUsageString(t).split(","),n},this.getExtKeyUsageBin=function(t){if(void 0===t){var e=this.getExtInfo("keyUsage");if(void 0===e)return"";t=o(this.hex,e.vidx)}if(8!=t.length&&10!=t.length)throw new Error("malformed key usage value: "+t);var r="000000000000000"+parseInt(t.substr(6),16).toString(2);return 8==t.length&&(r=r.slice(-8)),10==t.length&&(r=r.slice(-16)),""==(r=r.replace(/0+$/,""))&&(r="0"),r},this.getExtKeyUsageString=function(t){for(var e=this.getExtKeyUsageBin(t),r=new Array,n=0;n<e.length;n++)"1"==e.substr(n,1)&&r.push(Jt.KEYUSAGE_NAME[n]);return r.join(",")},this.getExtSubjectKeyIdentifier=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("subjectKeyIdentifier");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var n={extname:"subjectKeyIdentifier"};e&&(n.critical=!0);var s=i(t,0);return n.kid={hex:s},n},this.getExtAuthorityKeyIdentifier=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("authorityKeyIdentifier");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var s={extname:"authorityKeyIdentifier"};e&&(s.critical=!0);for(var a=n(t,0),u=0;u<a.length;u++){var c=t.substr(a[u],2);if("80"===c&&(s.kid={hex:i(t,a[u])}),"a1"===c){var f=o(t,a[u]),h=this.getGeneralNames(f);s.issuer=h[0].dn}"82"===c&&(s.sn={hex:i(t,a[u])})}return s},this.getExtExtKeyUsage=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("extKeyUsage");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var s={extname:"extKeyUsage",array:[]};e&&(s.critical=!0);for(var a=n(t,0),u=0;u<a.length;u++)s.array.push(p(i(t,a[u])));return s},this.getExtExtKeyUsageName=function(){var t=this.getExtInfo("extKeyUsage");if(void 0===t)return t;var e=new Array,r=o(this.hex,t.vidx);if(""===r)return e;for(var s=n(r,0),a=0;a<s.length;a++)e.push(p(i(r,s[a])));return e},this.getExtSubjectAltName=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("subjectAltName");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var n={extname:"subjectAltName",array:[]};return e&&(n.critical=!0),n.array=this.getGeneralNames(t),n},this.getExtIssuerAltName=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("issuerAltName");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var n={extname:"issuerAltName",array:[]};return e&&(n.critical=!0),n.array=this.getGeneralNames(t),n},this.getGeneralNames=function(t){for(var e=n(t,0),r=[],i=0;i<e.length;i++){var s=this.getGeneralName(o(t,e[i]));void 0!==s&&r.push(s)}return r},this.getGeneralName=function(t){var e=t.substr(0,2),r=i(t,0),n=Et(r);return"81"==e?{rfc822:n}:"82"==e?{dns:n}:"86"==e?{uri:n}:"87"==e?{ip:Lt(r)}:"a4"==e?{dn:this.getX500Name(r)}:void 0},this.getExtSubjectAltName2=function(){var t,e,r,s=this.getExtInfo("subjectAltName");if(void 0===s)return s;for(var a=new Array,u=o(this.hex,s.vidx),c=n(u,0),f=0;f<c.length;f++)r=u.substr(c[f],2),t=i(u,c[f]),"81"===r&&(e=_t(t),a.push(["MAIL",e])),"82"===r&&(e=_t(t),a.push(["DNS",e])),"84"===r&&(e=Jt.hex2dn(t,0),a.push(["DN",e])),"86"===r&&(e=_t(t),a.push(["URI",e])),"87"===r&&(e=Lt(t),a.push(["IP",e]));return a},this.getExtCRLDistributionPoints=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("cRLDistributionPoints");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"cRLDistributionPoints",array:[]};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){var u=o(t,s[a]);i.array.push(this.getDistributionPoint(u))}return i},this.getDistributionPoint=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=t.substr(r[i],2),a=o(t,r[i]);"a0"==s&&(e.dpname=this.getDistributionPointName(a))}return e},this.getDistributionPointName=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=t.substr(r[i],2),a=o(t,r[i]);"a0"==s&&(e.full=this.getGeneralNames(a))}return e},this.getExtCRLDistributionPointsURI=function(){var t=this.getExtInfo("cRLDistributionPoints");if(void 0===t)return t;for(var e=new Array,r=n(this.hex,t.vidx),i=0;i<r.length;i++)try{var o=_t(s(this.hex,r[i],[0,0,0],"86"));e.push(o)}catch(t){}return e},this.getExtAIAInfo=function(){var t=this.getExtInfo("authorityInfoAccess");if(void 0===t)return t;for(var e={ocsp:[],caissuer:[]},r=n(this.hex,t.vidx),i=0;i<r.length;i++){var o=s(this.hex,r[i],[0],"06"),a=s(this.hex,r[i],[1],"86");"2b06010505073001"===o&&e.ocsp.push(_t(a)),"2b06010505073002"===o&&e.caissuer.push(_t(a))}return e},this.getExtAuthorityInfoAccess=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("authorityInfoAccess");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"authorityInfoAccess",array:[]};e&&(i.critical=!0);for(var u=n(t,0),c=0;c<u.length;c++){var f=a(t,u[c],[0],"06"),h=_t(s(t,u[c],[1],"86"));if("2b06010505073001"==f)i.array.push({ocsp:h});else{if("2b06010505073002"!=f)throw new Error("unknown method: "+f);i.array.push({caissuer:h})}}return i},this.getExtCertificatePolicies=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("certificatePolicies");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"certificatePolicies",array:[]};e&&(i.critical=!0);for(var s=n(t,0),a=0;a<s.length;a++){var u=o(t,s[a]),c=this.getPolicyInformation(u);i.array.push(c)}return i},this.getPolicyInformation=function(t){var e={},r=s(t,0,[0],"06");e.policyoid=p(r);var i=h(t,0,[1],"30");if(-1!=i){e.array=[];for(var a=n(t,i),u=0;u<a.length;u++){var c=o(t,a[u]),f=this.getPolicyQualifierInfo(c);e.array.push(f)}}return e},this.getPolicyQualifierInfo=function(t){var e={},r=s(t,0,[0],"06");if("2b06010505070201"===r){var n=a(t,0,[1],"16");e.cps=Et(n)}else if("2b06010505070202"===r){var i=u(t,0,[1],"30");e.unotice=this.getUserNotice(i)}return e},this.getUserNotice=function(t){for(var e={},r=n(t,0),i=0;i<r.length;i++){var s=o(t,r[i]);"30"!=s.substr(0,2)&&(e.exptext=this.getDisplayText(s))}return e},this.getDisplayText=function(t){var e={};return e.type={"0c":"utf8",16:"ia5","1a":"vis","1e":"bmp"}[t.substr(0,2)],e.str=Et(i(t,0)),e},this.getExtCRLNumber=function(t,e){var r={extname:"cRLNumber"};if(e&&(r.critical=!0),"02"==t.substr(0,2))return r.num={hex:i(t,0)},r;throw new Error("hExtV parse error: "+t)},this.getExtCRLReason=function(t,e){var r={extname:"cRLReason"};if(e&&(r.critical=!0),"0a"==t.substr(0,2))return r.code=parseInt(i(t,0),16),r;throw new Error("hExtV parse error: "+t)},this.getExtOcspNonce=function(t,e){var r={extname:"ocspNonce"};e&&(r.critical=!0);var n=i(t,0);return r.hex=n,r},this.getExtOcspNoCheck=function(t,e){var r={extname:"ocspNoCheck"};return e&&(r.critical=!0),r},this.getExtAdobeTimeStamp=function(t,e){if(void 0===t&&void 0===e){var r=this.getExtInfo("adobeTimeStamp");if(void 0===r)return;t=o(this.hex,r.vidx),e=r.critical}var i={extname:"adobeTimeStamp"};e&&(i.critical=!0);var s=n(t,0);if(s.length>1){var a=o(t,s[1]),u=this.getGeneralName(a);null!=u.uri&&(i.uri=u.uri)}if(s.length>2){var c=o(t,s[2]);"0101ff"==c&&(i.reqauth=!0),"010100"==c&&(i.reqauth=!1)}return i},this.getX500NameRule=function(t){for(var e=null,r=[],n=0;n<t.length;n++)for(var i=t[n],o=0;o<i.length;o++)r.push(i[o]);for(n=0;n<r.length;n++){var s=r[n],a=s.ds,u=s.value,c=s.type;if("prn"!=a&&"utf8"!=a&&"ia5"!=a)return"mixed";if("ia5"==a){if("CN"!=c)return"mixed";if(ct.lang.String.isMail(u))continue;return"mixed"}if("C"==c){if("prn"==a)continue;return"mixed"}if(null==e)e=a;else if(e!==a)return"mixed"}return null==e?"prn":e},this.getX500Name=function(t){var e=this.getX500NameArray(t);return{array:e,str:this.dnarraytostr(e)}},this.getX500NameArray=function(t){for(var e=[],r=n(t,0),i=0;i<r.length;i++)e.push(this.getRDN(o(t,r[i])));return e},this.getRDN=function(t){for(var e=[],r=n(t,0),i=0;i<r.length;i++)e.push(this.getAttrTypeAndValue(o(t,r[i])));return e},this.getAttrTypeAndValue=function(t){var e={type:null,value:null,ds:null},r=n(t,0),i=s(t,r[0],[],"06"),o=s(t,r[1],[]),a=ct.asn1.ASN1Util.oidHexToInt(i);return e.type=ct.asn1.x509.OID.oid2atype(a),e.value=Et(o),e.ds=this.HEX2STAG[t.substr(r[1],2)],e},this.readCertPEM=function(t){this.readCertHex(v(t))},this.readCertHex=function(t){this.hex=t,this.getVersion();try{f(this.hex,0,[0,7],"a3"),this.parseExt()}catch(t){}},this.getParam=function(){var t={};return t.version=this.getVersion(),t.serial={hex:this.getSerialNumberHex()},t.sigalg=this.getSignatureAlgorithmField(),t.issuer=this.getIssuer(),t.notbefore=this.getNotBefore(),t.notafter=this.getNotAfter(),t.subject=this.getSubject(),t.sbjpubkey=kt(this.getPublicKeyHex(),"PUBLIC KEY"),this.aExtInfo.length>0&&(t.ext=this.getExtParamArray()),t.sighex=this.getSignatureValueHex(),t},this.getExtParamArray=function(t){null==t&&-1!=h(this.hex,0,[0,"[3]"])&&(t=c(this.hex,0,[0,"[3]",0],"30"));for(var e=[],r=n(t,0),i=0;i<r.length;i++){var s=o(t,r[i]),a=this.getExtParam(s);null!=a&&e.push(a)}return e},this.getExtParam=function(t){var e=n(t,0).length;if(2!=e&&3!=e)throw new Error("wrong number elements in Extension: "+e+" "+t);var r=g(s(t,0,[0],"06")),i=!1;3==e&&"0101ff"==u(t,0,[1])&&(i=!0);var o=u(t,0,[e-1,0]),a=void 0;if("2.5.29.14"==r?a=this.getExtSubjectKeyIdentifier(o,i):"2.5.29.15"==r?a=this.getExtKeyUsage(o,i):"2.5.29.17"==r?a=this.getExtSubjectAltName(o,i):"2.5.29.18"==r?a=this.getExtIssuerAltName(o,i):"2.5.29.19"==r?a=this.getExtBasicConstraints(o,i):"2.5.29.31"==r?a=this.getExtCRLDistributionPoints(o,i):"2.5.29.32"==r?a=this.getExtCertificatePolicies(o,i):"2.5.29.35"==r?a=this.getExtAuthorityKeyIdentifier(o,i):"2.5.29.37"==r?a=this.getExtExtKeyUsage(o,i):"1.3.6.1.5.5.7.1.1"==r?a=this.getExtAuthorityInfoAccess(o,i):"2.5.29.20"==r?a=this.getExtCRLNumber(o,i):"2.5.29.21"==r?a=this.getExtCRLReason(o,i):"1.3.6.1.5.5.7.48.1.2"==r?a=this.getExtOcspNonce(o,i):"1.3.6.1.5.5.7.48.1.5"==r?a=this.getExtOcspNoCheck(o,i):"1.2.840.113583.1.1.9.1"==r&&(a=this.getExtAdobeTimeStamp(o,i)),null!=a)return a;var c={extname:r,extn:o};return i&&(c.critical=!0),c},this.findExt=function(t,e){for(var r=0;r<t.length;r++)if(t[r].extname==e)return t[r];return null},this.updateExtCDPFullURI=function(t,e){var r=this.findExt(t,"cRLDistributionPoints");if(null!=r&&null!=r.array)for(var n=r.array,i=0;i<n.length;i++)if(null!=n[i].dpname&&null!=n[i].dpname.full)for(var o=n[i].dpname.full,s=0;s<o.length;s++){var a=o[i];null!=a.uri&&(a.uri=e)}},this.updateExtAIAOCSP=function(t,e){var r=this.findExt(t,"authorityInfoAccess");if(null!=r&&null!=r.array)for(var n=r.array,i=0;i<n.length;i++)null!=n[i].ocsp&&(n[i].ocsp=e)},this.updateExtAIACAIssuer=function(t,e){var r=this.findExt(t,"authorityInfoAccess");if(null!=r&&null!=r.array)for(var n=r.array,i=0;i<n.length;i++)null!=n[i].caissuer&&(n[i].caissuer=e)},this.dnarraytostr=function(t){return"/"+t.map((function(t){return function(t){return t.map((function(t){return function(t){return t.type+"="+t.value}(t)})).join("+")}(t)})).join("/")},this.getInfo=function(){var t,e,r,n=function(t){return JSON.stringify(t.array).replace(/[\[\]\{\}\"]/g,"")},i=function(t){for(var e="",r=t.array,n=0;n<r.length;n++){var i=r[n];if(e+="    policy oid: "+i.policyoid+"\n",void 0!==i.array)for(var o=0;o<i.array.length;o++){var s=i.array[o];void 0!==s.cps&&(e+="    cps: "+s.cps+"\n")}}return e},o=function(t){for(var e="",r=t.array,n=0;n<r.length;n++){var i=r[n];try{void 0!==i.dpname.full[0].uri&&(e+="    "+i.dpname.full[0].uri+"\n")}catch(t){}try{void 0!==i.dname.full[0].dn.hex&&(e+="    "+Jt.hex2dn(i.dpname.full[0].dn.hex)+"\n")}catch(t){}}return e},s=function(t){for(var e="",r=t.array,n=0;n<r.length;n++){var i=r[n];void 0!==i.caissuer&&(e+="    caissuer: "+i.caissuer+"\n"),void 0!==i.ocsp&&(e+="    ocsp: "+i.ocsp+"\n")}return e};if(t="Basic Fields\n",t+="  serial number: "+this.getSerialNumberHex()+"\n",t+="  signature algorithm: "+this.getSignatureAlgorithmField()+"\n",t+="  issuer: "+this.getIssuerString()+"\n",t+="  notBefore: "+this.getNotBefore()+"\n",t+="  notAfter: "+this.getNotAfter()+"\n",t+="  subject: "+this.getSubjectString()+"\n",t+="  subject public key info: \n",t+="    key algorithm: "+(e=this.getPublicKey()).type+"\n","RSA"===e.type&&(t+="    n="+Bt(e.n.toString(16)).substr(0,16)+"...\n",t+="    e="+Bt(e.e.toString(16))+"\n"),null!=(r=this.aExtInfo)){t+="X509v3 Extensions:\n";for(var a=0;a<r.length;a++){var u=r[a],c=ct.asn1.x509.OID.oid2name(u.oid);""===c&&(c=u.oid);var f="";if(!0===u.critical&&(f="CRITICAL"),t+="  "+c+" "+f+":\n","basicConstraints"===c){var h=this.getExtBasicConstraints();void 0===h.cA?t+="    {}\n":(t+="    cA=true",void 0!==h.pathLen&&(t+=", pathLen="+h.pathLen),t+="\n")}else if("keyUsage"===c)t+="    "+this.getExtKeyUsageString()+"\n";else if("subjectKeyIdentifier"===c)t+="    "+this.getExtSubjectKeyIdentifier().kid.hex+"\n";else if("authorityKeyIdentifier"===c){var l=this.getExtAuthorityKeyIdentifier();void 0!==l.kid&&(t+="    kid="+l.kid.hex+"\n")}else"extKeyUsage"===c?t+="    "+this.getExtExtKeyUsage().array.join(", ")+"\n":"subjectAltName"===c?t+="    "+n(this.getExtSubjectAltName())+"\n":"cRLDistributionPoints"===c?t+=o(this.getExtCRLDistributionPoints()):"authorityInfoAccess"===c?t+=s(this.getExtAuthorityInfoAccess()):"certificatePolicies"===c&&(t+=i(this.getExtCertificatePolicies()))}}return(t+="signature algorithm: "+this.getSignatureAlgorithmName()+"\n")+"signature: "+this.getSignatureValueHex().substr(0,16)+"...\n"},"string"==typeof t&&(-1!=t.indexOf("-----BEGIN")?this.readCertPEM(t):ct.lang.String.isHex(t)&&this.readCertHex(t))}it.prototype.sign=function(t,e){var r=function(t){return ct.crypto.Util.hashString(t,e)}(t);return this.signWithMessageHash(r,e)},it.prototype.signWithMessageHash=function(t,e){var r=rt(ct.crypto.Util.getPaddedDigestInfoHex(t,e,this.n.bitLength()),16);return qt(this.doPrivate(r).toString(16),this.n.bitLength())},it.prototype.signPSS=function(t,e,r){var n=function(t){return ct.crypto.Util.hashHex(t,e)}(xt(t));return void 0===r&&(r=-1),this.signWithMessageHashPSS(n,e,r)},it.prototype.signWithMessageHashPSS=function(t,e,r){var n,i=Et(t),o=i.length,s=this.n.bitLength()-1,a=Math.ceil(s/8),u=function(t){return ct.crypto.Util.hashHex(t,e)};if(-1===r||void 0===r)r=o;else if(-2===r)r=a-o-2;else if(r<-2)throw new Error("invalid salt length");if(a<o+r+2)throw new Error("data too long");var c="";r>0&&(c=new Array(r),(new et).nextBytes(c),c=String.fromCharCode.apply(String,c));var f=Et(u(xt("\0\0\0\0\0\0\0\0"+i+c))),h=[];for(n=0;n<a-r-o-2;n+=1)h[n]=0;var l=String.fromCharCode.apply(String,h)+""+c,p=Kt(f,l.length,u),g=[];for(n=0;n<l.length;n+=1)g[n]=l.charCodeAt(n)^p.charCodeAt(n);var d=65280>>8*a-s&255;for(g[0]&=~d,n=0;n<o;n++)g.push(f.charCodeAt(n));return g.push(188),qt(this.doPrivate(new E(g)).toString(16),this.n.bitLength())},it.prototype.verify=function(t,e){var r=rt(e=(e=e.replace(Vt,"")).replace(/[ \n]+/g,""),16);if(r.bitLength()>this.n.bitLength())return 0;var n=Wt(this.doPublic(r).toString(16).replace(/^1f+00/,""));if(0==n.length)return!1;var i=n[0];return n[1]==function(t){return ct.crypto.Util.hashString(t,i)}(t)},it.prototype.verifyWithMessageHash=function(t,e){if(e.length!=Math.ceil(this.n.bitLength()/4))return!1;var r=rt(e,16);if(r.bitLength()>this.n.bitLength())return 0;var n=Wt(this.doPublic(r).toString(16).replace(/^1f+00/,""));return 0!=n.length&&(n[0],n[1]==t)},it.prototype.verifyPSS=function(t,e,r,n){var i=function(t){return ct.crypto.Util.hashHex(t,r)}(xt(t));return void 0===n&&(n=-1),this.verifyWithMessageHashPSS(i,e,r,n)},it.prototype.verifyWithMessageHashPSS=function(t,e,r,n){if(e.length!=Math.ceil(this.n.bitLength()/4))return!1;var i,o=new E(e,16),s=function(t){return ct.crypto.Util.hashHex(t,r)},a=Et(t),u=a.length,c=this.n.bitLength()-1,f=Math.ceil(c/8);if(-1===n||void 0===n)n=u;else if(-2===n)n=f-u-2;else if(n<-2)throw new Error("invalid salt length");if(f<u+n+2)throw new Error("data too long");var h=this.doPublic(o).toByteArray();for(i=0;i<h.length;i+=1)h[i]&=255;for(;h.length<f;)h.unshift(0);if(188!==h[f-1])throw new Error("encoded message does not end in 0xbc");var l=(h=String.fromCharCode.apply(String,h)).substr(0,f-u-1),p=h.substr(l.length,u),g=65280>>8*f-c&255;if(0!=(l.charCodeAt(0)&g))throw new Error("bits beyond keysize not zero");var d=Kt(p,l.length,s),v=[];for(i=0;i<l.length;i+=1)v[i]=l.charCodeAt(i)^d.charCodeAt(i);v[0]&=~g;var y=f-u-n-2;for(i=0;i<y;i+=1)if(0!==v[i])throw new Error("leftmost octets not zero");if(1!==v[y])throw new Error("0x01 marker not found");return p===Et(s(xt("\0\0\0\0\0\0\0\0"+a+String.fromCharCode.apply(String,v.slice(-n)))))},it.SALT_LEN_HLEN=-1,it.SALT_LEN_MAX=-2,it.SALT_LEN_RECOVER=-2,Jt.hex2dn=function(t,e){if(void 0===e&&(e=0),"30"!==t.substr(e,2))throw new Error("malformed DN");for(var r=new Array,n=lt.getChildIdx(t,e),i=0;i<n.length;i++)r.push(Jt.hex2rdn(t,n[i]));return"/"+(r=r.map((function(t){return t.replace("/","\\/")}))).join("/")},Jt.hex2rdn=function(t,e){if(void 0===e&&(e=0),"31"!==t.substr(e,2))throw new Error("malformed RDN");for(var r=new Array,n=lt.getChildIdx(t,e),i=0;i<n.length;i++)r.push(Jt.hex2attrTypeValue(t,n[i]));return(r=r.map((function(t){return t.replace("+","\\+")}))).join("+")},Jt.hex2attrTypeValue=function(t,e){var r=lt,n=r.getV;if(void 0===e&&(e=0),"30"!==t.substr(e,2))throw new Error("malformed attribute type and value");var i=r.getChildIdx(t,e);2!==i.length||t.substr(i[0],2);var o=n(t,i[0]),s=ct.asn1.ASN1Util.oidHexToInt(o);return ct.asn1.x509.OID.oid2atype(s)+"="+Et(n(t,i[1]))},Jt.getPublicKeyFromCertHex=function(t){var e=new Jt;return e.readCertHex(t),e.getPublicKey()},Jt.getPublicKeyFromCertPEM=function(t){var e=new Jt;return e.readCertPEM(t),e.getPublicKey()},Jt.getPublicKeyInfoPropOfCertPEM=function(t){var e,r,n=lt.getVbyList,i={algparam:null};return(e=new Jt).readCertPEM(t),r=e.getPublicKeyHex(),i.keyhex=n(r,0,[1],"03").substr(2),i.algoid=n(r,0,[0,0],"06"),"2a8648ce3d0201"===i.algoid&&(i.algparam=n(r,0,[0,1],"06")),i},Jt.KEYUSAGE_NAME=["digitalSignature","nonRepudiation","keyEncipherment","dataEncipherment","keyAgreement","keyCertSign","cRLSign","encipherOnly","decipherOnly"],void 0!==ct&&ct||(e.KJUR=ct={}),void 0!==ct.jws&&ct.jws||(ct.jws={}),ct.jws.JWS=function(){var t=ct.jws.JWS.isSafeJSONString;this.parseJWS=function(e,r){if(void 0===this.parsedJWS||!r&&void 0===this.parsedJWS.sigvalH){var n=e.match(/^([^.]+)\.([^.]+)\.([^.]+)$/);if(null==n)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";var i=n[1],o=n[2],s=n[3],a=i+"."+o;if(this.parsedJWS={},this.parsedJWS.headB64U=i,this.parsedJWS.payloadB64U=o,this.parsedJWS.sigvalB64U=s,this.parsedJWS.si=a,!r){var u=St(s),c=rt(u,16);this.parsedJWS.sigvalH=u,this.parsedJWS.sigvalBI=c}var f=ht(i),h=ht(o);if(this.parsedJWS.headS=f,this.parsedJWS.payloadS=h,!t(f,this.parsedJWS,"headP"))throw"malformed JSON string for JWS Head: "+f}}},ct.jws.JWS.sign=function(t,e,n,i,o){var s,a,u,c=ct,f=c.jws.JWS,h=f.readSafeJSONString,l=f.isSafeJSONString,p=c.crypto,g=(p.ECDSA,p.Mac),d=p.Signature,v=JSON;if("string"!=typeof e&&"object"!=(void 0===e?"undefined":r(e)))throw"spHeader must be JSON string or object: "+e;if("object"==(void 0===e?"undefined":r(e))&&(a=e,s=v.stringify(a)),"string"==typeof e){if(!l(s=e))throw"JWS Head is not safe JSON string: "+s;a=h(s)}if(u=n,"object"==(void 0===n?"undefined":r(n))&&(u=v.stringify(n)),""!=t&&null!=t||void 0===a.alg||(t=a.alg),""!=t&&null!=t&&void 0===a.alg&&(a.alg=t,s=v.stringify(a)),t!==a.alg)throw"alg and sHeader.alg doesn't match: "+t+"!="+a.alg;var y=null;if(void 0===f.jwsalg2sigalg[t])throw"unsupported alg name: "+t;y=f.jwsalg2sigalg[t];var m=ft(s)+"."+ft(u),b="";if("Hmac"==y.substr(0,4)){if(void 0===i)throw"mac key shall be specified for HS* alg";var S=new g({alg:y,prov:"cryptojs",pass:i});S.updateString(m),b=S.doFinal()}else if(-1!=y.indexOf("withECDSA")){(_=new d({alg:y})).init(i,o),_.updateString(m);var w=_.sign();b=ct.crypto.ECDSA.asn1SigToConcatSig(w)}else{var _;"none"!=y&&((_=new d({alg:y})).init(i,o),_.updateString(m),b=_.sign())}return m+"."+bt(b)},ct.jws.JWS.verify=function(t,e,n){var i,o=ct,s=o.jws.JWS,a=s.readSafeJSONString,u=o.crypto,c=u.ECDSA,f=u.Mac,h=u.Signature;void 0!==r(it)&&(i=it);var l=t.split(".");if(3!==l.length)return!1;var p,g=l[0]+"."+l[1],d=St(l[2]),v=a(ht(l[0])),y=null;if(void 0===v.alg)throw"algorithm not specified in header";if(p=(y=v.alg).substr(0,2),null!=n&&"[object Array]"===Object.prototype.toString.call(n)&&n.length>0&&-1==(":"+n.join(":")+":").indexOf(":"+y+":"))throw"algorithm '"+y+"' not accepted in the list";if("none"!=y&&null===e)throw"key shall be specified to verify.";if("string"==typeof e&&-1!=e.indexOf("-----BEGIN ")&&(e=Ht.getKey(e)),!("RS"!=p&&"PS"!=p||e instanceof i))throw"key shall be a RSAKey obj for RS* and PS* algs";if("ES"==p&&!(e instanceof c))throw"key shall be a ECDSA obj for ES* algs";var m=null;if(void 0===s.jwsalg2sigalg[v.alg])throw"unsupported alg name: "+y;if("none"==(m=s.jwsalg2sigalg[y]))throw"not supported";if("Hmac"==m.substr(0,4)){if(void 0===e)throw"hexadecimal key shall be specified for HMAC";var b=new f({alg:m,pass:e});return b.updateString(g),d==b.doFinal()}if(-1!=m.indexOf("withECDSA")){var S,w=null;try{w=c.concatSigToASN1Sig(d)}catch(t){return!1}return(S=new h({alg:m})).init(e),S.updateString(g),S.verify(w)}return(S=new h({alg:m})).init(e),S.updateString(g),S.verify(d)},ct.jws.JWS.parse=function(t){var e,r,n,i=t.split("."),o={};if(2!=i.length&&3!=i.length)throw"malformed sJWS: wrong number of '.' splitted elements";return e=i[0],r=i[1],3==i.length&&(n=i[2]),o.headerObj=ct.jws.JWS.readSafeJSONString(ht(e)),o.payloadObj=ct.jws.JWS.readSafeJSONString(ht(r)),o.headerPP=JSON.stringify(o.headerObj,null,"  "),null==o.payloadObj?o.payloadPP=ht(r):o.payloadPP=JSON.stringify(o.payloadObj,null,"  "),void 0!==n&&(o.sigHex=St(n)),o},ct.jws.JWS.verifyJWT=function(t,e,n){var i=ct.jws,o=i.JWS,s=o.readSafeJSONString,a=o.inArray,u=o.includedArray,c=t.split("."),f=c[0],h=c[1],l=(St(c[2]),s(ht(f))),p=s(ht(h));if(void 0===l.alg)return!1;if(void 0===n.alg)throw"acceptField.alg shall be specified";if(!a(l.alg,n.alg))return!1;if(void 0!==p.iss&&"object"===r(n.iss)&&!a(p.iss,n.iss))return!1;if(void 0!==p.sub&&"object"===r(n.sub)&&!a(p.sub,n.sub))return!1;if(void 0!==p.aud&&"object"===r(n.aud))if("string"==typeof p.aud){if(!a(p.aud,n.aud))return!1}else if("object"==r(p.aud)&&!u(p.aud,n.aud))return!1;var g=i.IntDate.getNow();return void 0!==n.verifyAt&&"number"==typeof n.verifyAt&&(g=n.verifyAt),void 0!==n.gracePeriod&&"number"==typeof n.gracePeriod||(n.gracePeriod=0),!(void 0!==p.exp&&"number"==typeof p.exp&&p.exp+n.gracePeriod<g||void 0!==p.nbf&&"number"==typeof p.nbf&&g<p.nbf-n.gracePeriod||void 0!==p.iat&&"number"==typeof p.iat&&g<p.iat-n.gracePeriod||void 0!==p.jti&&void 0!==n.jti&&p.jti!==n.jti||!o.verify(t,e,n.alg))},ct.jws.JWS.includedArray=function(t,e){var n=ct.jws.JWS.inArray;if(null===t)return!1;if("object"!==(void 0===t?"undefined":r(t)))return!1;if("number"!=typeof t.length)return!1;for(var i=0;i<t.length;i++)if(!n(t[i],e))return!1;return!0},ct.jws.JWS.inArray=function(t,e){if(null===e)return!1;if("object"!==(void 0===e?"undefined":r(e)))return!1;if("number"!=typeof e.length)return!1;for(var n=0;n<e.length;n++)if(e[n]==t)return!0;return!1},ct.jws.JWS.jwsalg2sigalg={HS256:"HmacSHA256",HS384:"HmacSHA384",HS512:"HmacSHA512",RS256:"SHA256withRSA",RS384:"SHA384withRSA",RS512:"SHA512withRSA",ES256:"SHA256withECDSA",ES384:"SHA384withECDSA",PS256:"SHA256withRSAandMGF1",PS384:"SHA384withRSAandMGF1",PS512:"SHA512withRSAandMGF1",none:"none"},ct.jws.JWS.isSafeJSONString=function(t,e,n){var i=null;try{return"object"!=(void 0===(i=ut(t))?"undefined":r(i))||i.constructor===Array?0:(e&&(e[n]=i),1)}catch(t){return 0}},ct.jws.JWS.readSafeJSONString=function(t){var e=null;try{return"object"!=(void 0===(e=ut(t))?"undefined":r(e))||e.constructor===Array?null:e}catch(t){return null}},ct.jws.JWS.getEncodedSignatureValueFromJWS=function(t){var e=t.match(/^[^.]+\.[^.]+\.([^.]+)$/);if(null==e)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";return e[1]},ct.jws.JWS.getJWKthumbprint=function(t){if("RSA"!==t.kty&&"EC"!==t.kty&&"oct"!==t.kty)throw"unsupported algorithm for JWK Thumprint";var e="{";if("RSA"===t.kty){if("string"!=typeof t.n||"string"!=typeof t.e)throw"wrong n and e value for RSA key";e+='"e":"'+t.e+'",',e+='"kty":"'+t.kty+'",',e+='"n":"'+t.n+'"}'}else if("EC"===t.kty){if("string"!=typeof t.crv||"string"!=typeof t.x||"string"!=typeof t.y)throw"wrong crv, x and y value for EC key";e+='"crv":"'+t.crv+'",',e+='"kty":"'+t.kty+'",',e+='"x":"'+t.x+'",',e+='"y":"'+t.y+'"}'}else if("oct"===t.kty){if("string"!=typeof t.k)throw"wrong k value for oct(symmetric) key";e+='"kty":"'+t.kty+'",',e+='"k":"'+t.k+'"}'}var r=xt(e);return bt(ct.crypto.Util.hashHex(r,"sha256"))},ct.jws.IntDate={},ct.jws.IntDate.get=function(t){var e=ct.jws.IntDate,r=e.getNow,n=e.getZulu;if("now"==t)return r();if("now + 1hour"==t)return r()+3600;if("now + 1day"==t)return r()+86400;if("now + 1month"==t)return r()+2592e3;if("now + 1year"==t)return r()+31536e3;if(t.match(/Z$/))return n(t);if(t.match(/^[0-9]+$/))return parseInt(t);throw"unsupported format: "+t},ct.jws.IntDate.getZulu=function(t){return It(t)},ct.jws.IntDate.getNow=function(){return~~(new Date/1e3)},ct.jws.IntDate.intDate2UTCString=function(t){return new Date(1e3*t).toUTCString()},ct.jws.IntDate.intDate2Zulu=function(t){var e=new Date(1e3*t);return("0000"+e.getUTCFullYear()).slice(-4)+("00"+(e.getUTCMonth()+1)).slice(-2)+("00"+e.getUTCDate()).slice(-2)+("00"+e.getUTCHours()).slice(-2)+("00"+e.getUTCMinutes()).slice(-2)+("00"+e.getUTCSeconds()).slice(-2)+"Z"},e.SecureRandom=et,e.rng_seed_time=G,e.BigInteger=E,e.RSAKey=it;var zt=ct.crypto.EDSA;e.EDSA=zt;var Yt=ct.crypto.DSA;e.DSA=Yt;var Gt=ct.crypto.Signature;e.Signature=Gt;var $t=ct.crypto.MessageDigest;e.MessageDigest=$t;var Xt=ct.crypto.Mac;e.Mac=Xt;var Zt=ct.crypto.Cipher;e.Cipher=Zt,e.KEYUTIL=Ht,e.ASN1HEX=lt,e.X509=Jt,e.CryptoJS=y,e.b64tohex=w,e.b64toBA=_,e.stoBA=pt,e.BAtos=gt,e.BAtohex=dt,e.stohex=vt,e.stob64=function(t){return S(vt(t))},e.stob64u=function(t){return yt(S(vt(t)))},e.b64utos=function(t){return gt(_(mt(t)))},e.b64tob64u=yt,e.b64utob64=mt,e.hex2b64=S,e.hextob64u=bt,e.b64utohex=St,e.utf8tob64u=ft,e.b64utoutf8=ht,e.utf8tob64=function(t){return S(Ct(jt(t)))},e.b64toutf8=function(t){return decodeURIComponent(Ot(w(t)))},e.utf8tohex=wt,e.hextoutf8=_t,e.hextorstr=Et,e.rstrtohex=xt,e.hextob64=Ft,e.hextob64nl=At,e.b64nltohex=Tt,e.hextopem=kt,e.pemtohex=Pt,e.hextoArrayBuffer=function(t){if(t.length%2!=0)throw"input is not even length";if(null==t.match(/^[0-9A-Fa-f]+$/))throw"input is not hexadecimal";for(var e=new ArrayBuffer(t.length/2),r=new DataView(e),n=0;n<t.length/2;n++)r.setUint8(n,parseInt(t.substr(2*n,2),16));return e},e.ArrayBuffertohex=function(t){for(var e="",r=new DataView(t),n=0;n<t.byteLength;n++)e+=("00"+r.getUint8(n).toString(16)).slice(-2);return e},e.zulutomsec=Rt,e.zulutosec=It,e.zulutodate=function(t){return new Date(Rt(t))},e.datetozulu=function(t,e,r){var n,i=t.getUTCFullYear();if(e){if(i<1950||2049<i)throw"not proper year for UTCTime: "+i;n=(""+i).slice(-2)}else n=("000"+i).slice(-4);if(n+=("0"+(t.getUTCMonth()+1)).slice(-2),n+=("0"+t.getUTCDate()).slice(-2),n+=("0"+t.getUTCHours()).slice(-2),n+=("0"+t.getUTCMinutes()).slice(-2),n+=("0"+t.getUTCSeconds()).slice(-2),r){var o=t.getUTCMilliseconds();0!==o&&(n+="."+(o=(o=("00"+o).slice(-3)).replace(/0+$/g,"")))}return n+"Z"},e.uricmptohex=Ct,e.hextouricmp=Ot,e.ipv6tohex=Dt,e.hextoipv6=Nt,e.hextoip=Lt,e.iptohex=function(t){var e="malformed IP address";if(!(t=t.toLowerCase(t)).match(/^[0-9.]+$/)){if(t.match(/^[0-9a-f:]+$/)&&-1!==t.indexOf(":"))return Dt(t);throw e}var r=t.split(".");if(4!==r.length)throw e;var n="";try{for(var i=0;i<4;i++)n+=("0"+parseInt(r[i]).toString(16)).slice(-2);return n}catch(t){throw e}},e.encodeURIComponentAll=jt,e.newline_toUnix=function(t){return t.replace(/\r\n/gm,"\n")},e.newline_toDos=function(t){return(t=t.replace(/\r\n/gm,"\n")).replace(/\n/gm,"\r\n")},e.hextoposhex=Bt,e.intarystrtohex=function(t){t=(t=(t=t.replace(/^\s*\[\s*/,"")).replace(/\s*\]\s*$/,"")).replace(/\s*/g,"");try{return t.split(/,/).map((function(t,e,r){var n=parseInt(t);if(n<0||255<n)throw"integer not in range 0-255";return("00"+n.toString(16)).slice(-2)})).join("")}catch(t){throw"malformed integer array string: "+t}},e.strdiffidx=function(t,e){var r=t.length;t.length>e.length&&(r=e.length);for(var n=0;n<r;n++)if(t.charCodeAt(n)!=e.charCodeAt(n))return n;return t.length!=e.length?r:-1},e.KJUR=ct;var Qt=ct.crypto;e.crypto=Qt;var te=ct.asn1;e.asn1=te;var ee=ct.jws;e.jws=ee;var re=ct.lang;e.lang=re}).call(this,r(28).Buffer)},function(t,e,r){"use strict";(function(t){
/*!
       * The buffer module from node.js, for the browser.
       *
       * <AUTHOR> Aboukhadijeh <http://feross.org>
       * @license  MIT
       */
var n=r(30),i=r(31),o=r(32);function s(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function a(t,e){if(s()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=u.prototype:(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return h(this,t)}return c(this,t,e,r)}function c(t,e,r,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n),u.TYPED_ARRAY_SUPPORT?(t=e).__proto__=u.prototype:t=l(t,e),t}(t,e,r,n):"string"==typeof e?function(t,e,r){if("string"==typeof r&&""!==r||(r="utf8"),!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|g(e,r),i=(t=a(t,n)).write(e,r);return i!==n&&(t=t.slice(0,i)),t}(t,e,r):function(t,e){if(u.isBuffer(e)){var r=0|p(e.length);return 0===(t=a(t,r)).length||e.copy(t,0,0,r),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||function(t){return t!=t}(e.length)?a(t,0):l(t,e);if("Buffer"===e.type&&o(e.data))return l(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function f(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function h(t,e){if(f(e),t=a(t,e<0?0:0|p(e)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function l(t,e){var r=e.length<0?0:0|p(e.length);t=a(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function p(t){if(t>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|t}function g(t,e){if(u.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return H(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(t).length;default:if(n)return H(t).length;e=(""+e).toLowerCase(),n=!0}}function d(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return R(this,e,r);case"utf8":case"utf-8":return A(this,e,r);case"ascii":return k(this,e,r);case"latin1":case"binary":return P(this,e,r);case"base64":return F(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function v(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):m(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var o,s=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var f=-1;for(o=r;o<a;o++)if(c(t,o)===c(e,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===u)return f*s}else-1!==f&&(o-=o-f),f=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var h=!0,l=0;l<u;l++)if(c(t,o+l)!==c(e,l)){h=!1;break}if(h)return o}return-1}function b(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(e.substr(2*s,2),16);if(isNaN(a))return s;t[r+s]=a}return s}function S(t,e,r,n){return q(H(e,t.length-r),t,r,n)}function w(t,e,r,n){return q(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function _(t,e,r,n){return w(t,e,r,n)}function E(t,e,r,n){return q(V(e),t,r,n)}function x(t,e,r,n){return q(function(t,e){for(var r,n,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)n=(r=t.charCodeAt(s))>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function F(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function A(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,u,c=t[i],f=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h<=r)switch(h){case 1:c<128&&(f=c);break;case 2:128==(192&(o=t[i+1]))&&(u=(31&c)<<6|63&o)>127&&(f=u);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(f=u);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(f=u)}null===f?(f=65533,h=1):f>65535&&(f-=65536,n.push(f>>>10&1023|55296),f=56320|1023&f),n.push(f),i+=h}return function(t){var e=t.length;if(e<=T)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=T));return r}(n)}e.Buffer=u,e.SlowBuffer=function(t){return+t!=t&&(t=0),u.alloc(+t)},e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=s(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,r){return c(null,t,e,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,r){return function(t,e,r,n){return f(e),e<=0?a(t,e):void 0!==r?"string"==typeof n?a(t,e).fill(r,n):a(t,e).fill(r):a(t,e)}(null,t,e,r)},u.allocUnsafe=function(t){return h(null,t)},u.allocUnsafeSlow=function(t){return h(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(!u.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},u.byteLength=g,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)v(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)v(this,e,e+3),v(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)v(this,e,e+7),v(this,e+1,e+6),v(this,e+2,e+5),v(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?A(this,0,t):d.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,r,n,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0),a=Math.min(o,s),c=this.slice(n,i),f=t.slice(e,r),h=0;h<a;++h)if(c[h]!==f[h]){o=c[h],s=f[h];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return b(this,t,e,r);case"utf8":case"utf-8":return S(this,t,e,r);case"ascii":return w(this,t,e,r);case"latin1":case"binary":return _(this,t,e,r);case"base64":return E(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var T=4096;function k(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function P(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function R(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=U(t[o]);return i}function I(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function C(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function O(t,e,r,n,i,o){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function D(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-r,2);i<o;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function N(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-r,4);i<o;++i)t[r+i]=e>>>8*(n?i:3-i)&255}function L(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function j(t,e,r,n,o){return o||L(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function M(t,e,r,n,o){return o||L(t,0,r,8),i.write(t,e,r,n,52,8),r+8}u.prototype.slice=function(t,e){var r,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=u.prototype;else{var i=e-t;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},u.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUInt8=function(t,e){return e||C(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||C(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||C(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||C(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||C(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return e||C(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||C(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){e||C(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return e||C(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||C(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||C(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||C(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||C(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||C(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,r,n){t=+t,e|=0,r|=0,n||O(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},u.prototype.writeUIntBE=function(t,e,r,n){t=+t,e|=0,r|=0,n||O(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},u.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):D(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):D(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):N(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):N(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);O(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);O(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):D(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):D(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):N(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||O(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):N(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,r){return j(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return j(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return M(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return M(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i,o=n-r;if(this===t&&r<e&&e<n)for(i=o-1;i>=0;--i)t[i+e]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+o),e);return o},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var s=u.isBuffer(t)?t:H(new u(t,n).toString()),a=s.length;for(o=0;o<r-e;++o)this[o+e]=s[o%a]}return this};var B=/[^+\/0-9A-Za-z-_]/g;function U(t){return t<16?"0"+t.toString(16):t.toString(16)}function H(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function V(t){return n.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(B,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}}).call(this,r(29))},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){"use strict";e.byteLength=function(t){var e=c(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,n=c(t),s=n[0],a=n[1],u=new o(function(t,e,r){return 3*(e+r)/4-r}(0,s,a)),f=0,h=a>0?s-4:s;for(r=0;r<h;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],u[f++]=e>>16&255,u[f++]=e>>8&255,u[f++]=255&e;return 2===a&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,u[f++]=255&e),1===a&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,u[f++]=e>>8&255,u[f++]=255&e),u},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],s=16383,a=0,u=r-i;a<u;a+=s)o.push(f(t,a,a+s>u?u:a+s));return 1===i?(e=t[r-1],o.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],o.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"=")),o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function c(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function f(t,e,r){for(var i,o,s=[],a=e;a<r;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,f=-7,h=r?i-1:0,l=r?-1:1,p=t[e+h];for(h+=l,o=p&(1<<-f)-1,p>>=-f,f+=a;f>0;o=256*o+t[e+h],h+=l,f-=8);for(s=o&(1<<-f)-1,o>>=-f,f+=n;f>0;s=256*s+t[e+h],h+=l,f-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=c}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,u,c=8*o-i-1,f=(1<<c)-1,h=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,g=n?1:-1,d=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=f):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),(e+=s+h>=1?l/u:l*Math.pow(2,1-h))*u>=2&&(s++,u/=2),s+h>=f?(a=0,s=f):s+h>=1?(a=(e*u-1)*Math.pow(2,i),s+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=g,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;t[r+p]=255&s,p+=g,s/=256,c-=8);t[r+p-g]|=128*d}},function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=t.jws,r=t.KeyUtil,i=t.X509,o=t.crypto,s=t.hextob64u,a=t.b64tohex,u=t.AllowedSigningAlgs;return function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.parseJwt=function t(r){n.Log.debug("JoseUtil.parseJwt");try{var i=e.JWS.parse(r);return{header:i.headerObj,payload:i.payloadObj}}catch(t){n.Log.error(t)}},t.validateJwt=function(e,o,s,u,c,f,h){n.Log.debug("JoseUtil.validateJwt");try{if("RSA"===o.kty)if(o.e&&o.n)o=r.getKey(o);else{if(!o.x5c||!o.x5c.length)return n.Log.error("JoseUtil.validateJwt: RSA key missing key material",o),Promise.reject(new Error("RSA key missing key material"));var l=a(o.x5c[0]);o=i.getPublicKeyFromCertHex(l)}else{if("EC"!==o.kty)return n.Log.error("JoseUtil.validateJwt: Unsupported key type",o&&o.kty),Promise.reject(new Error(o.kty));if(!(o.crv&&o.x&&o.y))return n.Log.error("JoseUtil.validateJwt: EC key missing key material",o),Promise.reject(new Error("EC key missing key material"));o=r.getKey(o)}return t._validateJwt(e,o,s,u,c,f,h)}catch(t){return n.Log.error(t&&t.message||t),Promise.reject("JWT validation failed")}},t.validateJwtAttributes=function(e,r,i,o,s,a){o||(o=0),s||(s=parseInt(Date.now()/1e3));var u=t.parseJwt(e).payload;if(!u.iss)return n.Log.error("JoseUtil._validateJwt: issuer was not provided"),Promise.reject(new Error("issuer was not provided"));if(u.iss!==r)return n.Log.error("JoseUtil._validateJwt: Invalid issuer in token",u.iss),Promise.reject(new Error("Invalid issuer in token: "+u.iss));if(!u.aud)return n.Log.error("JoseUtil._validateJwt: aud was not provided"),Promise.reject(new Error("aud was not provided"));if(!(u.aud===i||Array.isArray(u.aud)&&u.aud.indexOf(i)>=0))return n.Log.error("JoseUtil._validateJwt: Invalid audience in token",u.aud),Promise.reject(new Error("Invalid audience in token: "+u.aud));if(u.azp&&u.azp!==i)return n.Log.error("JoseUtil._validateJwt: Invalid azp in token",u.azp),Promise.reject(new Error("Invalid azp in token: "+u.azp));if(!a){var c=s+o,f=s-o;if(!u.iat)return n.Log.error("JoseUtil._validateJwt: iat was not provided"),Promise.reject(new Error("iat was not provided"));if(c<u.iat)return n.Log.error("JoseUtil._validateJwt: iat is in the future",u.iat),Promise.reject(new Error("iat is in the future: "+u.iat));if(u.nbf&&c<u.nbf)return n.Log.error("JoseUtil._validateJwt: nbf is in the future",u.nbf),Promise.reject(new Error("nbf is in the future: "+u.nbf));if(!u.exp)return n.Log.error("JoseUtil._validateJwt: exp was not provided"),Promise.reject(new Error("exp was not provided"));if(u.exp<f)return n.Log.error("JoseUtil._validateJwt: exp is in the past",u.exp),Promise.reject(new Error("exp is in the past:"+u.exp))}return Promise.resolve(u)},t._validateJwt=function(r,i,o,s,a,c,f){return t.validateJwtAttributes(r,o,s,a,c,f).then((function(t){try{return e.JWS.verify(r,i,u)?t:(n.Log.error("JoseUtil._validateJwt: signature validation failed"),Promise.reject(new Error("signature validation failed")))}catch(t){return n.Log.error(t&&t.message||t),Promise.reject(new Error("signature validation failed"))}}))},t.hashString=function t(e,r){try{return o.Util.hashString(e,r)}catch(t){n.Log.error(t)}},t.hexToBase64Url=function t(e){try{return s(e)}catch(t){n.Log.error(t)}},t}()};var n=r(0);t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SigninResponse=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(3);function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.SigninResponse=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#";o(this,t);var n=i.UrlUtility.parseUrlFragment(e,r);this.error=n.error,this.error_description=n.error_description,this.error_uri=n.error_uri,this.code=n.code,this.state=n.state,this.id_token=n.id_token,this.session_state=n.session_state,this.access_token=n.access_token,this.token_type=n.token_type,this.scope=n.scope,this.profile=void 0,this.expires_in=n.expires_in}return n(t,[{key:"expires_in",get:function(){if(this.expires_at){var t=parseInt(Date.now()/1e3);return this.expires_at-t}},set:function(t){var e=parseInt(t);if("number"==typeof e&&e>0){var r=parseInt(Date.now()/1e3);this.expires_at=r+e}}},{key:"expired",get:function(){var t=this.expires_in;if(void 0!==t)return t<=0}},{key:"scopes",get:function(){return(this.scope||"").split(" ")}},{key:"isOpenIdConnect",get:function(){return this.scopes.indexOf("openid")>=0||!!this.id_token}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SignoutRequest=void 0;var n=r(0),i=r(3),o=r(9);e.SignoutRequest=function t(e){var r=e.url,s=e.id_token_hint,a=e.post_logout_redirect_uri,u=e.data,c=e.extraQueryParams,f=e.request_type;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!r)throw n.Log.error("SignoutRequest.ctor: No url passed"),new Error("url");for(var h in s&&(r=i.UrlUtility.addQueryParam(r,"id_token_hint",s)),a&&(r=i.UrlUtility.addQueryParam(r,"post_logout_redirect_uri",a),u&&(this.state=new o.State({data:u,request_type:f}),r=i.UrlUtility.addQueryParam(r,"state",this.state.id))),c)r=i.UrlUtility.addQueryParam(r,h,c[h]);this.url=r}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SignoutResponse=void 0;var n=r(3);e.SignoutResponse=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var r=n.UrlUtility.parseUrlFragment(e,"?");this.error=r.error,this.error_description=r.error_description,this.error_uri=r.error_uri,this.state=r.state}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.InMemoryWebStorage=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0);e.InMemoryWebStorage=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._data={}}return t.prototype.getItem=function(t){return i.Log.debug("InMemoryWebStorage.getItem",t),this._data[t]},t.prototype.setItem=function(t,e){i.Log.debug("InMemoryWebStorage.setItem",t),this._data[t]=e},t.prototype.removeItem=function(t){i.Log.debug("InMemoryWebStorage.removeItem",t),delete this._data[t]},t.prototype.key=function(t){return Object.getOwnPropertyNames(this._data)[t]},n(t,[{key:"length",get:function(){return Object.getOwnPropertyNames(this._data).length}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UserManager=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(10),s=r(39),a=r(15),u=r(45),c=r(47),f=r(18),h=r(8),l=r(20),p=r(11),g=r(4);function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function v(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}e.UserManager=function(t){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c.SilentRenewService,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:f.SessionMonitor,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:l.TokenRevocationClient,h=arguments.length>4&&void 0!==arguments[4]?arguments[4]:p.TokenClient,y=arguments.length>5&&void 0!==arguments[5]?arguments[5]:g.JoseUtil;d(this,e),r instanceof s.UserManagerSettings||(r=new s.UserManagerSettings(r));var m=v(this,t.call(this,r));return m._events=new u.UserManagerEvents(r),m._silentRenewService=new n(m),m.settings.automaticSilentRenew&&(i.Log.debug("UserManager.ctor: automaticSilentRenew is configured, setting up silent renew"),m.startSilentRenew()),m.settings.monitorSession&&(i.Log.debug("UserManager.ctor: monitorSession is configured, setting up session monitor"),m._sessionMonitor=new o(m)),m._tokenRevocationClient=new a(m._settings),m._tokenClient=new h(m._settings),m._joseUtil=y,m}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.getUser=function(){var t=this;return this._loadUser().then((function(e){return e?(i.Log.info("UserManager.getUser: user loaded"),t._events.load(e,!1),e):(i.Log.info("UserManager.getUser: user not found in storage"),null)}))},e.prototype.removeUser=function(){var t=this;return this.storeUser(null).then((function(){i.Log.info("UserManager.removeUser: user removed from storage"),t._events.unload()}))},e.prototype.signinRedirect=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(t=Object.assign({},t)).request_type="si:r";var e={useReplaceToNavigate:t.useReplaceToNavigate};return this._signinStart(t,this._redirectNavigator,e).then((function(){i.Log.info("UserManager.signinRedirect: successful")}))},e.prototype.signinRedirectCallback=function(t){return this._signinEnd(t||this._redirectNavigator.url).then((function(t){return t.profile&&t.profile.sub?i.Log.info("UserManager.signinRedirectCallback: successful, signed in sub: ",t.profile.sub):i.Log.info("UserManager.signinRedirectCallback: no sub"),t}))},e.prototype.signinPopup=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(t=Object.assign({},t)).request_type="si:p";var e=t.redirect_uri||this.settings.popup_redirect_uri||this.settings.redirect_uri;return e?(t.redirect_uri=e,t.display="popup",this._signin(t,this._popupNavigator,{startUrl:e,popupWindowFeatures:t.popupWindowFeatures||this.settings.popupWindowFeatures,popupWindowTarget:t.popupWindowTarget||this.settings.popupWindowTarget}).then((function(t){return t&&(t.profile&&t.profile.sub?i.Log.info("UserManager.signinPopup: signinPopup successful, signed in sub: ",t.profile.sub):i.Log.info("UserManager.signinPopup: no sub")),t}))):(i.Log.error("UserManager.signinPopup: No popup_redirect_uri or redirect_uri configured"),Promise.reject(new Error("No popup_redirect_uri or redirect_uri configured")))},e.prototype.signinPopupCallback=function(t){return this._signinCallback(t,this._popupNavigator).then((function(t){return t&&(t.profile&&t.profile.sub?i.Log.info("UserManager.signinPopupCallback: successful, signed in sub: ",t.profile.sub):i.Log.info("UserManager.signinPopupCallback: no sub")),t})).catch((function(t){i.Log.error(t.message)}))},e.prototype.signinSilent=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e=Object.assign({},e),this._loadUser().then((function(r){return r&&r.refresh_token?(e.refresh_token=r.refresh_token,t._useRefreshToken(e)):(e.request_type="si:s",e.id_token_hint=e.id_token_hint||t.settings.includeIdTokenInSilentRenew&&r&&r.id_token,r&&t._settings.validateSubOnSilentRenew&&(i.Log.debug("UserManager.signinSilent, subject prior to silent renew: ",r.profile.sub),e.current_sub=r.profile.sub),t._signinSilentIframe(e))}))},e.prototype._useRefreshToken=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._tokenClient.exchangeRefreshToken(e).then((function(e){return e?e.access_token?t._loadUser().then((function(r){if(r){var n=Promise.resolve();return e.id_token&&(n=t._validateIdTokenFromTokenRefreshToken(r.profile,e.id_token)),n.then((function(){return i.Log.debug("UserManager._useRefreshToken: refresh token response success"),r.id_token=e.id_token||r.id_token,r.access_token=e.access_token,r.refresh_token=e.refresh_token||r.refresh_token,r.expires_in=e.expires_in,t.storeUser(r).then((function(){return t._events.load(r),r}))}))}return null})):(i.Log.error("UserManager._useRefreshToken: No access token returned from token endpoint"),Promise.reject("No access token returned from token endpoint")):(i.Log.error("UserManager._useRefreshToken: No response returned from token endpoint"),Promise.reject("No response returned from token endpoint"))}))},e.prototype._validateIdTokenFromTokenRefreshToken=function(t,e){var r=this;return this._metadataService.getIssuer().then((function(n){return r.settings.getEpochTime().then((function(o){return r._joseUtil.validateJwtAttributes(e,n,r._settings.client_id,r._settings.clockSkew,o).then((function(e){return e?e.sub!==t.sub?(i.Log.error("UserManager._validateIdTokenFromTokenRefreshToken: sub in id_token does not match current sub"),Promise.reject(new Error("sub in id_token does not match current sub"))):e.auth_time&&e.auth_time!==t.auth_time?(i.Log.error("UserManager._validateIdTokenFromTokenRefreshToken: auth_time in id_token does not match original auth_time"),Promise.reject(new Error("auth_time in id_token does not match original auth_time"))):e.azp&&e.azp!==t.azp?(i.Log.error("UserManager._validateIdTokenFromTokenRefreshToken: azp in id_token does not match original azp"),Promise.reject(new Error("azp in id_token does not match original azp"))):!e.azp&&t.azp?(i.Log.error("UserManager._validateIdTokenFromTokenRefreshToken: azp not in id_token, but present in original id_token"),Promise.reject(new Error("azp not in id_token, but present in original id_token"))):void 0:(i.Log.error("UserManager._validateIdTokenFromTokenRefreshToken: Failed to validate id_token"),Promise.reject(new Error("Failed to validate id_token")))}))}))}))},e.prototype._signinSilentIframe=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.redirect_uri||this.settings.silent_redirect_uri||this.settings.redirect_uri;return e?(t.redirect_uri=e,t.prompt=t.prompt||"none",this._signin(t,this._iframeNavigator,{startUrl:e,silentRequestTimeout:t.silentRequestTimeout||this.settings.silentRequestTimeout}).then((function(t){return t&&(t.profile&&t.profile.sub?i.Log.info("UserManager.signinSilent: successful, signed in sub: ",t.profile.sub):i.Log.info("UserManager.signinSilent: no sub")),t}))):(i.Log.error("UserManager.signinSilent: No silent_redirect_uri configured"),Promise.reject(new Error("No silent_redirect_uri configured")))},e.prototype.signinSilentCallback=function(t){return this._signinCallback(t,this._iframeNavigator).then((function(t){return t&&(t.profile&&t.profile.sub?i.Log.info("UserManager.signinSilentCallback: successful, signed in sub: ",t.profile.sub):i.Log.info("UserManager.signinSilentCallback: no sub")),t}))},e.prototype.signinCallback=function(t){var e=this;return this.readSigninResponseState(t).then((function(r){var n=r.state;return r.response,"si:r"===n.request_type?e.signinRedirectCallback(t):"si:p"===n.request_type?e.signinPopupCallback(t):"si:s"===n.request_type?e.signinSilentCallback(t):Promise.reject(new Error("invalid response_type in state"))}))},e.prototype.signoutCallback=function(t,e){var r=this;return this.readSignoutResponseState(t).then((function(n){var i=n.state,o=n.response;return i?"so:r"===i.request_type?r.signoutRedirectCallback(t):"so:p"===i.request_type?r.signoutPopupCallback(t,e):Promise.reject(new Error("invalid response_type in state")):o}))},e.prototype.querySessionStatus=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(e=Object.assign({},e)).request_type="si:s";var r=e.redirect_uri||this.settings.silent_redirect_uri||this.settings.redirect_uri;return r?(e.redirect_uri=r,e.prompt="none",e.response_type=e.response_type||this.settings.query_status_response_type,e.scope=e.scope||"openid",e.skipUserInfo=!0,this._signinStart(e,this._iframeNavigator,{startUrl:r,silentRequestTimeout:e.silentRequestTimeout||this.settings.silentRequestTimeout}).then((function(e){return t.processSigninResponse(e.url).then((function(t){if(i.Log.debug("UserManager.querySessionStatus: got signin response"),t.session_state&&t.profile.sub)return i.Log.info("UserManager.querySessionStatus: querySessionStatus success for sub: ",t.profile.sub),{session_state:t.session_state,sub:t.profile.sub,sid:t.profile.sid};i.Log.info("querySessionStatus successful, user not authenticated")})).catch((function(e){if(e.session_state&&t.settings.monitorAnonymousSession&&("login_required"==e.message||"consent_required"==e.message||"interaction_required"==e.message||"account_selection_required"==e.message))return i.Log.info("UserManager.querySessionStatus: querySessionStatus success for anonymous user"),{session_state:e.session_state};throw e}))}))):(i.Log.error("UserManager.querySessionStatus: No silent_redirect_uri configured"),Promise.reject(new Error("No silent_redirect_uri configured")))},e.prototype._signin=function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._signinStart(t,e,n).then((function(e){return r._signinEnd(e.url,t)}))},e.prototype._signinStart=function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e.prepare(n).then((function(e){return i.Log.debug("UserManager._signinStart: got navigator window handle"),r.createSigninRequest(t).then((function(t){return i.Log.debug("UserManager._signinStart: got signin request"),n.url=t.url,n.id=t.state.id,e.navigate(n)})).catch((function(t){throw e.close&&(i.Log.debug("UserManager._signinStart: Error after preparing navigator, closing navigator window"),e.close()),t}))}))},e.prototype._signinEnd=function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.processSigninResponse(t).then((function(t){i.Log.debug("UserManager._signinEnd: got signin response");var n=new a.User(t);if(r.current_sub){if(r.current_sub!==n.profile.sub)return i.Log.debug("UserManager._signinEnd: current user does not match user returned from signin. sub from signin: ",n.profile.sub),Promise.reject(new Error("login_required"));i.Log.debug("UserManager._signinEnd: current user matches user returned from signin")}return e.storeUser(n).then((function(){return i.Log.debug("UserManager._signinEnd: user stored"),e._events.load(n),n}))}))},e.prototype._signinCallback=function(t,e){i.Log.debug("UserManager._signinCallback");var r="query"===this._settings.response_mode||!this._settings.response_mode&&h.SigninRequest.isCode(this._settings.response_type)?"?":"#";return e.callback(t,void 0,r)},e.prototype.signoutRedirect=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(t=Object.assign({},t)).request_type="so:r";var e=t.post_logout_redirect_uri||this.settings.post_logout_redirect_uri;e&&(t.post_logout_redirect_uri=e);var r={useReplaceToNavigate:t.useReplaceToNavigate};return this._signoutStart(t,this._redirectNavigator,r).then((function(){i.Log.info("UserManager.signoutRedirect: successful")}))},e.prototype.signoutRedirectCallback=function(t){return this._signoutEnd(t||this._redirectNavigator.url).then((function(t){return i.Log.info("UserManager.signoutRedirectCallback: successful"),t}))},e.prototype.signoutPopup=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(t=Object.assign({},t)).request_type="so:p";var e=t.post_logout_redirect_uri||this.settings.popup_post_logout_redirect_uri||this.settings.post_logout_redirect_uri;return t.post_logout_redirect_uri=e,t.display="popup",t.post_logout_redirect_uri&&(t.state=t.state||{}),this._signout(t,this._popupNavigator,{startUrl:e,popupWindowFeatures:t.popupWindowFeatures||this.settings.popupWindowFeatures,popupWindowTarget:t.popupWindowTarget||this.settings.popupWindowTarget}).then((function(){i.Log.info("UserManager.signoutPopup: successful")}))},e.prototype.signoutPopupCallback=function(t,e){return void 0===e&&"boolean"==typeof t&&(e=t,t=null),this._popupNavigator.callback(t,e,"?").then((function(){i.Log.info("UserManager.signoutPopupCallback: successful")}))},e.prototype._signout=function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._signoutStart(t,e,n).then((function(t){return r._signoutEnd(t.url)}))},e.prototype._signoutStart=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=this,r=arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.prepare(n).then((function(r){return i.Log.debug("UserManager._signoutStart: got navigator window handle"),e._loadUser().then((function(o){return i.Log.debug("UserManager._signoutStart: loaded current user from storage"),(e._settings.revokeAccessTokenOnSignout?e._revokeInternal(o):Promise.resolve()).then((function(){var s=t.id_token_hint||o&&o.id_token;return s&&(i.Log.debug("UserManager._signoutStart: Setting id_token into signout request"),t.id_token_hint=s),e.removeUser().then((function(){return i.Log.debug("UserManager._signoutStart: user removed, creating signout request"),e.createSignoutRequest(t).then((function(t){return i.Log.debug("UserManager._signoutStart: got signout request"),n.url=t.url,t.state&&(n.id=t.state.id),r.navigate(n)}))}))}))})).catch((function(t){throw r.close&&(i.Log.debug("UserManager._signoutStart: Error after preparing navigator, closing navigator window"),r.close()),t}))}))},e.prototype._signoutEnd=function(t){return this.processSignoutResponse(t).then((function(t){return i.Log.debug("UserManager._signoutEnd: got signout response"),t}))},e.prototype.revokeAccessToken=function(){var t=this;return this._loadUser().then((function(e){return t._revokeInternal(e,!0).then((function(r){if(r)return i.Log.debug("UserManager.revokeAccessToken: removing token properties from user and re-storing"),e.access_token=null,e.refresh_token=null,e.expires_at=null,e.token_type=null,t.storeUser(e).then((function(){i.Log.debug("UserManager.revokeAccessToken: user stored"),t._events.load(e)}))}))})).then((function(){i.Log.info("UserManager.revokeAccessToken: access token revoked successfully")}))},e.prototype._revokeInternal=function(t,e){var r=this;if(t){var n=t.access_token,o=t.refresh_token;return this._revokeAccessTokenInternal(n,e).then((function(t){return r._revokeRefreshTokenInternal(o,e).then((function(e){return t||e||i.Log.debug("UserManager.revokeAccessToken: no need to revoke due to no token(s), or JWT format"),t||e}))}))}return Promise.resolve(!1)},e.prototype._revokeAccessTokenInternal=function(t,e){return!t||t.indexOf(".")>=0?Promise.resolve(!1):this._tokenRevocationClient.revoke(t,e).then((function(){return!0}))},e.prototype._revokeRefreshTokenInternal=function(t,e){return t?this._tokenRevocationClient.revoke(t,e,"refresh_token").then((function(){return!0})):Promise.resolve(!1)},e.prototype.startSilentRenew=function(){this._silentRenewService.start()},e.prototype.stopSilentRenew=function(){this._silentRenewService.stop()},e.prototype._loadUser=function(){return this._userStore.get(this._userStoreKey).then((function(t){return t?(i.Log.debug("UserManager._loadUser: user storageString loaded"),a.User.fromStorageString(t)):(i.Log.debug("UserManager._loadUser: no user storageString"),null)}))},e.prototype.storeUser=function(t){if(t){i.Log.debug("UserManager.storeUser: storing user");var e=t.toStorageString();return this._userStore.set(this._userStoreKey,e)}return i.Log.debug("storeUser.storeUser: removing user"),this._userStore.remove(this._userStoreKey)},n(e,[{key:"_redirectNavigator",get:function(){return this.settings.redirectNavigator}},{key:"_popupNavigator",get:function(){return this.settings.popupNavigator}},{key:"_iframeNavigator",get:function(){return this.settings.iframeNavigator}},{key:"_userStore",get:function(){return this.settings.userStore}},{key:"events",get:function(){return this._events}},{key:"_userStoreKey",get:function(){return"user:"+this.settings.authority+":"+this.settings.client_id}}]),e}(o.OidcClient)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UserManagerSettings=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=(r(0),r(5)),o=r(40),s=r(41),a=r(43),u=r(6),c=r(1),f=r(8);function h(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}e.UserManagerSettings=function(t){function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=r.popup_redirect_uri,i=r.popup_post_logout_redirect_uri,p=r.popupWindowFeatures,g=r.popupWindowTarget,d=r.silent_redirect_uri,v=r.silentRequestTimeout,y=r.automaticSilentRenew,m=void 0!==y&&y,b=r.validateSubOnSilentRenew,S=void 0!==b&&b,w=r.includeIdTokenInSilentRenew,_=void 0===w||w,E=r.monitorSession,x=void 0===E||E,F=r.monitorAnonymousSession,A=void 0!==F&&F,T=r.checkSessionInterval,k=void 0===T?2e3:T,P=r.stopCheckSessionOnError,R=void 0===P||P,I=r.query_status_response_type,C=r.revokeAccessTokenOnSignout,O=void 0!==C&&C,D=r.accessTokenExpiringNotificationTime,N=void 0===D?60:D,L=r.redirectNavigator,j=void 0===L?new o.RedirectNavigator:L,M=r.popupNavigator,B=void 0===M?new s.PopupNavigator:M,U=r.iframeNavigator,H=void 0===U?new a.IFrameNavigator:U,V=r.userStore,q=void 0===V?new u.WebStorageStateStore({store:c.Global.sessionStorage}):V;h(this,e);var K=l(this,t.call(this,arguments[0]));return K._popup_redirect_uri=n,K._popup_post_logout_redirect_uri=i,K._popupWindowFeatures=p,K._popupWindowTarget=g,K._silent_redirect_uri=d,K._silentRequestTimeout=v,K._automaticSilentRenew=m,K._validateSubOnSilentRenew=S,K._includeIdTokenInSilentRenew=_,K._accessTokenExpiringNotificationTime=N,K._monitorSession=x,K._monitorAnonymousSession=A,K._checkSessionInterval=k,K._stopCheckSessionOnError=R,I?K._query_status_response_type=I:arguments[0]&&arguments[0].response_type?K._query_status_response_type=f.SigninRequest.isOidc(arguments[0].response_type)?"id_token":"code":K._query_status_response_type="id_token",K._revokeAccessTokenOnSignout=O,K._redirectNavigator=j,K._popupNavigator=B,K._iframeNavigator=H,K._userStore=q,K}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"popup_redirect_uri",get:function(){return this._popup_redirect_uri}},{key:"popup_post_logout_redirect_uri",get:function(){return this._popup_post_logout_redirect_uri}},{key:"popupWindowFeatures",get:function(){return this._popupWindowFeatures}},{key:"popupWindowTarget",get:function(){return this._popupWindowTarget}},{key:"silent_redirect_uri",get:function(){return this._silent_redirect_uri}},{key:"silentRequestTimeout",get:function(){return this._silentRequestTimeout}},{key:"automaticSilentRenew",get:function(){return this._automaticSilentRenew}},{key:"validateSubOnSilentRenew",get:function(){return this._validateSubOnSilentRenew}},{key:"includeIdTokenInSilentRenew",get:function(){return this._includeIdTokenInSilentRenew}},{key:"accessTokenExpiringNotificationTime",get:function(){return this._accessTokenExpiringNotificationTime}},{key:"monitorSession",get:function(){return this._monitorSession}},{key:"monitorAnonymousSession",get:function(){return this._monitorAnonymousSession}},{key:"checkSessionInterval",get:function(){return this._checkSessionInterval}},{key:"stopCheckSessionOnError",get:function(){return this._stopCheckSessionOnError}},{key:"query_status_response_type",get:function(){return this._query_status_response_type}},{key:"revokeAccessTokenOnSignout",get:function(){return this._revokeAccessTokenOnSignout}},{key:"redirectNavigator",get:function(){return this._redirectNavigator}},{key:"popupNavigator",get:function(){return this._popupNavigator}},{key:"iframeNavigator",get:function(){return this._iframeNavigator}},{key:"userStore",get:function(){return this._userStore}}]),e}(i.OidcClientSettings)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RedirectNavigator=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0);e.RedirectNavigator=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.prepare=function(){return Promise.resolve(this)},t.prototype.navigate=function(t){return t&&t.url?(t.useReplaceToNavigate?window.location.replace(t.url):window.location=t.url,Promise.resolve()):(i.Log.error("RedirectNavigator.navigate: No url provided"),Promise.reject(new Error("No url provided")))},n(t,[{key:"url",get:function(){return window.location.href}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PopupNavigator=void 0;var n=r(0),i=r(42);e.PopupNavigator=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.prepare=function(t){var e=new i.PopupWindow(t);return Promise.resolve(e)},t.prototype.callback=function t(e,r,o){n.Log.debug("PopupNavigator.callback");try{return i.PopupWindow.notifyOpener(e,r,o),Promise.resolve()}catch(t){return Promise.reject(t)}},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PopupWindow=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(3);e.PopupWindow=function(){function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._promise=new Promise((function(t,e){r._resolve=t,r._reject=e}));var n=e.popupWindowTarget||"_blank",o=e.popupWindowFeatures||"location=no,toolbar=no,width=500,height=500,left=100,top=100;";this._popup=window.open("",n,o),this._popup&&(i.Log.debug("PopupWindow.ctor: popup successfully created"),this._checkForPopupClosedTimer=window.setInterval(this._checkForPopupClosed.bind(this),500))}return t.prototype.navigate=function(t){return this._popup?t&&t.url?(i.Log.debug("PopupWindow.navigate: Setting URL in popup"),this._id=t.id,this._id&&(window["popupCallback_"+t.id]=this._callback.bind(this)),this._popup.focus(),this._popup.window.location=t.url):(this._error("PopupWindow.navigate: no url provided"),this._error("No url provided")):this._error("PopupWindow.navigate: Error opening popup window"),this.promise},t.prototype._success=function(t){i.Log.debug("PopupWindow.callback: Successful response from popup window"),this._cleanup(),this._resolve(t)},t.prototype._error=function(t){i.Log.error("PopupWindow.error: ",t),this._cleanup(),this._reject(new Error(t))},t.prototype.close=function(){this._cleanup(!1)},t.prototype._cleanup=function(t){i.Log.debug("PopupWindow.cleanup"),window.clearInterval(this._checkForPopupClosedTimer),this._checkForPopupClosedTimer=null,delete window["popupCallback_"+this._id],this._popup&&!t&&this._popup.close(),this._popup=null},t.prototype._checkForPopupClosed=function(){this._popup&&!this._popup.closed||this._error("Popup window closed")},t.prototype._callback=function(t,e){this._cleanup(e),t?(i.Log.debug("PopupWindow.callback success"),this._success({url:t})):(i.Log.debug("PopupWindow.callback: Invalid response from popup"),this._error("Invalid response from popup"))},t.notifyOpener=function(t,e,r){if(window.opener){if(t=t||window.location.href){var n=o.UrlUtility.parseUrlFragment(t,r);if(n.state){var s="popupCallback_"+n.state,a=window.opener[s];a?(i.Log.debug("PopupWindow.notifyOpener: passing url message to opener"),a(t,e)):i.Log.warn("PopupWindow.notifyOpener: no matching callback found on opener")}else i.Log.warn("PopupWindow.notifyOpener: no state found in response url")}}else i.Log.warn("PopupWindow.notifyOpener: no window.opener. Can't complete notification.")},n(t,[{key:"promise",get:function(){return this._promise}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IFrameNavigator=void 0;var n=r(0),i=r(44);e.IFrameNavigator=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.prepare=function(t){var e=new i.IFrameWindow(t);return Promise.resolve(e)},t.prototype.callback=function t(e){n.Log.debug("IFrameNavigator.callback");try{return i.IFrameWindow.notifyParent(e),Promise.resolve()}catch(t){return Promise.reject(t)}},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IFrameWindow=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0);e.IFrameWindow=function(){function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._promise=new Promise((function(t,e){r._resolve=t,r._reject=e})),this._boundMessageEvent=this._message.bind(this),window.addEventListener("message",this._boundMessageEvent,!1),this._frame=window.document.createElement("iframe"),this._frame.style.visibility="hidden",this._frame.style.position="absolute",this._frame.width=0,this._frame.height=0,window.document.body.appendChild(this._frame)}return t.prototype.navigate=function(t){if(t&&t.url){var e=t.silentRequestTimeout||1e4;i.Log.debug("IFrameWindow.navigate: Using timeout of:",e),this._timer=window.setTimeout(this._timeout.bind(this),e),this._frame.src=t.url}else this._error("No url provided");return this.promise},t.prototype._success=function(t){this._cleanup(),i.Log.debug("IFrameWindow: Successful response from frame window"),this._resolve(t)},t.prototype._error=function(t){this._cleanup(),i.Log.error(t),this._reject(new Error(t))},t.prototype.close=function(){this._cleanup()},t.prototype._cleanup=function(){this._frame&&(i.Log.debug("IFrameWindow: cleanup"),window.removeEventListener("message",this._boundMessageEvent,!1),window.clearTimeout(this._timer),window.document.body.removeChild(this._frame),this._timer=null,this._frame=null,this._boundMessageEvent=null)},t.prototype._timeout=function(){i.Log.debug("IFrameWindow.timeout"),this._error("Frame window timed out")},t.prototype._message=function(t){if(i.Log.debug("IFrameWindow.message"),this._timer&&t.origin===this._origin&&t.source===this._frame.contentWindow&&"string"==typeof t.data&&(t.data.startsWith("http://")||t.data.startsWith("https://"))){var e=t.data;e?this._success({url:e}):this._error("Invalid response from frame")}},t.notifyParent=function(t){i.Log.debug("IFrameWindow.notifyParent"),(t=t||window.location.href)&&(i.Log.debug("IFrameWindow.notifyParent: posting url message to parent"),window.parent.postMessage(t,location.protocol+"//"+location.host))},n(t,[{key:"promise",get:function(){return this._promise}},{key:"_origin",get:function(){return location.protocol+"//"+location.host}}]),t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UserManagerEvents=void 0;var n=r(0),i=r(16),o=r(17);e.UserManagerEvents=function(t){function e(r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n._userLoaded=new o.Event("User loaded"),n._userUnloaded=new o.Event("User unloaded"),n._silentRenewError=new o.Event("Silent renew error"),n._userSignedIn=new o.Event("User signed in"),n._userSignedOut=new o.Event("User signed out"),n._userSessionChanged=new o.Event("User session changed"),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.load=function(e){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];n.Log.debug("UserManagerEvents.load"),t.prototype.load.call(this,e),r&&this._userLoaded.raise(e)},e.prototype.unload=function(){n.Log.debug("UserManagerEvents.unload"),t.prototype.unload.call(this),this._userUnloaded.raise()},e.prototype.addUserLoaded=function(t){this._userLoaded.addHandler(t)},e.prototype.removeUserLoaded=function(t){this._userLoaded.removeHandler(t)},e.prototype.addUserUnloaded=function(t){this._userUnloaded.addHandler(t)},e.prototype.removeUserUnloaded=function(t){this._userUnloaded.removeHandler(t)},e.prototype.addSilentRenewError=function(t){this._silentRenewError.addHandler(t)},e.prototype.removeSilentRenewError=function(t){this._silentRenewError.removeHandler(t)},e.prototype._raiseSilentRenewError=function(t){n.Log.debug("UserManagerEvents._raiseSilentRenewError",t.message),this._silentRenewError.raise(t)},e.prototype.addUserSignedIn=function(t){this._userSignedIn.addHandler(t)},e.prototype.removeUserSignedIn=function(t){this._userSignedIn.removeHandler(t)},e.prototype._raiseUserSignedIn=function(){n.Log.debug("UserManagerEvents._raiseUserSignedIn"),this._userSignedIn.raise()},e.prototype.addUserSignedOut=function(t){this._userSignedOut.addHandler(t)},e.prototype.removeUserSignedOut=function(t){this._userSignedOut.removeHandler(t)},e.prototype._raiseUserSignedOut=function(){n.Log.debug("UserManagerEvents._raiseUserSignedOut"),this._userSignedOut.raise()},e.prototype.addUserSessionChanged=function(t){this._userSessionChanged.addHandler(t)},e.prototype.removeUserSessionChanged=function(t){this._userSessionChanged.removeHandler(t)},e.prototype._raiseUserSessionChanged=function(){n.Log.debug("UserManagerEvents._raiseUserSessionChanged"),this._userSessionChanged.raise()},e}(i.AccessTokenEvents)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Timer=void 0;var n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),i=r(0),o=r(1),s=r(17);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}e.Timer=function(t){function e(r){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.Global.timer,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;a(this,e);var s=u(this,t.call(this,r));return s._timer=n,s._nowFunc=i||function(){return Date.now()/1e3},s}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.init=function(t){t<=0&&(t=1),t=parseInt(t);var e=this.now+t;if(this.expiration===e&&this._timerHandle)i.Log.debug("Timer.init timer "+this._name+" skipping initialization since already initialized for expiration:",this.expiration);else{this.cancel(),i.Log.debug("Timer.init timer "+this._name+" for duration:",t),this._expiration=e;var r=5;t<r&&(r=t),this._timerHandle=this._timer.setInterval(this._callback.bind(this),1e3*r)}},e.prototype.cancel=function(){this._timerHandle&&(i.Log.debug("Timer.cancel: ",this._name),this._timer.clearInterval(this._timerHandle),this._timerHandle=null)},e.prototype._callback=function(){var e=this._expiration-this.now;i.Log.debug("Timer.callback; "+this._name+" timer expires in:",e),this._expiration<=this.now&&(this.cancel(),t.prototype.raise.call(this))},n(e,[{key:"now",get:function(){return parseInt(this._nowFunc())}},{key:"expiration",get:function(){return this._expiration}}]),e}(s.Event)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SilentRenewService=void 0;var n=r(0);e.SilentRenewService=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._userManager=e}return t.prototype.start=function(){this._callback||(this._callback=this._tokenExpiring.bind(this),this._userManager.events.addAccessTokenExpiring(this._callback),this._userManager.getUser().then((function(t){})).catch((function(t){n.Log.error("SilentRenewService.start: Error from getUser:",t.message)})))},t.prototype.stop=function(){this._callback&&(this._userManager.events.removeAccessTokenExpiring(this._callback),delete this._callback)},t.prototype._tokenExpiring=function(){var t=this;this._userManager.signinSilent().then((function(t){n.Log.debug("SilentRenewService._tokenExpiring: Silent token renewal successful")}),(function(e){n.Log.error("SilentRenewService._tokenExpiring: Error from signinSilent:",e.message),t._userManager.events._raiseSilentRenewError(e)}))},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CordovaPopupNavigator=void 0;var n=r(21);e.CordovaPopupNavigator=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.prepare=function(t){var e=new n.CordovaPopupWindow(t);return Promise.resolve(e)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CordovaIFrameNavigator=void 0;var n=r(21);e.CordovaIFrameNavigator=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}return t.prototype.prepare=function(t){t.popupWindowFeatures="hidden=yes";var e=new n.CordovaPopupWindow(t);return Promise.resolve(e)},t}()},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Version="1.11.5"}])},t.exports=n()},function(t,e,r){var n=r(3);t.exports=function(t,e){if(!n(t))return t;var r,i;if(e&&"function"==typeof(r=t.toString)&&!n(i=r.call(t)))return i;if("function"==typeof(r=t.valueOf)&&!n(i=r.call(t)))return i;if(!e&&"function"==typeof(r=t.toString)&&!n(i=r.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},function(t,e,r){var n,i=r(4),o=r(97),s=r(94),a=r(58),u=r(123),c=r(88),f=r(70),h=f("IE_PROTO"),l=function(){},p=function(t){return"<script>"+t+"<\/script>"},g=function(){try{n=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;g=n?function(t){t.write(p("")),t.close();var e=t.parentWindow.Object;return t=null,e}(n):((e=c("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(p("document.F=Object")),t.close(),t.F);for(var r=s.length;r--;)delete g.prototype[s[r]];return g()};a[h]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(l.prototype=i(t),r=new l,l.prototype=null,r[h]=t):r=g(),void 0===e?r:o(r,e)}},function(t,e,r){var n=r(9).f,i=r(12),o=r(6)("toStringTag");t.exports=function(t,e,r){t&&!i(t=r?t:t.prototype,o)&&n(t,o,{configurable:!0,value:e})}},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e){var r={}.toString;t.exports=function(t){return r.call(t).slice(8,-1)}},function(t,e,r){var n=r(4),i=r(126);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),e=r instanceof Array}catch(t){}return function(r,o){return n(r),i(o),e?t.call(r,o):r.__proto__=o,r}}():void 0)},function(t,e,r){"use strict";var n=r(1);t.exports=function(t,e){var r=[][t];return!!r&&n((function(){r.call(null,e||function(){throw 1},1)}))}},function(t,e,r){var n=r(4),i=r(19),o=r(6)("species");t.exports=function(t,e){var r,s=n(t).constructor;return void 0===s||null==(r=n(s)[o])?e:i(r)}},function(t,e,r){"use strict";var n=r(0),i=r(2),o=r(5),s=r(116),a=r(8),u=r(75),c=r(46),f=r(34),h=r(14),l=r(7),p=r(137),g=r(156),d=r(31),v=r(12),y=r(65),m=r(3),b=r(32),S=r(36),w=r(47).f,_=r(157),E=r(16).forEach,x=r(52),F=r(9),A=r(17),T=r(15),k=r(77),P=T.get,R=T.set,I=F.f,C=A.f,O=Math.round,D=i.RangeError,N=u.ArrayBuffer,L=u.DataView,j=a.NATIVE_ARRAY_BUFFER_VIEWS,M=a.TYPED_ARRAY_TAG,B=a.TypedArray,U=a.TypedArrayPrototype,H=a.aTypedArrayConstructor,V=a.isTypedArray,q=function(t,e){for(var r=0,n=e.length,i=new(H(t))(n);n>r;)i[r]=e[r++];return i},K=function(t,e){I(t,e,{get:function(){return P(this)[e]}})},W=function(t){var e;return t instanceof N||"ArrayBuffer"==(e=y(t))||"SharedArrayBuffer"==e},J=function(t,e){return V(t)&&"symbol"!=typeof e&&e in t&&String(+e)==String(e)},z=function(t,e){return J(t,e=d(e,!0))?f(2,t[e]):C(t,e)},Y=function(t,e,r){return!(J(t,e=d(e,!0))&&m(r)&&v(r,"value"))||v(r,"get")||v(r,"set")||r.configurable||v(r,"writable")&&!r.writable||v(r,"enumerable")&&!r.enumerable?I(t,e,r):(t[e]=r.value,t)};o?(j||(A.f=z,F.f=Y,K(U,"buffer"),K(U,"byteOffset"),K(U,"byteLength"),K(U,"length")),n({target:"Object",stat:!0,forced:!j},{getOwnPropertyDescriptor:z,defineProperty:Y}),t.exports=function(t,e,r){var o=t.match(/\d+$/)[0]/8,a=t+(r?"Clamped":"")+"Array",u="get"+t,f="set"+t,d=i[a],v=d,y=v&&v.prototype,F={},A=function(t,e){I(t,e,{get:function(){return function(t,e){var r=P(t);return r.view[u](e*o+r.byteOffset,!0)}(this,e)},set:function(t){return function(t,e,n){var i=P(t);r&&(n=(n=O(n))<0?0:n>255?255:255&n),i.view[f](e*o+i.byteOffset,n,!0)}(this,e,t)},enumerable:!0})};j?s&&(v=e((function(t,e,r,n){return c(t,v,a),k(m(e)?W(e)?void 0!==n?new d(e,g(r,o),n):void 0!==r?new d(e,g(r,o)):new d(e):V(e)?q(v,e):_.call(v,e):new d(p(e)),t,v)})),S&&S(v,B),E(w(d),(function(t){t in v||h(v,t,d[t])})),v.prototype=y):(v=e((function(t,e,r,n){c(t,v,a);var i,s,u,f=0,h=0;if(m(e)){if(!W(e))return V(e)?q(v,e):_.call(v,e);i=e,h=g(r,o);var d=e.byteLength;if(void 0===n){if(d%o)throw D("Wrong length");if((s=d-h)<0)throw D("Wrong length")}else if((s=l(n)*o)+h>d)throw D("Wrong length");u=s/o}else u=p(e),i=new N(s=u*o);for(R(t,{buffer:i,byteOffset:h,byteLength:s,length:u,view:new L(i)});f<u;)A(t,f++)})),S&&S(v,B),y=v.prototype=b(U)),y.constructor!==v&&h(y,"constructor",v),M&&h(y,M,a),F[a]=v,n({global:!0,forced:v!=d,sham:!j},F),"BYTES_PER_ELEMENT"in v||h(v,"BYTES_PER_ELEMENT",o),"BYTES_PER_ELEMENT"in y||h(y,"BYTES_PER_ELEMENT",o),x(a)}):t.exports=function(){}},function(t,e,r){var n=r(20),i=Math.max,o=Math.min;t.exports=function(t,e){var r=n(t);return r<0?i(r+e,0):o(r,e)}},function(t,e,r){var n,i,o=r(2),s=r(42),a=o.process,u=a&&a.versions,c=u&&u.v8;c?i=(n=c.split("."))[0]<4?1:n[0]+n[1]:s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(i=n[1]),t.exports=i&&+i},function(t,e,r){var n=r(23);t.exports=n("navigator","userAgent")||""},function(t,e,r){var n=r(19);t.exports=function(t,e,r){if(n(t),void 0===e)return t;switch(r){case 0:return function(){return t.call(e)};case 1:return function(r){return t.call(e,r)};case 2:return function(r,n){return t.call(e,r,n)};case 3:return function(r,n,i){return t.call(e,r,n,i)}}return function(){return t.apply(e,arguments)}}},function(t,e,r){var n=r(4),i=r(99),o=r(7),s=r(43),a=r(64),u=r(127),c=function(t,e){this.stopped=t,this.result=e};t.exports=function(t,e,r){var f,h,l,p,g,d,v,y=r&&r.that,m=!(!r||!r.AS_ENTRIES),b=!(!r||!r.IS_ITERATOR),S=!(!r||!r.INTERRUPTED),w=s(e,y,1+m+S),_=function(t){return f&&u(f),new c(!0,t)},E=function(t){return m?(n(t),S?w(t[0],t[1],_):w(t[0],t[1])):S?w(t,_):w(t)};if(b)f=t;else{if("function"!=typeof(h=a(t)))throw TypeError("Target is not iterable");if(i(h)){for(l=0,p=o(t.length);p>l;l++)if((g=E(t[l]))&&g instanceof c)return g;return new c(!1)}f=h.call(t)}for(d=f.next;!(v=d.call(f)).done;){try{g=E(v.value)}catch(t){throw u(f),t}if("object"==typeof g&&g&&g instanceof c)return g}return new c(!1)}},function(t,e,r){var n=r(6),i=r(32),o=r(9),s=n("unscopables"),a=Array.prototype;null==a[s]&&o.f(a,s,{configurable:!0,value:i(null)}),t.exports=function(t){a[s][t]=!0}},function(t,e){t.exports=function(t,e,r){if(!(t instanceof e))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return t}},function(t,e,r){var n=r(121),i=r(94).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,i)}},function(t,e,r){var n=r(35);t.exports=Array.isArray||function(t){return"Array"==n(t)}},function(t,e,r){"use strict";var n=r(31),i=r(9),o=r(34);t.exports=function(t,e,r){var s=n(e);s in t?i.f(t,s,o(0,r)):t[s]=r}},function(t,e,r){var n=r(58),i=r(3),o=r(12),s=r(9).f,a=r(57),u=r(67),c=a("meta"),f=0,h=Object.isExtensible||function(){return!0},l=function(t){s(t,c,{value:{objectID:"O"+f++,weakData:{}}})},p=t.exports={REQUIRED:!1,fastKey:function(t,e){if(!i(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,c)){if(!h(t))return"F";if(!e)return"E";l(t)}return t[c].objectID},getWeakData:function(t,e){if(!o(t,c)){if(!h(t))return!0;if(!e)return!1;l(t)}return t[c].weakData},onFreeze:function(t){return u&&p.REQUIRED&&h(t)&&!o(t,c)&&l(t),t}};n[c]=!0},function(t,e,r){var n=r(35),i=r(2);t.exports="process"==n(i.process)},function(t,e,r){"use strict";var n=r(23),i=r(9),o=r(6),s=r(5),a=o("species");t.exports=function(t){var e=n(t),r=i.f;s&&e&&!e[a]&&r(e,a,{configurable:!0,get:function(){return this}})}},function(t,e,r){var n=r(18);t.exports=function(t,e,r){for(var i in e)n(t,i,e[i],r);return t}},function(t,e,r){var n=r(13),i="["+r(79)+"]",o=RegExp("^"+i+i+"*"),s=RegExp(i+i+"*$"),a=function(t){return function(e){var r=String(n(e));return 1&t&&(r=r.replace(o,"")),2&t&&(r=r.replace(s,"")),r}};t.exports={start:a(1),end:a(2),trim:a(3)}},function(t,e,r){"use strict";var n=r(4);t.exports=function(){var t=n(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},function(t,e,r){var n=r(1),i=r(35),o="".split;t.exports=n((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==i(t)?o.call(t,""):Object(t)}:Object},function(t,e){var r=0,n=Math.random();t.exports=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++r+n).toString(36)}},function(t,e){t.exports={}},function(t,e,r){var n=r(22),i=r(7),o=r(40),s=function(t){return function(e,r,s){var a,u=n(e),c=i(u.length),f=o(s,c);if(t&&r!=r){for(;c>f;)if((a=u[f++])!=a)return!0}else for(;c>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},function(t,e,r){var n=r(1),i=/#|\.prototype\./,o=function(t,e){var r=a[s(t)];return r==c||r!=u&&("function"==typeof e?n(e):!!e)},s=o.normalize=function(t){return String(t).replace(i,".").toLowerCase()},a=o.data={},u=o.NATIVE="N",c=o.POLYFILL="P";t.exports=o},function(t,e,r){var n=r(121),i=r(94);t.exports=Object.keys||function(t){return n(t,i)}},function(t,e,r){var n=r(3),i=r(48),o=r(6)("species");t.exports=function(t,e){var r;return i(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!i(r.prototype)?n(r)&&null===(r=r[o])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===e?0:e)}},function(t,e){t.exports={}},function(t,e,r){var n=r(65),i=r(63),o=r(6)("iterator");t.exports=function(t){if(null!=t)return t[o]||t["@@iterator"]||i[n(t)]}},function(t,e,r){var n=r(100),i=r(35),o=r(6)("toStringTag"),s="Arguments"==i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),o))?r:s?i(e):"Object"==(n=i(e))&&"function"==typeof e.callee?"Arguments":n}},function(t,e,r){var n=r(1),i=r(6),o=r(41),s=i("species");t.exports=function(t){return o>=51||!n((function(){var e=[];return(e.constructor={})[s]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},function(t,e,r){var n=r(1);t.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},function(t,e,r){var n=r(3),i=r(35),o=r(6)("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==i(t))}},function(t,e,r){"use strict";var n={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,o=i&&!n.call({1:2},1);e.f=o?function(t){var e=i(this,t);return!!e&&e.enumerable}:n},function(t,e,r){var n=r(71),i=r(57),o=n("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},function(t,e,r){var n=r(28),i=r(91);(t.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.15.2",mode:n?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},function(t,e,r){var n=r(6)("iterator"),i=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){i=!0}};s[n]=function(){return this},Array.from(s,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!i)return!1;var r=!1;try{var o={};o[n]=function(){return{next:function(){return{done:r=!0}}}},t(o)}catch(t){}return r}},function(t,e,r){"use strict";var n=r(22),i=r(45),o=r(63),s=r(15),a=r(102),u=s.set,c=s.getterFor("Array Iterator");t.exports=a(Array,"Array",(function(t,e){u(this,{type:"Array Iterator",target:n(t),index:0,kind:e})}),(function(){var t=c(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},function(t,e,r){var n=r(19),i=r(10),o=r(56),s=r(7),a=function(t){return function(e,r,a,u){n(r);var c=i(e),f=o(c),h=s(c.length),l=t?h-1:0,p=t?-1:1;if(a<2)for(;;){if(l in f){u=f[l],l+=p;break}if(l+=p,t?l<0:h<=l)throw TypeError("Reduce of empty array with no initial value")}for(;t?l>=0:h>l;l+=p)l in f&&(u=r(u,f[l],l,c));return u}};t.exports={left:a(!1),right:a(!0)}},function(t,e,r){"use strict";var n=r(2),i=r(5),o=r(105),s=r(14),a=r(53),u=r(1),c=r(46),f=r(20),h=r(7),l=r(137),p=r(223),g=r(29),d=r(36),v=r(47).f,y=r(9).f,m=r(101),b=r(33),S=r(15),w=S.get,_=S.set,E=n.ArrayBuffer,x=E,F=n.DataView,A=F&&F.prototype,T=Object.prototype,k=n.RangeError,P=p.pack,R=p.unpack,I=function(t){return[255&t]},C=function(t){return[255&t,t>>8&255]},O=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},D=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},N=function(t){return P(t,23,4)},L=function(t){return P(t,52,8)},j=function(t,e){y(t.prototype,e,{get:function(){return w(this)[e]}})},M=function(t,e,r,n){var i=l(r),o=w(t);if(i+e>o.byteLength)throw k("Wrong index");var s=w(o.buffer).bytes,a=i+o.byteOffset,u=s.slice(a,a+e);return n?u:u.reverse()},B=function(t,e,r,n,i,o){var s=l(r),a=w(t);if(s+e>a.byteLength)throw k("Wrong index");for(var u=w(a.buffer).bytes,c=s+a.byteOffset,f=n(+i),h=0;h<e;h++)u[c+h]=f[o?h:e-h-1]};if(o){if(!u((function(){E(1)}))||!u((function(){new E(-1)}))||u((function(){return new E,new E(1.5),new E(NaN),"ArrayBuffer"!=E.name}))){for(var U,H=(x=function(t){return c(this,x),new E(l(t))}).prototype=E.prototype,V=v(E),q=0;V.length>q;)(U=V[q++])in x||s(x,U,E[U]);H.constructor=x}d&&g(A)!==T&&d(A,T);var K=new F(new x(2)),W=A.setInt8;K.setInt8(0,2147483648),K.setInt8(1,2147483649),!K.getInt8(0)&&K.getInt8(1)||a(A,{setInt8:function(t,e){W.call(this,t,e<<24>>24)},setUint8:function(t,e){W.call(this,t,e<<24>>24)}},{unsafe:!0})}else x=function(t){c(this,x,"ArrayBuffer");var e=l(t);_(this,{bytes:m.call(new Array(e),0),byteLength:e}),i||(this.byteLength=e)},F=function(t,e,r){c(this,F,"DataView"),c(t,x,"DataView");var n=w(t).byteLength,o=f(e);if(o<0||o>n)throw k("Wrong offset");if(o+(r=void 0===r?n-o:h(r))>n)throw k("Wrong length");_(this,{buffer:t,byteLength:r,byteOffset:o}),i||(this.buffer=t,this.byteLength=r,this.byteOffset=o)},i&&(j(x,"byteLength"),j(F,"buffer"),j(F,"byteLength"),j(F,"byteOffset")),a(F.prototype,{getInt8:function(t){return M(this,1,t)[0]<<24>>24},getUint8:function(t){return M(this,1,t)[0]},getInt16:function(t){var e=M(this,2,t,arguments.length>1?arguments[1]:void 0);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=M(this,2,t,arguments.length>1?arguments[1]:void 0);return e[1]<<8|e[0]},getInt32:function(t){return D(M(this,4,t,arguments.length>1?arguments[1]:void 0))},getUint32:function(t){return D(M(this,4,t,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(t){return R(M(this,4,t,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(t){return R(M(this,8,t,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(t,e){B(this,1,t,I,e)},setUint8:function(t,e){B(this,1,t,I,e)},setInt16:function(t,e){B(this,2,t,C,e,arguments.length>2?arguments[2]:void 0)},setUint16:function(t,e){B(this,2,t,C,e,arguments.length>2?arguments[2]:void 0)},setInt32:function(t,e){B(this,4,t,O,e,arguments.length>2?arguments[2]:void 0)},setUint32:function(t,e){B(this,4,t,O,e,arguments.length>2?arguments[2]:void 0)},setFloat32:function(t,e){B(this,4,t,N,e,arguments.length>2?arguments[2]:void 0)},setFloat64:function(t,e){B(this,8,t,L,e,arguments.length>2?arguments[2]:void 0)}});b(x,"ArrayBuffer"),b(F,"DataView"),t.exports={ArrayBuffer:x,DataView:F}},function(t,e,r){"use strict";var n=r(0),i=r(2),o=r(60),s=r(18),a=r(50),u=r(44),c=r(46),f=r(3),h=r(1),l=r(72),p=r(33),g=r(77);t.exports=function(t,e,r){var d=-1!==t.indexOf("Map"),v=-1!==t.indexOf("Weak"),y=d?"set":"add",m=i[t],b=m&&m.prototype,S=m,w={},_=function(t){var e=b[t];s(b,t,"add"==t?function(t){return e.call(this,0===t?0:t),this}:"delete"==t?function(t){return!(v&&!f(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return v&&!f(t)?void 0:e.call(this,0===t?0:t)}:"has"==t?function(t){return!(v&&!f(t))&&e.call(this,0===t?0:t)}:function(t,r){return e.call(this,0===t?0:t,r),this})};if(o(t,"function"!=typeof m||!(v||b.forEach&&!h((function(){(new m).entries().next()})))))S=r.getConstructor(e,t,d,y),a.REQUIRED=!0;else if(o(t,!0)){var E=new S,x=E[y](v?{}:-0,1)!=E,F=h((function(){E.has(1)})),A=l((function(t){new m(t)})),T=!v&&h((function(){for(var t=new m,e=5;e--;)t[y](e,e);return!t.has(-0)}));A||((S=e((function(e,r){c(e,S,t);var n=g(new m,e,S);return null!=r&&u(r,n[y],{that:n,AS_ENTRIES:d}),n}))).prototype=b,b.constructor=S),(F||T)&&(_("delete"),_("has"),d&&_("get")),(T||x)&&_(y),v&&b.clear&&delete b.clear}return w[t]=S,n({global:!0,forced:S!=m},w),p(S,t),v||r.setStrong(S,t,d),S}},function(t,e,r){var n=r(3),i=r(36);t.exports=function(t,e,r){var o,s;return i&&"function"==typeof(o=e.constructor)&&o!==r&&n(s=o.prototype)&&s!==r.prototype&&i(t,s),t}},function(t,e){var r=Math.expm1,n=Math.exp;t.exports=!r||r(10)>22025.465794806718||r(10)<22025.465794806718||-2e-17!=r(-2e-17)?function(t){return 0==(t=+t)?t:t>-1e-6&&t<1e-6?t+t*t/2:n(t)-1}:r},function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},function(t,e,r){"use strict";var n=r(28),i=r(2),o=r(1),s=r(104);t.exports=n||!o((function(){if(!(s&&s<535)){var t=Math.random();__defineSetter__.call(null,t,(function(){})),delete i[t]}}))},function(t,e,r){"use strict";var n=r(19),i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},function(t,e,r){var n=r(1),i=function(t,e){return RegExp(t,e)};e.UNSUPPORTED_Y=n((function(){var t=i("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),e.BROKEN_CARET=n((function(){var t=i("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},function(t,e,r){"use strict";var n,i,o=r(55),s=r(82),a=r(71),u=r(32),c=r(15).get,f=r(111),h=r(152),l=RegExp.prototype.exec,p=a("native-string-replace",String.prototype.replace),g=l,d=(n=/a/,i=/b*/g,l.call(n,"a"),l.call(i,"a"),0!==n.lastIndex||0!==i.lastIndex),v=s.UNSUPPORTED_Y||s.BROKEN_CARET,y=void 0!==/()??/.exec("")[1];(d||y||v||f||h)&&(g=function(t){var e,r,n,i,s,a,f,h=this,m=c(h),b=m.raw;if(b)return b.lastIndex=h.lastIndex,e=g.call(b,t),h.lastIndex=b.lastIndex,e;var S=m.groups,w=v&&h.sticky,_=o.call(h),E=h.source,x=0,F=t;if(w&&(-1===(_=_.replace("y","")).indexOf("g")&&(_+="g"),F=String(t).slice(h.lastIndex),h.lastIndex>0&&(!h.multiline||h.multiline&&"\n"!==t[h.lastIndex-1])&&(E="(?: "+E+")",F=" "+F,x++),r=new RegExp("^(?:"+E+")",_)),y&&(r=new RegExp("^"+E+"$(?!\\s)",_)),d&&(n=h.lastIndex),i=l.call(w?r:h,F),w?i?(i.input=i.input.slice(x),i[0]=i[0].slice(x),i.index=h.lastIndex,h.lastIndex+=i[0].length):h.lastIndex=0:d&&i&&(h.lastIndex=h.global?i.index+i[0].length:n),y&&i&&i.length>1&&p.call(i[0],r,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)})),i&&S)for(i.groups=a=u(null),s=0;s<S.length;s++)a[(f=S[s])[0]]=i[f[1]];return i}),t.exports=g},function(t,e,r){var n=r(20),i=r(13),o=function(t){return function(e,r){var o,s,a=String(i(e)),u=n(r),c=a.length;return u<0||u>=c?t?"":void 0:(o=a.charCodeAt(u))<55296||o>56319||u+1===c||(s=a.charCodeAt(u+1))<56320||s>57343?t?a.charAt(u):o:t?a.slice(u,u+2):s-56320+(o-55296<<10)+65536}};t.exports={codeAt:o(!1),charAt:o(!0)}},function(t,e,r){"use strict";r(112);var n=r(18),i=r(83),o=r(1),s=r(6),a=r(14),u=s("species"),c=RegExp.prototype;t.exports=function(t,e,r,f){var h=s(t),l=!o((function(){var e={};return e[h]=function(){return 7},7!=""[t](e)})),p=l&&!o((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[u]=function(){return r},r.flags="",r[h]=/./[h]),r.exec=function(){return e=!0,null},r[h](""),!e}));if(!l||!p||r){var g=/./[h],d=e(h,""[t],(function(t,e,r,n,o){var s=e.exec;return s===i||s===c.exec?l&&!o?{done:!0,value:g.call(e,r,n)}:{done:!0,value:t.call(r,e,n)}:{done:!1}}));n(String.prototype,t,d[0]),n(c,h,d[1])}f&&a(c[h],"sham",!0)}},function(t,e,r){"use strict";var n=r(84).charAt;t.exports=function(t,e,r){return e+(r?n(t,e).length:1)}},function(t,e,r){var n=r(35),i=r(83);t.exports=function(t,e){var r=t.exec;if("function"==typeof r){var o=r.call(t,e);if("object"!=typeof o)throw TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==n(t))throw TypeError("RegExp#exec called on incompatible receiver");return i.call(t,e)}},function(t,e,r){var n=r(2),i=r(3),o=n.document,s=i(o)&&i(o.createElement);t.exports=function(t){return s?o.createElement(t):{}}},function(t,e,r){var n=r(2),i=r(14);t.exports=function(t,e){try{i(n,t,e)}catch(r){n[t]=e}return e}},function(t,e,r){var n=r(91),i=Function.toString;"function"!=typeof n.inspectSource&&(n.inspectSource=function(t){return i.call(t)}),t.exports=n.inspectSource},function(t,e,r){var n=r(2),i=r(89),o=n["__core-js_shared__"]||i("__core-js_shared__",{});t.exports=o},function(t,e,r){var n=r(23),i=r(47),o=r(95),s=r(4);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(s(t)),r=o.f;return r?e.concat(r(t)):e}},function(t,e,r){var n=r(2);t.exports=n},function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e,r){var n=r(41),i=r(1);t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},function(t,e,r){var n=r(5),i=r(9),o=r(4),s=r(61);t.exports=n?Object.defineProperties:function(t,e){o(t);for(var r,n=s(e),a=n.length,u=0;a>u;)i.f(t,r=n[u++],e[r]);return t}},function(t,e,r){var n=r(1);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},function(t,e,r){var n=r(6),i=r(63),o=n("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||s[o]===t)}},function(t,e,r){var n={};n[r(6)("toStringTag")]="z",t.exports="[object z]"===String(n)},function(t,e,r){"use strict";var n=r(10),i=r(40),o=r(7);t.exports=function(t){for(var e=n(this),r=o(e.length),s=arguments.length,a=i(s>1?arguments[1]:void 0,r),u=s>2?arguments[2]:void 0,c=void 0===u?r:i(u,r);c>a;)e[a++]=t;return e}},function(t,e,r){"use strict";var n=r(0),i=r(103),o=r(29),s=r(36),a=r(33),u=r(14),c=r(18),f=r(6),h=r(28),l=r(63),p=r(132),g=p.IteratorPrototype,d=p.BUGGY_SAFARI_ITERATORS,v=f("iterator"),y=function(){return this};t.exports=function(t,e,r,f,p,m,b){i(r,e,f);var S,w,_,E=function(t){if(t===p&&k)return k;if(!d&&t in A)return A[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},x=e+" Iterator",F=!1,A=t.prototype,T=A[v]||A["@@iterator"]||p&&A[p],k=!d&&T||E(p),P="Array"==e&&A.entries||T;if(P&&(S=o(P.call(new t)),g!==Object.prototype&&S.next&&(h||o(S)===g||(s?s(S,g):"function"!=typeof S[v]&&u(S,v,y)),a(S,x,!0,!0),h&&(l[x]=y))),"values"==p&&T&&"values"!==T.name&&(F=!0,k=function(){return T.call(this)}),h&&!b||A[v]===k||u(A,v,k),l[e]=k,p)if(w={values:E("values"),keys:m?k:E("keys"),entries:E("entries")},b)for(_ in w)(d||F||!(_ in A))&&c(A,_,w[_]);else n({target:e,proto:!0,forced:d||F},w);return w}},function(t,e,r){"use strict";var n=r(132).IteratorPrototype,i=r(32),o=r(34),s=r(33),a=r(63),u=function(){return this};t.exports=function(t,e,r){var c=e+" Iterator";return t.prototype=i(n,{next:o(1,r)}),s(t,c,!1,!0),a[c]=u,t}},function(t,e,r){var n=r(42).match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},function(t,e){t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},function(t,e,r){var n=r(7),i=r(107),o=r(13),s=Math.ceil,a=function(t){return function(e,r,a){var u,c,f=String(o(e)),h=f.length,l=void 0===a?" ":String(a),p=n(r);return p<=h||""==l?f:(u=p-h,(c=i.call(l,s(u/l.length))).length>u&&(c=c.slice(0,u)),t?f+c:c+f)}};t.exports={start:a(!1),end:a(!0)}},function(t,e,r){"use strict";var n=r(20),i=r(13);t.exports=function(t){var e=String(i(this)),r="",o=n(t);if(o<0||o==1/0)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(e+=e))1&o&&(r+=e);return r}},function(t,e){t.exports=Math.sign||function(t){return 0==(t=+t)||t!=t?t:t<0?-1:1}},function(t,e,r){var n,i,o,s=r(2),a=r(1),u=r(43),c=r(123),f=r(88),h=r(149),l=r(51),p=s.location,g=s.setImmediate,d=s.clearImmediate,v=s.process,y=s.MessageChannel,m=s.Dispatch,b=0,S={},w=function(t){if(S.hasOwnProperty(t)){var e=S[t];delete S[t],e()}},_=function(t){return function(){w(t)}},E=function(t){w(t.data)},x=function(t){s.postMessage(t+"",p.protocol+"//"+p.host)};g&&d||(g=function(t){for(var e=[],r=1;arguments.length>r;)e.push(arguments[r++]);return S[++b]=function(){("function"==typeof t?t:Function(t)).apply(void 0,e)},n(b),b},d=function(t){delete S[t]},l?n=function(t){v.nextTick(_(t))}:m&&m.now?n=function(t){m.now(_(t))}:y&&!h?(o=(i=new y).port2,i.port1.onmessage=E,n=u(o.postMessage,o,1)):s.addEventListener&&"function"==typeof postMessage&&!s.importScripts&&p&&"file:"!==p.protocol&&!a(x)?(n=x,s.addEventListener("message",E,!1)):n="onreadystatechange"in f("script")?function(t){c.appendChild(f("script")).onreadystatechange=function(){c.removeChild(this),w(t)}}:function(t){setTimeout(_(t),0)}),t.exports={set:g,clear:d}},function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},function(t,e,r){var n=r(1);t.exports=n((function(){var t=RegExp(".","string".charAt(0));return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)}))},function(t,e,r){"use strict";var n=r(0),i=r(83);n({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},function(t,e,r){var n=r(68);t.exports=function(t){if(n(t))throw TypeError("The method doesn't accept regular expressions");return t}},function(t,e,r){var n=r(6)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,"/./"[t](e)}catch(t){}}return!1}},function(t,e,r){var n=r(1),i=r(79);t.exports=function(t){return n((function(){return!!i[t]()||"​᠎"!="​᠎"[t]()||i[t].name!==t}))}},function(t,e,r){var n=r(2),i=r(1),o=r(72),s=r(8).NATIVE_ARRAY_BUFFER_VIEWS,a=n.ArrayBuffer,u=n.Int8Array;t.exports=!s||!i((function(){u(1)}))||!i((function(){new u(-1)}))||!o((function(t){new u,new u(null),new u(1.5),new u(t)}),!0)||i((function(){return 1!==new u(new a(2),1,void 0).length}))},function(t,e,r){var n=function(t){"use strict";var e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function a(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{a({},"")}catch(t){a=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof h?e:h,o=Object.create(i.prototype),s=new E(n||[]);return o._invoke=function(t,e,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return F()}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var a=S(s,r);if(a){if(a===f)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=c(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===f)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(t,r,s),o}function c(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var f={};function h(){}function l(){}function p(){}var g={};g[i]=function(){return this};var d=Object.getPrototypeOf,v=d&&d(d(x([])));v&&v!==e&&r.call(v,i)&&(g=v);var y=p.prototype=h.prototype=Object.create(g);function m(t){["next","throw","return"].forEach((function(e){a(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){var n;this._invoke=function(i,o){function s(){return new e((function(n,s){!function n(i,o,s,a){var u=c(t[i],t,o);if("throw"!==u.type){var f=u.arg,h=f.value;return h&&"object"==typeof h&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,s,a)}),(function(t){n("throw",t,s,a)})):e.resolve(h).then((function(t){f.value=t,s(f)}),(function(t){return n("throw",t,s,a)}))}a(u.arg)}(i,o,n,s)}))}return n=n?n.then(s,s):s()}}function S(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method))return f;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=c(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function w(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(w,this),this.reset(!0)}function x(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:F}}function F(){return{value:void 0,done:!0}}return l.prototype=y.constructor=p,p.constructor=l,l.displayName=a(p,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===l||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,a(t,s,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},m(b.prototype),b.prototype[o]=function(){return this},t.AsyncIterator=b,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var s=new b(u(e,r,n,i),o);return t.isGeneratorFunction(r)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},m(y),a(y,s,"Generator"),y[i]=function(){return this},y.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=x,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return s.type="throw",s.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var a=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(a&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;_(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:x(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}(t.exports);try{regeneratorRuntime=n}catch(t){Function("r","regeneratorRuntime = r")(n)}},function(t,e,r){var n=r(5),i=r(1),o=r(88);t.exports=!n&&!i((function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},function(t,e,r){var n=r(2),i=r(90),o=n.WeakMap;t.exports="function"==typeof o&&/native code/.test(i(o))},function(t,e,r){var n=r(12),i=r(92),o=r(17),s=r(9);t.exports=function(t,e){for(var r=i(e),a=s.f,u=o.f,c=0;c<r.length;c++){var f=r[c];n(t,f)||a(t,f,u(e,f))}}},function(t,e,r){var n=r(12),i=r(22),o=r(59).indexOf,s=r(58);t.exports=function(t,e){var r,a=i(t),u=0,c=[];for(r in a)!n(s,r)&&n(a,r)&&c.push(r);for(;e.length>u;)n(a,r=e[u++])&&(~o(c,r)||c.push(r));return c}},function(t,e,r){var n=r(96);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(t,e,r){var n=r(23);t.exports=n("document","documentElement")},function(t,e,r){var n=r(22),i=r(47).f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return s&&"[object Window]"==o.call(t)?function(t){try{return i(t)}catch(t){return s.slice()}}(t):i(n(t))}},function(t,e,r){var n=r(6);e.f=n},function(t,e,r){var n=r(3);t.exports=function(t){if(!n(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype");return t}},function(t,e,r){var n=r(4);t.exports=function(t){var e=t.return;if(void 0!==e)return n(e.call(t)).value}},function(t,e,r){"use strict";var n=r(10),i=r(40),o=r(7),s=Math.min;t.exports=[].copyWithin||function(t,e){var r=n(this),a=o(r.length),u=i(t,a),c=i(e,a),f=arguments.length>2?arguments[2]:void 0,h=s((void 0===f?a:i(f,a))-c,a-u),l=1;for(c<u&&u<c+h&&(l=-1,c+=h-1,u+=h-1);h-- >0;)c in r?r[u]=r[c]:delete r[u],u+=l,c+=l;return r}},function(t,e,r){"use strict";var n=r(48),i=r(7),o=r(43),s=function(t,e,r,a,u,c,f,h){for(var l,p=u,g=0,d=!!f&&o(f,h,3);g<a;){if(g in r){if(l=d?d(r[g],g,e):r[g],c>0&&n(l))p=s(t,e,l,i(l.length),p,c-1)-1;else{if(p>=9007199254740991)throw TypeError("Exceed the acceptable array length");t[p]=l}p++}g++}return p};t.exports=s},function(t,e,r){"use strict";var n=r(16).forEach,i=r(37)("forEach");t.exports=i?[].forEach:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}},function(t,e,r){"use strict";var n=r(43),i=r(10),o=r(204),s=r(99),a=r(7),u=r(49),c=r(64);t.exports=function(t){var e,r,f,h,l,p,g=i(t),d="function"==typeof this?this:Array,v=arguments.length,y=v>1?arguments[1]:void 0,m=void 0!==y,b=c(g),S=0;if(m&&(y=n(y,v>2?arguments[2]:void 0,2)),null==b||d==Array&&s(b))for(r=new d(e=a(g.length));e>S;S++)p=m?y(g[S],S):g[S],u(r,S,p);else for(l=(h=b.call(g)).next,r=new d;!(f=l.call(h)).done;S++)p=m?o(h,y,[f.value,S],!0):f.value,u(r,S,p);return r.length=S,r}},function(t,e,r){"use strict";var n,i,o,s=r(1),a=r(29),u=r(14),c=r(12),f=r(6),h=r(28),l=f("iterator"),p=!1;[].keys&&("next"in(o=[].keys())?(i=a(a(o)))!==Object.prototype&&(n=i):p=!0);var g=null==n||s((function(){var t={};return n[l].call(t)!==t}));g&&(n={}),h&&!g||c(n,l)||u(n,l,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:p}},function(t,e,r){"use strict";var n=r(22),i=r(20),o=r(7),s=r(37),a=Math.min,u=[].lastIndexOf,c=!!u&&1/[1].lastIndexOf(1,-0)<0,f=s("lastIndexOf"),h=c||!f;t.exports=h?function(t){if(c)return u.apply(this,arguments)||0;var e=n(this),r=o(e.length),s=r-1;for(arguments.length>1&&(s=a(s,i(arguments[1]))),s<0&&(s=r+s);s>=0;s--)if(s in e&&e[s]===t)return s||0;return-1}:u},function(t,e){var r=Math.floor,n=function(t,e){var s=t.length,a=r(s/2);return s<8?i(t,e):o(n(t.slice(0,a),e),n(t.slice(a),e),e)},i=function(t,e){for(var r,n,i=t.length,o=1;o<i;){for(n=o,r=t[o];n&&e(t[n-1],r)>0;)t[n]=t[--n];n!==o++&&(t[n]=r)}return t},o=function(t,e,r){for(var n=t.length,i=e.length,o=0,s=0,a=[];o<n||s<i;)o<n&&s<i?a.push(r(t[o],e[s])<=0?t[o++]:e[s++]):a.push(o<n?t[o++]:e[s++]);return a};t.exports=n},function(t,e,r){var n=r(42).match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},function(t,e,r){var n=r(42);t.exports=/MSIE|Trident/.test(n)},function(t,e,r){var n=r(20),i=r(7);t.exports=function(t){if(void 0===t)return 0;var e=n(t),r=i(e);if(e!==r)throw RangeError("Wrong length or index");return r}},function(t,e,r){"use strict";var n=r(19),i=r(3),o=[].slice,s={},a=function(t,e,r){if(!(e in s)){for(var n=[],i=0;i<e;i++)n[i]="a["+i+"]";s[e]=Function("C,a","return new C("+n.join(",")+")")}return s[e](t,r)};t.exports=Function.bind||function(t){var e=n(this),r=o.call(arguments,1),s=function(){var n=r.concat(o.call(arguments));return this instanceof s?a(e,n.length,n):e.apply(t,n)};return i(e.prototype)&&(s.prototype=e.prototype),s}},function(t,e,r){"use strict";var n=r(9).f,i=r(32),o=r(53),s=r(43),a=r(46),u=r(44),c=r(102),f=r(52),h=r(5),l=r(50).fastKey,p=r(15),g=p.set,d=p.getterFor;t.exports={getConstructor:function(t,e,r,c){var f=t((function(t,n){a(t,f,e),g(t,{type:e,index:i(null),first:void 0,last:void 0,size:0}),h||(t.size=0),null!=n&&u(n,t[c],{that:t,AS_ENTRIES:r})})),p=d(e),v=function(t,e,r){var n,i,o=p(t),s=y(t,e);return s?s.value=r:(o.last=s={index:i=l(e,!0),key:e,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=s),n&&(n.next=s),h?o.size++:t.size++,"F"!==i&&(o.index[i]=s)),t},y=function(t,e){var r,n=p(t),i=l(e);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==e)return r};return o(f.prototype,{clear:function(){for(var t=p(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,h?t.size=0:this.size=0},delete:function(t){var e=p(this),r=y(this,t);if(r){var n=r.next,i=r.previous;delete e.index[r.index],r.removed=!0,i&&(i.next=n),n&&(n.previous=i),e.first==r&&(e.first=n),e.last==r&&(e.last=i),h?e.size--:this.size--}return!!r},forEach:function(t){for(var e,r=p(this),n=s(t,arguments.length>1?arguments[1]:void 0,3);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!y(this,t)}}),o(f.prototype,r?{get:function(t){var e=y(this,t);return e&&e.value},set:function(t,e){return v(this,0===t?0:t,e)}}:{add:function(t){return v(this,t=0===t?0:t,t)}}),h&&n(f.prototype,"size",{get:function(){return p(this).size}}),f},setStrong:function(t,e,r){var n=e+" Iterator",i=d(e),o=d(n);c(t,e,(function(t,e){g(this,{type:n,target:t,state:i(t),kind:e,last:void 0})}),(function(){for(var t=o(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?"keys"==e?{value:r.key,done:!1}:"values"==e?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),f(e)}}},function(t,e){var r=Math.log;t.exports=Math.log1p||function(t){return(t=+t)>-1e-8&&t<1e-8?t-t*t/2:r(1+t)}},function(t,e,r){var n=r(3),i=Math.floor;t.exports=function(t){return!n(t)&&isFinite(t)&&i(t)===t}},function(t,e,r){var n=r(2),i=r(54).trim,o=r(79),s=n.parseFloat,a=1/s(o+"-0")!=-1/0;t.exports=a?function(t){var e=i(String(t)),r=s(e);return 0===r&&"-"==e.charAt(0)?-0:r}:s},function(t,e,r){var n=r(2),i=r(54).trim,o=r(79),s=n.parseInt,a=/^[+-]?0[Xx]/,u=8!==s(o+"08")||22!==s(o+"0x16");t.exports=u?function(t,e){var r=i(String(t));return s(r,e>>>0||(a.test(r)?16:10))}:s},function(t,e,r){var n=r(35);t.exports=function(t){if("number"!=typeof t&&"Number"!=n(t))throw TypeError("Incorrect invocation");return+t}},function(t,e,r){"use strict";var n=r(5),i=r(1),o=r(61),s=r(95),a=r(69),u=r(10),c=r(56),f=Object.assign,h=Object.defineProperty;t.exports=!f||i((function(){if(n&&1!==f({b:1},f(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol();return t[r]=7,"abcdefghijklmnopqrst".split("").forEach((function(t){e[t]=t})),7!=f({},t)[r]||"abcdefghijklmnopqrst"!=o(f({},e)).join("")}))?function(t,e){for(var r=u(t),i=arguments.length,f=1,h=s.f,l=a.f;i>f;)for(var p,g=c(arguments[f++]),d=h?o(g).concat(h(g)):o(g),v=d.length,y=0;v>y;)p=d[y++],n&&!l.call(g,p)||(r[p]=g[p]);return r}:f},function(t,e,r){var n=r(5),i=r(61),o=r(22),s=r(69).f,a=function(t){return function(e){for(var r,a=o(e),u=i(a),c=u.length,f=0,h=[];c>f;)r=u[f++],n&&!s.call(a,r)||h.push(t?[r,a[r]]:a[r]);return h}};t.exports={entries:a(!0),values:a(!1)}},function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},function(t,e,r){var n=r(2);t.exports=n.Promise},function(t,e,r){var n=r(42);t.exports=/(?:iphone|ipod|ipad).*applewebkit/i.test(n)},function(t,e,r){var n,i,o,s,a,u,c,f,h=r(2),l=r(17).f,p=r(109).set,g=r(149),d=r(306),v=r(51),y=h.MutationObserver||h.WebKitMutationObserver,m=h.document,b=h.process,S=h.Promise,w=l(h,"queueMicrotask"),_=w&&w.value;_||(n=function(){var t,e;for(v&&(t=b.domain)&&t.exit();i;){e=i.fn,i=i.next;try{e()}catch(t){throw i?s():o=void 0,t}}o=void 0,t&&t.enter()},g||v||d||!y||!m?S&&S.resolve?((c=S.resolve(void 0)).constructor=S,f=c.then,s=function(){f.call(c,n)}):s=v?function(){b.nextTick(n)}:function(){p.call(h,n)}:(a=!0,u=m.createTextNode(""),new y(n).observe(u,{characterData:!0}),s=function(){u.data=a=!a})),t.exports=_||function(t){var e={fn:t,next:void 0};o&&(o.next=e),i||(i=e,s()),o=e}},function(t,e,r){var n=r(4),i=r(3),o=r(81);t.exports=function(t,e){if(n(t),i(e)&&e.constructor===t)return e;var r=o.f(t);return(0,r.resolve)(e),r.promise}},function(t,e,r){var n=r(1);t.exports=n((function(){var t=RegExp("(?<a>b)","string".charAt(5));return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},function(t,e,r){"use strict";var n=r(84).charAt,i=r(15),o=r(102),s=i.set,a=i.getterFor("String Iterator");o(String,"String",(function(t){s(this,{type:"String Iterator",string:String(t),index:0})}),(function(){var t,e=a(this),r=e.string,i=e.index;return i>=r.length?{value:void 0,done:!0}:(t=n(r,i),e.index+=t.length,{value:t,done:!1})}))},function(t,e,r){var n=r(42);t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},function(t,e,r){var n=r(10),i=Math.floor,o="".replace,s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,a=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,u,c,f){var h=r+t.length,l=u.length,p=a;return void 0!==c&&(c=n(c),p=s),o.call(f,p,(function(n,o){var s;switch(o.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,r);case"'":return e.slice(h);case"<":s=c[o.slice(1,-1)];break;default:var a=+o;if(0===a)return n;if(a>l){var f=i(a/10);return 0===f?n:f<=l?void 0===u[f-1]?o.charAt(1):u[f-1]+o.charAt(1):n}s=u[a-1]}return void 0===s?"":s}))}},function(t,e,r){var n=r(366);t.exports=function(t,e){var r=n(t);if(r%e)throw RangeError("Wrong offset");return r}},function(t,e,r){var n=r(10),i=r(7),o=r(64),s=r(99),a=r(43),u=r(8).aTypedArrayConstructor;t.exports=function(t){var e,r,c,f,h,l,p=n(t),g=arguments.length,d=g>1?arguments[1]:void 0,v=void 0!==d,y=o(p);if(null!=y&&!s(y))for(l=(h=y.call(p)).next,p=[];!(f=l.call(h)).done;)p.push(f.value);for(v&&g>2&&(d=a(d,arguments[2],2)),r=i(p.length),c=new(u(this))(r),e=0;r>e;e++)c[e]=v?d(p[e],e):p[e];return c}},function(t,e,r){"use strict";var n=r(53),i=r(50).getWeakData,o=r(4),s=r(3),a=r(46),u=r(44),c=r(16),f=r(12),h=r(15),l=h.set,p=h.getterFor,g=c.find,d=c.findIndex,v=0,y=function(t){return t.frozen||(t.frozen=new m)},m=function(){this.entries=[]},b=function(t,e){return g(t.entries,(function(t){return t[0]===e}))};m.prototype={get:function(t){var e=b(this,t);if(e)return e[1]},has:function(t){return!!b(this,t)},set:function(t,e){var r=b(this,t);r?r[1]=e:this.entries.push([t,e])},delete:function(t){var e=d(this.entries,(function(e){return e[0]===t}));return~e&&this.entries.splice(e,1),!!~e}},t.exports={getConstructor:function(t,e,r,c){var h=t((function(t,n){a(t,h,e),l(t,{type:e,id:v++,frozen:void 0}),null!=n&&u(n,t[c],{that:t,AS_ENTRIES:r})})),g=p(e),d=function(t,e,r){var n=g(t),s=i(o(e),!0);return!0===s?y(n).set(e,r):s[n.id]=r,t};return n(h.prototype,{delete:function(t){var e=g(this);if(!s(t))return!1;var r=i(t);return!0===r?y(e).delete(t):r&&f(r,e.id)&&delete r[e.id]},has:function(t){var e=g(this);if(!s(t))return!1;var r=i(t);return!0===r?y(e).has(t):r&&f(r,e.id)}}),n(h.prototype,r?{get:function(t){var e=g(this);if(s(t)){var r=i(t);return!0===r?y(e).get(t):r?r[e.id]:void 0}},set:function(t,e){return d(this,t,e)}}:{add:function(t){return d(this,t,!0)}}),h}}},function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(t,e,r){var n=r(1),i=r(6),o=r(28),s=i("iterator");t.exports=!n((function(){var t=new URL("b?a=1&b=2&c=3","http://a"),e=t.searchParams,r="";return t.pathname="c%20d",e.forEach((function(t,n){e.delete("b"),r+=n+t})),o&&!t.toJSON||!e.sort||"http://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[s]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host}))},function(t,e,r){"use strict";r(73);var n=r(0),i=r(23),o=r(160),s=r(18),a=r(53),u=r(33),c=r(103),f=r(15),h=r(46),l=r(12),p=r(43),g=r(65),d=r(4),v=r(3),y=r(32),m=r(34),b=r(411),S=r(64),w=r(6),_=i("fetch"),E=i("Headers"),x=w("iterator"),F=f.set,A=f.getterFor("URLSearchParams"),T=f.getterFor("URLSearchParamsIterator"),k=/\+/g,P=Array(4),R=function(t){return P[t-1]||(P[t-1]=RegExp("((?:%[\\da-f]{2}){"+t+"})","gi"))},I=function(t){try{return decodeURIComponent(t)}catch(e){return t}},C=function(t){var e=t.replace(k," "),r=4;try{return decodeURIComponent(e)}catch(t){for(;r;)e=e.replace(R(r--),I);return e}},O=/[!'()~]|%20/g,D={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},N=function(t){return D[t]},L=function(t){return encodeURIComponent(t).replace(O,N)},j=function(t,e){if(e)for(var r,n,i=e.split("&"),o=0;o<i.length;)(r=i[o++]).length&&(n=r.split("="),t.push({key:C(n.shift()),value:C(n.join("="))}))},M=function(t){this.entries.length=0,j(this.entries,t)},B=function(t,e){if(t<e)throw TypeError("Not enough arguments")},U=c((function(t,e){F(this,{type:"URLSearchParamsIterator",iterator:b(A(t).entries),kind:e})}),"Iterator",(function(){var t=T(this),e=t.kind,r=t.iterator.next(),n=r.value;return r.done||(r.value="keys"===e?n.key:"values"===e?n.value:[n.key,n.value]),r})),H=function(){h(this,H,"URLSearchParams");var t,e,r,n,i,o,s,a,u,c=arguments.length>0?arguments[0]:void 0,f=this,p=[];if(F(f,{type:"URLSearchParams",entries:p,updateURL:function(){},updateSearchParams:M}),void 0!==c)if(v(c))if("function"==typeof(t=S(c)))for(r=(e=t.call(c)).next;!(n=r.call(e)).done;){if((s=(o=(i=b(d(n.value))).next).call(i)).done||(a=o.call(i)).done||!o.call(i).done)throw TypeError("Expected sequence with length 2");p.push({key:s.value+"",value:a.value+""})}else for(u in c)l(c,u)&&p.push({key:u,value:c[u]+""});else j(p,"string"==typeof c?"?"===c.charAt(0)?c.slice(1):c:c+"")},V=H.prototype;a(V,{append:function(t,e){B(arguments.length,2);var r=A(this);r.entries.push({key:t+"",value:e+""}),r.updateURL()},delete:function(t){B(arguments.length,1);for(var e=A(this),r=e.entries,n=t+"",i=0;i<r.length;)r[i].key===n?r.splice(i,1):i++;e.updateURL()},get:function(t){B(arguments.length,1);for(var e=A(this).entries,r=t+"",n=0;n<e.length;n++)if(e[n].key===r)return e[n].value;return null},getAll:function(t){B(arguments.length,1);for(var e=A(this).entries,r=t+"",n=[],i=0;i<e.length;i++)e[i].key===r&&n.push(e[i].value);return n},has:function(t){B(arguments.length,1);for(var e=A(this).entries,r=t+"",n=0;n<e.length;)if(e[n++].key===r)return!0;return!1},set:function(t,e){B(arguments.length,1);for(var r,n=A(this),i=n.entries,o=!1,s=t+"",a=e+"",u=0;u<i.length;u++)(r=i[u]).key===s&&(o?i.splice(u--,1):(o=!0,r.value=a));o||i.push({key:s,value:a}),n.updateURL()},sort:function(){var t,e,r,n=A(this),i=n.entries,o=i.slice();for(i.length=0,r=0;r<o.length;r++){for(t=o[r],e=0;e<r;e++)if(i[e].key>t.key){i.splice(e,0,t);break}e===r&&i.push(t)}n.updateURL()},forEach:function(t){for(var e,r=A(this).entries,n=p(t,arguments.length>1?arguments[1]:void 0,3),i=0;i<r.length;)n((e=r[i++]).value,e.key,this)},keys:function(){return new U(this,"keys")},values:function(){return new U(this,"values")},entries:function(){return new U(this,"entries")}},{enumerable:!0}),s(V,x,V.entries),s(V,"toString",(function(){for(var t,e=A(this).entries,r=[],n=0;n<e.length;)t=e[n++],r.push(L(t.key)+"="+L(t.value));return r.join("&")}),{enumerable:!0}),u(H,"URLSearchParams"),n({global:!0,forced:!o},{URLSearchParams:H}),o||"function"!=typeof _||"function"!=typeof E||n({global:!0,enumerable:!0,forced:!0},{fetch:function(t){var e,r,n,i=[t];return arguments.length>1&&(v(e=arguments[1])&&(r=e.body,"URLSearchParams"===g(r)&&((n=e.headers?new E(e.headers):new E).has("content-type")||n.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),e=y(e,{body:m(0,String(r)),headers:m(0,n)}))),i.push(e)),_.apply(this,i)}}),t.exports={URLSearchParams:H,getState:A}},function(t,e,r){"use strict";t.exports=function(t,e){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return t.apply(e,r)}}},function(t,e,r){"use strict";var n=r(26);function i(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,r){if(!e)return t;var o;if(r)o=r(e);else if(n.isURLSearchParams(e))o=e.toString();else{var s=[];n.forEach(e,(function(t,e){null!=t&&(n.isArray(t)?e+="[]":t=[t],n.forEach(t,(function(t){n.isDate(t)?t=t.toISOString():n.isObject(t)&&(t=JSON.stringify(t)),s.push(i(e)+"="+i(t))})))})),o=s.join("&")}if(o){var a=t.indexOf("#");-1!==a&&(t=t.slice(0,a)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}},function(t,e,r){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},function(t,e,r){"use strict";(function(e){var n=r(26),i=r(419),o={"Content-Type":"application/x-www-form-urlencoded"};function s(t,e){!n.isUndefined(t)&&n.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var a,u={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==e&&"[object process]"===Object.prototype.toString.call(e))&&(a=r(166)),a),transformRequest:[function(t,e){return i(e,"Accept"),i(e,"Content-Type"),n.isFormData(t)||n.isArrayBuffer(t)||n.isBuffer(t)||n.isStream(t)||n.isFile(t)||n.isBlob(t)?t:n.isArrayBufferView(t)?t.buffer:n.isURLSearchParams(t)?(s(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):n.isObject(t)?(s(e,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(t){}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(t){return t>=200&&t<300}};u.headers={common:{Accept:"application/json, text/plain, */*"}},n.forEach(["delete","get","head"],(function(t){u.headers[t]={}})),n.forEach(["post","put","patch"],(function(t){u.headers[t]=n.merge(o)})),t.exports=u}).call(this,r(418))},function(t,e,r){"use strict";var n=r(26),i=r(420),o=r(422),s=r(163),a=r(423),u=r(426),c=r(427),f=r(167);t.exports=function(t){return new Promise((function(e,r){var h=t.data,l=t.headers;n.isFormData(h)&&delete l["Content-Type"];var p=new XMLHttpRequest;if(t.auth){var g=t.auth.username||"",d=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";l.Authorization="Basic "+btoa(g+":"+d)}var v=a(t.baseURL,t.url);if(p.open(t.method.toUpperCase(),s(v,t.params,t.paramsSerializer),!0),p.timeout=t.timeout,p.onreadystatechange=function(){if(p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?u(p.getAllResponseHeaders()):null,o={data:t.responseType&&"text"!==t.responseType?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:n,config:t,request:p};i(e,r,o),p=null}},p.onabort=function(){p&&(r(f("Request aborted",t,"ECONNABORTED",p)),p=null)},p.onerror=function(){r(f("Network Error",t,null,p)),p=null},p.ontimeout=function(){var e="timeout of "+t.timeout+"ms exceeded";t.timeoutErrorMessage&&(e=t.timeoutErrorMessage),r(f(e,t,"ECONNABORTED",p)),p=null},n.isStandardBrowserEnv()){var y=(t.withCredentials||c(v))&&t.xsrfCookieName?o.read(t.xsrfCookieName):void 0;y&&(l[t.xsrfHeaderName]=y)}if("setRequestHeader"in p&&n.forEach(l,(function(t,e){void 0===h&&"content-type"===e.toLowerCase()?delete l[e]:p.setRequestHeader(e,t)})),n.isUndefined(t.withCredentials)||(p.withCredentials=!!t.withCredentials),t.responseType)try{p.responseType=t.responseType}catch(e){if("json"!==t.responseType)throw e}"function"==typeof t.onDownloadProgress&&p.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",t.onUploadProgress),t.cancelToken&&t.cancelToken.promise.then((function(t){p&&(p.abort(),r(t),p=null)})),h||(h=null),p.send(h)}))}},function(t,e,r){"use strict";var n=r(421);t.exports=function(t,e,r,i,o){var s=new Error(t);return n(s,e,r,i,o)}},function(t,e,r){"use strict";var n=r(26);t.exports=function(t,e){e=e||{};var r={},i=["url","method","data"],o=["headers","auth","proxy","params"],s=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],a=["validateStatus"];function u(t,e){return n.isPlainObject(t)&&n.isPlainObject(e)?n.merge(t,e):n.isPlainObject(e)?n.merge({},e):n.isArray(e)?e.slice():e}function c(i){n.isUndefined(e[i])?n.isUndefined(t[i])||(r[i]=u(void 0,t[i])):r[i]=u(t[i],e[i])}n.forEach(i,(function(t){n.isUndefined(e[t])||(r[t]=u(void 0,e[t]))})),n.forEach(o,c),n.forEach(s,(function(i){n.isUndefined(e[i])?n.isUndefined(t[i])||(r[i]=u(void 0,t[i])):r[i]=u(void 0,e[i])})),n.forEach(a,(function(n){n in e?r[n]=u(t[n],e[n]):n in t&&(r[n]=u(void 0,t[n]))}));var f=i.concat(o).concat(s).concat(a),h=Object.keys(t).concat(Object.keys(e)).filter((function(t){return-1===f.indexOf(t)}));return n.forEach(h,c),r}},function(t,e,r){"use strict";function n(t){this.message=t}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,t.exports=n},function(t,e){function r(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?(t.exports=r=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=r=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),r(e)}t.exports=r,t.exports.default=t.exports,t.exports.__esModule=!0},function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t.exports.default=t.exports,t.exports.__esModule=!0},function(t,e){function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}t.exports=function(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t},t.exports.default=t.exports,t.exports.__esModule=!0},function(t){t.exports=JSON.parse('{"version":"3.2.0"}')},function(t,e,r){t.exports=r(413)},function(t,e,r){r(176),r(178),r(179),r(180),r(181),r(182),r(183),r(184),r(185),r(186),r(187),r(188),r(189),r(190),r(191),r(192),r(193),r(194),r(195),r(196),r(197),r(198),r(199),r(200),r(201),r(202),r(203),r(205),r(206),r(207),r(73),r(208),r(209),r(210),r(211),r(212),r(213),r(214),r(215),r(216),r(217),r(218),r(219),r(220),r(221),r(222),r(224),r(225),r(226),r(227),r(228),r(229),r(230),r(231),r(233),r(234),r(236),r(237),r(238),r(239),r(240),r(241),r(242),r(243),r(244),r(245),r(246),r(247),r(248),r(249),r(250),r(251),r(252),r(254),r(255),r(256),r(257),r(258),r(259),r(260),r(261),r(262),r(263),r(264),r(265),r(266),r(268),r(269),r(270),r(271),r(272),r(273),r(274),r(275),r(276),r(277),r(278),r(279),r(280),r(281),r(282),r(283),r(284),r(285),r(286),r(287),r(288),r(289),r(290),r(291),r(292),r(293),r(294),r(295),r(296),r(297),r(298),r(299),r(300),r(302),r(303),r(304),r(305),r(309),r(310),r(311),r(312),r(313),r(314),r(315),r(316),r(317),r(318),r(319),r(320),r(321),r(322),r(323),r(324),r(325),r(326),r(327),r(112),r(328),r(329),r(330),r(331),r(332),r(333),r(334),r(335),r(336),r(153),r(337),r(338),r(339),r(340),r(341),r(342),r(343),r(344),r(345),r(346),r(347),r(348),r(349),r(350),r(351),r(352),r(353),r(354),r(355),r(356),r(357),r(358),r(359),r(360),r(361),r(362),r(363),r(364),r(365),r(367),r(368),r(369),r(370),r(371),r(372),r(373),r(374),r(375),r(376),r(377),r(378),r(380),r(381),r(382),r(383),r(384),r(385),r(386),r(387),r(388),r(389),r(390),r(391),r(392),r(393),r(394),r(395),r(396),r(397),r(398),r(399),r(400),r(401),r(402),r(403),r(404),r(405),r(406),r(407),r(408),r(409),r(412),r(161),t.exports=r(93)},function(t,e,r){"use strict";var n=r(0),i=r(2),o=r(23),s=r(28),a=r(5),u=r(96),c=r(122),f=r(1),h=r(12),l=r(48),p=r(3),g=r(4),d=r(10),v=r(22),y=r(31),m=r(34),b=r(32),S=r(61),w=r(47),_=r(124),E=r(95),x=r(17),F=r(9),A=r(69),T=r(14),k=r(18),P=r(71),R=r(70),I=r(58),C=r(57),O=r(6),D=r(125),N=r(21),L=r(33),j=r(15),M=r(16).forEach,B=R("hidden"),U=O("toPrimitive"),H=j.set,V=j.getterFor("Symbol"),q=Object.prototype,K=i.Symbol,W=o("JSON","stringify"),J=x.f,z=F.f,Y=_.f,G=A.f,$=P("symbols"),X=P("op-symbols"),Z=P("string-to-symbol-registry"),Q=P("symbol-to-string-registry"),tt=P("wks"),et=i.QObject,rt=!et||!et.prototype||!et.prototype.findChild,nt=a&&f((function(){return 7!=b(z({},"a",{get:function(){return z(this,"a",{value:7}).a}})).a}))?function(t,e,r){var n=J(q,e);n&&delete q[e],z(t,e,r),n&&t!==q&&z(q,e,n)}:z,it=function(t,e){var r=$[t]=b(K.prototype);return H(r,{type:"Symbol",tag:t,description:e}),a||(r.description=e),r},ot=c?function(t){return"symbol"==typeof t}:function(t){return Object(t)instanceof K},st=function(t,e,r){t===q&&st(X,e,r),g(t);var n=y(e,!0);return g(r),h($,n)?(r.enumerable?(h(t,B)&&t[B][n]&&(t[B][n]=!1),r=b(r,{enumerable:m(0,!1)})):(h(t,B)||z(t,B,m(1,{})),t[B][n]=!0),nt(t,n,r)):z(t,n,r)},at=function(t,e){g(t);var r=v(e),n=S(r).concat(ht(r));return M(n,(function(e){a&&!ut.call(r,e)||st(t,e,r[e])})),t},ut=function(t){var e=y(t,!0),r=G.call(this,e);return!(this===q&&h($,e)&&!h(X,e))&&(!(r||!h(this,e)||!h($,e)||h(this,B)&&this[B][e])||r)},ct=function(t,e){var r=v(t),n=y(e,!0);if(r!==q||!h($,n)||h(X,n)){var i=J(r,n);return!i||!h($,n)||h(r,B)&&r[B][n]||(i.enumerable=!0),i}},ft=function(t){var e=Y(v(t)),r=[];return M(e,(function(t){h($,t)||h(I,t)||r.push(t)})),r},ht=function(t){var e=t===q,r=Y(e?X:v(t)),n=[];return M(r,(function(t){!h($,t)||e&&!h(q,t)||n.push($[t])})),n};(u||(k((K=function(){if(this instanceof K)throw TypeError("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,e=C(t),r=function(t){this===q&&r.call(X,t),h(this,B)&&h(this[B],e)&&(this[B][e]=!1),nt(this,e,m(1,t))};return a&&rt&&nt(q,e,{configurable:!0,set:r}),it(e,t)}).prototype,"toString",(function(){return V(this).tag})),k(K,"withoutSetter",(function(t){return it(C(t),t)})),A.f=ut,F.f=st,x.f=ct,w.f=_.f=ft,E.f=ht,D.f=function(t){return it(O(t),t)},a&&(z(K.prototype,"description",{configurable:!0,get:function(){return V(this).description}}),s||k(q,"propertyIsEnumerable",ut,{unsafe:!0}))),n({global:!0,wrap:!0,forced:!u,sham:!u},{Symbol:K}),M(S(tt),(function(t){N(t)})),n({target:"Symbol",stat:!0,forced:!u},{for:function(t){var e=String(t);if(h(Z,e))return Z[e];var r=K(e);return Z[e]=r,Q[r]=e,r},keyFor:function(t){if(!ot(t))throw TypeError(t+" is not a symbol");if(h(Q,t))return Q[t]},useSetter:function(){rt=!0},useSimple:function(){rt=!1}}),n({target:"Object",stat:!0,forced:!u,sham:!a},{create:function(t,e){return void 0===e?b(t):at(b(t),e)},defineProperty:st,defineProperties:at,getOwnPropertyDescriptor:ct}),n({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:ft,getOwnPropertySymbols:ht}),n({target:"Object",stat:!0,forced:f((function(){E.f(1)}))},{getOwnPropertySymbols:function(t){return E.f(d(t))}}),W)&&n({target:"JSON",stat:!0,forced:!u||f((function(){var t=K();return"[null]"!=W([t])||"{}"!=W({a:t})||"{}"!=W(Object(t))}))},{stringify:function(t,e,r){for(var n,i=[t],o=1;arguments.length>o;)i.push(arguments[o++]);if(n=e,(p(e)||void 0!==t)&&!ot(t))return l(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!ot(e))return e}),i[1]=e,W.apply(null,i)}});K.prototype[U]||T(K.prototype,U,K.prototype.valueOf),L(K,"Symbol"),I[B]=!0},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){"use strict";var n=r(0),i=r(5),o=r(2),s=r(12),a=r(3),u=r(9).f,c=r(120),f=o.Symbol;if(i&&"function"==typeof f&&(!("description"in f.prototype)||void 0!==f().description)){var h={},l=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),e=this instanceof l?new f(t):void 0===t?f():f(t);return""===t&&(h[e]=!0),e};c(l,f);var p=l.prototype=f.prototype;p.constructor=l;var g=p.toString,d="Symbol(test)"==String(f("test")),v=/^Symbol\((.*)\)[^)]+$/;u(p,"description",{configurable:!0,get:function(){var t=a(this)?this.valueOf():this,e=g.call(t);if(s(h,t))return"";var r=d?e.slice(7,-1):e.replace(v,"$1");return""===r?void 0:r}}),n({global:!0,forced:!0},{Symbol:l})}},function(t,e,r){r(21)("asyncIterator")},function(t,e,r){r(21)("hasInstance")},function(t,e,r){r(21)("isConcatSpreadable")},function(t,e,r){r(21)("iterator")},function(t,e,r){r(21)("match")},function(t,e,r){r(21)("matchAll")},function(t,e,r){r(21)("replace")},function(t,e,r){r(21)("search")},function(t,e,r){r(21)("species")},function(t,e,r){r(21)("split")},function(t,e,r){r(21)("toPrimitive")},function(t,e,r){r(21)("toStringTag")},function(t,e,r){r(21)("unscopables")},function(t,e,r){"use strict";var n=r(0),i=r(29),o=r(36),s=r(32),a=r(14),u=r(34),c=r(44),f=function(t,e){var r=this;if(!(r instanceof f))return new f(t,e);o&&(r=o(new Error(void 0),i(r))),void 0!==e&&a(r,"message",String(e));var n=[];return c(t,n.push,{that:n}),a(r,"errors",n),r};f.prototype=s(Error.prototype,{constructor:u(5,f),message:u(5,""),name:u(5,"AggregateError")}),n({global:!0},{AggregateError:f})},function(t,e,r){"use strict";var n=r(0),i=r(1),o=r(48),s=r(3),a=r(10),u=r(7),c=r(49),f=r(62),h=r(66),l=r(6),p=r(41),g=l("isConcatSpreadable"),d=p>=51||!i((function(){var t=[];return t[g]=!1,t.concat()[0]!==t})),v=h("concat"),y=function(t){if(!s(t))return!1;var e=t[g];return void 0!==e?!!e:o(t)};n({target:"Array",proto:!0,forced:!d||!v},{concat:function(t){var e,r,n,i,o,s=a(this),h=f(s,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(y(o=-1===e?s:arguments[e])){if(l+(i=u(o.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<i;r++,l++)r in o&&c(h,l,o[r])}else{if(l>=9007199254740991)throw TypeError("Maximum allowed index exceeded");c(h,l++,o)}return h.length=l,h}})},function(t,e,r){var n=r(0),i=r(128),o=r(45);n({target:"Array",proto:!0},{copyWithin:i}),o("copyWithin")},function(t,e,r){"use strict";var n=r(0),i=r(16).every;n({target:"Array",proto:!0,forced:!r(37)("every")},{every:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){var n=r(0),i=r(101),o=r(45);n({target:"Array",proto:!0},{fill:i}),o("fill")},function(t,e,r){"use strict";var n=r(0),i=r(16).filter;n({target:"Array",proto:!0,forced:!r(66)("filter")},{filter:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(16).find,o=r(45),s=!0;"find"in[]&&Array(1).find((function(){s=!1})),n({target:"Array",proto:!0,forced:s},{find:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o("find")},function(t,e,r){"use strict";var n=r(0),i=r(16).findIndex,o=r(45),s=!0;"findIndex"in[]&&Array(1).findIndex((function(){s=!1})),n({target:"Array",proto:!0,forced:s},{findIndex:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o("findIndex")},function(t,e,r){"use strict";var n=r(0),i=r(129),o=r(10),s=r(7),a=r(20),u=r(62);n({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=o(this),r=s(e.length),n=u(e,0);return n.length=i(n,e,e,r,0,void 0===t?1:a(t)),n}})},function(t,e,r){"use strict";var n=r(0),i=r(129),o=r(10),s=r(7),a=r(19),u=r(62);n({target:"Array",proto:!0},{flatMap:function(t){var e,r=o(this),n=s(r.length);return a(t),(e=u(r,0)).length=i(e,r,r,n,0,1,t,arguments.length>1?arguments[1]:void 0),e}})},function(t,e,r){"use strict";var n=r(0),i=r(130);n({target:"Array",proto:!0,forced:[].forEach!=i},{forEach:i})},function(t,e,r){var n=r(0),i=r(131);n({target:"Array",stat:!0,forced:!r(72)((function(t){Array.from(t)}))},{from:i})},function(t,e,r){var n=r(4),i=r(127);t.exports=function(t,e,r,o){try{return o?e(n(r)[0],r[1]):e(r)}catch(e){throw i(t),e}}},function(t,e,r){"use strict";var n=r(0),i=r(59).includes,o=r(45);n({target:"Array",proto:!0},{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")},function(t,e,r){"use strict";var n=r(0),i=r(59).indexOf,o=r(37),s=[].indexOf,a=!!s&&1/[1].indexOf(1,-0)<0,u=o("indexOf");n({target:"Array",proto:!0,forced:a||!u},{indexOf:function(t){return a?s.apply(this,arguments)||0:i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){r(0)({target:"Array",stat:!0},{isArray:r(48)})},function(t,e,r){"use strict";var n=r(0),i=r(56),o=r(22),s=r(37),a=[].join,u=i!=Object,c=s("join",",");n({target:"Array",proto:!0,forced:u||!c},{join:function(t){return a.call(o(this),void 0===t?",":t)}})},function(t,e,r){var n=r(0),i=r(133);n({target:"Array",proto:!0,forced:i!==[].lastIndexOf},{lastIndexOf:i})},function(t,e,r){"use strict";var n=r(0),i=r(16).map;n({target:"Array",proto:!0,forced:!r(66)("map")},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(1),o=r(49);n({target:"Array",stat:!0,forced:i((function(){function t(){}return!(Array.of.call(t)instanceof t)}))},{of:function(){for(var t=0,e=arguments.length,r=new("function"==typeof this?this:Array)(e);e>t;)o(r,t,arguments[t++]);return r.length=e,r}})},function(t,e,r){"use strict";var n=r(0),i=r(74).left,o=r(37),s=r(41),a=r(51);n({target:"Array",proto:!0,forced:!o("reduce")||!a&&s>79&&s<83},{reduce:function(t){return i(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(74).right,o=r(37),s=r(41),a=r(51);n({target:"Array",proto:!0,forced:!o("reduceRight")||!a&&s>79&&s<83},{reduceRight:function(t){return i(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(48),o=[].reverse,s=[1,2];n({target:"Array",proto:!0,forced:String(s)===String(s.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),o.call(this)}})},function(t,e,r){"use strict";var n=r(0),i=r(3),o=r(48),s=r(40),a=r(7),u=r(22),c=r(49),f=r(6),h=r(66)("slice"),l=f("species"),p=[].slice,g=Math.max;n({target:"Array",proto:!0,forced:!h},{slice:function(t,e){var r,n,f,h=u(this),d=a(h.length),v=s(t,d),y=s(void 0===e?d:e,d);if(o(h)&&("function"!=typeof(r=h.constructor)||r!==Array&&!o(r.prototype)?i(r)&&null===(r=r[l])&&(r=void 0):r=void 0,r===Array||void 0===r))return p.call(h,v,y);for(n=new(void 0===r?Array:r)(g(y-v,0)),f=0;v<y;v++,f++)v in h&&c(n,f,h[v]);return n.length=f,n}})},function(t,e,r){"use strict";var n=r(0),i=r(16).some;n({target:"Array",proto:!0,forced:!r(37)("some")},{some:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(19),o=r(10),s=r(7),a=r(1),u=r(134),c=r(37),f=r(135),h=r(136),l=r(41),p=r(104),g=[],d=g.sort,v=a((function(){g.sort(void 0)})),y=a((function(){g.sort(null)})),m=c("sort"),b=!a((function(){if(l)return l<70;if(!(f&&f>3)){if(h)return!0;if(p)return p<603;var t,e,r,n,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)g.push({k:e+n,v:r})}for(g.sort((function(t,e){return e.v-t.v})),n=0;n<g.length;n++)e=g[n].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}}));n({target:"Array",proto:!0,forced:v||!y||!m||!b},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(b)return void 0===t?d.call(e):d.call(e,t);var r,n,a=[],c=s(e.length);for(n=0;n<c;n++)n in e&&a.push(e[n]);for(r=(a=u(a,function(t){return function(e,r){return void 0===r?-1:void 0===e?1:void 0!==t?+t(e,r)||0:String(e)>String(r)?1:-1}}(t))).length,n=0;n<r;)e[n]=a[n++];for(;n<c;)delete e[n++];return e}})},function(t,e,r){r(52)("Array")},function(t,e,r){"use strict";var n=r(0),i=r(40),o=r(20),s=r(7),a=r(10),u=r(62),c=r(49),f=r(66)("splice"),h=Math.max,l=Math.min;n({target:"Array",proto:!0,forced:!f},{splice:function(t,e){var r,n,f,p,g,d,v=a(this),y=s(v.length),m=i(t,y),b=arguments.length;if(0===b?r=n=0:1===b?(r=0,n=y-m):(r=b-2,n=l(h(o(e),0),y-m)),y+r-n>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(f=u(v,n),p=0;p<n;p++)(g=m+p)in v&&c(f,p,v[g]);if(f.length=n,r<n){for(p=m;p<y-n;p++)d=p+r,(g=p+n)in v?v[d]=v[g]:delete v[d];for(p=y;p>y-n+r;p--)delete v[p-1]}else if(r>n)for(p=y-n;p>m;p--)d=p+r-1,(g=p+n-1)in v?v[d]=v[g]:delete v[d];for(p=0;p<r;p++)v[p+m]=arguments[p+2];return v.length=y-n+r,f}})},function(t,e,r){r(45)("flat")},function(t,e,r){r(45)("flatMap")},function(t,e,r){"use strict";var n=r(0),i=r(2),o=r(75),s=r(52),a=o.ArrayBuffer;n({global:!0,forced:i.ArrayBuffer!==a},{ArrayBuffer:a}),s("ArrayBuffer")},function(t,e){var r=Math.abs,n=Math.pow,i=Math.floor,o=Math.log,s=Math.LN2;t.exports={pack:function(t,e,a){var u,c,f,h=new Array(a),l=8*a-e-1,p=(1<<l)-1,g=p>>1,d=23===e?n(2,-24)-n(2,-77):0,v=t<0||0===t&&1/t<0?1:0,y=0;for((t=r(t))!=t||t===1/0?(c=t!=t?1:0,u=p):(u=i(o(t)/s),t*(f=n(2,-u))<1&&(u--,f*=2),(t+=u+g>=1?d/f:d*n(2,1-g))*f>=2&&(u++,f/=2),u+g>=p?(c=0,u=p):u+g>=1?(c=(t*f-1)*n(2,e),u+=g):(c=t*n(2,g-1)*n(2,e),u=0));e>=8;h[y++]=255&c,c/=256,e-=8);for(u=u<<e|c,l+=e;l>0;h[y++]=255&u,u/=256,l-=8);return h[--y]|=128*v,h},unpack:function(t,e){var r,i=t.length,o=8*i-e-1,s=(1<<o)-1,a=s>>1,u=o-7,c=i-1,f=t[c--],h=127&f;for(f>>=7;u>0;h=256*h+t[c],c--,u-=8);for(r=h&(1<<-u)-1,h>>=-u,u+=e;u>0;r=256*r+t[c],c--,u-=8);if(0===h)h=1-a;else{if(h===s)return r?NaN:f?-1/0:1/0;r+=n(2,e),h-=a}return(f?-1:1)*r*n(2,h-e)}}},function(t,e,r){var n=r(0),i=r(8);n({target:"ArrayBuffer",stat:!0,forced:!i.NATIVE_ARRAY_BUFFER_VIEWS},{isView:i.isView})},function(t,e,r){"use strict";var n=r(0),i=r(1),o=r(75),s=r(4),a=r(40),u=r(7),c=r(38),f=o.ArrayBuffer,h=o.DataView,l=f.prototype.slice;n({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:i((function(){return!new f(2).slice(1,void 0).byteLength}))},{slice:function(t,e){if(void 0!==l&&void 0===e)return l.call(s(this),t);for(var r=s(this).byteLength,n=a(t,r),i=a(void 0===e?r:e,r),o=new(c(this,f))(u(i-n)),p=new h(this),g=new h(o),d=0;n<i;)g.setUint8(d++,p.getUint8(n++));return o}})},function(t,e,r){var n=r(0),i=r(75);n({global:!0,forced:!r(105)},{DataView:i.DataView})},function(t,e,r){"use strict";var n=r(0),i=Date.prototype.getFullYear;n({target:"Date",proto:!0},{getYear:function(){return i.call(this)-1900}})},function(t,e,r){r(0)({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}})},function(t,e,r){"use strict";var n=r(0),i=r(20),o=Date.prototype.getTime,s=Date.prototype.setFullYear;n({target:"Date",proto:!0},{setYear:function(t){o.call(this);var e=i(t),r=0<=e&&e<=99?e+1900:e;return s.call(this,r)}})},function(t,e,r){r(0)({target:"Date",proto:!0},{toGMTString:Date.prototype.toUTCString})},function(t,e,r){var n=r(0),i=r(232);n({target:"Date",proto:!0,forced:Date.prototype.toISOString!==i},{toISOString:i})},function(t,e,r){"use strict";var n=r(1),i=r(106).start,o=Math.abs,s=Date.prototype,a=s.getTime,u=s.toISOString;t.exports=n((function(){return"0385-07-25T07:06:39.999Z"!=u.call(new Date(-50000000000001))}))||!n((function(){u.call(new Date(NaN))}))?function(){if(!isFinite(a.call(this)))throw RangeError("Invalid time value");var t=this.getUTCFullYear(),e=this.getUTCMilliseconds(),r=t<0?"-":t>9999?"+":"";return r+i(o(t),r?6:4,0)+"-"+i(this.getUTCMonth()+1,2,0)+"-"+i(this.getUTCDate(),2,0)+"T"+i(this.getUTCHours(),2,0)+":"+i(this.getUTCMinutes(),2,0)+":"+i(this.getUTCSeconds(),2,0)+"."+i(e,3,0)+"Z"}:u},function(t,e,r){"use strict";var n=r(0),i=r(1),o=r(10),s=r(31);n({target:"Date",proto:!0,forced:i((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}))},{toJSON:function(t){var e=o(this),r=s(e);return"number"!=typeof r||isFinite(r)?e.toISOString():null}})},function(t,e,r){var n=r(14),i=r(235),o=r(6)("toPrimitive"),s=Date.prototype;o in s||n(s,o,i)},function(t,e,r){"use strict";var n=r(4),i=r(31);t.exports=function(t){if("string"!==t&&"number"!==t&&"default"!==t)throw TypeError("Incorrect hint");return i(n(this),"number"!==t)}},function(t,e,r){var n=r(18),i=Date.prototype,o=i.toString,s=i.getTime;new Date(NaN)+""!="Invalid Date"&&n(i,"toString",(function(){var t=s.call(this);return t==t?o.call(this):"Invalid Date"}))},function(t,e,r){"use strict";var n=r(0),i=/[\w*+\-./@]/,o=function(t,e){for(var r=t.toString(16);r.length<e;)r="0"+r;return r};n({global:!0},{escape:function(t){for(var e,r,n=String(t),s="",a=n.length,u=0;u<a;)e=n.charAt(u++),i.test(e)?s+=e:s+=(r=e.charCodeAt(0))<256?"%"+o(r,2):"%u"+o(r,4).toUpperCase();return s}})},function(t,e,r){r(0)({target:"Function",proto:!0},{bind:r(138)})},function(t,e,r){"use strict";var n=r(3),i=r(9),o=r(29),s=r(6)("hasInstance"),a=Function.prototype;s in a||i.f(a,s,{value:function(t){if("function"!=typeof this||!n(t))return!1;if(!n(this.prototype))return t instanceof this;for(;t=o(t);)if(this.prototype===t)return!0;return!1}})},function(t,e,r){var n=r(5),i=r(9).f,o=Function.prototype,s=o.toString,a=/^\s*function ([^ (]*)/;n&&!("name"in o)&&i(o,"name",{configurable:!0,get:function(){try{return s.call(this).match(a)[1]}catch(t){return""}}})},function(t,e,r){r(0)({global:!0},{globalThis:r(2)})},function(t,e,r){var n=r(0),i=r(23),o=r(1),s=i("JSON","stringify"),a=/[\uD800-\uDFFF]/g,u=/^[\uD800-\uDBFF]$/,c=/^[\uDC00-\uDFFF]$/,f=function(t,e,r){var n=r.charAt(e-1),i=r.charAt(e+1);return u.test(t)&&!c.test(i)||c.test(t)&&!u.test(n)?"\\u"+t.charCodeAt(0).toString(16):t},h=o((function(){return'"\\udf06\\ud834"'!==s("\udf06\ud834")||'"\\udead"'!==s("\udead")}));s&&n({target:"JSON",stat:!0,forced:h},{stringify:function(t,e,r){var n=s.apply(null,arguments);return"string"==typeof n?n.replace(a,f):n}})},function(t,e,r){var n=r(2);r(33)(n.JSON,"JSON",!0)},function(t,e,r){"use strict";var n=r(76),i=r(139);t.exports=n("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),i)},function(t,e,r){var n=r(0),i=r(140),o=Math.acosh,s=Math.log,a=Math.sqrt,u=Math.LN2;n({target:"Math",stat:!0,forced:!o||710!=Math.floor(o(Number.MAX_VALUE))||o(1/0)!=1/0},{acosh:function(t){return(t=+t)<1?NaN:t>94906265.62425156?s(t)+u:i(t-1+a(t-1)*a(t+1))}})},function(t,e,r){var n=r(0),i=Math.asinh,o=Math.log,s=Math.sqrt;n({target:"Math",stat:!0,forced:!(i&&1/i(0)>0)},{asinh:function t(e){return isFinite(e=+e)&&0!=e?e<0?-t(-e):o(e+s(e*e+1)):e}})},function(t,e,r){var n=r(0),i=Math.atanh,o=Math.log;n({target:"Math",stat:!0,forced:!(i&&1/i(-0)<0)},{atanh:function(t){return 0==(t=+t)?t:o((1+t)/(1-t))/2}})},function(t,e,r){var n=r(0),i=r(108),o=Math.abs,s=Math.pow;n({target:"Math",stat:!0},{cbrt:function(t){return i(t=+t)*s(o(t),1/3)}})},function(t,e,r){var n=r(0),i=Math.floor,o=Math.log,s=Math.LOG2E;n({target:"Math",stat:!0},{clz32:function(t){return(t>>>=0)?31-i(o(t+.5)*s):32}})},function(t,e,r){var n=r(0),i=r(78),o=Math.cosh,s=Math.abs,a=Math.E;n({target:"Math",stat:!0,forced:!o||o(710)===1/0},{cosh:function(t){var e=i(s(t)-1)+1;return(e+1/(e*a*a))*(a/2)}})},function(t,e,r){var n=r(0),i=r(78);n({target:"Math",stat:!0,forced:i!=Math.expm1},{expm1:i})},function(t,e,r){r(0)({target:"Math",stat:!0},{fround:r(253)})},function(t,e,r){var n=r(108),i=Math.abs,o=Math.pow,s=o(2,-52),a=o(2,-23),u=o(2,127)*(2-a),c=o(2,-126);t.exports=Math.fround||function(t){var e,r,o=i(t),f=n(t);return o<c?f*(o/c/a+1/s-1/s)*c*a:(r=(e=(1+a/s)*o)-(e-o))>u||r!=r?f*(1/0):f*r}},function(t,e,r){var n=r(0),i=Math.hypot,o=Math.abs,s=Math.sqrt;n({target:"Math",stat:!0,forced:!!i&&i(1/0,NaN)!==1/0},{hypot:function(t,e){for(var r,n,i=0,a=0,u=arguments.length,c=0;a<u;)c<(r=o(arguments[a++]))?(i=i*(n=c/r)*n+1,c=r):i+=r>0?(n=r/c)*n:r;return c===1/0?1/0:c*s(i)}})},function(t,e,r){var n=r(0),i=r(1),o=Math.imul;n({target:"Math",stat:!0,forced:i((function(){return-5!=o(4294967295,5)||2!=o.length}))},{imul:function(t,e){var r=+t,n=+e,i=65535&r,o=65535&n;return 0|i*o+((65535&r>>>16)*o+i*(65535&n>>>16)<<16>>>0)}})},function(t,e,r){var n=r(0),i=Math.log,o=Math.LOG10E;n({target:"Math",stat:!0},{log10:function(t){return i(t)*o}})},function(t,e,r){r(0)({target:"Math",stat:!0},{log1p:r(140)})},function(t,e,r){var n=r(0),i=Math.log,o=Math.LN2;n({target:"Math",stat:!0},{log2:function(t){return i(t)/o}})},function(t,e,r){r(0)({target:"Math",stat:!0},{sign:r(108)})},function(t,e,r){var n=r(0),i=r(1),o=r(78),s=Math.abs,a=Math.exp,u=Math.E;n({target:"Math",stat:!0,forced:i((function(){return-2e-17!=Math.sinh(-2e-17)}))},{sinh:function(t){return s(t=+t)<1?(o(t)-o(-t))/2:(a(t-1)-a(-t-1))*(u/2)}})},function(t,e,r){var n=r(0),i=r(78),o=Math.exp;n({target:"Math",stat:!0},{tanh:function(t){var e=i(t=+t),r=i(-t);return e==1/0?1:r==1/0?-1:(e-r)/(o(t)+o(-t))}})},function(t,e,r){r(33)(Math,"Math",!0)},function(t,e,r){var n=r(0),i=Math.ceil,o=Math.floor;n({target:"Math",stat:!0},{trunc:function(t){return(t>0?o:i)(t)}})},function(t,e,r){"use strict";var n=r(5),i=r(2),o=r(60),s=r(18),a=r(12),u=r(35),c=r(77),f=r(31),h=r(1),l=r(32),p=r(47).f,g=r(17).f,d=r(9).f,v=r(54).trim,y=i.Number,m=y.prototype,b="Number"==u(l(m)),S=function(t){var e,r,n,i,o,s,a,u,c=f(t,!1);if("string"==typeof c&&c.length>2)if(43===(e=(c=v(c)).charCodeAt(0))||45===e){if(88===(r=c.charCodeAt(2))||120===r)return NaN}else if(48===e){switch(c.charCodeAt(1)){case 66:case 98:n=2,i=49;break;case 79:case 111:n=8,i=55;break;default:return+c}for(s=(o=c.slice(2)).length,a=0;a<s;a++)if((u=o.charCodeAt(a))<48||u>i)return NaN;return parseInt(o,n)}return+c};if(o("Number",!y(" 0o1")||!y("0b1")||y("+0x1"))){for(var w,_=function(t){var e=arguments.length<1?0:t,r=this;return r instanceof _&&(b?h((function(){m.valueOf.call(r)})):"Number"!=u(r))?c(new y(S(e)),r,_):S(e)},E=n?p(y):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),x=0;E.length>x;x++)a(y,w=E[x])&&!a(_,w)&&d(_,w,g(y,w));_.prototype=m,m.constructor=_,s(i,"Number",_)}},function(t,e,r){r(0)({target:"Number",stat:!0},{EPSILON:Math.pow(2,-52)})},function(t,e,r){r(0)({target:"Number",stat:!0},{isFinite:r(267)})},function(t,e,r){var n=r(2).isFinite;t.exports=Number.isFinite||function(t){return"number"==typeof t&&n(t)}},function(t,e,r){r(0)({target:"Number",stat:!0},{isInteger:r(141)})},function(t,e,r){r(0)({target:"Number",stat:!0},{isNaN:function(t){return t!=t}})},function(t,e,r){var n=r(0),i=r(141),o=Math.abs;n({target:"Number",stat:!0},{isSafeInteger:function(t){return i(t)&&o(t)<=9007199254740991}})},function(t,e,r){r(0)({target:"Number",stat:!0},{MAX_SAFE_INTEGER:9007199254740991})},function(t,e,r){r(0)({target:"Number",stat:!0},{MIN_SAFE_INTEGER:-9007199254740991})},function(t,e,r){var n=r(0),i=r(142);n({target:"Number",stat:!0,forced:Number.parseFloat!=i},{parseFloat:i})},function(t,e,r){var n=r(0),i=r(143);n({target:"Number",stat:!0,forced:Number.parseInt!=i},{parseInt:i})},function(t,e,r){"use strict";var n=r(0),i=r(20),o=r(144),s=r(107),a=r(1),u=1..toFixed,c=Math.floor,f=function(t,e,r){return 0===e?r:e%2==1?f(t,e-1,r*t):f(t*t,e/2,r)},h=function(t,e,r){for(var n=-1,i=r;++n<6;)i+=e*t[n],t[n]=i%1e7,i=c(i/1e7)},l=function(t,e){for(var r=6,n=0;--r>=0;)n+=t[r],t[r]=c(n/e),n=n%e*1e7},p=function(t){for(var e=6,r="";--e>=0;)if(""!==r||0===e||0!==t[e]){var n=String(t[e]);r=""===r?n:r+s.call("0",7-n.length)+n}return r};n({target:"Number",proto:!0,forced:u&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!a((function(){u.call({})}))},{toFixed:function(t){var e,r,n,a,u=o(this),c=i(t),g=[0,0,0,0,0,0],d="",v="0";if(c<0||c>20)throw RangeError("Incorrect fraction digits");if(u!=u)return"NaN";if(u<=-1e21||u>=1e21)return String(u);if(u<0&&(d="-",u=-u),u>1e-21)if(r=(e=function(t){for(var e=0,r=t;r>=4096;)e+=12,r/=4096;for(;r>=2;)e+=1,r/=2;return e}(u*f(2,69,1))-69)<0?u*f(2,-e,1):u/f(2,e,1),r*=4503599627370496,(e=52-e)>0){for(h(g,0,r),n=c;n>=7;)h(g,1e7,0),n-=7;for(h(g,f(10,n,1),0),n=e-1;n>=23;)l(g,1<<23),n-=23;l(g,1<<n),h(g,1,1),l(g,2),v=p(g)}else h(g,0,r),h(g,1<<-e,0),v=p(g)+s.call("0",c);return v=c>0?d+((a=v.length)<=c?"0."+s.call("0",c-a)+v:v.slice(0,a-c)+"."+v.slice(a-c)):d+v}})},function(t,e,r){"use strict";var n=r(0),i=r(1),o=r(144),s=1..toPrecision;n({target:"Number",proto:!0,forced:i((function(){return"1"!==s.call(1,void 0)}))||!i((function(){s.call({})}))},{toPrecision:function(t){return void 0===t?s.call(o(this)):s.call(o(this),t)}})},function(t,e,r){var n=r(0),i=r(145);n({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},function(t,e,r){r(0)({target:"Object",stat:!0,sham:!r(5)},{create:r(32)})},function(t,e,r){"use strict";var n=r(0),i=r(5),o=r(80),s=r(10),a=r(19),u=r(9);i&&n({target:"Object",proto:!0,forced:o},{__defineGetter__:function(t,e){u.f(s(this),t,{get:a(e),enumerable:!0,configurable:!0})}})},function(t,e,r){var n=r(0),i=r(5);n({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperties:r(97)})},function(t,e,r){var n=r(0),i=r(5);n({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:r(9).f})},function(t,e,r){"use strict";var n=r(0),i=r(5),o=r(80),s=r(10),a=r(19),u=r(9);i&&n({target:"Object",proto:!0,forced:o},{__defineSetter__:function(t,e){u.f(s(this),t,{set:a(e),enumerable:!0,configurable:!0})}})},function(t,e,r){var n=r(0),i=r(146).entries;n({target:"Object",stat:!0},{entries:function(t){return i(t)}})},function(t,e,r){var n=r(0),i=r(67),o=r(1),s=r(3),a=r(50).onFreeze,u=Object.freeze;n({target:"Object",stat:!0,forced:o((function(){u(1)})),sham:!i},{freeze:function(t){return u&&s(t)?u(a(t)):t}})},function(t,e,r){var n=r(0),i=r(44),o=r(49);n({target:"Object",stat:!0},{fromEntries:function(t){var e={};return i(t,(function(t,r){o(e,t,r)}),{AS_ENTRIES:!0}),e}})},function(t,e,r){var n=r(0),i=r(1),o=r(22),s=r(17).f,a=r(5),u=i((function(){s(1)}));n({target:"Object",stat:!0,forced:!a||u,sham:!a},{getOwnPropertyDescriptor:function(t,e){return s(o(t),e)}})},function(t,e,r){var n=r(0),i=r(5),o=r(92),s=r(22),a=r(17),u=r(49);n({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(t){for(var e,r,n=s(t),i=a.f,c=o(n),f={},h=0;c.length>h;)void 0!==(r=i(n,e=c[h++]))&&u(f,e,r);return f}})},function(t,e,r){var n=r(0),i=r(1),o=r(124).f;n({target:"Object",stat:!0,forced:i((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:o})},function(t,e,r){var n=r(0),i=r(1),o=r(10),s=r(29),a=r(98);n({target:"Object",stat:!0,forced:i((function(){s(1)})),sham:!a},{getPrototypeOf:function(t){return s(o(t))}})},function(t,e,r){r(0)({target:"Object",stat:!0},{is:r(147)})},function(t,e,r){var n=r(0),i=r(1),o=r(3),s=Object.isExtensible;n({target:"Object",stat:!0,forced:i((function(){s(1)}))},{isExtensible:function(t){return!!o(t)&&(!s||s(t))}})},function(t,e,r){var n=r(0),i=r(1),o=r(3),s=Object.isFrozen;n({target:"Object",stat:!0,forced:i((function(){s(1)}))},{isFrozen:function(t){return!o(t)||!!s&&s(t)}})},function(t,e,r){var n=r(0),i=r(1),o=r(3),s=Object.isSealed;n({target:"Object",stat:!0,forced:i((function(){s(1)}))},{isSealed:function(t){return!o(t)||!!s&&s(t)}})},function(t,e,r){var n=r(0),i=r(10),o=r(61);n({target:"Object",stat:!0,forced:r(1)((function(){o(1)}))},{keys:function(t){return o(i(t))}})},function(t,e,r){"use strict";var n=r(0),i=r(5),o=r(80),s=r(10),a=r(31),u=r(29),c=r(17).f;i&&n({target:"Object",proto:!0,forced:o},{__lookupGetter__:function(t){var e,r=s(this),n=a(t,!0);do{if(e=c(r,n))return e.get}while(r=u(r))}})},function(t,e,r){"use strict";var n=r(0),i=r(5),o=r(80),s=r(10),a=r(31),u=r(29),c=r(17).f;i&&n({target:"Object",proto:!0,forced:o},{__lookupSetter__:function(t){var e,r=s(this),n=a(t,!0);do{if(e=c(r,n))return e.set}while(r=u(r))}})},function(t,e,r){var n=r(0),i=r(3),o=r(50).onFreeze,s=r(67),a=r(1),u=Object.preventExtensions;n({target:"Object",stat:!0,forced:a((function(){u(1)})),sham:!s},{preventExtensions:function(t){return u&&i(t)?u(o(t)):t}})},function(t,e,r){var n=r(0),i=r(3),o=r(50).onFreeze,s=r(67),a=r(1),u=Object.seal;n({target:"Object",stat:!0,forced:a((function(){u(1)})),sham:!s},{seal:function(t){return u&&i(t)?u(o(t)):t}})},function(t,e,r){r(0)({target:"Object",stat:!0},{setPrototypeOf:r(36)})},function(t,e,r){var n=r(100),i=r(18),o=r(301);n||i(Object.prototype,"toString",o,{unsafe:!0})},function(t,e,r){"use strict";var n=r(100),i=r(65);t.exports=n?{}.toString:function(){return"[object "+i(this)+"]"}},function(t,e,r){var n=r(0),i=r(146).values;n({target:"Object",stat:!0},{values:function(t){return i(t)}})},function(t,e,r){var n=r(0),i=r(142);n({global:!0,forced:parseFloat!=i},{parseFloat:i})},function(t,e,r){var n=r(0),i=r(143);n({global:!0,forced:parseInt!=i},{parseInt:i})},function(t,e,r){"use strict";var n,i,o,s,a=r(0),u=r(28),c=r(2),f=r(23),h=r(148),l=r(18),p=r(53),g=r(36),d=r(33),v=r(52),y=r(3),m=r(19),b=r(46),S=r(90),w=r(44),_=r(72),E=r(38),x=r(109).set,F=r(150),A=r(151),T=r(307),k=r(81),P=r(110),R=r(15),I=r(60),C=r(6),O=r(308),D=r(51),N=r(41),L=C("species"),j="Promise",M=R.get,B=R.set,U=R.getterFor(j),H=h&&h.prototype,V=h,q=H,K=c.TypeError,W=c.document,J=c.process,z=k.f,Y=z,G=!!(W&&W.createEvent&&c.dispatchEvent),$="function"==typeof PromiseRejectionEvent,X=!1,Z=I(j,(function(){var t=S(V),e=t!==String(V);if(!e&&66===N)return!0;if(u&&!q.finally)return!0;if(N>=51&&/native code/.test(t))return!1;var r=new V((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};return(r.constructor={})[L]=n,!(X=r.then((function(){}))instanceof n)||!e&&O&&!$})),Q=Z||!_((function(t){V.all(t).catch((function(){}))})),tt=function(t){var e;return!(!y(t)||"function"!=typeof(e=t.then))&&e},et=function(t,e){if(!t.notified){t.notified=!0;var r=t.reactions;F((function(){for(var n=t.value,i=1==t.state,o=0;r.length>o;){var s,a,u,c=r[o++],f=i?c.ok:c.fail,h=c.resolve,l=c.reject,p=c.domain;try{f?(i||(2===t.rejection&&ot(t),t.rejection=1),!0===f?s=n:(p&&p.enter(),s=f(n),p&&(p.exit(),u=!0)),s===c.promise?l(K("Promise-chain cycle")):(a=tt(s))?a.call(s,h,l):h(s)):l(n)}catch(t){p&&!u&&p.exit(),l(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&nt(t)}))}},rt=function(t,e,r){var n,i;G?((n=W.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),c.dispatchEvent(n)):n={promise:e,reason:r},!$&&(i=c["on"+t])?i(n):"unhandledrejection"===t&&T("Unhandled promise rejection",r)},nt=function(t){x.call(c,(function(){var e,r=t.facade,n=t.value;if(it(t)&&(e=P((function(){D?J.emit("unhandledRejection",n,r):rt("unhandledrejection",r,n)})),t.rejection=D||it(t)?2:1,e.error))throw e.value}))},it=function(t){return 1!==t.rejection&&!t.parent},ot=function(t){x.call(c,(function(){var e=t.facade;D?J.emit("rejectionHandled",e):rt("rejectionhandled",e,t.value)}))},st=function(t,e,r){return function(n){t(e,n,r)}},at=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,et(t,!0))},ut=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw K("Promise can't be resolved itself");var n=tt(e);n?F((function(){var r={done:!1};try{n.call(e,st(ut,r,t),st(at,r,t))}catch(e){at(r,e,t)}})):(t.value=e,t.state=1,et(t,!1))}catch(e){at({done:!1},e,t)}}};if(Z&&(q=(V=function(t){b(this,V,j),m(t),n.call(this);var e=M(this);try{t(st(ut,e),st(at,e))}catch(t){at(e,t)}}).prototype,(n=function(t){B(this,{type:j,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=p(q,{then:function(t,e){var r=U(this),n=z(E(this,V));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=D?J.domain:void 0,r.parent=!0,r.reactions.push(n),0!=r.state&&et(r,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),i=function(){var t=new n,e=M(t);this.promise=t,this.resolve=st(ut,e),this.reject=st(at,e)},k.f=z=function(t){return t===V||t===o?new i(t):Y(t)},!u&&"function"==typeof h&&H!==Object.prototype)){s=H.then,X||(l(H,"then",(function(t,e){var r=this;return new V((function(t,e){s.call(r,t,e)})).then(t,e)}),{unsafe:!0}),l(H,"catch",q.catch,{unsafe:!0}));try{delete H.constructor}catch(t){}g&&g(H,q)}a({global:!0,wrap:!0,forced:Z},{Promise:V}),d(V,j,!1,!0),v(j),o=f(j),a({target:j,stat:!0,forced:Z},{reject:function(t){var e=z(this);return e.reject.call(void 0,t),e.promise}}),a({target:j,stat:!0,forced:u||Z},{resolve:function(t){return A(u&&this===o?V:this,t)}}),a({target:j,stat:!0,forced:Q},{all:function(t){var e=this,r=z(e),n=r.resolve,i=r.reject,o=P((function(){var r=m(e.resolve),o=[],s=0,a=1;w(t,(function(t){var u=s++,c=!1;o.push(void 0),a++,r.call(e,t).then((function(t){c||(c=!0,o[u]=t,--a||n(o))}),i)})),--a||n(o)}));return o.error&&i(o.value),r.promise},race:function(t){var e=this,r=z(e),n=r.reject,i=P((function(){var i=m(e.resolve);w(t,(function(t){i.call(e,t).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}})},function(t,e,r){var n=r(42);t.exports=/web0s(?!.*chrome)/i.test(n)},function(t,e,r){var n=r(2);t.exports=function(t,e){var r=n.console;r&&r.error&&(1===arguments.length?r.error(t):r.error(t,e))}},function(t,e){t.exports="object"==typeof window},function(t,e,r){"use strict";var n=r(0),i=r(19),o=r(81),s=r(110),a=r(44);n({target:"Promise",stat:!0},{allSettled:function(t){var e=this,r=o.f(e),n=r.resolve,u=r.reject,c=s((function(){var r=i(e.resolve),o=[],s=0,u=1;a(t,(function(t){var i=s++,a=!1;o.push(void 0),u++,r.call(e,t).then((function(t){a||(a=!0,o[i]={status:"fulfilled",value:t},--u||n(o))}),(function(t){a||(a=!0,o[i]={status:"rejected",reason:t},--u||n(o))}))})),--u||n(o)}));return c.error&&u(c.value),r.promise}})},function(t,e,r){"use strict";var n=r(0),i=r(19),o=r(23),s=r(81),a=r(110),u=r(44);n({target:"Promise",stat:!0},{any:function(t){var e=this,r=s.f(e),n=r.resolve,c=r.reject,f=a((function(){var r=i(e.resolve),s=[],a=0,f=1,h=!1;u(t,(function(t){var i=a++,u=!1;s.push(void 0),f++,r.call(e,t).then((function(t){u||h||(h=!0,n(t))}),(function(t){u||h||(u=!0,s[i]=t,--f||c(new(o("AggregateError"))(s,"No one promise resolved")))}))})),--f||c(new(o("AggregateError"))(s,"No one promise resolved"))}));return f.error&&c(f.value),r.promise}})},function(t,e,r){"use strict";var n=r(0),i=r(28),o=r(148),s=r(1),a=r(23),u=r(38),c=r(151),f=r(18);if(n({target:"Promise",proto:!0,real:!0,forced:!!o&&s((function(){o.prototype.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=u(this,a("Promise")),r="function"==typeof t;return this.then(r?function(r){return c(e,t()).then((function(){return r}))}:t,r?function(r){return c(e,t()).then((function(){throw r}))}:t)}}),!i&&"function"==typeof o){var h=a("Promise").prototype.finally;o.prototype.finally!==h&&f(o.prototype,"finally",h,{unsafe:!0})}},function(t,e,r){var n=r(0),i=r(23),o=r(19),s=r(4),a=r(1),u=i("Reflect","apply"),c=Function.apply;n({target:"Reflect",stat:!0,forced:!a((function(){u((function(){}))}))},{apply:function(t,e,r){return o(t),s(r),u?u(t,e,r):c.call(t,e,r)}})},function(t,e,r){var n=r(0),i=r(23),o=r(19),s=r(4),a=r(3),u=r(32),c=r(138),f=r(1),h=i("Reflect","construct"),l=f((function(){function t(){}return!(h((function(){}),[],t)instanceof t)})),p=!f((function(){h((function(){}))})),g=l||p;n({target:"Reflect",stat:!0,forced:g,sham:g},{construct:function(t,e){o(t),s(e);var r=arguments.length<3?t:o(arguments[2]);if(p&&!l)return h(t,e,r);if(t==r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return n.push.apply(n,e),new(c.apply(t,n))}var i=r.prototype,f=u(a(i)?i:Object.prototype),g=Function.apply.call(t,f,e);return a(g)?g:f}})},function(t,e,r){var n=r(0),i=r(5),o=r(4),s=r(31),a=r(9);n({target:"Reflect",stat:!0,forced:r(1)((function(){Reflect.defineProperty(a.f({},1,{value:1}),1,{value:2})})),sham:!i},{defineProperty:function(t,e,r){o(t);var n=s(e,!0);o(r);try{return a.f(t,n,r),!0}catch(t){return!1}}})},function(t,e,r){var n=r(0),i=r(4),o=r(17).f;n({target:"Reflect",stat:!0},{deleteProperty:function(t,e){var r=o(i(t),e);return!(r&&!r.configurable)&&delete t[e]}})},function(t,e,r){var n=r(0),i=r(3),o=r(4),s=r(12),a=r(17),u=r(29);n({target:"Reflect",stat:!0},{get:function t(e,r){var n,c,f=arguments.length<3?e:arguments[2];return o(e)===f?e[r]:(n=a.f(e,r))?s(n,"value")?n.value:void 0===n.get?void 0:n.get.call(f):i(c=u(e))?t(c,r,f):void 0}})},function(t,e,r){var n=r(0),i=r(5),o=r(4),s=r(17);n({target:"Reflect",stat:!0,sham:!i},{getOwnPropertyDescriptor:function(t,e){return s.f(o(t),e)}})},function(t,e,r){var n=r(0),i=r(4),o=r(29);n({target:"Reflect",stat:!0,sham:!r(98)},{getPrototypeOf:function(t){return o(i(t))}})},function(t,e,r){r(0)({target:"Reflect",stat:!0},{has:function(t,e){return e in t}})},function(t,e,r){var n=r(0),i=r(4),o=Object.isExtensible;n({target:"Reflect",stat:!0},{isExtensible:function(t){return i(t),!o||o(t)}})},function(t,e,r){r(0)({target:"Reflect",stat:!0},{ownKeys:r(92)})},function(t,e,r){var n=r(0),i=r(23),o=r(4);n({target:"Reflect",stat:!0,sham:!r(67)},{preventExtensions:function(t){o(t);try{var e=i("Object","preventExtensions");return e&&e(t),!0}catch(t){return!1}}})},function(t,e,r){var n=r(0),i=r(4),o=r(3),s=r(12),a=r(1),u=r(9),c=r(17),f=r(29),h=r(34);n({target:"Reflect",stat:!0,forced:a((function(){var t=function(){},e=u.f(new t,"a",{configurable:!0});return!1!==Reflect.set(t.prototype,"a",1,e)}))},{set:function t(e,r,n){var a,l,p=arguments.length<4?e:arguments[3],g=c.f(i(e),r);if(!g){if(o(l=f(e)))return t(l,r,n,p);g=h(0)}if(s(g,"value")){if(!1===g.writable||!o(p))return!1;if(a=c.f(p,r)){if(a.get||a.set||!1===a.writable)return!1;a.value=n,u.f(p,r,a)}else u.f(p,r,h(0,n));return!0}return void 0!==g.set&&(g.set.call(p,n),!0)}})},function(t,e,r){var n=r(0),i=r(4),o=r(126),s=r(36);s&&n({target:"Reflect",stat:!0},{setPrototypeOf:function(t,e){i(t),o(e);try{return s(t,e),!0}catch(t){return!1}}})},function(t,e,r){var n=r(0),i=r(2),o=r(33);n({global:!0},{Reflect:{}}),o(i.Reflect,"Reflect",!0)},function(t,e,r){var n=r(5),i=r(2),o=r(60),s=r(77),a=r(14),u=r(9).f,c=r(47).f,f=r(68),h=r(55),l=r(82),p=r(18),g=r(1),d=r(12),v=r(15).enforce,y=r(52),m=r(6),b=r(111),S=r(152),w=m("match"),_=i.RegExp,E=_.prototype,x=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,F=/a/g,A=/a/g,T=new _(F)!==F,k=l.UNSUPPORTED_Y,P=n&&(!T||k||b||S||g((function(){return A[w]=!1,_(F)!=F||_(A)==A||"/a/i"!=_(F,"i")})));if(o("RegExp",P)){for(var R=function(t,e){var r,n,i,o,u,c,l=this instanceof R,p=f(t),g=void 0===e,y=[],m=t;if(!l&&p&&g&&t.constructor===R)return t;if((p||t instanceof R)&&(t=t.source,g&&(e="flags"in m?m.flags:h.call(m))),t=void 0===t?"":String(t),e=void 0===e?"":String(e),m=t,b&&"dotAll"in F&&(n=!!e&&e.indexOf("s")>-1)&&(e=e.replace(/s/g,"")),r=e,k&&"sticky"in F&&(i=!!e&&e.indexOf("y")>-1)&&(e=e.replace(/y/g,"")),S&&(t=(o=function(t){for(var e,r=t.length,n=0,i="",o=[],s={},a=!1,u=!1,c=0,f="";n<=r;n++){if("\\"===(e=t.charAt(n)))e+=t.charAt(++n);else if("]"===e)a=!1;else if(!a)switch(!0){case"["===e:a=!0;break;case"("===e:x.test(t.slice(n+1))&&(n+=2,u=!0),i+=e,c++;continue;case">"===e&&u:if(""===f||d(s,f))throw new SyntaxError("Invalid capture group name");s[f]=!0,o.push([f,c]),u=!1,f="";continue}u?f+=e:i+=e}return[i,o]}(t))[0],y=o[1]),u=s(_(t,e),l?this:E,R),(n||i||y.length)&&(c=v(u),n&&(c.dotAll=!0,c.raw=R(function(t){for(var e,r=t.length,n=0,i="",o=!1;n<=r;n++)"\\"!==(e=t.charAt(n))?o||"."!==e?("["===e?o=!0:"]"===e&&(o=!1),i+=e):i+="[\\s\\S]":i+=e+t.charAt(++n);return i}(t),r)),i&&(c.sticky=!0),y.length&&(c.groups=y)),t!==m)try{a(u,"source",""===m?"(?:)":m)}catch(t){}return u},I=function(t){t in R||u(R,t,{configurable:!0,get:function(){return _[t]},set:function(e){_[t]=e}})},C=c(_),O=0;C.length>O;)I(C[O++]);E.constructor=R,R.prototype=E,p(i,"RegExp",R)}y("RegExp")},function(t,e,r){var n=r(5),i=r(111),o=r(9).f,s=r(15).get,a=RegExp.prototype;n&&i&&o(a,"dotAll",{configurable:!0,get:function(){if(this!==a){if(this instanceof RegExp)return!!s(this).dotAll;throw TypeError("Incompatible receiver, RegExp required")}}})},function(t,e,r){var n=r(5),i=r(9),o=r(55),s=r(1);n&&s((function(){return"sy"!==Object.getOwnPropertyDescriptor(RegExp.prototype,"flags").get.call({dotAll:!0,sticky:!0})}))&&i.f(RegExp.prototype,"flags",{configurable:!0,get:o})},function(t,e,r){var n=r(5),i=r(82).UNSUPPORTED_Y,o=r(9).f,s=r(15).get,a=RegExp.prototype;n&&i&&o(a,"sticky",{configurable:!0,get:function(){if(this!==a){if(this instanceof RegExp)return!!s(this).sticky;throw TypeError("Incompatible receiver, RegExp required")}}})},function(t,e,r){"use strict";r(112);var n,i,o=r(0),s=r(3),a=(n=!1,(i=/[ac]/).exec=function(){return n=!0,/./.exec.apply(this,arguments)},!0===i.test("abc")&&n),u=/./.test;o({target:"RegExp",proto:!0,forced:!a},{test:function(t){if("function"!=typeof this.exec)return u.call(this,t);var e=this.exec(t);if(null!==e&&!s(e))throw new Error("RegExp exec method returned something other than an Object or null");return!!e}})},function(t,e,r){"use strict";var n=r(18),i=r(4),o=r(1),s=r(55),a=RegExp.prototype,u=a.toString,c=o((function(){return"/a/b"!=u.call({source:"a",flags:"b"})})),f="toString"!=u.name;(c||f)&&n(RegExp.prototype,"toString",(function(){var t=i(this),e=String(t.source),r=t.flags;return"/"+e+"/"+String(void 0===r&&t instanceof RegExp&&!("flags"in a)?s.call(t):r)}),{unsafe:!0})},function(t,e,r){"use strict";var n=r(76),i=r(139);t.exports=n("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),i)},function(t,e,r){"use strict";var n=r(0),i=r(84).codeAt;n({target:"String",proto:!0},{codePointAt:function(t){return i(this,t)}})},function(t,e,r){"use strict";var n,i=r(0),o=r(17).f,s=r(7),a=r(113),u=r(13),c=r(114),f=r(28),h="".endsWith,l=Math.min,p=c("endsWith");i({target:"String",proto:!0,forced:!!(f||p||(n=o(String.prototype,"endsWith"),!n||n.writable))&&!p},{endsWith:function(t){var e=String(u(this));a(t);var r=arguments.length>1?arguments[1]:void 0,n=s(e.length),i=void 0===r?n:l(s(r),n),o=String(t);return h?h.call(e,o,i):e.slice(i-o.length,i)===o}})},function(t,e,r){var n=r(0),i=r(40),o=String.fromCharCode,s=String.fromCodePoint;n({target:"String",stat:!0,forced:!!s&&1!=s.length},{fromCodePoint:function(t){for(var e,r=[],n=arguments.length,s=0;n>s;){if(e=+arguments[s++],i(e,1114111)!==e)throw RangeError(e+" is not a valid code point");r.push(e<65536?o(e):o(55296+((e-=65536)>>10),e%1024+56320))}return r.join("")}})},function(t,e,r){"use strict";var n=r(0),i=r(113),o=r(13);n({target:"String",proto:!0,forced:!r(114)("includes")},{includes:function(t){return!!~String(o(this)).indexOf(i(t),arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(85),i=r(4),o=r(7),s=r(13),a=r(86),u=r(87);n("match",(function(t,e,r){return[function(e){var r=s(this),n=null==e?void 0:e[t];return void 0!==n?n.call(e,r):new RegExp(e)[t](String(r))},function(t){var n=r(e,this,t);if(n.done)return n.value;var s=i(this),c=String(t);if(!s.global)return u(s,c);var f=s.unicode;s.lastIndex=0;for(var h,l=[],p=0;null!==(h=u(s,c));){var g=String(h[0]);l[p]=g,""===g&&(s.lastIndex=a(c,o(s.lastIndex),f)),p++}return 0===p?null:l}]}))},function(t,e,r){"use strict";var n=r(0),i=r(103),o=r(13),s=r(7),a=r(19),u=r(4),c=r(35),f=r(68),h=r(55),l=r(14),p=r(1),g=r(6),d=r(38),v=r(86),y=r(15),m=r(28),b=g("matchAll"),S=y.set,w=y.getterFor("RegExp String Iterator"),_=RegExp.prototype,E=_.exec,x="".matchAll,F=!!x&&!p((function(){"a".matchAll(/./)})),A=i((function(t,e,r,n){S(this,{type:"RegExp String Iterator",regexp:t,string:e,global:r,unicode:n,done:!1})}),"RegExp String",(function(){var t=w(this);if(t.done)return{value:void 0,done:!0};var e=t.regexp,r=t.string,n=function(t,e){var r,n=t.exec;if("function"==typeof n){if("object"!=typeof(r=n.call(t,e)))throw TypeError("Incorrect exec result");return r}return E.call(t,e)}(e,r);return null===n?{value:void 0,done:t.done=!0}:t.global?(""==String(n[0])&&(e.lastIndex=v(r,s(e.lastIndex),t.unicode)),{value:n,done:!1}):(t.done=!0,{value:n,done:!1})})),T=function(t){var e,r,n,i,o,a,c=u(this),f=String(t);return e=d(c,RegExp),void 0===(r=c.flags)&&c instanceof RegExp&&!("flags"in _)&&(r=h.call(c)),n=void 0===r?"":String(r),i=new e(e===RegExp?c.source:c,n),o=!!~n.indexOf("g"),a=!!~n.indexOf("u"),i.lastIndex=s(c.lastIndex),new A(i,f,o,a)};n({target:"String",proto:!0,forced:F},{matchAll:function(t){var e,r,n,i=o(this);if(null!=t){if(f(t)&&!~String(o("flags"in _?t.flags:h.call(t))).indexOf("g"))throw TypeError("`.matchAll` does not allow non-global regexes");if(F)return x.apply(i,arguments);if(void 0===(r=t[b])&&m&&"RegExp"==c(t)&&(r=T),null!=r)return a(r).call(t,i)}else if(F)return x.apply(i,arguments);return e=String(i),n=new RegExp(t,"g"),m?T.call(n,e):n[b](e)}}),m||b in _||l(_,b,T)},function(t,e,r){"use strict";var n=r(0),i=r(106).end;n({target:"String",proto:!0,forced:r(154)},{padEnd:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){"use strict";var n=r(0),i=r(106).start;n({target:"String",proto:!0,forced:r(154)},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,r){var n=r(0),i=r(22),o=r(7);n({target:"String",stat:!0},{raw:function(t){for(var e=i(t.raw),r=o(e.length),n=arguments.length,s=[],a=0;r>a;)s.push(String(e[a++])),a<n&&s.push(String(arguments[a]));return s.join("")}})},function(t,e,r){r(0)({target:"String",proto:!0},{repeat:r(107)})},function(t,e,r){"use strict";var n=r(85),i=r(1),o=r(4),s=r(7),a=r(20),u=r(13),c=r(86),f=r(155),h=r(87),l=r(6)("replace"),p=Math.max,g=Math.min,d="$0"==="a".replace(/./,"$0"),v=!!/./[l]&&""===/./[l]("a","$0");n("replace",(function(t,e,r){var n=v?"$":"$0";return[function(t,r){var n=u(this),i=null==t?void 0:t[l];return void 0!==i?i.call(t,n,r):e.call(String(n),t,r)},function(t,i){if("string"==typeof i&&-1===i.indexOf(n)&&-1===i.indexOf("$<")){var u=r(e,this,t,i);if(u.done)return u.value}var l=o(this),d=String(t),v="function"==typeof i;v||(i=String(i));var y=l.global;if(y){var m=l.unicode;l.lastIndex=0}for(var b=[];;){var S=h(l,d);if(null===S)break;if(b.push(S),!y)break;""===String(S[0])&&(l.lastIndex=c(d,s(l.lastIndex),m))}for(var w,_="",E=0,x=0;x<b.length;x++){S=b[x];for(var F=String(S[0]),A=p(g(a(S.index),d.length),0),T=[],k=1;k<S.length;k++)T.push(void 0===(w=S[k])?w:String(w));var P=S.groups;if(v){var R=[F].concat(T,A,d);void 0!==P&&R.push(P);var I=String(i.apply(void 0,R))}else I=f(F,d,A,T,P,i);A>=E&&(_+=d.slice(E,A)+I,E=A+F.length)}return _+d.slice(E)}]}),!!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!d||v)},function(t,e,r){"use strict";var n=r(0),i=r(13),o=r(68),s=r(55),a=r(155),u=r(6),c=r(28),f=u("replace"),h=RegExp.prototype,l=Math.max,p=function(t,e,r){return r>t.length?-1:""===e?r:t.indexOf(e,r)};n({target:"String",proto:!0},{replaceAll:function(t,e){var r,n,u,g,d,v,y,m,b=i(this),S=0,w=0,_="";if(null!=t){if((r=o(t))&&!~String(i("flags"in h?t.flags:s.call(t))).indexOf("g"))throw TypeError("`.replaceAll` does not allow non-global regexes");if(void 0!==(n=t[f]))return n.call(t,b,e);if(c&&r)return String(b).replace(t,e)}for(u=String(b),g=String(t),(d="function"==typeof e)||(e=String(e)),v=g.length,y=l(1,v),S=p(u,g,0);-1!==S;)m=d?String(e(g,S,u)):a(g,u,S,[],void 0,e),_+=u.slice(w,S)+m,w=S+v,S=p(u,g,S+y);return w<u.length&&(_+=u.slice(w)),_}})},function(t,e,r){"use strict";var n=r(85),i=r(4),o=r(13),s=r(147),a=r(87);n("search",(function(t,e,r){return[function(e){var r=o(this),n=null==e?void 0:e[t];return void 0!==n?n.call(e,r):new RegExp(e)[t](String(r))},function(t){var n=r(e,this,t);if(n.done)return n.value;var o=i(this),u=String(t),c=o.lastIndex;s(c,0)||(o.lastIndex=0);var f=a(o,u);return s(o.lastIndex,c)||(o.lastIndex=c),null===f?-1:f.index}]}))},function(t,e,r){"use strict";var n=r(85),i=r(68),o=r(4),s=r(13),a=r(38),u=r(86),c=r(7),f=r(87),h=r(83),l=r(82),p=r(1),g=l.UNSUPPORTED_Y,d=[].push,v=Math.min;n("split",(function(t,e,r){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var n=String(s(this)),o=void 0===r?4294967295:r>>>0;if(0===o)return[];if(void 0===t)return[n];if(!i(t))return e.call(n,t,o);for(var a,u,c,f=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,g=new RegExp(t.source,l+"g");(a=h.call(g,n))&&!((u=g.lastIndex)>p&&(f.push(n.slice(p,a.index)),a.length>1&&a.index<n.length&&d.apply(f,a.slice(1)),c=a[0].length,p=u,f.length>=o));)g.lastIndex===a.index&&g.lastIndex++;return p===n.length?!c&&g.test("")||f.push(""):f.push(n.slice(p)),f.length>o?f.slice(0,o):f}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:e.call(this,t,r)}:e,[function(e,r){var i=s(this),o=null==e?void 0:e[t];return void 0!==o?o.call(e,i,r):n.call(String(i),e,r)},function(t,i){var s=r(n,this,t,i,n!==e);if(s.done)return s.value;var h=o(this),l=String(t),p=a(h,RegExp),d=h.unicode,y=(h.ignoreCase?"i":"")+(h.multiline?"m":"")+(h.unicode?"u":"")+(g?"g":"y"),m=new p(g?"^(?:"+h.source+")":h,y),b=void 0===i?4294967295:i>>>0;if(0===b)return[];if(0===l.length)return null===f(m,l)?[l]:[];for(var S=0,w=0,_=[];w<l.length;){m.lastIndex=g?0:w;var E,x=f(m,g?l.slice(w):l);if(null===x||(E=v(c(m.lastIndex+(g?w:0)),l.length))===S)w=u(l,w,d);else{if(_.push(l.slice(S,w)),_.length===b)return _;for(var F=1;F<=x.length-1;F++)if(_.push(x[F]),_.length===b)return _;w=S=E}}return _.push(l.slice(S)),_}]}),!!p((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),g)},function(t,e,r){"use strict";var n,i=r(0),o=r(17).f,s=r(7),a=r(113),u=r(13),c=r(114),f=r(28),h="".startsWith,l=Math.min,p=c("startsWith");i({target:"String",proto:!0,forced:!!(f||p||(n=o(String.prototype,"startsWith"),!n||n.writable))&&!p},{startsWith:function(t){var e=String(u(this));a(t);var r=s(l(arguments.length>1?arguments[1]:void 0,e.length)),n=String(t);return h?h.call(e,n,r):e.slice(r,r+n.length)===n}})},function(t,e,r){"use strict";var n=r(0),i=r(13),o=r(20),s="".slice,a=Math.max,u=Math.min;n({target:"String",proto:!0},{substr:function(t,e){var r,n,c=String(i(this)),f=c.length,h=o(t);return h===1/0&&(h=0),h<0&&(h=a(f+h,0)),(r=void 0===e?f:o(e))<=0||r===1/0||h>=(n=u(h+r,f))?"":s.call(c,h,n)}})},function(t,e,r){"use strict";var n=r(0),i=r(54).trim;n({target:"String",proto:!0,forced:r(115)("trim")},{trim:function(){return i(this)}})},function(t,e,r){"use strict";var n=r(0),i=r(54).end,o=r(115)("trimEnd"),s=o?function(){return i(this)}:"".trimEnd;n({target:"String",proto:!0,forced:o},{trimEnd:s,trimRight:s})},function(t,e,r){"use strict";var n=r(0),i=r(54).start,o=r(115)("trimStart"),s=o?function(){return i(this)}:"".trimStart;n({target:"String",proto:!0,forced:o},{trimStart:s,trimLeft:s})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("anchor")},{anchor:function(t){return i(this,"a","name",t)}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("big")},{big:function(){return i(this,"big","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("blink")},{blink:function(){return i(this,"blink","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("bold")},{bold:function(){return i(this,"b","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("fixed")},{fixed:function(){return i(this,"tt","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("fontcolor")},{fontcolor:function(t){return i(this,"font","color",t)}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("fontsize")},{fontsize:function(t){return i(this,"font","size",t)}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("italics")},{italics:function(){return i(this,"i","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("link")},{link:function(t){return i(this,"a","href",t)}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("small")},{small:function(){return i(this,"small","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("strike")},{strike:function(){return i(this,"strike","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("sub")},{sub:function(){return i(this,"sub","","")}})},function(t,e,r){"use strict";var n=r(0),i=r(24);n({target:"String",proto:!0,forced:r(25)("sup")},{sup:function(){return i(this,"sup","","")}})},function(t,e,r){r(39)("Float32",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){var n=r(20);t.exports=function(t){var e=n(t);if(e<0)throw RangeError("The argument can't be less than 0");return e}},function(t,e,r){r(39)("Float64",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Int8",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Int16",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Int32",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Uint8",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Uint8",(function(t){return function(e,r,n){return t(this,e,r,n)}}),!0)},function(t,e,r){r(39)("Uint16",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){r(39)("Uint32",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},function(t,e,r){"use strict";var n=r(8),i=r(128),o=n.aTypedArray;(0,n.exportTypedArrayMethod)("copyWithin",(function(t,e){return i.call(o(this),t,e,arguments.length>2?arguments[2]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(16).every,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("every",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(101),o=n.aTypedArray;(0,n.exportTypedArrayMethod)("fill",(function(t){return i.apply(o(this),arguments)}))},function(t,e,r){"use strict";var n=r(8),i=r(16).filter,o=r(379),s=n.aTypedArray;(0,n.exportTypedArrayMethod)("filter",(function(t){var e=i(s(this),t,arguments.length>1?arguments[1]:void 0);return o(this,e)}))},function(t,e,r){var n=r(8).aTypedArrayConstructor,i=r(38);t.exports=function(t,e){for(var r=i(t,t.constructor),o=0,s=e.length,a=new(n(r))(s);s>o;)a[o]=e[o++];return a}},function(t,e,r){"use strict";var n=r(8),i=r(16).find,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("find",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(16).findIndex,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("findIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(16).forEach,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("forEach",(function(t){i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(116);(0,r(8).exportTypedArrayStaticMethod)("from",r(157),n)},function(t,e,r){"use strict";var n=r(8),i=r(59).includes,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("includes",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(59).indexOf,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("indexOf",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(2),i=r(8),o=r(73),s=r(6)("iterator"),a=n.Uint8Array,u=o.values,c=o.keys,f=o.entries,h=i.aTypedArray,l=i.exportTypedArrayMethod,p=a&&a.prototype[s],g=!!p&&("values"==p.name||null==p.name),d=function(){return u.call(h(this))};l("entries",(function(){return f.call(h(this))})),l("keys",(function(){return c.call(h(this))})),l("values",d,!g),l(s,d,!g)},function(t,e,r){"use strict";var n=r(8),i=n.aTypedArray,o=n.exportTypedArrayMethod,s=[].join;o("join",(function(t){return s.apply(i(this),arguments)}))},function(t,e,r){"use strict";var n=r(8),i=r(133),o=n.aTypedArray;(0,n.exportTypedArrayMethod)("lastIndexOf",(function(t){return i.apply(o(this),arguments)}))},function(t,e,r){"use strict";var n=r(8),i=r(16).map,o=r(38),s=n.aTypedArray,a=n.aTypedArrayConstructor;(0,n.exportTypedArrayMethod)("map",(function(t){return i(s(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(a(o(t,t.constructor)))(e)}))}))},function(t,e,r){"use strict";var n=r(8),i=r(116),o=n.aTypedArrayConstructor;(0,n.exportTypedArrayStaticMethod)("of",(function(){for(var t=0,e=arguments.length,r=new(o(this))(e);e>t;)r[t]=arguments[t++];return r}),i)},function(t,e,r){"use strict";var n=r(8),i=r(74).left,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduce",(function(t){return i(o(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(74).right,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduceRight",(function(t){return i(o(this),t,arguments.length,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=n.aTypedArray,o=n.exportTypedArrayMethod,s=Math.floor;o("reverse",(function(){for(var t,e=i(this).length,r=s(e/2),n=0;n<r;)t=this[n],this[n++]=this[--e],this[e]=t;return this}))},function(t,e,r){"use strict";var n=r(8),i=r(7),o=r(156),s=r(10),a=r(1),u=n.aTypedArray;(0,n.exportTypedArrayMethod)("set",(function(t){u(this);var e=o(arguments.length>1?arguments[1]:void 0,1),r=this.length,n=s(t),a=i(n.length),c=0;if(a+e>r)throw RangeError("Wrong length");for(;c<a;)this[e+c]=n[c++]}),a((function(){new Int8Array(1).set({})})))},function(t,e,r){"use strict";var n=r(8),i=r(38),o=r(1),s=n.aTypedArray,a=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod,c=[].slice;u("slice",(function(t,e){for(var r=c.call(s(this),t,e),n=i(this,this.constructor),o=0,u=r.length,f=new(a(n))(u);u>o;)f[o]=r[o++];return f}),o((function(){new Int8Array(1).slice()})))},function(t,e,r){"use strict";var n=r(8),i=r(16).some,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("some",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},function(t,e,r){"use strict";var n=r(8),i=r(2),o=r(1),s=r(19),a=r(7),u=r(134),c=r(135),f=r(136),h=r(41),l=r(104),p=n.aTypedArray,g=n.exportTypedArrayMethod,d=i.Uint16Array,v=d&&d.prototype.sort,y=!!v&&!o((function(){var t=new d(2);t.sort(null),t.sort({})})),m=!!v&&!o((function(){if(h)return h<74;if(c)return c<67;if(f)return!0;if(l)return l<602;var t,e,r=new d(516),n=Array(516);for(t=0;t<516;t++)e=t%4,r[t]=515-t,n[t]=t-2*e+3;for(r.sort((function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(r[t]!==n[t])return!0}));g("sort",(function(t){if(void 0!==t&&s(t),m)return v.call(this,t);p(this);var e,r=a(this.length),n=Array(r);for(e=0;e<r;e++)n[e]=this[e];for(n=u(this,function(t){return function(e,r){return void 0!==t?+t(e,r)||0:r!=r?-1:e!=e?1:0===e&&0===r?1/e>0&&1/r<0?1:-1:e>r}}(t)),e=0;e<r;e++)this[e]=n[e];return this}),!m||y)},function(t,e,r){"use strict";var n=r(8),i=r(7),o=r(40),s=r(38),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("subarray",(function(t,e){var r=a(this),n=r.length,u=o(t,n);return new(s(r,r.constructor))(r.buffer,r.byteOffset+u*r.BYTES_PER_ELEMENT,i((void 0===e?n:o(e,n))-u))}))},function(t,e,r){"use strict";var n=r(2),i=r(8),o=r(1),s=n.Int8Array,a=i.aTypedArray,u=i.exportTypedArrayMethod,c=[].toLocaleString,f=[].slice,h=!!s&&o((function(){c.call(new s(1))}));u("toLocaleString",(function(){return c.apply(h?f.call(a(this)):a(this),arguments)}),o((function(){return[1,2].toLocaleString()!=new s([1,2]).toLocaleString()}))||!o((function(){s.prototype.toLocaleString.call([1,2])})))},function(t,e,r){"use strict";var n=r(8).exportTypedArrayMethod,i=r(1),o=r(2).Uint8Array,s=o&&o.prototype||{},a=[].toString,u=[].join;i((function(){a.call({})}))&&(a=function(){return u.call(this)});var c=s.toString!=a;n("toString",a,c)},function(t,e,r){"use strict";var n=r(0),i=String.fromCharCode,o=/^[\da-f]{2}$/i,s=/^[\da-f]{4}$/i;n({global:!0},{unescape:function(t){for(var e,r,n=String(t),a="",u=n.length,c=0;c<u;){if("%"===(e=n.charAt(c++)))if("u"===n.charAt(c)){if(r=n.slice(c+1,c+5),s.test(r)){a+=i(parseInt(r,16)),c+=5;continue}}else if(r=n.slice(c,c+2),o.test(r)){a+=i(parseInt(r,16)),c+=2;continue}a+=e}return a}})},function(t,e,r){"use strict";var n,i=r(2),o=r(53),s=r(50),a=r(76),u=r(158),c=r(3),f=r(15).enforce,h=r(119),l=!i.ActiveXObject&&"ActiveXObject"in i,p=Object.isExtensible,g=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},d=t.exports=a("WeakMap",g,u);if(h&&l){n=u.getConstructor(g,"WeakMap",!0),s.REQUIRED=!0;var v=d.prototype,y=v.delete,m=v.has,b=v.get,S=v.set;o(v,{delete:function(t){if(c(t)&&!p(t)){var e=f(this);return e.frozen||(e.frozen=new n),y.call(this,t)||e.frozen.delete(t)}return y.call(this,t)},has:function(t){if(c(t)&&!p(t)){var e=f(this);return e.frozen||(e.frozen=new n),m.call(this,t)||e.frozen.has(t)}return m.call(this,t)},get:function(t){if(c(t)&&!p(t)){var e=f(this);return e.frozen||(e.frozen=new n),m.call(this,t)?b.call(this,t):e.frozen.get(t)}return b.call(this,t)},set:function(t,e){if(c(t)&&!p(t)){var r=f(this);r.frozen||(r.frozen=new n),m.call(this,t)?S.call(this,t,e):r.frozen.set(t,e)}else S.call(this,t,e);return this}})}},function(t,e,r){"use strict";r(76)("WeakSet",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),r(158))},function(t,e,r){var n=r(2),i=r(159),o=r(130),s=r(14);for(var a in i){var u=n[a],c=u&&u.prototype;if(c&&c.forEach!==o)try{s(c,"forEach",o)}catch(t){c.forEach=o}}},function(t,e,r){var n=r(2),i=r(159),o=r(73),s=r(14),a=r(6),u=a("iterator"),c=a("toStringTag"),f=o.values;for(var h in i){var l=n[h],p=l&&l.prototype;if(p){if(p[u]!==f)try{s(p,u,f)}catch(t){p[u]=f}if(p[c]||s(p,c,h),i[h])for(var g in o)if(p[g]!==o[g])try{s(p,g,o[g])}catch(t){p[g]=o[g]}}}},function(t,e,r){var n=r(0),i=r(2),o=r(109);n({global:!0,bind:!0,enumerable:!0,forced:!i.setImmediate||!i.clearImmediate},{setImmediate:o.set,clearImmediate:o.clear})},function(t,e,r){var n=r(0),i=r(2),o=r(150),s=r(51),a=i.process;n({global:!0,enumerable:!0,noTargetGet:!0},{queueMicrotask:function(t){var e=s&&a.domain;o(e?e.bind(t):t)}})},function(t,e,r){var n=r(0),i=r(2),o=r(42),s=[].slice,a=function(t){return function(e,r){var n=arguments.length>2,i=n?s.call(arguments,2):void 0;return t(n?function(){("function"==typeof e?e:Function(e)).apply(this,i)}:e,r)}};n({global:!0,bind:!0,forced:/MSIE .\./.test(o)},{setTimeout:a(i.setTimeout),setInterval:a(i.setInterval)})},function(t,e,r){"use strict";r(153);var n,i=r(0),o=r(5),s=r(160),a=r(2),u=r(97),c=r(18),f=r(46),h=r(12),l=r(145),p=r(131),g=r(84).codeAt,d=r(410),v=r(33),y=r(161),m=r(15),b=a.URL,S=y.URLSearchParams,w=y.getState,_=m.set,E=m.getterFor("URL"),x=Math.floor,F=Math.pow,A=/[A-Za-z]/,T=/[\d+-.A-Za-z]/,k=/\d/,P=/^0x/i,R=/^[0-7]+$/,I=/^\d+$/,C=/^[\dA-Fa-f]+$/,O=/[\0\t\n\r #%/:<>?@[\\\]^|]/,D=/[\0\t\n\r #/:<>?@[\\\]^|]/,N=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,L=/[\t\n\r]/g,j=function(t,e){var r,n,i;if("["==e.charAt(0)){if("]"!=e.charAt(e.length-1))return"Invalid host";if(!(r=B(e.slice(1,-1))))return"Invalid host";t.host=r}else if(z(t)){if(e=d(e),O.test(e))return"Invalid host";if(null===(r=M(e)))return"Invalid host";t.host=r}else{if(D.test(e))return"Invalid host";for(r="",n=p(e),i=0;i<n.length;i++)r+=W(n[i],H);t.host=r}},M=function(t){var e,r,n,i,o,s,a,u=t.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(e=u.length)>4)return t;for(r=[],n=0;n<e;n++){if(""==(i=u[n]))return t;if(o=10,i.length>1&&"0"==i.charAt(0)&&(o=P.test(i)?16:8,i=i.slice(8==o?1:2)),""===i)s=0;else{if(!(10==o?I:8==o?R:C).test(i))return t;s=parseInt(i,o)}r.push(s)}for(n=0;n<e;n++)if(s=r[n],n==e-1){if(s>=F(256,5-e))return null}else if(s>255)return null;for(a=r.pop(),n=0;n<r.length;n++)a+=r[n]*F(256,3-n);return a},B=function(t){var e,r,n,i,o,s,a,u=[0,0,0,0,0,0,0,0],c=0,f=null,h=0,l=function(){return t.charAt(h)};if(":"==l()){if(":"!=t.charAt(1))return;h+=2,f=++c}for(;l();){if(8==c)return;if(":"!=l()){for(e=r=0;r<4&&C.test(l());)e=16*e+parseInt(l(),16),h++,r++;if("."==l()){if(0==r)return;if(h-=r,c>6)return;for(n=0;l();){if(i=null,n>0){if(!("."==l()&&n<4))return;h++}if(!k.test(l()))return;for(;k.test(l());){if(o=parseInt(l(),10),null===i)i=o;else{if(0==i)return;i=10*i+o}if(i>255)return;h++}u[c]=256*u[c]+i,2!=++n&&4!=n||c++}if(4!=n)return;break}if(":"==l()){if(h++,!l())return}else if(l())return;u[c++]=e}else{if(null!==f)return;h++,f=++c}}if(null!==f)for(s=c-f,c=7;0!=c&&s>0;)a=u[c],u[c--]=u[f+s-1],u[f+--s]=a;else if(8!=c)return;return u},U=function(t){var e,r,n,i;if("number"==typeof t){for(e=[],r=0;r<4;r++)e.unshift(t%256),t=x(t/256);return e.join(".")}if("object"==typeof t){for(e="",n=function(t){for(var e=null,r=1,n=null,i=0,o=0;o<8;o++)0!==t[o]?(i>r&&(e=n,r=i),n=null,i=0):(null===n&&(n=o),++i);return i>r&&(e=n,r=i),e}(t),r=0;r<8;r++)i&&0===t[r]||(i&&(i=!1),n===r?(e+=r?":":"::",i=!0):(e+=t[r].toString(16),r<7&&(e+=":")));return"["+e+"]"}return t},H={},V=l({},H,{" ":1,'"':1,"<":1,">":1,"`":1}),q=l({},V,{"#":1,"?":1,"{":1,"}":1}),K=l({},q,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),W=function(t,e){var r=g(t,0);return r>32&&r<127&&!h(e,t)?t:encodeURIComponent(t)},J={ftp:21,file:null,http:80,https:443,ws:80,wss:443},z=function(t){return h(J,t.scheme)},Y=function(t){return""!=t.username||""!=t.password},G=function(t){return!t.host||t.cannotBeABaseURL||"file"==t.scheme},$=function(t,e){var r;return 2==t.length&&A.test(t.charAt(0))&&(":"==(r=t.charAt(1))||!e&&"|"==r)},X=function(t){var e;return t.length>1&&$(t.slice(0,2))&&(2==t.length||"/"===(e=t.charAt(2))||"\\"===e||"?"===e||"#"===e)},Z=function(t){var e=t.path,r=e.length;!r||"file"==t.scheme&&1==r&&$(e[0],!0)||e.pop()},Q=function(t){return"."===t||"%2e"===t.toLowerCase()},tt={},et={},rt={},nt={},it={},ot={},st={},at={},ut={},ct={},ft={},ht={},lt={},pt={},gt={},dt={},vt={},yt={},mt={},bt={},St={},wt=function(t,e,r,i){var o,s,a,u,c,f=r||tt,l=0,g="",d=!1,v=!1,y=!1;for(r||(t.scheme="",t.username="",t.password="",t.host=null,t.port=null,t.path=[],t.query=null,t.fragment=null,t.cannotBeABaseURL=!1,e=e.replace(N,"")),e=e.replace(L,""),o=p(e);l<=o.length;){switch(s=o[l],f){case tt:if(!s||!A.test(s)){if(r)return"Invalid scheme";f=rt;continue}g+=s.toLowerCase(),f=et;break;case et:if(s&&(T.test(s)||"+"==s||"-"==s||"."==s))g+=s.toLowerCase();else{if(":"!=s){if(r)return"Invalid scheme";g="",f=rt,l=0;continue}if(r&&(z(t)!=h(J,g)||"file"==g&&(Y(t)||null!==t.port)||"file"==t.scheme&&!t.host))return;if(t.scheme=g,r)return void(z(t)&&J[t.scheme]==t.port&&(t.port=null));g="","file"==t.scheme?f=pt:z(t)&&i&&i.scheme==t.scheme?f=nt:z(t)?f=at:"/"==o[l+1]?(f=it,l++):(t.cannotBeABaseURL=!0,t.path.push(""),f=mt)}break;case rt:if(!i||i.cannotBeABaseURL&&"#"!=s)return"Invalid scheme";if(i.cannotBeABaseURL&&"#"==s){t.scheme=i.scheme,t.path=i.path.slice(),t.query=i.query,t.fragment="",t.cannotBeABaseURL=!0,f=St;break}f="file"==i.scheme?pt:ot;continue;case nt:if("/"!=s||"/"!=o[l+1]){f=ot;continue}f=ut,l++;break;case it:if("/"==s){f=ct;break}f=yt;continue;case ot:if(t.scheme=i.scheme,s==n)t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query=i.query;else if("/"==s||"\\"==s&&z(t))f=st;else if("?"==s)t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query="",f=bt;else{if("#"!=s){t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.path.pop(),f=yt;continue}t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,t.path=i.path.slice(),t.query=i.query,t.fragment="",f=St}break;case st:if(!z(t)||"/"!=s&&"\\"!=s){if("/"!=s){t.username=i.username,t.password=i.password,t.host=i.host,t.port=i.port,f=yt;continue}f=ct}else f=ut;break;case at:if(f=ut,"/"!=s||"/"!=g.charAt(l+1))continue;l++;break;case ut:if("/"!=s&&"\\"!=s){f=ct;continue}break;case ct:if("@"==s){d&&(g="%40"+g),d=!0,a=p(g);for(var m=0;m<a.length;m++){var b=a[m];if(":"!=b||y){var S=W(b,K);y?t.password+=S:t.username+=S}else y=!0}g=""}else if(s==n||"/"==s||"?"==s||"#"==s||"\\"==s&&z(t)){if(d&&""==g)return"Invalid authority";l-=p(g).length+1,g="",f=ft}else g+=s;break;case ft:case ht:if(r&&"file"==t.scheme){f=dt;continue}if(":"!=s||v){if(s==n||"/"==s||"?"==s||"#"==s||"\\"==s&&z(t)){if(z(t)&&""==g)return"Invalid host";if(r&&""==g&&(Y(t)||null!==t.port))return;if(u=j(t,g))return u;if(g="",f=vt,r)return;continue}"["==s?v=!0:"]"==s&&(v=!1),g+=s}else{if(""==g)return"Invalid host";if(u=j(t,g))return u;if(g="",f=lt,r==ht)return}break;case lt:if(!k.test(s)){if(s==n||"/"==s||"?"==s||"#"==s||"\\"==s&&z(t)||r){if(""!=g){var w=parseInt(g,10);if(w>65535)return"Invalid port";t.port=z(t)&&w===J[t.scheme]?null:w,g=""}if(r)return;f=vt;continue}return"Invalid port"}g+=s;break;case pt:if(t.scheme="file","/"==s||"\\"==s)f=gt;else{if(!i||"file"!=i.scheme){f=yt;continue}if(s==n)t.host=i.host,t.path=i.path.slice(),t.query=i.query;else if("?"==s)t.host=i.host,t.path=i.path.slice(),t.query="",f=bt;else{if("#"!=s){X(o.slice(l).join(""))||(t.host=i.host,t.path=i.path.slice(),Z(t)),f=yt;continue}t.host=i.host,t.path=i.path.slice(),t.query=i.query,t.fragment="",f=St}}break;case gt:if("/"==s||"\\"==s){f=dt;break}i&&"file"==i.scheme&&!X(o.slice(l).join(""))&&($(i.path[0],!0)?t.path.push(i.path[0]):t.host=i.host),f=yt;continue;case dt:if(s==n||"/"==s||"\\"==s||"?"==s||"#"==s){if(!r&&$(g))f=yt;else if(""==g){if(t.host="",r)return;f=vt}else{if(u=j(t,g))return u;if("localhost"==t.host&&(t.host=""),r)return;g="",f=vt}continue}g+=s;break;case vt:if(z(t)){if(f=yt,"/"!=s&&"\\"!=s)continue}else if(r||"?"!=s)if(r||"#"!=s){if(s!=n&&(f=yt,"/"!=s))continue}else t.fragment="",f=St;else t.query="",f=bt;break;case yt:if(s==n||"/"==s||"\\"==s&&z(t)||!r&&("?"==s||"#"==s)){if(".."===(c=(c=g).toLowerCase())||"%2e."===c||".%2e"===c||"%2e%2e"===c?(Z(t),"/"==s||"\\"==s&&z(t)||t.path.push("")):Q(g)?"/"==s||"\\"==s&&z(t)||t.path.push(""):("file"==t.scheme&&!t.path.length&&$(g)&&(t.host&&(t.host=""),g=g.charAt(0)+":"),t.path.push(g)),g="","file"==t.scheme&&(s==n||"?"==s||"#"==s))for(;t.path.length>1&&""===t.path[0];)t.path.shift();"?"==s?(t.query="",f=bt):"#"==s&&(t.fragment="",f=St)}else g+=W(s,q);break;case mt:"?"==s?(t.query="",f=bt):"#"==s?(t.fragment="",f=St):s!=n&&(t.path[0]+=W(s,H));break;case bt:r||"#"!=s?s!=n&&("'"==s&&z(t)?t.query+="%27":t.query+="#"==s?"%23":W(s,H)):(t.fragment="",f=St);break;case St:s!=n&&(t.fragment+=W(s,V))}l++}},_t=function(t){var e,r,n=f(this,_t,"URL"),i=arguments.length>1?arguments[1]:void 0,s=String(t),a=_(n,{type:"URL"});if(void 0!==i)if(i instanceof _t)e=E(i);else if(r=wt(e={},String(i)))throw TypeError(r);if(r=wt(a,s,null,e))throw TypeError(r);var u=a.searchParams=new S,c=w(u);c.updateSearchParams(a.query),c.updateURL=function(){a.query=String(u)||null},o||(n.href=xt.call(n),n.origin=Ft.call(n),n.protocol=At.call(n),n.username=Tt.call(n),n.password=kt.call(n),n.host=Pt.call(n),n.hostname=Rt.call(n),n.port=It.call(n),n.pathname=Ct.call(n),n.search=Ot.call(n),n.searchParams=Dt.call(n),n.hash=Nt.call(n))},Et=_t.prototype,xt=function(){var t=E(this),e=t.scheme,r=t.username,n=t.password,i=t.host,o=t.port,s=t.path,a=t.query,u=t.fragment,c=e+":";return null!==i?(c+="//",Y(t)&&(c+=r+(n?":"+n:"")+"@"),c+=U(i),null!==o&&(c+=":"+o)):"file"==e&&(c+="//"),c+=t.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==a&&(c+="?"+a),null!==u&&(c+="#"+u),c},Ft=function(){var t=E(this),e=t.scheme,r=t.port;if("blob"==e)try{return new _t(e.path[0]).origin}catch(t){return"null"}return"file"!=e&&z(t)?e+"://"+U(t.host)+(null!==r?":"+r:""):"null"},At=function(){return E(this).scheme+":"},Tt=function(){return E(this).username},kt=function(){return E(this).password},Pt=function(){var t=E(this),e=t.host,r=t.port;return null===e?"":null===r?U(e):U(e)+":"+r},Rt=function(){var t=E(this).host;return null===t?"":U(t)},It=function(){var t=E(this).port;return null===t?"":String(t)},Ct=function(){var t=E(this),e=t.path;return t.cannotBeABaseURL?e[0]:e.length?"/"+e.join("/"):""},Ot=function(){var t=E(this).query;return t?"?"+t:""},Dt=function(){return E(this).searchParams},Nt=function(){var t=E(this).fragment;return t?"#"+t:""},Lt=function(t,e){return{get:t,set:e,configurable:!0,enumerable:!0}};if(o&&u(Et,{href:Lt(xt,(function(t){var e=E(this),r=String(t),n=wt(e,r);if(n)throw TypeError(n);w(e.searchParams).updateSearchParams(e.query)})),origin:Lt(Ft),protocol:Lt(At,(function(t){var e=E(this);wt(e,String(t)+":",tt)})),username:Lt(Tt,(function(t){var e=E(this),r=p(String(t));if(!G(e)){e.username="";for(var n=0;n<r.length;n++)e.username+=W(r[n],K)}})),password:Lt(kt,(function(t){var e=E(this),r=p(String(t));if(!G(e)){e.password="";for(var n=0;n<r.length;n++)e.password+=W(r[n],K)}})),host:Lt(Pt,(function(t){var e=E(this);e.cannotBeABaseURL||wt(e,String(t),ft)})),hostname:Lt(Rt,(function(t){var e=E(this);e.cannotBeABaseURL||wt(e,String(t),ht)})),port:Lt(It,(function(t){var e=E(this);G(e)||(""==(t=String(t))?e.port=null:wt(e,t,lt))})),pathname:Lt(Ct,(function(t){var e=E(this);e.cannotBeABaseURL||(e.path=[],wt(e,t+"",vt))})),search:Lt(Ot,(function(t){var e=E(this);""==(t=String(t))?e.query=null:("?"==t.charAt(0)&&(t=t.slice(1)),e.query="",wt(e,t,bt)),w(e.searchParams).updateSearchParams(e.query)})),searchParams:Lt(Dt),hash:Lt(Nt,(function(t){var e=E(this);""!=(t=String(t))?("#"==t.charAt(0)&&(t=t.slice(1)),e.fragment="",wt(e,t,St)):e.fragment=null}))}),c(Et,"toJSON",(function(){return xt.call(this)}),{enumerable:!0}),c(Et,"toString",(function(){return xt.call(this)}),{enumerable:!0}),b){var jt=b.createObjectURL,Mt=b.revokeObjectURL;jt&&c(_t,"createObjectURL",(function(t){return jt.apply(b,arguments)})),Mt&&c(_t,"revokeObjectURL",(function(t){return Mt.apply(b,arguments)}))}v(_t,"URL"),i({global:!0,forced:!s,sham:!o},{URL:_t})},function(t,e,r){"use strict";var n=/[^\0-\u007E]/,i=/[.\u3002\uFF0E\uFF61]/g,o="Overflow: input needs wider integers to process",s=Math.floor,a=String.fromCharCode,u=function(t){return t+22+75*(t<26)},c=function(t,e,r){var n=0;for(t=r?s(t/700):t>>1,t+=s(t/e);t>455;n+=36)t=s(t/35);return s(n+36*t/(t+38))},f=function(t){var e,r,n=[],i=(t=function(t){for(var e=[],r=0,n=t.length;r<n;){var i=t.charCodeAt(r++);if(i>=55296&&i<=56319&&r<n){var o=t.charCodeAt(r++);56320==(64512&o)?e.push(((1023&i)<<10)+(1023&o)+65536):(e.push(i),r--)}else e.push(i)}return e}(t)).length,f=128,h=0,l=72;for(e=0;e<t.length;e++)(r=t[e])<128&&n.push(a(r));var p=n.length,g=p;for(p&&n.push("-");g<i;){var d=**********;for(e=0;e<t.length;e++)(r=t[e])>=f&&r<d&&(d=r);var v=g+1;if(d-f>s((**********-h)/v))throw RangeError(o);for(h+=(d-f)*v,f=d,e=0;e<t.length;e++){if((r=t[e])<f&&++h>**********)throw RangeError(o);if(r==f){for(var y=h,m=36;;m+=36){var b=m<=l?1:m>=l+26?26:m-l;if(y<b)break;var S=y-b,w=36-b;n.push(a(u(b+S%w))),y=s(S/w)}n.push(a(u(y))),l=c(h,v,g==p),h=0,++g}}++h,++f}return n.join("")};t.exports=function(t){var e,r,o=[],s=t.toLowerCase().replace(i,".").split(".");for(e=0;e<s.length;e++)r=s[e],o.push(n.test(r)?"xn--"+f(r):r);return o.join(".")}},function(t,e,r){var n=r(4),i=r(64);t.exports=function(t){var e=i(t);if("function"!=typeof e)throw TypeError(String(t)+" is not iterable");return n(e.call(t))}},function(t,e,r){"use strict";r(0)({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},function(t,e,r){"use strict";var n=r(26),i=r(162),o=r(414),s=r(168);function a(t){var e=new o(t),r=i(o.prototype.request,e);return n.extend(r,o.prototype,e),n.extend(r,e),r}var u=a(r(165));u.Axios=o,u.create=function(t){return a(s(u.defaults,t))},u.Cancel=r(169),u.CancelToken=r(428),u.isCancel=r(164),u.all=function(t){return Promise.all(t)},u.spread=r(429),u.isAxiosError=r(430),t.exports=u,t.exports.default=u},function(t,e,r){"use strict";var n=r(26),i=r(163),o=r(415),s=r(416),a=r(168);function u(t){this.defaults=t,this.interceptors={request:new o,response:new o}}u.prototype.request=function(t){"string"==typeof t?(t=arguments[1]||{}).url=arguments[0]:t=t||{},(t=a(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var e=[s,void 0],r=Promise.resolve(t);for(this.interceptors.request.forEach((function(t){e.unshift(t.fulfilled,t.rejected)})),this.interceptors.response.forEach((function(t){e.push(t.fulfilled,t.rejected)}));e.length;)r=r.then(e.shift(),e.shift());return r},u.prototype.getUri=function(t){return t=a(this.defaults,t),i(t.url,t.params,t.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],(function(t){u.prototype[t]=function(e,r){return this.request(a(r||{},{method:t,url:e,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(t){u.prototype[t]=function(e,r,n){return this.request(a(n||{},{method:t,url:e,data:r}))}})),t.exports=u},function(t,e,r){"use strict";var n=r(26);function i(){this.handlers=[]}i.prototype.use=function(t,e){return this.handlers.push({fulfilled:t,rejected:e}),this.handlers.length-1},i.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},i.prototype.forEach=function(t){n.forEach(this.handlers,(function(e){null!==e&&t(e)}))},t.exports=i},function(t,e,r){"use strict";var n=r(26),i=r(417),o=r(164),s=r(165);function a(t){t.cancelToken&&t.cancelToken.throwIfRequested()}t.exports=function(t){return a(t),t.headers=t.headers||{},t.data=i(t.data,t.headers,t.transformRequest),t.headers=n.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||s.adapter)(t).then((function(e){return a(t),e.data=i(e.data,e.headers,t.transformResponse),e}),(function(e){return o(e)||(a(t),e&&e.response&&(e.response.data=i(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},function(t,e,r){"use strict";var n=r(26);t.exports=function(t,e,r){return n.forEach(r,(function(r){t=r(t,e)})),t}},function(t,e){var r,n,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(t){n=s}}();var u,c=[],f=!1,h=-1;function l(){f&&u&&(f=!1,u.length?c=u.concat(c):h=-1,c.length&&p())}function p(){if(!f){var t=a(l);f=!0;for(var e=c.length;e;){for(u=c,c=[];++h<e;)u&&u[h].run();h=-1,e=c.length}u=null,f=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function g(t,e){this.fun=t,this.array=e}function d(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];c.push(new g(t,e)),1!==c.length||f||a(p)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=d,i.addListener=d,i.once=d,i.off=d,i.removeListener=d,i.removeAllListeners=d,i.emit=d,i.prependListener=d,i.prependOnceListener=d,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,e,r){"use strict";var n=r(26);t.exports=function(t,e){n.forEach(t,(function(r,n){n!==e&&n.toUpperCase()===e.toUpperCase()&&(t[e]=r,delete t[n])}))}},function(t,e,r){"use strict";var n=r(167);t.exports=function(t,e,r){var i=r.config.validateStatus;r.status&&i&&!i(r.status)?e(n("Request failed with status code "+r.status,r.config,null,r.request,r)):t(r)}},function(t,e,r){"use strict";t.exports=function(t,e,r,n,i){return t.config=e,r&&(t.code=r),t.request=n,t.response=i,t.isAxiosError=!0,t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},t}},function(t,e,r){"use strict";var n=r(26);t.exports=n.isStandardBrowserEnv()?{write:function(t,e,r,i,o,s){var a=[];a.push(t+"="+encodeURIComponent(e)),n.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),n.isString(i)&&a.push("path="+i),n.isString(o)&&a.push("domain="+o),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(t,e,r){"use strict";var n=r(424),i=r(425);t.exports=function(t,e){return t&&!n(e)?i(t,e):e}},function(t,e,r){"use strict";t.exports=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}},function(t,e,r){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},function(t,e,r){"use strict";var n=r(26),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(t){var e,r,o,s={};return t?(n.forEach(t.split("\n"),(function(t){if(o=t.indexOf(":"),e=n.trim(t.substr(0,o)).toLowerCase(),r=n.trim(t.substr(o+1)),e){if(s[e]&&i.indexOf(e)>=0)return;s[e]="set-cookie"===e?(s[e]?s[e]:[]).concat([r]):s[e]?s[e]+", "+r:r}})),s):s}},function(t,e,r){"use strict";var n=r(26);t.exports=n.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function i(t){var n=t;return e&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return t=i(window.location.href),function(e){var r=n.isString(e)?i(e):e;return r.protocol===t.protocol&&r.host===t.host}}():function(){return!0}},function(t,e,r){"use strict";var n=r(169);function i(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(t){e=t}));var r=this;t((function(t){r.reason||(r.reason=new n(t),e(r.reason))}))}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var t;return{token:new i((function(e){t=e})),cancel:t}},t.exports=i},function(t,e,r){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},function(t,e,r){"use strict";t.exports=function(t){return"object"==typeof t&&!0===t.isAxiosError}},function(t,e,r){"use strict";r.r(e),r.d(e,"OidcBytAuth",(function(){return Pt})),r.d(e,"oidcAuthCallback",(function(){return v})),r.d(e,"oidcAuthSilent",(function(){return y}));var n=r(27),i=r.n(n),o=r(11),s=r.n(o),a=(r(175),r(117),r(170)),u=r.n(a),c=r(171),f=r.n(c),h=r(172),l=r.n(h),p=r(30),g=r.n(p),d={authority:"",client_id:"",redirect_uri:"",response_type:"code",scope:"openid profile AppGateway",automaticSilentRenew:!0,silent_redirect_uri:"",accessTokenExpiringNotificationTime:60,revokeAccessTokenOnSignout:!0,silentRequestTimeout:1e4,checkSessionInterval:2e3,popupWindowTarget:""};function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={response_mode:"query"},r=null,n=null,i=!0;t instanceof Object&&("userStore"in t&&"localStorage"===t.userStore&&(e.userStore=new g.a.WebStorageStateStore({store:window.localStorage})),"customCallback"in t&&t.customCallback instanceof Function&&(r=t.customCallback),"customErrorCallback"in t&&t.customErrorCallback instanceof Function&&(n=t.customErrorCallback),"sendTopMessage"in t&&(i=t.sendTopMessage));var o=window.location.href.replace("/callback.html","");o=o.replace("/callback","");var s=window.sessionStorage.getItem("idsrv:redirect_url")||o;return new g.a.UserManager(e).signinRedirectCallback().then((function(){return i&&self!==top?(top.postMessage(JSON.stringify({code:1,originID:"clientSite",returnUrl:""}),"*"),Promise.resolve()):r?(r(),Promise.resolve(s)):(window.sessionStorage.removeItem("idsrv:redirect_url"),window.location.replace(s),Promise.resolve(s))})).catch((function(t){return console.log(t),n?(n(),Promise.reject(t)):(window.sessionStorage.removeItem("idsrv:redirect_url"),window.location.replace(s),Promise.reject(t))}))}function y(){return(new g.a.UserManager).signinSilentCallback().then((function(){return console.log("Success: Refresh Token"),Promise.resolve(!0)})).catch((function(t){return console.log(t),Promise.reject(new Error(t))}))}var m=function(){function t(e){f()(this,t);var r={site:{label:"业务站点",required:!0},baseUrl:{label:"当前站点路由",required:!0},authority:{label:"sso登录站点",required:!0},client_id:{label:"业务站点clientId",required:!0},redirect_uri:{label:"获取token回调地址",required:!0},silent_redirect_uri:{label:"token刷新地址",required:!0}};for(var n in"redirect_uri"in e||"silent_redirect_uri"in e?(r.site.required=!1,r.baseUrl.required=!1):(r.redirect_uri.required=!1,r.silent_redirect_uri.required=!1),r)if(r[n].required&&!(n in e))return console.error("".concat(n," is required：").concat(r[n].label));if(this.setLanguage=function(){return"zh"},"language"in e){if(!(e.language instanceof Function))return console.error("language must be function");this.setLanguage=e.language}r.site.required&&(e.redirect_uri="".concat(e.site).concat(e.baseUrl,"callback"),e.silent_redirect_uri="".concat(e.site).concat(e.baseUrl,"silent"));var i=function(t){var e={};for(var r in d)e[r]=r in t?t[r]:d[r];return"userStore"in t&&"localStorage"===t.userStore&&(e.userStore=new g.a.WebStorageStateStore({store:window.localStorage})),e}(e);this.userStore=null,"userStore"in e&&(this.userStore=e.userStore),this.customCallback=null,"customCallback"in e&&e.customCallback instanceof Function&&(this.customCallback=e.customCallback),this.customErrorCallback=null,"customErrorCallback"in e&&e.customErrorCallback instanceof Function&&(this.customErrorCallback=e.customErrorCallback),this.customLogin="customLogin"in e&&e.customLogin,this.sendTopMessage=!("sendTopMessage"in e)||e.sendTopMessage,this.baseUrl="baseUrl"in e?e.baseUrl:"/","debug"in e&&e.debug&&(g.a.Log.logger=window.console,g.a.Log.level=g.a.Log.DEBUG),this.addParams={},this.userManager=new g.a.UserManager(i)}var e,r,n,o,a;return l()(t,[{key:"setLoginLanguage",value:function(t){this.setLanguage=function(){return t}}},{key:"userSignin",value:(a=i()(s.a.mark((function t(){var e,r,n,o,a,u,c,f,h=this,l=arguments;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=l.length>0&&void 0!==l[0]?l[0]:{},r=!("redirect"in e)||e.redirect,n="signinRedirectUrl"in e?e.signinRedirectUrl:"",o="logoutRedirectUrl"in e?e.logoutRedirectUrl:"",a="customSigninRedirect"in e?e.customSigninRedirect:null,u="customLogoutRedirect"in e?e.customLogoutRedirect:null,t.next=8,this.validateToken();case 8:if(c=t.sent,f=c.token){t.next=24;break}if(!a){t.next=22;break}return t.next=14,this.userManager.signinSilent().catch((function(){}));case 14:return t.next=16,this.validateToken();case 16:if(t.sent.token){t.next=20;break}return a(),t.abrupt("return",Promise.reject(new Error("no token")));case 20:t.next=24;break;case 22:return r&&this.userSigninRedirect(n),t.abrupt("return",Promise.reject(new Error("no token")));case 24:return this.userManager.events.addUserSignedOut(i()(s.a.mark((function t(){return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!u){t.next=7;break}return t.next=3,h.userManager.events.removeUserSignedOut();case 3:return t.next=5,h.userManager.removeUser();case 5:return u(),t.abrupt("return",Promise.reject(new Error("login out")));case 7:return r&&h.userSignout(o),t.abrupt("return",Promise.reject(new Error("login out")));case 9:case"end":return t.stop()}}),t)})))),t.abrupt("return",Promise.resolve(f));case 26:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"userSignout",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";t||(t=location.href),e||(e=t),window.sessionStorage.setItem("idsrv:redirect_url",e),this.userManager.events.removeUserSignedOut(),this.userManager.signoutRedirect({useReplaceToNavigate:!0,post_logout_redirect_uri:t})}},{key:"getToken",value:(o=i()(s.a.mark((function t(){var e,r,n,i=arguments;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=!(i.length>0&&void 0!==i[0])||i[0],t.next=3,this.validateToken();case 3:if(r=t.sent,n=r.token){t.next=8;break}return e&&this.userSigninRedirect(),t.abrupt("return",Promise.reject(new Error(null)));case 8:return t.abrupt("return",Promise.resolve(n));case 9:case"end":return t.stop()}}),t,this)}))),function(){return o.apply(this,arguments)})},{key:"userSigninRedirect",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";t||(t=window.location.href),window.sessionStorage.setItem("idsrv:redirect_url",t);var e={useReplaceToNavigate:!0,extraQueryParams:{language:this.setLanguage()}};for(var r in this.addParams)e.extraQueryParams[r]=this.addParams[r];this.userManager.signinRedirect(e),this.clearAddParams()}},{key:"validateToken",value:function(){var t={token:null,user:null};return this.userManager.getUser().then((function(e){return e?"access_token"in e&&!e.access_token||"expired"in e&&e.expired?t:{token:"".concat(e.token_type," ").concat(e.access_token),user:e}:t})).catch((function(){return t}))}},{key:"getUserInfo",value:(n=i()(s.a.mark((function t(){var e,r;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.validateToken();case 2:return e=t.sent,r=e.user,t.abrupt("return",r);case 5:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"getUserProfile",value:(r=i()(s.a.mark((function t(){var e,r;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.validateToken();case 2:return e=t.sent,r=e.user,t.abrupt("return",Promise.resolve(r?r.profile:{}));case 5:case"end":return t.stop()}}),t,this)}))),function(){return r.apply(this,arguments)})},{key:"setAddParams",value:function(t){this.addParams=t}},{key:"clearAddParams",value:function(){this.addParams={}}},{key:"getUserManager",value:function(){return this.userManager}},{key:"createIframe",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ssoIframe";if(""===t&&(t=this.popupWindowTarget),!document.querySelector("#".concat(t))){var e=document.querySelector("body"),r=document.createElement("iframe");r.setAttribute("id",t),r.setAttribute("name",t),r.setAttribute("style","display:none;"),e.appendChild(r)}}},{key:"removeSsoIframe",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ssoIframe",e=document.querySelector("#".concat(t));e&&document.querySelector("body").removeChild(e)}},{key:"userSigninPopup",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;return this.createIframe(),this.userManager.events.removeUserSignedOut(),this.userManager.events.addUserSignedOut(i()(s.a.mark((function e(){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.userManager.events.removeUserSignedOut(),console.log("login out popup"),e.next=4,t.userManager.removeUser();case 4:if(t.customLogin){e.next=7;break}return t.userSignout(),e.abrupt("return");case 7:if("function"!=typeof t.customLogin){e.next=10;break}return t.customLogin(),e.abrupt("return");case 10:case"end":return e.stop()}}),e)})))),this.userManager.signinPopup({extraQueryParams:e}),this.userManager.signinPopupCallback(),new Promise((function(t,e){var n=null;n&&clearTimeout(n),n=setTimeout((function(){e(new Error("登录超时")),clearTimeout(n)}),1e3*r);var i=function r(i){if(console.log("oidc listen message",i.data),"object"!==u()(i.data)){var o={};try{o=JSON.parse(i.data)}catch(t){console.log("oidc listen message json",t)}"code"in o&&(window.removeEventListener("message",r),n&&clearTimeout(n),-1!==o.code?t({code:o.code}):e(new Error("errmsg"in o?o.errmsg:"登录失败")))}};window.removeEventListener("message",i),window.addEventListener("message",i)}))}},{key:"userSignoutPopup",value:(e=i()(s.a.mark((function t(){var e,r=arguments;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=r.length>0&&void 0!==r[0]?r[0]:window.location.href,this.createIframe(),this.userManager.signoutPopup({post_logout_redirect_uri:e}),this.userManager.signoutPopupCallback().catch((function(t){return Promise.reject(new Error(t))})),t.abrupt("return",Promise.resolve());case 5:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"beforeEach",value:function(t,e){var r=this,n=[{path:"".concat(this.baseUrl,"callback"),name:"OidcCallback"},{path:"".concat(this.baseUrl,"silent"),name:"OidcSilent"},{path:"".concat(this.baseUrl,"autoLogin"),name:"AutoLogin"}];t.addRoutes(n),t.beforeEach(function(){var t=i()(s.a.mark((function t(n,i,o){var a,u,c,f,h,l,p,g;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("OidcCallback"!==n.name){t.next=11;break}return a={},r.userStore&&(a.userStore=r.userStore),r.customCallback&&(a.customCallback=r.customCallback),r.customErrorCallback&&(a.customErrorCallback=r.customErrorCallback),a.sendTopMessage=r.sendTopMessage,t.next=8,v(a).catch((function(){return Promise.reject(new Error(!1))}));case 8:return t.abrupt("return",Promise.resolve(!0));case 11:if("OidcSilent"!==n.name){t.next=17;break}return t.next=14,y().catch((function(){return Promise.reject(new Error(!1))}));case 14:return t.abrupt("return",Promise.resolve(!0));case 17:if("AutoLogin"!==n.name){t.next=46;break}if(u=n.query.uid,c=n.query.code,f=n.query.did||"",h=n.query.returl,u){t.next=25;break}return console.error("uid is required"),t.abrupt("return",Promise.reject(new Error(!1)));case 25:if(c){t.next=28;break}return console.error("code is required"),t.abrupt("return",Promise.reject(new Error(!1)));case 28:if(h){t.next=31;break}return console.error("retUrl is required"),t.abrupt("return",Promise.reject(new Error(!1)));case 31:return r.setAddParams({uid:u,code:c,did:f}),t.next=34,r.userSignin({signinRedirectUrl:h}).catch((function(t){return console.log(t,"error"),Promise.reject(new Error(!1))}));case 34:return t.next=36,r.validateToken();case 36:if(l=t.sent,p=l.token,!(g=l.user)||!("profile"in g)||u===g.profile.sub){t.next=42;break}return r.userSignout(),t.abrupt("return",Promise.resolve(!0));case 42:return console.log("middle page token={".concat(p,"} uid={").concat(u,"} code={").concat(c,"}")),p?window.location.replace(decodeURIComponent(h)):r.userSigninRedirect(h),r.clearAddParams(),t.abrupt("return",Promise.resolve(!0));case 46:e(n,i,o);case 47:case"end":return t.stop()}}),t)})));return function(e,r,n){return t.apply(this,arguments)}}())}}]),t}();function b(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function S(t,e){return t&e}function w(t,e){return t|e}function _(t,e){return t^e}function E(t,e){return t&~e}function x(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function F(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var A,T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function k(t){var e,r,n="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),n+=T.charAt(r>>6)+T.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),n+=T.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),n+=T.charAt(r>>2)+T.charAt((3&r)<<4));(3&n.length)>0;)n+="=";return n}function P(t){var e,r="",n=0,i=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var o=T.indexOf(t.charAt(e));o<0||(0==n?(r+=b(o>>2),i=3&o,n=1):1==n?(r+=b(i<<2|o>>4),i=15&o,n=2):2==n?(r+=b(i),r+=b(o>>2),i=3&o,n=3):(r+=b(i<<2|o>>4),r+=b(15&o),n=0))}return 1==n&&(r+=b(i<<2)),r}var R,I=function(t){var e;if(void 0===A){var r="0123456789ABCDEF";for(A={},e=0;e<16;++e)A[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)A[r.charAt(e)]=e;for(e=0;e<" \f\n\r\t \u2028\u2029".length;++e)A[" \f\n\r\t \u2028\u2029".charAt(e)]=-1}var n=[],i=0,o=0;for(e=0;e<t.length;++e){var s=t.charAt(e);if("="==s)break;if(-1!=(s=A[s])){if(void 0===s)throw new Error("Illegal character at offset "+e);i|=s,++o>=2?(n[n.length]=i,i=0,o=0):i<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return n},C={decode:function(t){var e;if(void 0===R){for(R=Object.create(null),e=0;e<64;++e)R["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(R["-"]=62,R._=63,e=0;e<"= \f\n\r\t \u2028\u2029".length;++e)R["= \f\n\r\t \u2028\u2029".charAt(e)]=-1}var r=[],n=0,i=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=R[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++i>=4?(r[r.length]=n>>16,r[r.length]=n>>8&255,r[r.length]=255&n,n=0,i=0):n<<=6}}switch(i){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=n>>10;break;case 3:r[r.length]=n>>16,r[r.length]=n>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=C.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return C.decode(t)}},O=1e13,D=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,n,i=this.buf,o=i.length;for(r=0;r<o;++r)(n=i[r]*t+e)<O?e=0:n-=(e=0|n/O)*O,i[r]=n;e>0&&(i[r]=e)},t.prototype.sub=function(t){var e,r,n=this.buf,i=n.length;for(e=0;e<i;++e)(r=n[e]-t)<0?(r+=O,t=1):t=0,n[e]=r;for(;0===n[n.length-1];)n.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;n>=0;--n)r+=(O+e[n]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*O+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),N=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,L=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function j(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var M,B=function(){function t(e,r){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset "+t+" on a stream of length "+this.enc.length);return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var n="",i=t;i<e;++i)if(n+=this.hexByte(this.get(i)),!0!==r)switch(15&i){case 7:n+="  ";break;case 15:n+="\n";break;default:n+=" "}return n},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",n=t;n<e;){var i=this.get(n++);r+=i<128?String.fromCharCode(i):i>191&&i<224?String.fromCharCode((31&i)<<6|63&this.get(n++)):String.fromCharCode((15&i)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,n,i="",o=t;o<e;)r=this.get(o++),n=this.get(o++),i+=String.fromCharCode(r<<8|n);return i},t.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),i=(r?N:L).exec(n);return i?(r&&(i[1]=+i[1],i[1]+=+i[1]<70?2e3:1900),n=i[1]+"-"+i[2]+"-"+i[3]+" "+i[4],i[5]&&(n+=":"+i[5],i[6]&&(n+=":"+i[6],i[7]&&(n+="."+i[7]))),i[8]&&(n+=" UTC","Z"!=i[8]&&(n+=i[8],i[9]&&(n+=":"+i[9]))),n):"Unrecognized time: "+n},t.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),i=n>127,o=i?255:0,s="";n==o&&++t<e;)n=this.get(t);if(0===(r=e-t))return i?-1:0;if(r>4){for(s=n,r<<=3;0==(128&(+s^o));)s=+s<<1,--r;s="("+r+" bit)\n"}i&&(n-=256);for(var a=new D(n),u=t+1;u<e;++u)a.mulAdd(256,this.get(u));return s+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),i="("+((e-t-1<<3)-n)+" bit)\n",o="",s=t+1;s<e;++s){for(var a=this.get(s),u=s==e-1?n:0,c=7;c>=u;--c)o+=a>>c&1?"1":"0";if(o.length>r)return i+j(o,r)}return i+o},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return j(this.parseStringISO(t,e),r);var n=e-t,i="("+n+" byte)\n";n>(r/=2)&&(e=t+r);for(var o=t;o<e;++o)i+=this.hexByte(this.get(o));return n>r&&(i+="…"),i},t.prototype.parseOID=function(t,e,r){for(var n="",i=new D,o=0,s=t;s<e;++s){var a=this.get(s);if(i.mulAdd(128,127&a),o+=7,!(128&a)){if(""===n)if((i=i.simplify())instanceof D)i.sub(80),n="2."+i.toString();else{var u=i<80?i<40?0:1:2;n=u+"."+(i-40*u)}else n+="."+i.toString();if(n.length>r)return j(n,r);i=new D,o=0}}return o>0&&(n+=".incomplete"),n},t}(),U=function(){function t(t,e,r,n,i){if(!(n instanceof H))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=i}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return j(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return j(this.stream.parseStringISO(e,e+r),t);case 30:return j(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var n=0;n<r;++n)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof B?e:new B(e,0);var n=new B(r),i=new H(r),o=t.decodeLength(r),s=r.pos,a=s-n.pos,u=null,c=function(){var e=[];if(null!==o){for(var n=s+o;r.pos<n;)e[e.length]=t.decode(r);if(r.pos!=n)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var i=t.decode(r);if(i.tag.isEOC())break;e[e.length]=i}o=s-r.pos}catch(t){throw new Error("Exception while decoding undefined length content: "+t)}return e};if(i.tagConstructed)u=c();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");u=c();for(var f=0;f<u.length;++f)if(u[f].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(t){u=null}if(null===u){if(null===o)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);r.pos=s+Math.abs(o)}return new t(n,a,o,i,u)},t}(),H=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new D;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),V=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],q=(1<<26)/V[V.length-1],K=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(r=this[s]>>a)>0&&(i=!0,o=b(r));s>=0;)a<e?(r=(this[s]&(1<<a)-1)<<e-a,r|=this[--s]>>(a+=this.DB-e)):(r=this[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),r>0&&(i=!0),i&&(o+=b(r));return i?o:"0"},t.prototype.negate=function(){var e=G();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+nt(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=G();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new J(e):new z(e),this.exp(t,r)},t.prototype.clone=function(){var t=G();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=G();return this.bitwiseTo(t,S,e),e},t.prototype.or=function(t){var e=G();return this.bitwiseTo(t,w,e),e},t.prototype.xor=function(t){var e=G();return this.bitwiseTo(t,_,e),e},t.prototype.andNot=function(t){var e=G();return this.bitwiseTo(t,E,e),e},t.prototype.not=function(){for(var t=G(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=G();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=G();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+x(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=F(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,w)},t.prototype.clearBit=function(t){return this.changeBit(t,E)},t.prototype.flipBit=function(t){return this.changeBit(t,_)},t.prototype.add=function(t){var e=G();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=G();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=G();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=G();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=G();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=G(),r=G();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),o=rt(1);if(i<=0)return o;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new J(e):e.isEven()?new Y(e):new z(e);var s=[],a=3,u=r-1,c=(1<<r)-1;if(s[1]=n.convert(this),r>1){var f=G();for(n.sqrTo(s[1],f);a<=c;)s[a]=G(),n.mulTo(f,s[a-2],s[a]),a+=2}var h,l,p=t.t-1,g=!0,d=G();for(i=nt(t[p])-1;p>=0;){for(i>=u?h=t[p]>>i-u&c:(h=(t[p]&(1<<i+1)-1)<<u-i,p>0&&(h|=t[p-1]>>this.DB+i-u)),a=r;0==(1&h);)h>>=1,--a;if((i-=a)<0&&(i+=this.DB,--p),g)s[h].copyTo(o),g=!1;else{for(;a>1;)n.sqrTo(o,d),n.sqrTo(d,o),a-=2;a>0?n.sqrTo(o,d):(l=o,o=d,d=l),n.mulTo(d,s[h],o)}for(;p>=0&&0==(t[p]&1<<i);)n.sqrTo(o,d),l=o,o=d,d=l,--i<0&&(i=this.DB-1,--p)}return n.revert(o)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var n=e.clone(),i=this.clone(),o=rt(1),s=rt(0),a=rt(0),u=rt(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),r?(o.isEven()&&s.isEven()||(o.addTo(this,o),s.subTo(e,s)),o.rShiftTo(1,o)):s.isEven()||s.subTo(e,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),r?(a.isEven()&&u.isEven()||(a.addTo(this,a),u.subTo(e,u)),a.rShiftTo(1,a)):u.isEven()||u.subTo(e,u),u.rShiftTo(1,u);n.compareTo(i)>=0?(n.subTo(i,n),r&&o.subTo(a,o),s.subTo(u,s)):(i.subTo(n,i),r&&a.subTo(o,a),u.subTo(s,u))}return 0!=i.compareTo(t.ONE)?t.ZERO:u.compareTo(e)>=0?u.subtract(e):u.signum()<0?(u.addTo(e,u),u.signum()<0?u.add(e):u):u},t.prototype.pow=function(t){return this.exp(t,new W)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(i<o&&(o=i),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=V[V.length-1]){for(e=0;e<V.length;++e)if(r[0]==V[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<V.length;){for(var n=V[e],i=e+1;i<V.length&&n<q;)n*=V[i++];for(n=r.modInt(n);e<i;)if(n%V[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var n;if(16==r)n=4;else if(8==r)n=3;else if(256==r)n=8;else if(2==r)n=1;else if(32==r)n=5;else{if(4!=r)return void this.fromRadix(e,r);n=2}this.t=0,this.s=0;for(var i=e.length,o=!1,s=0;--i>=0;){var a=8==n?255&+e[i]:et(e,i);a<0?"-"==e.charAt(i)&&(o=!0):(o=!1,0==s?this[this.t++]=a:s+n>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,(s+=n)>=this.DB&&(s-=this.DB))}8==n&&0!=(128&+e[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),o&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,i=(1<<n)-1,o=Math.floor(t/this.DB),s=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+o+1]=this[a]>>n|s,s=(this[a]&i)<<r;for(a=o-1;a>=0;--a)e[a]=0;e[o]=s,e.t=this.t+o+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&o)<<i,e[s-r]=this[s]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<i),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var n=this.abs(),i=e.abs(),o=n.t;for(r.t=o+i.t;--o>=0;)r[o]=0;for(o=0;o<i.t;++o)r[o+n.t]=n.am(0,i[o],r,o,0,n.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,n){var i=e.abs();if(!(i.t<=0)){var o=this.abs();if(o.t<i.t)return null!=r&&r.fromInt(0),void(null!=n&&this.copyTo(n));null==n&&(n=G());var s=G(),a=this.s,u=e.s,c=this.DB-nt(i[i.t-1]);c>0?(i.lShiftTo(c,s),o.lShiftTo(c,n)):(i.copyTo(s),o.copyTo(n));var f=s.t,h=s[f-1];if(0!=h){var l=h*(1<<this.F1)+(f>1?s[f-2]>>this.F2:0),p=this.FV/l,g=(1<<this.F1)/l,d=1<<this.F2,v=n.t,y=v-f,m=null==r?G():r;for(s.dlShiftTo(y,m),n.compareTo(m)>=0&&(n[n.t++]=1,n.subTo(m,n)),t.ONE.dlShiftTo(f,m),m.subTo(s,s);s.t<f;)s[s.t++]=0;for(;--y>=0;){var b=n[--v]==h?this.DM:Math.floor(n[v]*p+(n[v-1]+d)*g);if((n[v]+=s.am(0,b,n,y,0,f))<b)for(s.dlShiftTo(y,m),n.subTo(m,n);n[v]<--b;)n.subTo(m,n)}null!=r&&(n.drShiftTo(f,r),a!=u&&t.ZERO.subTo(r,r)),n.t=f,n.clamp(),c>0&&n.rShiftTo(c,n),a<0&&t.ZERO.subTo(n,n)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var n=G(),i=G(),o=r.convert(this),s=nt(e)-1;for(o.copyTo(n);--s>=0;)if(r.sqrTo(n,i),(e&1<<s)>0)r.mulTo(i,o,n);else{var a=n;n=i,i=a}return r.revert(n)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=rt(r),i=G(),o=G(),s="";for(this.divRemTo(n,i,o);i.signum()>0;)s=(r+o.intValue()).toString(t).substr(1)+s,i.divRemTo(n,i,o);return o.intValue().toString(t)+s},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var n=this.chunkSize(r),i=Math.pow(r,n),o=!1,s=0,a=0,u=0;u<e.length;++u){var c=et(e,u);c<0?"-"==e.charAt(u)&&0==this.signum()&&(o=!0):(a=r*a+c,++s>=n&&(this.dMultiply(i),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(r,s)),this.dAddOffset(a,0)),o&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,n){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),w,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var i=[],o=7&e;i.length=1+(e>>3),r.nextBytes(i),o>0?i[0]&=(1<<o)-1:i[0]=0,this.fromString(i,256)}},t.prototype.bitwiseTo=function(t,e,r){var n,i,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var n=t.ONE.shiftLeft(e);return this.bitwiseTo(n,r,n),n},t.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var i=r.t-this.t;n<i;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(i=Math.min(t.t,e);n<i;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),n=r.getLowestSetBit();if(n<=0)return!1;var i=r.shiftRight(n);(e=e+1>>1)>V.length&&(e=V.length);for(var o=G(),s=0;s<e;++s){o.fromInt(V[Math.floor(Math.random()*V.length)]);var a=o.modPow(i,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var u=1;u++<n&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=G();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var i=r;r=n,n=i}var o=r.getLowestSetBit(),s=n.getLowestSetBit();if(s<0)e(r);else{o<s&&(s=o),s>0&&(r.rShiftTo(s,r),n.rShiftTo(s,n));var a=function(){(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(a,0):(s>0&&n.lShiftTo(s,n),setTimeout((function(){e(n)}),0))};setTimeout(a,10)}},t.prototype.fromNumberAsync=function(e,r,n,i){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,n),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),w,this),this.isEven()&&this.dAddOffset(1,0);var o=this,s=function(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(t.ONE.shiftLeft(e-1),o),o.isProbablePrime(r)?setTimeout((function(){i()}),0):setTimeout(s,0)};setTimeout(s,0)}else{var a=[],u=7&e;a.length=1+(e>>3),r.nextBytes(a),u>0?a[0]&=(1<<u)-1:a[0]=0,this.fromString(a,256)}},t}(),W=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),J=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),z=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=G();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(K.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=G();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),Y=function(){function t(t){this.m=t,this.r2=G(),this.q3=G(),K.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=G();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function G(){return new K(null)}function $(t,e){return new K(t,e)}var X="undefined"!=typeof navigator;X&&"Microsoft Internet Explorer"==navigator.appName?(K.prototype.am=function(t,e,r,n,i,o){for(var s=32767&e,a=e>>15;--o>=0;){var u=32767&this[t],c=this[t++]>>15,f=a*u+c*s;i=((u=s*u+((32767&f)<<15)+r[n]+(**********&i))>>>30)+(f>>>15)+a*c+(i>>>30),r[n++]=**********&u}return i},M=30):X&&"Netscape"!=navigator.appName?(K.prototype.am=function(t,e,r,n,i,o){for(;--o>=0;){var s=e*this[t++]+r[n]+i;i=Math.floor(s/67108864),r[n++]=67108863&s}return i},M=26):(K.prototype.am=function(t,e,r,n,i,o){for(var s=16383&e,a=e>>14;--o>=0;){var u=16383&this[t],c=this[t++]>>14,f=a*u+c*s;i=((u=s*u+((16383&f)<<14)+r[n]+i)>>28)+(f>>14)+a*c,r[n++]=268435455&u}return i},M=28),K.prototype.DB=M,K.prototype.DM=(1<<M)-1,K.prototype.DV=1<<M;K.prototype.FV=Math.pow(2,52),K.prototype.F1=52-M,K.prototype.F2=2*M-52;var Z,Q,tt=[];for(Z="0".charCodeAt(0),Q=0;Q<=9;++Q)tt[Z++]=Q;for(Z="a".charCodeAt(0),Q=10;Q<36;++Q)tt[Z++]=Q;for(Z="A".charCodeAt(0),Q=10;Q<36;++Q)tt[Z++]=Q;function et(t,e){var r=tt[t.charCodeAt(e)];return null==r?-1:r}function rt(t){var e=G();return e.fromInt(t),e}function nt(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}K.ZERO=rt(0),K.ONE=rt(1);var it=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var ot,st,at=null;if(null==at){at=[],st=0;var ut=void 0;if(window.crypto&&window.crypto.getRandomValues){var ct=new Uint32Array(256);for(window.crypto.getRandomValues(ct),ut=0;ut<ct.length;++ut)at[st++]=255&ct[ut]}var ft=0,ht=function(t){if((ft=ft||0)>=256||st>=256)window.removeEventListener?window.removeEventListener("mousemove",ht,!1):window.detachEvent&&window.detachEvent("onmousemove",ht);else try{var e=t.x+t.y;at[st++]=255&e,ft+=1}catch(t){}};window.addEventListener?window.addEventListener("mousemove",ht,!1):window.attachEvent&&window.attachEvent("onmousemove",ht)}function lt(){if(null==ot){for(ot=new it;st<256;){var t=Math.floor(65536*Math.random());at[st++]=255&t}for(ot.init(at),st=0;st<at.length;++st)at[st]=0;st=0}return ot.next()}var pt=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=lt()},t}();var gt=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=$(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=t.length-1;n>=0&&e>0;){var i=t.charCodeAt(n--);i<128?r[--e]=i:i>127&&i<2048?(r[--e]=63&i|128,r[--e]=i>>6|192):(r[--e]=63&i|128,r[--e]=i>>6&63|128,r[--e]=i>>12|224)}r[--e]=0;for(var o=new pt,s=[];e>2;){for(s[0]=0;0==s[0];)o.nextBytes(s);r[--e]=s[0]}return r[--e]=2,r[--e]=0,new K(r)}(t,e);if(null==r)return null;var n=this.doPublic(r);if(null==n)return null;for(var i=n.toString(16),o=i.length,s=0;s<2*e-o;s++)i="0"+i;return i},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=$(t,16),this.e=parseInt(e,16),this.d=$(r,16)):console.error("Invalid RSA private key")},t.prototype.setPrivateEx=function(t,e,r,n,i,o,s,a){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=$(t,16),this.e=parseInt(e,16),this.d=$(r,16),this.p=$(n,16),this.q=$(i,16),this.dmp1=$(o,16),this.dmq1=$(s,16),this.coeff=$(a,16)):console.error("Invalid RSA private key")},t.prototype.generate=function(t,e){var r=new pt,n=t>>1;this.e=parseInt(e,16);for(var i=new K(e,16);;){for(;this.p=new K(t-n,1,r),0!=this.p.subtract(K.ONE).gcd(i).compareTo(K.ONE)||!this.p.isProbablePrime(10););for(;this.q=new K(n,1,r),0!=this.q.subtract(K.ONE).gcd(i).compareTo(K.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var s=this.p.subtract(K.ONE),a=this.q.subtract(K.ONE),u=s.multiply(a);if(0==u.gcd(i).compareTo(K.ONE)){this.n=this.p.multiply(this.q),this.d=i.modInverse(u),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=$(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){var r=t.toByteArray(),n=0;for(;n<r.length&&0==r[n];)++n;if(r.length-n!=e-1||2!=r[n])return null;++n;for(;0!=r[n];)if(++n>=r.length)return null;var i="";for(;++n<r.length;){var o=255&r[n];o<128?i+=String.fromCharCode(o):o>191&&o<224?(i+=String.fromCharCode((31&o)<<6|63&r[n+1]),++n):(i+=String.fromCharCode((15&o)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return i}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var n=new pt,i=t>>1;this.e=parseInt(e,16);var o=new K(e,16),s=this,a=function(){var e=function(){if(s.p.compareTo(s.q)<=0){var t=s.p;s.p=s.q,s.q=t}var e=s.p.subtract(K.ONE),n=s.q.subtract(K.ONE),i=e.multiply(n);0==i.gcd(o).compareTo(K.ONE)?(s.n=s.p.multiply(s.q),s.d=o.modInverse(i),s.dmp1=s.d.mod(e),s.dmq1=s.d.mod(n),s.coeff=s.q.modInverse(s.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},u=function(){s.q=G(),s.q.fromNumberAsync(i,1,n,(function(){s.q.subtract(K.ONE).gcda(o,(function(t){0==t.compareTo(K.ONE)&&s.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(u,0)}))}))},c=function(){s.p=G(),s.p.fromNumberAsync(t-i,1,n,(function(){s.p.subtract(K.ONE).gcda(o,(function(t){0==t.compareTo(K.ONE)&&s.p.isProbablePrime(10)?setTimeout(u,0):setTimeout(c,0)}))}))};setTimeout(c,0)};setTimeout(a,0)},t.prototype.sign=function(t,e,r){var n=function(t,e){if(e<t.length+22)return console.error("Message too long for RSA"),null;for(var r=e-t.length-6,n="",i=0;i<r;i+=2)n+="ff";return $("0001"+n+"00"+t,16)}((dt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(null==n)return null;var i=this.doPrivate(n);if(null==i)return null;var o=i.toString(16);return 0==(1&o.length)?o:"0"+o},t.prototype.verify=function(t,e,r){var n=$(e,16),i=this.doPublic(n);return null==i?null:function(t){for(var e in dt)if(dt.hasOwnProperty(e)){var r=dt[e],n=r.length;if(t.substr(0,n)==r)return t.substr(n)}return t}
/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/(i.toString(16).replace(/^1f+00/,""))==r(t).toString()},t}();var dt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var vt={};vt.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var n=function(){};if(n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var i;for(i in r)t.prototype[i]=r[i];var o=function(){},s=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(o=function(t,e){for(i=0;i<s.length;i+=1){var r=s[i],n=e[r];"function"==typeof n&&n!=Object.prototype[r]&&(t[r]=n)}})}catch(t){}o(t.prototype,r)}}};
/**
 * @fileOverview
 * @name asn1-1.0.js
 * <AUTHOR>
 * @version asn1 1.0.13 (2017-Jun-02)
 * @since jsrsasign 2.1
 * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
 */
var yt={};void 0!==yt.asn1&&yt.asn1||(yt.asn1={}),yt.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var n="",i=0;i<r;i++)n+="f";e=new K(n,16).xor(t).add(K.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=yt.asn1,r=e.DERBoolean,n=e.DERInteger,i=e.DERBitString,o=e.DEROctetString,s=e.DERNull,a=e.DERObjectIdentifier,u=e.DEREnumerated,c=e.DERUTF8String,f=e.DERNumericString,h=e.DERPrintableString,l=e.DERTeletexString,p=e.DERIA5String,g=e.DERUTCTime,d=e.DERGeneralizedTime,v=e.DERSequence,y=e.DERSet,m=e.DERTaggedObject,b=e.ASN1Util.newObject,S=Object.keys(t);if(1!=S.length)throw"key of param shall be only one.";var w=S[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+w+":"))throw"undefined key: "+w;if("bool"==w)return new r(t[w]);if("int"==w)return new n(t[w]);if("bitstr"==w)return new i(t[w]);if("octstr"==w)return new o(t[w]);if("null"==w)return new s(t[w]);if("oid"==w)return new a(t[w]);if("enum"==w)return new u(t[w]);if("utf8str"==w)return new c(t[w]);if("numstr"==w)return new f(t[w]);if("prnstr"==w)return new h(t[w]);if("telstr"==w)return new l(t[w]);if("ia5str"==w)return new p(t[w]);if("utctime"==w)return new g(t[w]);if("gentime"==w)return new d(t[w]);if("seq"==w){for(var _=t[w],E=[],x=0;x<_.length;x++){var F=b(_[x]);E.push(F)}return new v({array:E})}if("set"==w){for(_=t[w],E=[],x=0;x<_.length;x++){F=b(_[x]);E.push(F)}return new y({array:E})}if("tag"==w){var A=t[w];if("[object Array]"===Object.prototype.toString.call(A)&&3==A.length){var T=b(A[2]);return new m({tag:A[0],explicit:A[1],obj:T})}var k={};if(void 0!==A.explicit&&(k.explicit=A.explicit),void 0!==A.tag&&(k.tag=A.tag),void 0===A.obj)throw"obj shall be specified for 'tag'.";return k.obj=b(A.obj),new m(k)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},yt.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),n=(e=Math.floor(r/40)+"."+r%40,""),i=2;i<t.length;i+=2){var o=("00000000"+parseInt(t.substr(i,2),16).toString(2)).slice(-8);if(n+=o.substr(1,7),"0"==o.substr(0,1))e=e+"."+new K(n,2).toString(10),n=""}return e},yt.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new K(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var o="",s=0;s<i;s++)o+="0";n=o+n;for(s=0;s<n.length-1;s+=7){var a=n.substr(s,7);s!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),o=40*parseInt(i[0])+parseInt(i[1]);n+=e(o),i.splice(0,2);for(var s=0;s<i.length;s++)n+=r(i[s]);return n},yt.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},yt.asn1.DERAbstractString=function(t){yt.asn1.DERAbstractString.superclass.constructor.call(this);this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},vt.lang.extend(yt.asn1.DERAbstractString,yt.asn1.ASN1Object),yt.asn1.DERAbstractTime=function(t){yt.asn1.DERAbstractTime.superclass.constructor.call(this);this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,i=this.localDateToUTC(t),o=String(i.getFullYear());"utc"==e&&(o=o.substr(2,2));var s=o+n(String(i.getMonth()+1),2)+n(String(i.getDate()),2)+n(String(i.getHours()),2)+n(String(i.getMinutes()),2)+n(String(i.getSeconds()),2);if(!0===r){var a=i.getMilliseconds();if(0!=a){var u=n(String(a),3);s=s+"."+(u=u.replace(/[0]+$/,""))}}return s+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,i,o){var s=new Date(Date.UTC(t,e-1,r,n,i,o,0));this.setByDate(s)},this.getFreshValueHex=function(){return this.hV}},vt.lang.extend(yt.asn1.DERAbstractTime,yt.asn1.ASN1Object),yt.asn1.DERAbstractStructured=function(t){yt.asn1.DERAbstractString.superclass.constructor.call(this);this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},vt.lang.extend(yt.asn1.DERAbstractStructured,yt.asn1.ASN1Object),yt.asn1.DERBoolean=function(){yt.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},vt.lang.extend(yt.asn1.DERBoolean,yt.asn1.ASN1Object),yt.asn1.DERInteger=function(t){yt.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=yt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new K(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},vt.lang.extend(yt.asn1.DERInteger,yt.asn1.ASN1Object),yt.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=yt.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}yt.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var n="";for(r=0;r<t.length-1;r+=8){var i=t.substr(r,8),o=parseInt(i,2).toString(16);1==o.length&&(o="0"+o),n+=o}this.hTLV=null,this.isModified=!0,this.hV="0"+e+n},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},vt.lang.extend(yt.asn1.DERBitString,yt.asn1.ASN1Object),yt.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=yt.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}yt.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},vt.lang.extend(yt.asn1.DEROctetString,yt.asn1.DERAbstractString),yt.asn1.DERNull=function(){yt.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},vt.lang.extend(yt.asn1.DERNull,yt.asn1.ASN1Object),yt.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new K(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var o="",s=0;s<i;s++)o+="0";n=o+n;for(s=0;s<n.length-1;s+=7){var a=n.substr(s,7);s!=n.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};yt.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),o=40*parseInt(i[0])+parseInt(i[1]);n+=e(o),i.splice(0,2);for(var s=0;s<i.length;s++)n+=r(i[s]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(t){var e=yt.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},vt.lang.extend(yt.asn1.DERObjectIdentifier,yt.asn1.ASN1Object),yt.asn1.DEREnumerated=function(t){yt.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=yt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new K(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},vt.lang.extend(yt.asn1.DEREnumerated,yt.asn1.ASN1Object),yt.asn1.DERUTF8String=function(t){yt.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},vt.lang.extend(yt.asn1.DERUTF8String,yt.asn1.DERAbstractString),yt.asn1.DERNumericString=function(t){yt.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},vt.lang.extend(yt.asn1.DERNumericString,yt.asn1.DERAbstractString),yt.asn1.DERPrintableString=function(t){yt.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},vt.lang.extend(yt.asn1.DERPrintableString,yt.asn1.DERAbstractString),yt.asn1.DERTeletexString=function(t){yt.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},vt.lang.extend(yt.asn1.DERTeletexString,yt.asn1.DERAbstractString),yt.asn1.DERIA5String=function(t){yt.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},vt.lang.extend(yt.asn1.DERIA5String,yt.asn1.DERAbstractString),yt.asn1.DERUTCTime=function(t){yt.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},vt.lang.extend(yt.asn1.DERUTCTime,yt.asn1.DERAbstractTime),yt.asn1.DERGeneralizedTime=function(t){yt.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},vt.lang.extend(yt.asn1.DERGeneralizedTime,yt.asn1.DERAbstractTime),yt.asn1.DERSequence=function(t){yt.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},vt.lang.extend(yt.asn1.DERSequence,yt.asn1.DERAbstractStructured),yt.asn1.DERSet=function(t){yt.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},vt.lang.extend(yt.asn1.DERSet,yt.asn1.DERAbstractStructured),yt.asn1.DERTaggedObject=function(t){yt.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},vt.lang.extend(yt.asn1.DERTaggedObject,yt.asn1.ASN1Object);var mt,bt=(mt=function(t,e){return(mt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}mt(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),St=function(t){function e(r){var n=t.call(this)||this;return r&&("string"==typeof r?n.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&n.parsePropertiesFrom(r)),n}return bt(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,n=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?I(t):C.unarmor(t),i=U.decode(n);if(3===i.sub.length&&(i=i.sub[2].sub[0]),9===i.sub.length){e=i.sub[1].getHexStringValue(),this.n=$(e,16),r=i.sub[2].getHexStringValue(),this.e=parseInt(r,16);var o=i.sub[3].getHexStringValue();this.d=$(o,16);var s=i.sub[4].getHexStringValue();this.p=$(s,16);var a=i.sub[5].getHexStringValue();this.q=$(a,16);var u=i.sub[6].getHexStringValue();this.dmp1=$(u,16);var c=i.sub[7].getHexStringValue();this.dmq1=$(c,16);var f=i.sub[8].getHexStringValue();this.coeff=$(f,16)}else{if(2!==i.sub.length)return!1;var h=i.sub[1].sub[0];e=h.sub[0].getHexStringValue(),this.n=$(e,16),r=h.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(t){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new yt.asn1.DERInteger({int:0}),new yt.asn1.DERInteger({bigint:this.n}),new yt.asn1.DERInteger({int:this.e}),new yt.asn1.DERInteger({bigint:this.d}),new yt.asn1.DERInteger({bigint:this.p}),new yt.asn1.DERInteger({bigint:this.q}),new yt.asn1.DERInteger({bigint:this.dmp1}),new yt.asn1.DERInteger({bigint:this.dmq1}),new yt.asn1.DERInteger({bigint:this.coeff})]};return new yt.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return k(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new yt.asn1.DERSequence({array:[new yt.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new yt.asn1.DERNull]}),e=new yt.asn1.DERSequence({array:[new yt.asn1.DERInteger({bigint:this.n}),new yt.asn1.DERInteger({int:this.e})]}),r=new yt.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new yt.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return k(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(gt),wt=r(173),_t=function(){function t(t){t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new St(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(P(t))}catch(t){return!1}},t.prototype.encrypt=function(t){try{return k(this.getKey().encrypt(t))}catch(t){return!1}},t.prototype.sign=function(t,e,r){try{return k(this.getKey().sign(t,e,r))}catch(t){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,P(e),r)}catch(t){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new St,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=wt.version,t}(),Et=r(174),xt=r.n(Et),Ft="";"domainConfig"in window&&"gatewayServer"in window.domainConfig&&(Ft=window.domainConfig.gatewayServer);var At=xt.a.create({baseURL:Ft,timeout:6e4});function Tt(t){if(!t)return Promise.reject(new Error("加密字符串不能为空"));var e=new _t;return At.get("/Api/Security/Keys/v1/PublicKey").then((function(r){if(r&&"isSuccess"in r&&r.isSuccess){e.setPublicKey(r.returnObj);var n=e.encrypt(t);return Promise.resolve(n)}return Promise.reject(new Error("密码加密失败"))})).catch((function(t){return Promise.reject(new Error(t))}))}function kt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},r=function r(n){if("string"==typeof n.data){var i=null;try{i=JSON.parse(n.data)}catch(t){return}i&&"originID"in i&&"code"in i&&(console.log(i),1===i.code?t():-1===i.code&&e(i.errmsg),window.removeEventListener("message",r))}};window.removeEventListener("message",r),window.addEventListener("message",r)}function Pt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};window.domainConfig={},t.client_id="mode"in t&&"dev"===t.mode?"jsEvosClient":"evosClient","authority"in t?(t.popupWindowTarget="ssoIframe",this.OidcAuth=new m(t),this.loginStatus=this.OidcAuth.userSigninPopup()):console.error("authority is must")}At.interceptors.request.use((function(t){return t.baseURL=window.domainConfig.gatewayServer,t})),At.interceptors.response.use((function(t){return t&&t.data?Promise.resolve(t.data):Promise.reject("接口返回的数据格式错误！")}),(function(t){return Promise.reject(new Error(t))})),Pt.prototype.validateLogin=function(){},Pt.prototype.validateMobile=function(t){return/^1\d{10}$/.test(t)},Pt.prototype.validateEmail=function(t){return/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(t)},Pt.prototype.loginByCode=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e="success"in t?t.success:function(){},r="error"in t?t.error:function(){};if("[object Object]"!==Object.prototype.toString.call(t))return r("参数格式错误"),!1;var n="data"in t?t.data:{};if("[object Object]"!==Object.prototype.toString.call(n))return r("参数格式错误"),!1;if(!("userName"in n)||!("authCode"in n))return r("手机邮箱、验证码必不可少"),!1;if(!this.validateMobile(n.userName)&&!this.validateEmail(n.userName))return r("手机/邮箱格式错误"),!1;var i={userName:n.userName,authCode:n.authCode,originID:"clientSite",type:this.validateMobile(n.userName)?"phone":"email"},o=document.querySelector("#ssoIframe");o.contentWindow.postMessage(JSON.stringify(i),"*"),kt(e,r)},Pt.prototype.loginByUidCode=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e="success"in t?t.success:function(){},r="error"in t?t.error:function(){};if("[object Object]"!==Object.prototype.toString.call(t))return r("参数格式错误"),!1;var n="data"in t?t.data:{};return"[object Object]"!==Object.prototype.toString.call(n)?(r("参数格式错误"),!1):"uid"in n&&"code"in n?n.uid&&n.code?void this.OidcAuth.userSigninPopup({uid:n.uid,code:n.code}).then((function(){e()})).catch((function(t){r(t&&"message"in t?t.message:"登录错误")})):(r("uid、code不能为空"),!1):(r("参数错误"),!1)},Pt.prototype.loginByPwd=function(){var t=i()(s.a.mark((function t(e){var r,n,i,o;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r="success"in e?e.success:function(){},n="error"in e?e.error:function(){},"[object Object]"===Object.prototype.toString.call(e)){t.next=5;break}return n("参数格式错误"),t.abrupt("return",!1);case 5:if(i="data"in e?e.data:{},"[object Object]"===Object.prototype.toString.call(i)){t.next=9;break}return n("参数格式错误"),t.abrupt("return",!1);case 9:if("userName"in i&&"password"in i){t.next=12;break}return n("手机邮箱、密码必不可少"),t.abrupt("return",!1);case 12:return o={userName:i.userName,password:"",originID:"clientSite",type:"account"},t.next=15,Tt(i.password).catch((function(){}));case 15:o.password=t.sent,o.password?o["x-encrypt"]=1:o.password=i.password,document.querySelector("#ssoIframe").contentWindow.postMessage(JSON.stringify(o),"*"),kt(r,n);case 20:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),Pt.prototype.getToken=i()(s.a.mark((function t(){var e;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.loginStatus.catch((function(){return null}));case 2:return t.next=4,this.OidcAuth.getToken(!1).catch((function(){return null}));case 4:return e=t.sent,t.abrupt("return",e);case 6:case"end":return t.stop()}}),t,this)}))),Pt.prototype.getUserInfo=i()(s.a.mark((function t(){var e;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.loginStatus.catch((function(){return null}));case 2:return t.next=4,this.OidcAuth.getUserProfile().catch((function(){return null}));case 4:return e=t.sent,t.abrupt("return",{accountId:"x-accountId"in e?e["x-accountId"]:"",name:"x-name"in e?e["x-name"]:""});case 6:case"end":return t.stop()}}),t,this)}))),Pt.prototype.siginOut=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:top.location.href;return this.OidcAuth.userSignout(t)},Pt.prototype.gotoLogin=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:top.location.href;return this.OidcAuth.userSignin({signinRedirectUrl:t})}}]);