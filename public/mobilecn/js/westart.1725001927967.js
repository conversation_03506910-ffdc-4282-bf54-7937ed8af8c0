"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[217],{2923:function(t,e,s){s.r(e),s.d(e,{default:function(){return l}});var n=function(){var t=this;t._self._c;return t._m(0)},r=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("img",{staticClass:"bg",attrs:{src:s(6650)}})])}],a={name:"WeStart",data(){return{}},created(){},methods:{}},c=a,i=s(1656),u=(0,i.A)(c,n,r,!1,null,"7c9d2c6c",null),l=u.exports},6650:function(t,e,s){t.exports=s.p+"assets/img/westart.a3cad85e.jpg"}}]);