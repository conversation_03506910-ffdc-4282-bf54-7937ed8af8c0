"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[589],{3255:function(s,t,e){e.r(t),e.d(t,{default:function(){return u}});var a=function(){var s=this,t=s._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article-tit"},[s._v(s._s(s.news.title))]),t("div",{staticClass:"pd30"},[t("div",{staticClass:"article-info"},[t("span",{staticClass:"li"},[s._v("来源："+s._s(s.news.source))]),t("span",{staticClass:"li"},[s._v("作者："+s._s(s.news.author))]),t("span",{staticClass:"li"},[s._v("发布时间："+s._s(s.news.release_time))]),t("span",{staticClass:"li"},[s._v("访问量："+s._s(s.news.visit_num))])])]),t("div",{staticClass:"article",domProps:{innerHTML:s._s(s.news.detail)}})])])},i=[],n=e(3397),l={name:"newsDetail",data(){return{news:{}}},created(){let s=this.$route.query.id;s?n.PI({id:s}).then((s=>{200==s.data.code&&(this.news=s.data.data,this.news.release_time=s.data.data.release_time.substring(0,10))})):this.$router.replace({path:"/News"})},methods:{}},r=l,c=e(1656),d=(0,c.A)(r,a,i,!1,null,"fd9232b4",null),u=d.exports}}]);