"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[461],{8075:function(e,t,a){a.r(t),a.d(t,{default:function(){return u}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},n=[],l=a(3397),c={name:"meetingTraffic",data(){return{detail:""}},created(){l.cv().then((e=>{200==e.data.code&&(this.detail=e.data.data.detail)}))},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let i=a.fontSize.replace("px","");const n=308/75*i;document.querySelector(".article-detail").style.height=e-n+"px"},methods:{}},r=c,d=a(1656),s=(0,d.A)(r,i,n,!1,null,"6cca55fe",null),u=s.exports}}]);