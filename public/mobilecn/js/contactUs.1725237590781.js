"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[425],{4038:function(e,t,a){a.r(t),a.d(t,{default:function(){return o}});var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},i=[],l=a(3397),c={name:"contactUs",data(){return{detail:""}},created(){l.GO().then((e=>{200==e.data.code&&(this.detail=e.data.data.detail)}))},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let n=a.fontSize.replace("px","");const i=308/75*n;document.querySelector(".article-detail").style.height=e-i+"px"},methods:{}},d=c,r=a(1656),s=(0,r.A)(d,n,i,!1,null,"a0077fee",null),o=s.exports}}]);