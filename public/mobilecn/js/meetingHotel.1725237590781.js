"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[324],{8047:function(e,t,a){a.r(t),a.d(t,{default:function(){return o}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[t("div",{staticClass:"article-detail"},[t("div",{staticClass:"article",domProps:{innerHTML:e._s(e.detail)}})])])},l=[],n=a(3397),d={name:"meetingHotel",data(){return{detail:""}},created(){n.JX().then((e=>{200==e.data.code&&(this.detail=e.data.data.detail)}))},mounted(){const e=window.innerHeight;let t=document.querySelector("html");var a=document.defaultView.getComputedStyle(t,null);let i=a.fontSize.replace("px","");const l=308/75*i;document.querySelector(".article-detail").style.height=e-l+"px"},methods:{}},r=d,c=a(1656),s=(0,c.A)(r,i,l,!1,null,"514905d0",null),o=s.exports}}]);