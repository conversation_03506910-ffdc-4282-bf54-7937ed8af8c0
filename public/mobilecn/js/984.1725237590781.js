(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[984],{49:function(e,t,a){var i;a(4114),function(t,a){e.exports=a()}(0,(function(){function e(e){this.mode=a.MODE_8BIT_BYTE,this.data=e,this.parsedData=[];for(var t=0,i=this.data.length;t<i;t++){var r=[],s=this.data.charCodeAt(t);s>65536?(r[0]=240|(1835008&s)>>>18,r[1]=128|(258048&s)>>>12,r[2]=128|(4032&s)>>>6,r[3]=128|63&s):s>2048?(r[0]=224|(61440&s)>>>12,r[1]=128|(4032&s)>>>6,r[2]=128|63&s):s>128?(r[0]=192|(1984&s)>>>6,r[1]=128|63&s):r[0]=s,this.parsedData.push(r)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function t(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}e.prototype={getLength:function(e){return this.parsedData.length},write:function(e){for(var t=0,a=this.parsedData.length;t<a;t++)e.put(this.parsedData[t],8)}},t.prototype={addData:function(t){var a=new e(t);this.dataList.push(a),this.dataCache=null},isDark:function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(e,a){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[i][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,a),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=t.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,a)},setupPositionProbePattern:function(e,t){for(var a=-1;a<=7;a++)if(!(e+a<=-1||this.moduleCount<=e+a))for(var i=-1;i<=7;i++)t+i<=-1||this.moduleCount<=t+i||(this.modules[e+a][t+i]=0<=a&&a<=6&&(0==i||6==i)||0<=i&&i<=6&&(0==a||6==a)||2<=a&&a<=4&&2<=i&&i<=4)},getBestMaskPattern:function(){for(var e=0,t=0,a=0;a<8;a++){this.makeImpl(!0,a);var i=n.getLostPoint(this);(0==a||e>i)&&(e=i,t=a)}return t},createMovieClip:function(e,t,a){var i=e.createEmptyMovieClip(t,a),r=1;this.make();for(var s=0;s<this.modules.length;s++)for(var n=s*r,o=0;o<this.modules[s].length;o++){var l=o*r,d=this.modules[s][o];d&&(i.beginFill(0,100),i.moveTo(l,n),i.lineTo(l+r,n),i.lineTo(l+r,n+r),i.lineTo(l,n+r),i.endFill())}return i},setupTimingPattern:function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},setupPositionAdjustPattern:function(){for(var e=n.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var a=0;a<e.length;a++){var i=e[t],r=e[a];if(null==this.modules[i][r])for(var s=-2;s<=2;s++)for(var o=-2;o<=2;o++)this.modules[i+s][r+o]=-2==s||2==s||-2==o||2==o||0==s&&0==o}},setupTypeNumber:function(e){for(var t=n.getBCHTypeNumber(this.typeNumber),a=0;a<18;a++){var i=!e&&1==(t>>a&1);this.modules[Math.floor(a/3)][a%3+this.moduleCount-8-3]=i}for(a=0;a<18;a++){i=!e&&1==(t>>a&1);this.modules[a%3+this.moduleCount-8-3][Math.floor(a/3)]=i}},setupTypeInfo:function(e,t){for(var a=this.errorCorrectLevel<<3|t,i=n.getBCHTypeInfo(a),r=0;r<15;r++){var s=!e&&1==(i>>r&1);r<6?this.modules[r][8]=s:r<8?this.modules[r+1][8]=s:this.modules[this.moduleCount-15+r][8]=s}for(r=0;r<15;r++){s=!e&&1==(i>>r&1);r<8?this.modules[8][this.moduleCount-r-1]=s:r<9?this.modules[8][15-r-1+1]=s:this.modules[8][15-r-1]=s}this.modules[this.moduleCount-8][8]=!e},mapData:function(e,t){for(var a=-1,i=this.moduleCount-1,r=7,s=0,o=this.moduleCount-1;o>0;o-=2){6==o&&o--;while(1){for(var l=0;l<2;l++)if(null==this.modules[i][o-l]){var d=!1;s<e.length&&(d=1==(e[s]>>>r&1));var p=n.getMask(t,i,o-l);p&&(d=!d),this.modules[i][o-l]=d,r--,-1==r&&(s++,r=7)}if(i+=a,i<0||this.moduleCount<=i){i-=a,a=-a;break}}}}},t.PAD0=236,t.PAD1=17,t.createData=function(e,a,i){for(var r=p.getRSBlocks(e,a),s=new u,o=0;o<i.length;o++){var l=i[o];s.put(l.mode,4),s.put(l.getLength(),n.getLengthInBits(l.mode,e)),l.write(s)}var d=0;for(o=0;o<r.length;o++)d+=r[o].dataCount;if(s.getLengthInBits()>8*d)throw new Error("code length overflow. ("+s.getLengthInBits()+">"+8*d+")");s.getLengthInBits()+4<=8*d&&s.put(0,4);while(s.getLengthInBits()%8!=0)s.putBit(!1);while(1){if(s.getLengthInBits()>=8*d)break;if(s.put(t.PAD0,8),s.getLengthInBits()>=8*d)break;s.put(t.PAD1,8)}return t.createBytes(s,r)},t.createBytes=function(e,t){for(var a=0,i=0,r=0,s=new Array(t.length),o=new Array(t.length),l=0;l<t.length;l++){var p=t[l].dataCount,u=t[l].totalCount-p;i=Math.max(i,p),r=Math.max(r,u),s[l]=new Array(p);for(var c=0;c<s[l].length;c++)s[l][c]=255&e.buffer[c+a];a+=p;var h=n.getErrorCorrectPolynomial(u),f=new d(s[l],h.getLength()-1),v=f.mod(h);o[l]=new Array(h.getLength()-1);for(c=0;c<o[l].length;c++){var m=c+v.getLength()-o[l].length;o[l][c]=m>=0?v.get(m):0}}var g=0;for(c=0;c<t.length;c++)g+=t[c].totalCount;var w=new Array(g),b=0;for(c=0;c<i;c++)for(l=0;l<t.length;l++)c<s[l].length&&(w[b++]=s[l][c]);for(c=0;c<r;c++)for(l=0;l<t.length;l++)c<o[l].length&&(w[b++]=o[l][c]);return w};for(var a={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},r={L:1,M:0,Q:3,H:2},s={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},n={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){var t=e<<10;while(n.getBCHDigit(t)-n.getBCHDigit(n.G15)>=0)t^=n.G15<<n.getBCHDigit(t)-n.getBCHDigit(n.G15);return(e<<10|t)^n.G15_MASK},getBCHTypeNumber:function(e){var t=e<<12;while(n.getBCHDigit(t)-n.getBCHDigit(n.G18)>=0)t^=n.G18<<n.getBCHDigit(t)-n.getBCHDigit(n.G18);return e<<12|t},getBCHDigit:function(e){var t=0;while(0!=e)t++,e>>>=1;return t},getPatternPosition:function(e){return n.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,a){switch(e){case s.PATTERN000:return(t+a)%2==0;case s.PATTERN001:return t%2==0;case s.PATTERN010:return a%3==0;case s.PATTERN011:return(t+a)%3==0;case s.PATTERN100:return(Math.floor(t/2)+Math.floor(a/3))%2==0;case s.PATTERN101:return t*a%2+t*a%3==0;case s.PATTERN110:return(t*a%2+t*a%3)%2==0;case s.PATTERN111:return(t*a%3+(t+a)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new d([1],0),a=0;a<e;a++)t=t.multiply(new d([1,o.gexp(a)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case a.MODE_NUMBER:return 10;case a.MODE_ALPHA_NUM:return 9;case a.MODE_8BIT_BYTE:return 8;case a.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case a.MODE_NUMBER:return 12;case a.MODE_ALPHA_NUM:return 11;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case a.MODE_NUMBER:return 14;case a.MODE_ALPHA_NUM:return 13;case a.MODE_8BIT_BYTE:return 16;case a.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),a=0,i=0;i<t;i++)for(var r=0;r<t;r++){for(var s=0,n=e.isDark(i,r),o=-1;o<=1;o++)if(!(i+o<0||t<=i+o))for(var l=-1;l<=1;l++)r+l<0||t<=r+l||0==o&&0==l||n==e.isDark(i+o,r+l)&&s++;s>5&&(a+=3+s-5)}for(i=0;i<t-1;i++)for(r=0;r<t-1;r++){var d=0;e.isDark(i,r)&&d++,e.isDark(i+1,r)&&d++,e.isDark(i,r+1)&&d++,e.isDark(i+1,r+1)&&d++,0!=d&&4!=d||(a+=3)}for(i=0;i<t;i++)for(r=0;r<t-6;r++)e.isDark(i,r)&&!e.isDark(i,r+1)&&e.isDark(i,r+2)&&e.isDark(i,r+3)&&e.isDark(i,r+4)&&!e.isDark(i,r+5)&&e.isDark(i,r+6)&&(a+=40);for(r=0;r<t;r++)for(i=0;i<t-6;i++)e.isDark(i,r)&&!e.isDark(i+1,r)&&e.isDark(i+2,r)&&e.isDark(i+3,r)&&e.isDark(i+4,r)&&!e.isDark(i+5,r)&&e.isDark(i+6,r)&&(a+=40);var p=0;for(r=0;r<t;r++)for(i=0;i<t;i++)e.isDark(i,r)&&p++;var u=Math.abs(100*p/t/t-50)/5;return a+=10*u,a}},o={glog:function(e){if(e<1)throw new Error("glog("+e+")");return o.LOG_TABLE[e]},gexp:function(e){while(e<0)e+=255;while(e>=256)e-=255;return o.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},l=0;l<8;l++)o.EXP_TABLE[l]=1<<l;for(l=8;l<256;l++)o.EXP_TABLE[l]=o.EXP_TABLE[l-4]^o.EXP_TABLE[l-5]^o.EXP_TABLE[l-6]^o.EXP_TABLE[l-8];for(l=0;l<255;l++)o.LOG_TABLE[o.EXP_TABLE[l]]=l;function d(e,t){if(void 0==e.length)throw new Error(e.length+"/"+t);var a=0;while(a<e.length&&0==e[a])a++;this.num=new Array(e.length-a+t);for(var i=0;i<e.length-a;i++)this.num[i]=e[i+a]}function p(e,t){this.totalCount=e,this.dataCount=t}function u(){this.buffer=[],this.length=0}d.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),a=0;a<this.getLength();a++)for(var i=0;i<e.getLength();i++)t[a+i]^=o.gexp(o.glog(this.get(a))+o.glog(e.get(i)));return new d(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=o.glog(this.get(0))-o.glog(e.get(0)),a=new Array(this.getLength()),i=0;i<this.getLength();i++)a[i]=this.get(i);for(i=0;i<e.getLength();i++)a[i]^=o.gexp(o.glog(e.get(i))+t);return new d(a,0).mod(e)}},p.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],p.getRSBlocks=function(e,t){var a=p.getRsBlockTable(e,t);if(void 0==a)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var i=a.length/3,r=[],s=0;s<i;s++)for(var n=a[3*s+0],o=a[3*s+1],l=a[3*s+2],d=0;d<n;d++)r.push(new p(o,l));return r},p.getRsBlockTable=function(e,t){switch(t){case r.L:return p.RS_BLOCK_TABLE[4*(e-1)+0];case r.M:return p.RS_BLOCK_TABLE[4*(e-1)+1];case r.Q:return p.RS_BLOCK_TABLE[4*(e-1)+2];case r.H:return p.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},u.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var a=0;a<t;a++)this.putBit(1==(e>>>t-a-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var c=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function h(){return"undefined"!=typeof CanvasRenderingContext2D}function f(){var e=!1,t=navigator.userAgent;if(/android/i.test(t)){e=!0;var a=t.toString().match(/android ([0-9]\.[0-9])/i);a&&a[1]&&(e=parseFloat(a[1]))}return e}var v=function(){var e=function(e,t){this._el=e,this._htOption=t};return e.prototype.draw=function(e){var t=this._htOption,a=this._el,i=e.getModuleCount();Math.floor(t.width/i),Math.floor(t.height/i);function r(e,t){var a=document.createElementNS("http://www.w3.org/2000/svg",e);for(var i in t)t.hasOwnProperty(i)&&a.setAttribute(i,t[i]);return a}this.clear();var s=r("svg",{viewBox:"0 0 "+String(i)+" "+String(i),width:"100%",height:"100%",fill:t.colorLight});s.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),a.appendChild(s),s.appendChild(r("rect",{fill:t.colorLight,width:"100%",height:"100%"})),s.appendChild(r("rect",{fill:t.colorDark,width:"1",height:"1",id:"template"}));for(var n=0;n<i;n++)for(var o=0;o<i;o++)if(e.isDark(n,o)){var l=r("use",{x:String(o),y:String(n)});l.setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),s.appendChild(l)}},e.prototype.clear=function(){while(this._el.hasChildNodes())this._el.removeChild(this._el.lastChild)},e}(),m="svg"===document.documentElement.tagName.toLowerCase(),g=m?v:h()?function(){function e(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}if(this._android&&this._android<=2.1){var t=1/window.devicePixelRatio,a=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(e,i,r,s,n,o,l,d,p){if("nodeName"in e&&/img/i.test(e.nodeName))for(var u=arguments.length-1;u>=1;u--)arguments[u]=arguments[u]*t;else"undefined"==typeof d&&(arguments[1]*=t,arguments[2]*=t,arguments[3]*=t,arguments[4]*=t);a.apply(this,arguments)}}function i(e,t){var a=this;if(a._fFail=t,a._fSuccess=e,null===a._bSupportDataURI){var i=document.createElement("img"),r=function(){a._bSupportDataURI=!1,a._fFail&&a._fFail.call(a)},s=function(){a._bSupportDataURI=!0,a._fSuccess&&a._fSuccess.call(a)};return i.onabort=r,i.onerror=r,i.onload=s,void(i.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==")}!0===a._bSupportDataURI&&a._fSuccess?a._fSuccess.call(a):!1===a._bSupportDataURI&&a._fFail&&a._fFail.call(a)}var r=function(e,t){this._bIsPainted=!1,this._android=f(),this._htOption=t,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=t.width,this._elCanvas.height=t.height,e.appendChild(this._elCanvas),this._el=e,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.alt="Scan me!",this._elImage.style.display="none",this._el.appendChild(this._elImage),this._bSupportDataURI=null};return r.prototype.draw=function(e){var t=this._elImage,a=this._oContext,i=this._htOption,r=e.getModuleCount(),s=i.width/r,n=i.height/r,o=Math.round(s),l=Math.round(n);t.style.display="none",this.clear();for(var d=0;d<r;d++)for(var p=0;p<r;p++){var u=e.isDark(d,p),c=p*s,h=d*n;a.strokeStyle=u?i.colorDark:i.colorLight,a.lineWidth=1,a.fillStyle=u?i.colorDark:i.colorLight,a.fillRect(c,h,s,n),a.strokeRect(Math.floor(c)+.5,Math.floor(h)+.5,o,l),a.strokeRect(Math.ceil(c)-.5,Math.ceil(h)-.5,o,l)}this._bIsPainted=!0},r.prototype.makeImage=function(){this._bIsPainted&&i.call(this,e)},r.prototype.isPainted=function(){return this._bIsPainted},r.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},r.prototype.round=function(e){return e?Math.floor(1e3*e)/1e3:e},r}():function(){var e=function(e,t){this._el=e,this._htOption=t};return e.prototype.draw=function(e){for(var t=this._htOption,a=this._el,i=e.getModuleCount(),r=Math.floor(t.width/i),s=Math.floor(t.height/i),n=['<table style="border:0;border-collapse:collapse;">'],o=0;o<i;o++){n.push("<tr>");for(var l=0;l<i;l++)n.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+r+"px;height:"+s+"px;background-color:"+(e.isDark(o,l)?t.colorDark:t.colorLight)+';"></td>');n.push("</tr>")}n.push("</table>"),a.innerHTML=n.join("");var d=a.childNodes[0],p=(t.width-d.offsetWidth)/2,u=(t.height-d.offsetHeight)/2;p>0&&u>0&&(d.style.margin=u+"px "+p+"px")},e.prototype.clear=function(){this._el.innerHTML=""},e}();function w(e,t){for(var a=1,i=b(e),s=0,n=c.length;s<=n;s++){var o=0;switch(t){case r.L:o=c[s][0];break;case r.M:o=c[s][1];break;case r.Q:o=c[s][2];break;case r.H:o=c[s][3];break}if(i<=o)break;a++}if(a>c.length)throw new Error("Too long data");return a}function b(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=e?3:0)}return i=function(e,t){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:r.H},"string"===typeof t&&(t={text:t}),t)for(var a in t)this._htOption[a]=t[a];"string"==typeof e&&(e=document.getElementById(e)),this._htOption.useSVG&&(g=v),this._android=f(),this._el=e,this._oQRCode=null,this._oDrawing=new g(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)},i.prototype.makeCode=function(e){this._oQRCode=new t(w(e,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(e),this._oQRCode.make(),this._el.title=e,this._oDrawing.draw(this._oQRCode),this.makeImage()},i.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||this._android>=3)&&this._oDrawing.makeImage()},i.prototype.clear=function(){this._oDrawing.clear()},i.CorrectLevel=r,i}))},2791:function(e,t,a){a(4114),function(t,a){e.exports=a()}(0,(function(){"use strict";var e="undefined"===typeof document?{body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},location:{hash:""}}:document,t="undefined"===typeof window?{document:e,navigator:{userAgent:""},location:{},history:{},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){}}:window,a=function(e){for(var t=this,a=0;a<e.length;a+=1)t[a]=e[a];return t.length=e.length,this};function i(i,r){var s=[],n=0;if(i&&!r&&i instanceof a)return i;if(i)if("string"===typeof i){var o,l,d=i.trim();if(d.indexOf("<")>=0&&d.indexOf(">")>=0){var p="div";for(0===d.indexOf("<li")&&(p="ul"),0===d.indexOf("<tr")&&(p="tbody"),0!==d.indexOf("<td")&&0!==d.indexOf("<th")||(p="tr"),0===d.indexOf("<tbody")&&(p="table"),0===d.indexOf("<option")&&(p="select"),l=e.createElement(p),l.innerHTML=d,n=0;n<l.childNodes.length;n+=1)s.push(l.childNodes[n])}else for(o=r||"#"!==i[0]||i.match(/[ .<>:~]/)?(r||e).querySelectorAll(i.trim()):[e.getElementById(i.trim().split("#")[1])],n=0;n<o.length;n+=1)o[n]&&s.push(o[n])}else if(i.nodeType||i===t||i===e)s.push(i);else if(i.length>0&&i[0].nodeType)for(n=0;n<i.length;n+=1)s.push(i[n]);return new a(s)}function r(e){for(var t=[],a=0;a<e.length;a+=1)-1===t.indexOf(e[a])&&t.push(e[a]);return t}function s(e){if("undefined"===typeof e)return this;for(var t=e.split(" "),a=0;a<t.length;a+=1)for(var i=0;i<this.length;i+=1)"undefined"!==typeof this[i]&&"undefined"!==typeof this[i].classList&&this[i].classList.add(t[a]);return this}function n(e){for(var t=e.split(" "),a=0;a<t.length;a+=1)for(var i=0;i<this.length;i+=1)"undefined"!==typeof this[i]&&"undefined"!==typeof this[i].classList&&this[i].classList.remove(t[a]);return this}function o(e){return!!this[0]&&this[0].classList.contains(e)}function l(e){for(var t=e.split(" "),a=0;a<t.length;a+=1)for(var i=0;i<this.length;i+=1)"undefined"!==typeof this[i]&&"undefined"!==typeof this[i].classList&&this[i].classList.toggle(t[a]);return this}function d(e,t){var a=arguments;if(1===arguments.length&&"string"===typeof e)return this[0]?this[0].getAttribute(e):void 0;for(var i=0;i<this.length;i+=1)if(2===a.length)this[i].setAttribute(e,t);else for(var r in e)this[i][r]=e[r],this[i].setAttribute(r,e[r]);return this}function p(e){for(var t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this}function u(e,t){var a;if("undefined"!==typeof t){for(var i=0;i<this.length;i+=1)a=this[i],a.dom7ElementDataStorage||(a.dom7ElementDataStorage={}),a.dom7ElementDataStorage[e]=t;return this}if(a=this[0],a){if(a.dom7ElementDataStorage&&e in a.dom7ElementDataStorage)return a.dom7ElementDataStorage[e];var r=a.getAttribute("data-"+e);return r||void 0}}function c(e){for(var t=0;t<this.length;t+=1){var a=this[t].style;a.webkitTransform=e,a.transform=e}return this}function h(e){"string"!==typeof e&&(e+="ms");for(var t=0;t<this.length;t+=1){var a=this[t].style;a.webkitTransitionDuration=e,a.transitionDuration=e}return this}function f(){var e,t=[],a=arguments.length;while(a--)t[a]=arguments[a];var r=t[0],s=t[1],n=t[2],o=t[3];function l(e){var t=e.target;if(t){var a=e.target.dom7EventData||[];if(a.indexOf(e)<0&&a.unshift(e),i(t).is(s))n.apply(t,a);else for(var r=i(t).parents(),o=0;o<r.length;o+=1)i(r[o]).is(s)&&n.apply(r[o],a)}}function d(e){var t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),n.apply(this,t)}"function"===typeof t[1]&&(e=t,r=e[0],n=e[1],o=e[2],s=void 0),o||(o=!1);for(var p,u=r.split(" "),c=0;c<this.length;c+=1){var h=this[c];if(s)for(p=0;p<u.length;p+=1){var f=u[p];h.dom7LiveListeners||(h.dom7LiveListeners={}),h.dom7LiveListeners[f]||(h.dom7LiveListeners[f]=[]),h.dom7LiveListeners[f].push({listener:n,proxyListener:l}),h.addEventListener(f,l,o)}else for(p=0;p<u.length;p+=1){var v=u[p];h.dom7Listeners||(h.dom7Listeners={}),h.dom7Listeners[v]||(h.dom7Listeners[v]=[]),h.dom7Listeners[v].push({listener:n,proxyListener:d}),h.addEventListener(v,d,o)}}return this}function v(){var e,t=[],a=arguments.length;while(a--)t[a]=arguments[a];var i=t[0],r=t[1],s=t[2],n=t[3];"function"===typeof t[1]&&(e=t,i=e[0],s=e[1],n=e[2],r=void 0),n||(n=!1);for(var o=i.split(" "),l=0;l<o.length;l+=1)for(var d=o[l],p=0;p<this.length;p+=1){var u=this[p],c=void 0;if(!r&&u.dom7Listeners?c=u.dom7Listeners[d]:r&&u.dom7LiveListeners&&(c=u.dom7LiveListeners[d]),c&&c.length)for(var h=c.length-1;h>=0;h-=1){var f=c[h];s&&f.listener===s||s&&f.listener&&f.listener.dom7proxy&&f.listener.dom7proxy===s?(u.removeEventListener(d,f.proxyListener,n),c.splice(h,1)):s||(u.removeEventListener(d,f.proxyListener,n),c.splice(h,1))}}return this}function m(){var a=[],i=arguments.length;while(i--)a[i]=arguments[i];for(var r=a[0].split(" "),s=a[1],n=0;n<r.length;n+=1)for(var o=r[n],l=0;l<this.length;l+=1){var d=this[l],p=void 0;try{p=new t.CustomEvent(o,{detail:s,bubbles:!0,cancelable:!0})}catch(u){p=e.createEvent("Event"),p.initEvent(o,!0,!0),p.detail=s}d.dom7EventData=a.filter((function(e,t){return t>0})),d.dispatchEvent(p),d.dom7EventData=[],delete d.dom7EventData}return this}function g(e){var t,a=["webkitTransitionEnd","transitionend"],i=this;function r(s){if(s.target===this)for(e.call(this,s),t=0;t<a.length;t+=1)i.off(a[t],r)}if(e)for(t=0;t<a.length;t+=1)i.on(a[t],r);return this}function w(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null}function b(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null}function y(){if(this.length>0){var a=this[0],i=a.getBoundingClientRect(),r=e.body,s=a.clientTop||r.clientTop||0,n=a.clientLeft||r.clientLeft||0,o=a===t?t.scrollY:a.scrollTop,l=a===t?t.scrollX:a.scrollLeft;return{top:i.top+o-s,left:i.left+l-n}}return null}function x(){return this[0]?t.getComputedStyle(this[0],null):{}}function E(e,a){var i;if(1===arguments.length){if("string"!==typeof e){for(i=0;i<this.length;i+=1)for(var r in e)this[i].style[r]=e[r];return this}if(this[0])return t.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"===typeof e){for(i=0;i<this.length;i+=1)this[i].style[e]=a;return this}return this}function T(e){if(!e)return this;for(var t=0;t<this.length;t+=1)if(!1===e.call(this[t],t,this[t]))return this;return this}function C(e){if("undefined"===typeof e)return this[0]?this[0].innerHTML:void 0;for(var t=0;t<this.length;t+=1)this[t].innerHTML=e;return this}function S(e){if("undefined"===typeof e)return this[0]?this[0].textContent.trim():null;for(var t=0;t<this.length;t+=1)this[t].textContent=e;return this}function M(r){var s,n,o=this[0];if(!o||"undefined"===typeof r)return!1;if("string"===typeof r){if(o.matches)return o.matches(r);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(r);if(o.msMatchesSelector)return o.msMatchesSelector(r);for(s=i(r),n=0;n<s.length;n+=1)if(s[n]===o)return!0;return!1}if(r===e)return o===e;if(r===t)return o===t;if(r.nodeType||r instanceof a){for(s=r.nodeType?[r]:r,n=0;n<s.length;n+=1)if(s[n]===o)return!0;return!1}return!1}function P(){var e,t=this[0];if(t){e=0;while(null!==(t=t.previousSibling))1===t.nodeType&&(e+=1);return e}}function k(e){if("undefined"===typeof e)return this;var t,i=this.length;return e>i-1?new a([]):e<0?(t=i+e,new a(t<0?[]:[this[t]])):new a([this[e]])}function L(){var t,i=[],r=arguments.length;while(r--)i[r]=arguments[r];for(var s=0;s<i.length;s+=1){t=i[s];for(var n=0;n<this.length;n+=1)if("string"===typeof t){var o=e.createElement("div");o.innerHTML=t;while(o.firstChild)this[n].appendChild(o.firstChild)}else if(t instanceof a)for(var l=0;l<t.length;l+=1)this[n].appendChild(t[l]);else this[n].appendChild(t)}return this}function z(t){var i,r;for(i=0;i<this.length;i+=1)if("string"===typeof t){var s=e.createElement("div");for(s.innerHTML=t,r=s.childNodes.length-1;r>=0;r-=1)this[i].insertBefore(s.childNodes[r],this[i].childNodes[0])}else if(t instanceof a)for(r=0;r<t.length;r+=1)this[i].insertBefore(t[r],this[i].childNodes[0]);else this[i].insertBefore(t,this[i].childNodes[0]);return this}function I(e){return this.length>0?e?this[0].nextElementSibling&&i(this[0].nextElementSibling).is(e)?new a([this[0].nextElementSibling]):new a([]):this[0].nextElementSibling?new a([this[0].nextElementSibling]):new a([]):new a([])}function D(e){var t=[],r=this[0];if(!r)return new a([]);while(r.nextElementSibling){var s=r.nextElementSibling;e?i(s).is(e)&&t.push(s):t.push(s),r=s}return new a(t)}function $(e){if(this.length>0){var t=this[0];return e?t.previousElementSibling&&i(t.previousElementSibling).is(e)?new a([t.previousElementSibling]):new a([]):t.previousElementSibling?new a([t.previousElementSibling]):new a([])}return new a([])}function A(e){var t=[],r=this[0];if(!r)return new a([]);while(r.previousElementSibling){var s=r.previousElementSibling;e?i(s).is(e)&&t.push(s):t.push(s),r=s}return new a(t)}function O(e){for(var t=[],a=0;a<this.length;a+=1)null!==this[a].parentNode&&(e?i(this[a].parentNode).is(e)&&t.push(this[a].parentNode):t.push(this[a].parentNode));return i(r(t))}function _(e){for(var t=[],a=0;a<this.length;a+=1){var s=this[a].parentNode;while(s)e?i(s).is(e)&&t.push(s):t.push(s),s=s.parentNode}return i(r(t))}function B(e){var t=this;return"undefined"===typeof e?new a([]):(t.is(e)||(t=t.parents(e).eq(0)),t)}function N(e){for(var t=[],i=0;i<this.length;i+=1)for(var r=this[i].querySelectorAll(e),s=0;s<r.length;s+=1)t.push(r[s]);return new a(t)}function H(e){for(var t=[],s=0;s<this.length;s+=1)for(var n=this[s].childNodes,o=0;o<n.length;o+=1)e?1===n[o].nodeType&&i(n[o]).is(e)&&t.push(n[o]):1===n[o].nodeType&&t.push(n[o]);return new a(r(t))}function G(){for(var e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}function R(){var e=[],t=arguments.length;while(t--)e[t]=arguments[t];var a,r,s=this;for(a=0;a<e.length;a+=1){var n=i(e[a]);for(r=0;r<n.length;r+=1)s[s.length]=n[r],s.length+=1}return s}i.fn=a.prototype,i.Class=a,i.Dom7=a;var X={addClass:s,removeClass:n,hasClass:o,toggleClass:l,attr:d,removeAttr:p,data:u,transform:c,transition:h,on:f,off:v,trigger:m,transitionEnd:g,outerWidth:w,outerHeight:b,offset:y,css:E,each:T,html:C,text:S,is:M,index:P,eq:k,append:L,prepend:z,next:I,nextAll:D,prev:$,prevAll:A,parent:O,parents:_,closest:B,find:N,children:H,remove:G,add:R,styles:x};Object.keys(X).forEach((function(e){i.fn[e]=i.fn[e]||X[e]}));var V={deleteProps:function(e){var t=e;Object.keys(t).forEach((function(e){try{t[e]=null}catch(a){}try{delete t[e]}catch(a){}}))},nextTick:function(e,t){return void 0===t&&(t=0),setTimeout(e,t)},now:function(){return Date.now()},getTranslate:function(e,a){var i,r,s;void 0===a&&(a="x");var n=t.getComputedStyle(e,null);return t.WebKitCSSMatrix?(r=n.transform||n.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map((function(e){return e.replace(",",".")})).join(", ")),s=new t.WebKitCSSMatrix("none"===r?"":r)):(s=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=s.toString().split(",")),"x"===a&&(r=t.WebKitCSSMatrix?s.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===a&&(r=t.WebKitCSSMatrix?s.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0},parseUrlQuery:function(e){var a,i,r,s,n={},o=e||t.location.href;if("string"===typeof o&&o.length)for(o=o.indexOf("?")>-1?o.replace(/\S*\?/,""):"",i=o.split("&").filter((function(e){return""!==e})),s=i.length,a=0;a<s;a+=1)r=i[a].replace(/#\S+/g,"").split("="),n[decodeURIComponent(r[0])]="undefined"===typeof r[1]?void 0:decodeURIComponent(r[1])||"";return n},isObject:function(e){return"object"===typeof e&&null!==e&&e.constructor&&e.constructor===Object},extend:function(){var e=[],t=arguments.length;while(t--)e[t]=arguments[t];for(var a=Object(e[0]),i=1;i<e.length;i+=1){var r=e[i];if(void 0!==r&&null!==r)for(var s=Object.keys(Object(r)),n=0,o=s.length;n<o;n+=1){var l=s[n],d=Object.getOwnPropertyDescriptor(r,l);void 0!==d&&d.enumerable&&(V.isObject(a[l])&&V.isObject(r[l])?V.extend(a[l],r[l]):!V.isObject(a[l])&&V.isObject(r[l])?(a[l]={},V.extend(a[l],r[l])):a[l]=r[l])}}return a}},Y=function(){var a=e.createElement("div");return{touch:t.Modernizr&&!0===t.Modernizr.touch||function(){return!!(t.navigator.maxTouchPoints>0||"ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}(),pointerEvents:!!(t.navigator.pointerEnabled||t.PointerEvent||"maxTouchPoints"in t.navigator&&t.navigator.maxTouchPoints>0),prefixedPointerEvents:!!t.navigator.msPointerEnabled,transition:function(){var e=a.style;return"transition"in e||"webkitTransition"in e||"MozTransition"in e}(),transforms3d:t.Modernizr&&!0===t.Modernizr.csstransforms3d||function(){var e=a.style;return"webkitPerspective"in e||"MozPerspective"in e||"OPerspective"in e||"MsPerspective"in e||"perspective"in e}(),flexbox:function(){for(var e=a.style,t="alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient".split(" "),i=0;i<t.length;i+=1)if(t[i]in e)return!0;return!1}(),observer:function(){return"MutationObserver"in t||"WebkitMutationObserver"in t}(),passiveListener:function(){var e=!1;try{var a=Object.defineProperty({},"passive",{get:function(){e=!0}});t.addEventListener("testPassiveListener",null,a)}catch(i){}return e}(),gestures:function(){return"ongesturestart"in t}()}}(),F=function(){function e(){var e=t.navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0}return{isIE:!!t.navigator.userAgent.match(/Trident/g)||!!t.navigator.userAgent.match(/MSIE/g),isEdge:!!t.navigator.userAgent.match(/Edge/g),isSafari:e(),isUiWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}(),j=function(e){void 0===e&&(e={});var t=this;t.params=e,t.eventsListeners={},t.params&&t.params.on&&Object.keys(t.params.on).forEach((function(e){t.on(e,t.params.on[e])}))},q={components:{configurable:!0}};function W(){var e,t,a=this,i=a.$el;e="undefined"!==typeof a.params.width?a.params.width:i[0].clientWidth,t="undefined"!==typeof a.params.height?a.params.height:i[0].clientHeight,0===e&&a.isHorizontal()||0===t&&a.isVertical()||(e=e-parseInt(i.css("padding-left"),10)-parseInt(i.css("padding-right"),10),t=t-parseInt(i.css("padding-top"),10)-parseInt(i.css("padding-bottom"),10),V.extend(a,{width:e,height:t,size:a.isHorizontal()?e:t}))}function U(){var e=this,a=e.params,i=e.$wrapperEl,r=e.size,s=e.rtlTranslate,n=e.wrongRTL,o=e.virtual&&a.virtual.enabled,l=o?e.virtual.slides.length:e.slides.length,d=i.children("."+e.params.slideClass),p=o?e.virtual.slides.length:d.length,u=[],c=[],h=[],f=a.slidesOffsetBefore;"function"===typeof f&&(f=a.slidesOffsetBefore.call(e));var v=a.slidesOffsetAfter;"function"===typeof v&&(v=a.slidesOffsetAfter.call(e));var m=e.snapGrid.length,g=e.snapGrid.length,w=a.spaceBetween,b=-f,y=0,x=0;if("undefined"!==typeof r){var E,T;"string"===typeof w&&w.indexOf("%")>=0&&(w=parseFloat(w.replace("%",""))/100*r),e.virtualSize=-w,s?d.css({marginLeft:"",marginTop:""}):d.css({marginRight:"",marginBottom:""}),a.slidesPerColumn>1&&(E=Math.floor(p/a.slidesPerColumn)===p/e.params.slidesPerColumn?p:Math.ceil(p/a.slidesPerColumn)*a.slidesPerColumn,"auto"!==a.slidesPerView&&"row"===a.slidesPerColumnFill&&(E=Math.max(E,a.slidesPerView*a.slidesPerColumn)));for(var C,S=a.slidesPerColumn,M=E/S,P=Math.floor(p/a.slidesPerColumn),k=0;k<p;k+=1){T=0;var L=d.eq(k);if(a.slidesPerColumn>1){var z=void 0,I=void 0,D=void 0;if("column"===a.slidesPerColumnFill||"row"===a.slidesPerColumnFill&&a.slidesPerGroup>1){if("column"===a.slidesPerColumnFill)I=Math.floor(k/S),D=k-I*S,(I>P||I===P&&D===S-1)&&(D+=1,D>=S&&(D=0,I+=1));else{var $=Math.floor(k/a.slidesPerGroup);D=Math.floor(k/a.slidesPerView)-$*a.slidesPerColumn,I=k-D*a.slidesPerView-$*a.slidesPerView}z=I+D*E/S,L.css({"-webkit-box-ordinal-group":z,"-moz-box-ordinal-group":z,"-ms-flex-order":z,"-webkit-order":z,order:z})}else D=Math.floor(k/M),I=k-D*M;L.css("margin-"+(e.isHorizontal()?"top":"left"),0!==D&&a.spaceBetween&&a.spaceBetween+"px").attr("data-swiper-column",I).attr("data-swiper-row",D)}if("none"!==L.css("display")){if("auto"===a.slidesPerView){var A=t.getComputedStyle(L[0],null),O=L[0].style.transform,_=L[0].style.webkitTransform;if(O&&(L[0].style.transform="none"),_&&(L[0].style.webkitTransform="none"),a.roundLengths)T=e.isHorizontal()?L.outerWidth(!0):L.outerHeight(!0);else if(e.isHorizontal()){var B=parseFloat(A.getPropertyValue("width")),N=parseFloat(A.getPropertyValue("padding-left")),H=parseFloat(A.getPropertyValue("padding-right")),G=parseFloat(A.getPropertyValue("margin-left")),R=parseFloat(A.getPropertyValue("margin-right")),X=A.getPropertyValue("box-sizing");T=X&&"border-box"===X&&!F.isIE?B+G+R:B+N+H+G+R}else{var j=parseFloat(A.getPropertyValue("height")),q=parseFloat(A.getPropertyValue("padding-top")),W=parseFloat(A.getPropertyValue("padding-bottom")),U=parseFloat(A.getPropertyValue("margin-top")),K=parseFloat(A.getPropertyValue("margin-bottom")),Q=A.getPropertyValue("box-sizing");T=Q&&"border-box"===Q&&!F.isIE?j+U+K:j+q+W+U+K}O&&(L[0].style.transform=O),_&&(L[0].style.webkitTransform=_),a.roundLengths&&(T=Math.floor(T))}else T=(r-(a.slidesPerView-1)*w)/a.slidesPerView,a.roundLengths&&(T=Math.floor(T)),d[k]&&(e.isHorizontal()?d[k].style.width=T+"px":d[k].style.height=T+"px");d[k]&&(d[k].swiperSlideSize=T),h.push(T),a.centeredSlides?(b=b+T/2+y/2+w,0===y&&0!==k&&(b=b-r/2-w),0===k&&(b=b-r/2-w),Math.abs(b)<.001&&(b=0),a.roundLengths&&(b=Math.floor(b)),x%a.slidesPerGroup===0&&u.push(b),c.push(b)):(a.roundLengths&&(b=Math.floor(b)),x%a.slidesPerGroup===0&&u.push(b),c.push(b),b=b+T+w),e.virtualSize+=T+w,y=T,x+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,s&&n&&("slide"===a.effect||"coverflow"===a.effect)&&i.css({width:e.virtualSize+a.spaceBetween+"px"}),Y.flexbox&&!a.setWrapperSize||(e.isHorizontal()?i.css({width:e.virtualSize+a.spaceBetween+"px"}):i.css({height:e.virtualSize+a.spaceBetween+"px"})),a.slidesPerColumn>1&&(e.virtualSize=(T+a.spaceBetween)*E,e.virtualSize=Math.ceil(e.virtualSize/a.slidesPerColumn)-a.spaceBetween,e.isHorizontal()?i.css({width:e.virtualSize+a.spaceBetween+"px"}):i.css({height:e.virtualSize+a.spaceBetween+"px"}),a.centeredSlides)){C=[];for(var J=0;J<u.length;J+=1){var Z=u[J];a.roundLengths&&(Z=Math.floor(Z)),u[J]<e.virtualSize+u[0]&&C.push(Z)}u=C}if(!a.centeredSlides){C=[];for(var ee=0;ee<u.length;ee+=1){var te=u[ee];a.roundLengths&&(te=Math.floor(te)),u[ee]<=e.virtualSize-r&&C.push(te)}u=C,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-r)}if(0===u.length&&(u=[0]),0!==a.spaceBetween&&(e.isHorizontal()?s?d.css({marginLeft:w+"px"}):d.css({marginRight:w+"px"}):d.css({marginBottom:w+"px"})),a.centerInsufficientSlides){var ae=0;if(h.forEach((function(e){ae+=e+(a.spaceBetween?a.spaceBetween:0)})),ae-=a.spaceBetween,ae<r){var ie=(r-ae)/2;u.forEach((function(e,t){u[t]=e-ie})),c.forEach((function(e,t){c[t]=e+ie}))}}V.extend(e,{slides:d,snapGrid:u,slidesGrid:c,slidesSizesGrid:h}),p!==l&&e.emit("slidesLengthChange"),u.length!==m&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),c.length!==g&&e.emit("slidesGridLengthChange"),(a.watchSlidesProgress||a.watchSlidesVisibility)&&e.updateSlidesOffset()}}function K(e){var t,a=this,i=[],r=0;if("number"===typeof e?a.setTransition(e):!0===e&&a.setTransition(a.params.speed),"auto"!==a.params.slidesPerView&&a.params.slidesPerView>1)for(t=0;t<Math.ceil(a.params.slidesPerView);t+=1){var s=a.activeIndex+t;if(s>a.slides.length)break;i.push(a.slides.eq(s)[0])}else i.push(a.slides.eq(a.activeIndex)[0]);for(t=0;t<i.length;t+=1)if("undefined"!==typeof i[t]){var n=i[t].offsetHeight;r=n>r?n:r}r&&a.$wrapperEl.css("height",r+"px")}function Q(){for(var e=this,t=e.slides,a=0;a<t.length;a+=1)t[a].swiperSlideOffset=e.isHorizontal()?t[a].offsetLeft:t[a].offsetTop}function J(e){void 0===e&&(e=this&&this.translate||0);var t=this,a=t.params,r=t.slides,s=t.rtlTranslate;if(0!==r.length){"undefined"===typeof r[0].swiperSlideOffset&&t.updateSlidesOffset();var n=-e;s&&(n=e),r.removeClass(a.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(var o=0;o<r.length;o+=1){var l=r[o],d=(n+(a.centeredSlides?t.minTranslate():0)-l.swiperSlideOffset)/(l.swiperSlideSize+a.spaceBetween);if(a.watchSlidesVisibility){var p=-(n-l.swiperSlideOffset),u=p+t.slidesSizesGrid[o],c=p>=0&&p<t.size-1||u>1&&u<=t.size||p<=0&&u>=t.size;c&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(o),r.eq(o).addClass(a.slideVisibleClass))}l.progress=s?-d:d}t.visibleSlides=i(t.visibleSlides)}}function Z(e){void 0===e&&(e=this&&this.translate||0);var t=this,a=t.params,i=t.maxTranslate()-t.minTranslate(),r=t.progress,s=t.isBeginning,n=t.isEnd,o=s,l=n;0===i?(r=0,s=!0,n=!0):(r=(e-t.minTranslate())/i,s=r<=0,n=r>=1),V.extend(t,{progress:r,isBeginning:s,isEnd:n}),(a.watchSlidesProgress||a.watchSlidesVisibility)&&t.updateSlidesProgress(e),s&&!o&&t.emit("reachBeginning toEdge"),n&&!l&&t.emit("reachEnd toEdge"),(o&&!s||l&&!n)&&t.emit("fromEdge"),t.emit("progress",r)}function ee(){var e,t=this,a=t.slides,i=t.params,r=t.$wrapperEl,s=t.activeIndex,n=t.realIndex,o=t.virtual&&i.virtual.enabled;a.removeClass(i.slideActiveClass+" "+i.slideNextClass+" "+i.slidePrevClass+" "+i.slideDuplicateActiveClass+" "+i.slideDuplicateNextClass+" "+i.slideDuplicatePrevClass),e=o?t.$wrapperEl.find("."+i.slideClass+'[data-swiper-slide-index="'+s+'"]'):a.eq(s),e.addClass(i.slideActiveClass),i.loop&&(e.hasClass(i.slideDuplicateClass)?r.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+n+'"]').addClass(i.slideDuplicateActiveClass):r.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+n+'"]').addClass(i.slideDuplicateActiveClass));var l=e.nextAll("."+i.slideClass).eq(0).addClass(i.slideNextClass);i.loop&&0===l.length&&(l=a.eq(0),l.addClass(i.slideNextClass));var d=e.prevAll("."+i.slideClass).eq(0).addClass(i.slidePrevClass);i.loop&&0===d.length&&(d=a.eq(-1),d.addClass(i.slidePrevClass)),i.loop&&(l.hasClass(i.slideDuplicateClass)?r.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass):r.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass),d.hasClass(i.slideDuplicateClass)?r.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass):r.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass))}function te(e){var t,a=this,i=a.rtlTranslate?a.translate:-a.translate,r=a.slidesGrid,s=a.snapGrid,n=a.params,o=a.activeIndex,l=a.realIndex,d=a.snapIndex,p=e;if("undefined"===typeof p){for(var u=0;u<r.length;u+=1)"undefined"!==typeof r[u+1]?i>=r[u]&&i<r[u+1]-(r[u+1]-r[u])/2?p=u:i>=r[u]&&i<r[u+1]&&(p=u+1):i>=r[u]&&(p=u);n.normalizeSlideIndex&&(p<0||"undefined"===typeof p)&&(p=0)}if(t=s.indexOf(i)>=0?s.indexOf(i):Math.floor(p/n.slidesPerGroup),t>=s.length&&(t=s.length-1),p!==o){var c=parseInt(a.slides.eq(p).attr("data-swiper-slide-index")||p,10);V.extend(a,{snapIndex:t,realIndex:c,previousIndex:o,activeIndex:p}),a.emit("activeIndexChange"),a.emit("snapIndexChange"),l!==c&&a.emit("realIndexChange"),(a.initialized||a.runCallbacksOnInit)&&a.emit("slideChange")}else t!==d&&(a.snapIndex=t,a.emit("snapIndexChange"))}function ae(e){var t=this,a=t.params,r=i(e.target).closest("."+a.slideClass)[0],s=!1;if(r)for(var n=0;n<t.slides.length;n+=1)t.slides[n]===r&&(s=!0);if(!r||!s)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(i(r).attr("data-swiper-slide-index"),10):t.clickedIndex=i(r).index(),a.slideToClickedSlide&&void 0!==t.clickedIndex&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}j.prototype.on=function(e,t,a){var i=this;if("function"!==typeof t)return i;var r=a?"unshift":"push";return e.split(" ").forEach((function(e){i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)})),i},j.prototype.once=function(e,t,a){var i=this;if("function"!==typeof t)return i;function r(){var a=[],s=arguments.length;while(s--)a[s]=arguments[s];t.apply(i,a),i.off(e,r),r.f7proxy&&delete r.f7proxy}return r.f7proxy=t,i.on(e,r,a)},j.prototype.off=function(e,t){var a=this;return a.eventsListeners?(e.split(" ").forEach((function(e){"undefined"===typeof t?a.eventsListeners[e]=[]:a.eventsListeners[e]&&a.eventsListeners[e].length&&a.eventsListeners[e].forEach((function(i,r){(i===t||i.f7proxy&&i.f7proxy===t)&&a.eventsListeners[e].splice(r,1)}))})),a):a},j.prototype.emit=function(){var e=[],t=arguments.length;while(t--)e[t]=arguments[t];var a,i,r,s=this;if(!s.eventsListeners)return s;"string"===typeof e[0]||Array.isArray(e[0])?(a=e[0],i=e.slice(1,e.length),r=s):(a=e[0].events,i=e[0].data,r=e[0].context||s);var n=Array.isArray(a)?a:a.split(" ");return n.forEach((function(e){if(s.eventsListeners&&s.eventsListeners[e]){var t=[];s.eventsListeners[e].forEach((function(e){t.push(e)})),t.forEach((function(e){e.apply(r,i)}))}})),s},j.prototype.useModulesParams=function(e){var t=this;t.modules&&Object.keys(t.modules).forEach((function(a){var i=t.modules[a];i.params&&V.extend(e,i.params)}))},j.prototype.useModules=function(e){void 0===e&&(e={});var t=this;t.modules&&Object.keys(t.modules).forEach((function(a){var i=t.modules[a],r=e[a]||{};i.instance&&Object.keys(i.instance).forEach((function(e){var a=i.instance[e];t[e]="function"===typeof a?a.bind(t):a})),i.on&&t.on&&Object.keys(i.on).forEach((function(e){t.on(e,i.on[e])})),i.create&&i.create.bind(t)(r)}))},q.components.set=function(e){var t=this;t.use&&t.use(e)},j.installModule=function(e){var t=[],a=arguments.length-1;while(a-- >0)t[a]=arguments[a+1];var i=this;i.prototype.modules||(i.prototype.modules={});var r=e.name||Object.keys(i.prototype.modules).length+"_"+V.now();return i.prototype.modules[r]=e,e.proto&&Object.keys(e.proto).forEach((function(t){i.prototype[t]=e.proto[t]})),e.static&&Object.keys(e.static).forEach((function(t){i[t]=e.static[t]})),e.install&&e.install.apply(i,t),i},j.use=function(e){var t=[],a=arguments.length-1;while(a-- >0)t[a]=arguments[a+1];var i=this;return Array.isArray(e)?(e.forEach((function(e){return i.installModule(e)})),i):i.installModule.apply(i,[e].concat(t))},Object.defineProperties(j,q);var ie={updateSize:W,updateSlides:U,updateAutoHeight:K,updateSlidesOffset:Q,updateSlidesProgress:J,updateProgress:Z,updateSlidesClasses:ee,updateActiveIndex:te,updateClickedSlide:ae};function re(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this,a=t.params,i=t.rtlTranslate,r=t.translate,s=t.$wrapperEl;if(a.virtualTranslate)return i?-r:r;var n=V.getTranslate(s[0],e);return i&&(n=-n),n||0}function se(e,t){var a,i=this,r=i.rtlTranslate,s=i.params,n=i.$wrapperEl,o=i.progress,l=0,d=0,p=0;i.isHorizontal()?l=r?-e:e:d=e,s.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),s.virtualTranslate||(Y.transforms3d?n.transform("translate3d("+l+"px, "+d+"px, "+p+"px)"):n.transform("translate("+l+"px, "+d+"px)")),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d;var u=i.maxTranslate()-i.minTranslate();a=0===u?0:(e-i.minTranslate())/u,a!==o&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)}function ne(){return-this.snapGrid[0]}function oe(){return-this.snapGrid[this.snapGrid.length-1]}var le={getTranslate:re,setTranslate:se,minTranslate:ne,maxTranslate:oe};function de(e,t){var a=this;a.$wrapperEl.transition(e),a.emit("setTransition",e,t)}function pe(e,t){void 0===e&&(e=!0);var a=this,i=a.activeIndex,r=a.params,s=a.previousIndex;r.autoHeight&&a.updateAutoHeight();var n=t;if(n||(n=i>s?"next":i<s?"prev":"reset"),a.emit("transitionStart"),e&&i!==s){if("reset"===n)return void a.emit("slideResetTransitionStart");a.emit("slideChangeTransitionStart"),"next"===n?a.emit("slideNextTransitionStart"):a.emit("slidePrevTransitionStart")}}function ue(e,t){void 0===e&&(e=!0);var a=this,i=a.activeIndex,r=a.previousIndex;a.animating=!1,a.setTransition(0);var s=t;if(s||(s=i>r?"next":i<r?"prev":"reset"),a.emit("transitionEnd"),e&&i!==r){if("reset"===s)return void a.emit("slideResetTransitionEnd");a.emit("slideChangeTransitionEnd"),"next"===s?a.emit("slideNextTransitionEnd"):a.emit("slidePrevTransitionEnd")}}var ce={setTransition:de,transitionStart:pe,transitionEnd:ue};function he(e,t,a,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0);var r=this,s=e;s<0&&(s=0);var n=r.params,o=r.snapGrid,l=r.slidesGrid,d=r.previousIndex,p=r.activeIndex,u=r.rtlTranslate;if(r.animating&&n.preventInteractionOnTransition)return!1;var c=Math.floor(s/n.slidesPerGroup);c>=o.length&&(c=o.length-1),(p||n.initialSlide||0)===(d||0)&&a&&r.emit("beforeSlideChangeStart");var h,f=-o[c];if(r.updateProgress(f),n.normalizeSlideIndex)for(var v=0;v<l.length;v+=1)-Math.floor(100*f)>=Math.floor(100*l[v])&&(s=v);if(r.initialized&&s!==p){if(!r.allowSlideNext&&f<r.translate&&f<r.minTranslate())return!1;if(!r.allowSlidePrev&&f>r.translate&&f>r.maxTranslate()&&(p||0)!==s)return!1}return h=s>p?"next":s<p?"prev":"reset",u&&-f===r.translate||!u&&f===r.translate?(r.updateActiveIndex(s),n.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==n.effect&&r.setTranslate(f),"reset"!==h&&(r.transitionStart(a,h),r.transitionEnd(a,h)),!1):(0!==t&&Y.transition?(r.setTransition(t),r.setTranslate(f),r.updateActiveIndex(s),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,i),r.transitionStart(a,h),r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(a,h))}),r.$wrapperEl[0].addEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd))):(r.setTransition(0),r.setTranslate(f),r.updateActiveIndex(s),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,i),r.transitionStart(a,h),r.transitionEnd(a,h)),!0)}function fe(e,t,a,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0);var r=this,s=e;return r.params.loop&&(s+=r.loopedSlides),r.slideTo(s,t,a,i)}function ve(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,r=i.params,s=i.animating;return r.loop?!s&&(i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft,i.slideTo(i.activeIndex+r.slidesPerGroup,e,t,a)):i.slideTo(i.activeIndex+r.slidesPerGroup,e,t,a)}function me(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,r=i.params,s=i.animating,n=i.snapGrid,o=i.slidesGrid,l=i.rtlTranslate;if(r.loop){if(s)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}var d=l?i.translate:-i.translate;function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var u,c=p(d),h=n.map((function(e){return p(e)})),f=(o.map((function(e){return p(e)})),n[h.indexOf(c)],n[h.indexOf(c)-1]);return"undefined"!==typeof f&&(u=o.indexOf(f),u<0&&(u=i.activeIndex-1)),i.slideTo(u,e,t,a)}function ge(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this;return i.slideTo(i.activeIndex,e,t,a)}function we(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,r=i.activeIndex,s=Math.floor(r/i.params.slidesPerGroup);if(s<i.snapGrid.length-1){var n=i.rtlTranslate?i.translate:-i.translate,o=i.snapGrid[s],l=i.snapGrid[s+1];n-o>(l-o)/2&&(r=i.params.slidesPerGroup)}return i.slideTo(r,e,t,a)}function be(){var e,t=this,a=t.params,r=t.$wrapperEl,s="auto"===a.slidesPerView?t.slidesPerViewDynamic():a.slidesPerView,n=t.clickedIndex;if(a.loop){if(t.animating)return;e=parseInt(i(t.clickedSlide).attr("data-swiper-slide-index"),10),a.centeredSlides?n<t.loopedSlides-s/2||n>t.slides.length-t.loopedSlides+s/2?(t.loopFix(),n=r.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+a.slideDuplicateClass+")").eq(0).index(),V.nextTick((function(){t.slideTo(n)}))):t.slideTo(n):n>t.slides.length-s?(t.loopFix(),n=r.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+a.slideDuplicateClass+")").eq(0).index(),V.nextTick((function(){t.slideTo(n)}))):t.slideTo(n)}else t.slideTo(n)}var ye={slideTo:he,slideToLoop:fe,slideNext:ve,slidePrev:me,slideReset:ge,slideToClosest:we,slideToClickedSlide:be};function xe(){var t=this,a=t.params,r=t.$wrapperEl;r.children("."+a.slideClass+"."+a.slideDuplicateClass).remove();var s=r.children("."+a.slideClass);if(a.loopFillGroupWithBlank){var n=a.slidesPerGroup-s.length%a.slidesPerGroup;if(n!==a.slidesPerGroup){for(var o=0;o<n;o+=1){var l=i(e.createElement("div")).addClass(a.slideClass+" "+a.slideBlankClass);r.append(l)}s=r.children("."+a.slideClass)}}"auto"!==a.slidesPerView||a.loopedSlides||(a.loopedSlides=s.length),t.loopedSlides=parseInt(a.loopedSlides||a.slidesPerView,10),t.loopedSlides+=a.loopAdditionalSlides,t.loopedSlides>s.length&&(t.loopedSlides=s.length);var d=[],p=[];s.each((function(e,a){var r=i(a);e<t.loopedSlides&&p.push(a),e<s.length&&e>=s.length-t.loopedSlides&&d.push(a),r.attr("data-swiper-slide-index",e)}));for(var u=0;u<p.length;u+=1)r.append(i(p[u].cloneNode(!0)).addClass(a.slideDuplicateClass));for(var c=d.length-1;c>=0;c-=1)r.prepend(i(d[c].cloneNode(!0)).addClass(a.slideDuplicateClass))}function Ee(){var e,t=this,a=t.params,i=t.activeIndex,r=t.slides,s=t.loopedSlides,n=t.allowSlidePrev,o=t.allowSlideNext,l=t.snapGrid,d=t.rtlTranslate;t.allowSlidePrev=!0,t.allowSlideNext=!0;var p=-l[i],u=p-t.getTranslate();if(i<s){e=r.length-3*s+i,e+=s;var c=t.slideTo(e,0,!1,!0);c&&0!==u&&t.setTranslate((d?-t.translate:t.translate)-u)}else if("auto"===a.slidesPerView&&i>=2*s||i>=r.length-s){e=-r.length+i+s,e+=s;var h=t.slideTo(e,0,!1,!0);h&&0!==u&&t.setTranslate((d?-t.translate:t.translate)-u)}t.allowSlidePrev=n,t.allowSlideNext=o}function Te(){var e=this,t=e.$wrapperEl,a=e.params,i=e.slides;t.children("."+a.slideClass+"."+a.slideDuplicateClass+",."+a.slideClass+"."+a.slideBlankClass).remove(),i.removeAttr("data-swiper-slide-index")}var Ce={loopCreate:xe,loopFix:Ee,loopDestroy:Te};function Se(e){var t=this;if(!(Y.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked)){var a=t.el;a.style.cursor="move",a.style.cursor=e?"-webkit-grabbing":"-webkit-grab",a.style.cursor=e?"-moz-grabbin":"-moz-grab",a.style.cursor=e?"grabbing":"grab"}}function Me(){var e=this;Y.touch||e.params.watchOverflow&&e.isLocked||(e.el.style.cursor="")}var Pe={setGrabCursor:Se,unsetGrabCursor:Me};function ke(e){var t=this,a=t.$wrapperEl,i=t.params;if(i.loop&&t.loopDestroy(),"object"===typeof e&&"length"in e)for(var r=0;r<e.length;r+=1)e[r]&&a.append(e[r]);else a.append(e);i.loop&&t.loopCreate(),i.observer&&Y.observer||t.update()}function Le(e){var t=this,a=t.params,i=t.$wrapperEl,r=t.activeIndex;a.loop&&t.loopDestroy();var s=r+1;if("object"===typeof e&&"length"in e){for(var n=0;n<e.length;n+=1)e[n]&&i.prepend(e[n]);s=r+e.length}else i.prepend(e);a.loop&&t.loopCreate(),a.observer&&Y.observer||t.update(),t.slideTo(s,0,!1)}function ze(e,t){var a=this,i=a.$wrapperEl,r=a.params,s=a.activeIndex,n=s;r.loop&&(n-=a.loopedSlides,a.loopDestroy(),a.slides=i.children("."+r.slideClass));var o=a.slides.length;if(e<=0)a.prependSlide(t);else if(e>=o)a.appendSlide(t);else{for(var l=n>e?n+1:n,d=[],p=o-1;p>=e;p-=1){var u=a.slides.eq(p);u.remove(),d.unshift(u)}if("object"===typeof t&&"length"in t){for(var c=0;c<t.length;c+=1)t[c]&&i.append(t[c]);l=n>e?n+t.length:n}else i.append(t);for(var h=0;h<d.length;h+=1)i.append(d[h]);r.loop&&a.loopCreate(),r.observer&&Y.observer||a.update(),r.loop?a.slideTo(l+a.loopedSlides,0,!1):a.slideTo(l,0,!1)}}function Ie(e){var t=this,a=t.params,i=t.$wrapperEl,r=t.activeIndex,s=r;a.loop&&(s-=t.loopedSlides,t.loopDestroy(),t.slides=i.children("."+a.slideClass));var n,o=s;if("object"===typeof e&&"length"in e){for(var l=0;l<e.length;l+=1)n=e[l],t.slides[n]&&t.slides.eq(n).remove(),n<o&&(o-=1);o=Math.max(o,0)}else n=e,t.slides[n]&&t.slides.eq(n).remove(),n<o&&(o-=1),o=Math.max(o,0);a.loop&&t.loopCreate(),a.observer&&Y.observer||t.update(),a.loop?t.slideTo(o+t.loopedSlides,0,!1):t.slideTo(o,0,!1)}function De(){for(var e=this,t=[],a=0;a<e.slides.length;a+=1)t.push(a);e.removeSlide(t)}var $e={appendSlide:ke,prependSlide:Le,addSlide:ze,removeSlide:Ie,removeAllSlides:De},Ae=function(){var a=t.navigator.userAgent,i={ios:!1,android:!1,androidChrome:!1,desktop:!1,windows:!1,iphone:!1,ipod:!1,ipad:!1,cordova:t.cordova||t.phonegap,phonegap:t.cordova||t.phonegap},r=a.match(/(Windows Phone);?[\s\/]+([\d.]+)?/),s=a.match(/(Android);?[\s\/]+([\d.]+)?/),n=a.match(/(iPad).*OS\s([\d_]+)/),o=a.match(/(iPod)(.*OS\s([\d_]+))?/),l=!n&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/);if(r&&(i.os="windows",i.osVersion=r[2],i.windows=!0),s&&!r&&(i.os="android",i.osVersion=s[2],i.android=!0,i.androidChrome=a.toLowerCase().indexOf("chrome")>=0),(n||l||o)&&(i.os="ios",i.ios=!0),l&&!o&&(i.osVersion=l[2].replace(/_/g,"."),i.iphone=!0),n&&(i.osVersion=n[2].replace(/_/g,"."),i.ipad=!0),o&&(i.osVersion=o[3]?o[3].replace(/_/g,"."):null,i.iphone=!0),i.ios&&i.osVersion&&a.indexOf("Version/")>=0&&"10"===i.osVersion.split(".")[0]&&(i.osVersion=a.toLowerCase().split("version/")[1].split(" ")[0]),i.desktop=!(i.os||i.android||i.webView),i.webView=(l||n||o)&&a.match(/.*AppleWebKit(?!.*Safari)/i),i.os&&"ios"===i.os){var d=i.osVersion.split("."),p=e.querySelector('meta[name="viewport"]');i.minimalUi=!i.webView&&(o||l)&&(1*d[0]===7?1*d[1]>=1:1*d[0]>7)&&p&&p.getAttribute("content").indexOf("minimal-ui")>=0}return i.pixelRatio=t.devicePixelRatio||1,i}();function Oe(a){var r=this,s=r.touchEventsData,n=r.params,o=r.touches;if(!r.animating||!n.preventInteractionOnTransition){var l=a;if(l.originalEvent&&(l=l.originalEvent),s.isTouchEvent="touchstart"===l.type,(s.isTouchEvent||!("which"in l)||3!==l.which)&&!(!s.isTouchEvent&&"button"in l&&l.button>0)&&(!s.isTouched||!s.isMoved))if(n.noSwiping&&i(l.target).closest(n.noSwipingSelector?n.noSwipingSelector:"."+n.noSwipingClass)[0])r.allowClick=!0;else if(!n.swipeHandler||i(l).closest(n.swipeHandler)[0]){o.currentX="touchstart"===l.type?l.targetTouches[0].pageX:l.pageX,o.currentY="touchstart"===l.type?l.targetTouches[0].pageY:l.pageY;var d=o.currentX,p=o.currentY,u=n.edgeSwipeDetection||n.iOSEdgeSwipeDetection,c=n.edgeSwipeThreshold||n.iOSEdgeSwipeThreshold;if(!u||!(d<=c||d>=t.screen.width-c)){if(V.extend(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=d,o.startY=p,s.touchStartTime=V.now(),r.allowClick=!0,r.updateSize(),r.swipeDirection=void 0,n.threshold>0&&(s.allowThresholdMove=!1),"touchstart"!==l.type){var h=!0;i(l.target).is(s.formElements)&&(h=!1),e.activeElement&&i(e.activeElement).is(s.formElements)&&e.activeElement!==l.target&&e.activeElement.blur();var f=h&&r.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||f)&&l.preventDefault()}r.emit("touchStart",l)}}}}function _e(t){var a=this,r=a.touchEventsData,s=a.params,n=a.touches,o=a.rtlTranslate,l=t;if(l.originalEvent&&(l=l.originalEvent),r.isTouched){if(!r.isTouchEvent||"mousemove"!==l.type){var d="touchmove"===l.type?l.targetTouches[0].pageX:l.pageX,p="touchmove"===l.type?l.targetTouches[0].pageY:l.pageY;if(l.preventedByNestedSwiper)return n.startX=d,void(n.startY=p);if(!a.allowTouchMove)return a.allowClick=!1,void(r.isTouched&&(V.extend(n,{startX:d,startY:p,currentX:d,currentY:p}),r.touchStartTime=V.now()));if(r.isTouchEvent&&s.touchReleaseOnEdges&&!s.loop)if(a.isVertical()){if(p<n.startY&&a.translate<=a.maxTranslate()||p>n.startY&&a.translate>=a.minTranslate())return r.isTouched=!1,void(r.isMoved=!1)}else if(d<n.startX&&a.translate<=a.maxTranslate()||d>n.startX&&a.translate>=a.minTranslate())return;if(r.isTouchEvent&&e.activeElement&&l.target===e.activeElement&&i(l.target).is(r.formElements))return r.isMoved=!0,void(a.allowClick=!1);if(r.allowTouchCallbacks&&a.emit("touchMove",l),!(l.targetTouches&&l.targetTouches.length>1)){n.currentX=d,n.currentY=p;var u=n.currentX-n.startX,c=n.currentY-n.startY;if(!(a.params.threshold&&Math.sqrt(Math.pow(u,2)+Math.pow(c,2))<a.params.threshold)){var h;if("undefined"===typeof r.isScrolling)a.isHorizontal()&&n.currentY===n.startY||a.isVertical()&&n.currentX===n.startX?r.isScrolling=!1:u*u+c*c>=25&&(h=180*Math.atan2(Math.abs(c),Math.abs(u))/Math.PI,r.isScrolling=a.isHorizontal()?h>s.touchAngle:90-h>s.touchAngle);if(r.isScrolling&&a.emit("touchMoveOpposite",l),"undefined"===typeof r.startMoving&&(n.currentX===n.startX&&n.currentY===n.startY||(r.startMoving=!0)),r.isScrolling)r.isTouched=!1;else if(r.startMoving){a.allowClick=!1,l.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&l.stopPropagation(),r.isMoved||(s.loop&&a.loopFix(),r.startTranslate=a.getTranslate(),a.setTransition(0),a.animating&&a.$wrapperEl.trigger("webkitTransitionEnd transitionend"),r.allowMomentumBounce=!1,!s.grabCursor||!0!==a.allowSlideNext&&!0!==a.allowSlidePrev||a.setGrabCursor(!0),a.emit("sliderFirstMove",l)),a.emit("sliderMove",l),r.isMoved=!0;var f=a.isHorizontal()?u:c;n.diff=f,f*=s.touchRatio,o&&(f=-f),a.swipeDirection=f>0?"prev":"next",r.currentTranslate=f+r.startTranslate;var v=!0,m=s.resistanceRatio;if(s.touchReleaseOnEdges&&(m=0),f>0&&r.currentTranslate>a.minTranslate()?(v=!1,s.resistance&&(r.currentTranslate=a.minTranslate()-1+Math.pow(-a.minTranslate()+r.startTranslate+f,m))):f<0&&r.currentTranslate<a.maxTranslate()&&(v=!1,s.resistance&&(r.currentTranslate=a.maxTranslate()+1-Math.pow(a.maxTranslate()-r.startTranslate-f,m))),v&&(l.preventedByNestedSwiper=!0),!a.allowSlideNext&&"next"===a.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!a.allowSlidePrev&&"prev"===a.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),s.threshold>0){if(!(Math.abs(f)>s.threshold||r.allowThresholdMove))return void(r.currentTranslate=r.startTranslate);if(!r.allowThresholdMove)return r.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,r.currentTranslate=r.startTranslate,void(n.diff=a.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY)}s.followFinger&&((s.freeMode||s.watchSlidesProgress||s.watchSlidesVisibility)&&(a.updateActiveIndex(),a.updateSlidesClasses()),s.freeMode&&(0===r.velocities.length&&r.velocities.push({position:n[a.isHorizontal()?"startX":"startY"],time:r.touchStartTime}),r.velocities.push({position:n[a.isHorizontal()?"currentX":"currentY"],time:V.now()})),a.updateProgress(r.currentTranslate),a.setTranslate(r.currentTranslate))}}}}}else r.startMoving&&r.isScrolling&&a.emit("touchMoveOpposite",l)}function Be(e){var t=this,a=t.touchEventsData,i=t.params,r=t.touches,s=t.rtlTranslate,n=t.$wrapperEl,o=t.slidesGrid,l=t.snapGrid,d=e;if(d.originalEvent&&(d=d.originalEvent),a.allowTouchCallbacks&&t.emit("touchEnd",d),a.allowTouchCallbacks=!1,!a.isTouched)return a.isMoved&&i.grabCursor&&t.setGrabCursor(!1),a.isMoved=!1,void(a.startMoving=!1);i.grabCursor&&a.isMoved&&a.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var p,u=V.now(),c=u-a.touchStartTime;if(t.allowClick&&(t.updateClickedSlide(d),t.emit("tap",d),c<300&&u-a.lastClickTime>300&&(a.clickTimeout&&clearTimeout(a.clickTimeout),a.clickTimeout=V.nextTick((function(){t&&!t.destroyed&&t.emit("click",d)}),300)),c<300&&u-a.lastClickTime<300&&(a.clickTimeout&&clearTimeout(a.clickTimeout),t.emit("doubleTap",d))),a.lastClickTime=V.now(),V.nextTick((function(){t.destroyed||(t.allowClick=!0)})),!a.isTouched||!a.isMoved||!t.swipeDirection||0===r.diff||a.currentTranslate===a.startTranslate)return a.isTouched=!1,a.isMoved=!1,void(a.startMoving=!1);if(a.isTouched=!1,a.isMoved=!1,a.startMoving=!1,p=i.followFinger?s?t.translate:-t.translate:-a.currentTranslate,i.freeMode){if(p<-t.minTranslate())return void t.slideTo(t.activeIndex);if(p>-t.maxTranslate())return void(t.slides.length<l.length?t.slideTo(l.length-1):t.slideTo(t.slides.length-1));if(i.freeModeMomentum){if(a.velocities.length>1){var h=a.velocities.pop(),f=a.velocities.pop(),v=h.position-f.position,m=h.time-f.time;t.velocity=v/m,t.velocity/=2,Math.abs(t.velocity)<i.freeModeMinimumVelocity&&(t.velocity=0),(m>150||V.now()-h.time>300)&&(t.velocity=0)}else t.velocity=0;t.velocity*=i.freeModeMomentumVelocityRatio,a.velocities.length=0;var g=1e3*i.freeModeMomentumRatio,w=t.velocity*g,b=t.translate+w;s&&(b=-b);var y,x,E=!1,T=20*Math.abs(t.velocity)*i.freeModeMomentumBounceRatio;if(b<t.maxTranslate())i.freeModeMomentumBounce?(b+t.maxTranslate()<-T&&(b=t.maxTranslate()-T),y=t.maxTranslate(),E=!0,a.allowMomentumBounce=!0):b=t.maxTranslate(),i.loop&&i.centeredSlides&&(x=!0);else if(b>t.minTranslate())i.freeModeMomentumBounce?(b-t.minTranslate()>T&&(b=t.minTranslate()+T),y=t.minTranslate(),E=!0,a.allowMomentumBounce=!0):b=t.minTranslate(),i.loop&&i.centeredSlides&&(x=!0);else if(i.freeModeSticky){for(var C,S=0;S<l.length;S+=1)if(l[S]>-b){C=S;break}b=Math.abs(l[C]-b)<Math.abs(l[C-1]-b)||"next"===t.swipeDirection?l[C]:l[C-1],b=-b}if(x&&t.once("transitionEnd",(function(){t.loopFix()})),0!==t.velocity)g=s?Math.abs((-b-t.translate)/t.velocity):Math.abs((b-t.translate)/t.velocity);else if(i.freeModeSticky)return void t.slideToClosest();i.freeModeMomentumBounce&&E?(t.updateProgress(y),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating=!0,n.transitionEnd((function(){t&&!t.destroyed&&a.allowMomentumBounce&&(t.emit("momentumBounce"),t.setTransition(i.speed),t.setTranslate(y),n.transitionEnd((function(){t&&!t.destroyed&&t.transitionEnd()})))}))):t.velocity?(t.updateProgress(b),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,n.transitionEnd((function(){t&&!t.destroyed&&t.transitionEnd()})))):t.updateProgress(b),t.updateActiveIndex(),t.updateSlidesClasses()}else if(i.freeModeSticky)return void t.slideToClosest();(!i.freeModeMomentum||c>=i.longSwipesMs)&&(t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses())}else{for(var M=0,P=t.slidesSizesGrid[0],k=0;k<o.length;k+=i.slidesPerGroup)"undefined"!==typeof o[k+i.slidesPerGroup]?p>=o[k]&&p<o[k+i.slidesPerGroup]&&(M=k,P=o[k+i.slidesPerGroup]-o[k]):p>=o[k]&&(M=k,P=o[o.length-1]-o[o.length-2]);var L=(p-o[M])/P;if(c>i.longSwipesMs){if(!i.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(L>=i.longSwipesRatio?t.slideTo(M+i.slidesPerGroup):t.slideTo(M)),"prev"===t.swipeDirection&&(L>1-i.longSwipesRatio?t.slideTo(M+i.slidesPerGroup):t.slideTo(M))}else{if(!i.shortSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&t.slideTo(M+i.slidesPerGroup),"prev"===t.swipeDirection&&t.slideTo(M)}}}function Ne(){var e=this,t=e.params,a=e.el;if(!a||0!==a.offsetWidth){t.breakpoints&&e.setBreakpoint();var i=e.allowSlideNext,r=e.allowSlidePrev,s=e.snapGrid;if(e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),t.freeMode){var n=Math.min(Math.max(e.translate,e.maxTranslate()),e.minTranslate());e.setTranslate(n),e.updateActiveIndex(),e.updateSlidesClasses(),t.autoHeight&&e.updateAutoHeight()}else e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0);e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}}function He(e){var t=this;t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function Ge(){var t=this,a=t.params,i=t.touchEvents,r=t.el,s=t.wrapperEl;t.onTouchStart=Oe.bind(t),t.onTouchMove=_e.bind(t),t.onTouchEnd=Be.bind(t),t.onClick=He.bind(t);var n="container"===a.touchEventsTarget?r:s,o=!!a.nested;if(Y.touch||!Y.pointerEvents&&!Y.prefixedPointerEvents){if(Y.touch){var l=!("touchstart"!==i.start||!Y.passiveListener||!a.passiveListeners)&&{passive:!0,capture:!1};n.addEventListener(i.start,t.onTouchStart,l),n.addEventListener(i.move,t.onTouchMove,Y.passiveListener?{passive:!1,capture:o}:o),n.addEventListener(i.end,t.onTouchEnd,l)}(a.simulateTouch&&!Ae.ios&&!Ae.android||a.simulateTouch&&!Y.touch&&Ae.ios)&&(n.addEventListener("mousedown",t.onTouchStart,!1),e.addEventListener("mousemove",t.onTouchMove,o),e.addEventListener("mouseup",t.onTouchEnd,!1))}else n.addEventListener(i.start,t.onTouchStart,!1),e.addEventListener(i.move,t.onTouchMove,o),e.addEventListener(i.end,t.onTouchEnd,!1);(a.preventClicks||a.preventClicksPropagation)&&n.addEventListener("click",t.onClick,!0),t.on(Ae.ios||Ae.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ne,!0)}function Re(){var t=this,a=t.params,i=t.touchEvents,r=t.el,s=t.wrapperEl,n="container"===a.touchEventsTarget?r:s,o=!!a.nested;if(Y.touch||!Y.pointerEvents&&!Y.prefixedPointerEvents){if(Y.touch){var l=!("onTouchStart"!==i.start||!Y.passiveListener||!a.passiveListeners)&&{passive:!0,capture:!1};n.removeEventListener(i.start,t.onTouchStart,l),n.removeEventListener(i.move,t.onTouchMove,o),n.removeEventListener(i.end,t.onTouchEnd,l)}(a.simulateTouch&&!Ae.ios&&!Ae.android||a.simulateTouch&&!Y.touch&&Ae.ios)&&(n.removeEventListener("mousedown",t.onTouchStart,!1),e.removeEventListener("mousemove",t.onTouchMove,o),e.removeEventListener("mouseup",t.onTouchEnd,!1))}else n.removeEventListener(i.start,t.onTouchStart,!1),e.removeEventListener(i.move,t.onTouchMove,o),e.removeEventListener(i.end,t.onTouchEnd,!1);(a.preventClicks||a.preventClicksPropagation)&&n.removeEventListener("click",t.onClick,!0),t.off(Ae.ios||Ae.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ne)}var Xe={attachEvents:Ge,detachEvents:Re};function Ve(){var e=this,t=e.activeIndex,a=e.initialized,i=e.loopedSlides;void 0===i&&(i=0);var r=e.params,s=r.breakpoints;if(s&&(!s||0!==Object.keys(s).length)){var n=e.getBreakpoint(s);if(n&&e.currentBreakpoint!==n){var o=n in s?s[n]:void 0;o&&["slidesPerView","spaceBetween","slidesPerGroup"].forEach((function(e){var t=o[e];"undefined"!==typeof t&&(o[e]="slidesPerView"!==e||"AUTO"!==t&&"auto"!==t?"slidesPerView"===e?parseFloat(t):parseInt(t,10):"auto")}));var l=o||e.originalParams,d=l.direction&&l.direction!==r.direction,p=r.loop&&(l.slidesPerView!==r.slidesPerView||d);d&&a&&e.changeDirection(),V.extend(e.params,l),V.extend(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),e.currentBreakpoint=n,p&&a&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-i+e.loopedSlides,0,!1)),e.emit("breakpoint",l)}}}function Ye(e){var a=this;if(e){var i=!1,r=[];Object.keys(e).forEach((function(e){r.push(e)})),r.sort((function(e,t){return parseInt(e,10)-parseInt(t,10)}));for(var s=0;s<r.length;s+=1){var n=r[s];a.params.breakpointsInverse?n<=t.innerWidth&&(i=n):n>=t.innerWidth&&!i&&(i=n)}return i||"max"}}var Fe={setBreakpoint:Ve,getBreakpoint:Ye};function je(){var e=this,t=e.classNames,a=e.params,i=e.rtl,r=e.$el,s=[];s.push("initialized"),s.push(a.direction),a.freeMode&&s.push("free-mode"),Y.flexbox||s.push("no-flexbox"),a.autoHeight&&s.push("autoheight"),i&&s.push("rtl"),a.slidesPerColumn>1&&s.push("multirow"),Ae.android&&s.push("android"),Ae.ios&&s.push("ios"),(F.isIE||F.isEdge)&&(Y.pointerEvents||Y.prefixedPointerEvents)&&s.push("wp8-"+a.direction),s.forEach((function(e){t.push(a.containerModifierClass+e)})),r.addClass(t.join(" "))}function qe(){var e=this,t=e.$el,a=e.classNames;t.removeClass(a.join(" "))}var We={addClasses:je,removeClasses:qe};function Ue(e,a,i,r,s,n){var o;function l(){n&&n()}e.complete&&s?l():a?(o=new t.Image,o.onload=l,o.onerror=l,r&&(o.sizes=r),i&&(o.srcset=i),a&&(o.src=a)):l()}function Ke(){var e=this;function t(){"undefined"!==typeof e&&null!==e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(var a=0;a<e.imagesToLoad.length;a+=1){var i=e.imagesToLoad[a];e.loadImage(i,i.currentSrc||i.getAttribute("src"),i.srcset||i.getAttribute("srcset"),i.sizes||i.getAttribute("sizes"),!0,t)}}var Qe={loadImage:Ue,preloadImages:Ke};function Je(){var e=this,t=e.isLocked;e.isLocked=1===e.snapGrid.length,e.allowSlideNext=!e.isLocked,e.allowSlidePrev=!e.isLocked,t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock"),t&&t!==e.isLocked&&(e.isEnd=!1,e.navigation.update())}var Ze={checkOverflow:Je},et={init:!0,direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,preventInteractionOnTransition:!1,edgeSwipeDetection:!1,edgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsInverse:!1,spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,centeredSlides:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!1,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!0,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-container-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0},tt={update:ie,translate:le,transition:ce,slide:ye,loop:Ce,grabCursor:Pe,manipulation:$e,events:Xe,breakpoints:Fe,checkOverflow:Ze,classes:We,images:Qe},at={},it=function(e){function t(){var a,r,s,n=[],o=arguments.length;while(o--)n[o]=arguments[o];1===n.length&&n[0].constructor&&n[0].constructor===Object?s=n[0]:(a=n,r=a[0],s=a[1]),s||(s={}),s=V.extend({},s),r&&!s.el&&(s.el=r),e.call(this,s),Object.keys(tt).forEach((function(e){Object.keys(tt[e]).forEach((function(a){t.prototype[a]||(t.prototype[a]=tt[e][a])}))}));var l=this;"undefined"===typeof l.modules&&(l.modules={}),Object.keys(l.modules).forEach((function(e){var t=l.modules[e];if(t.params){var a=Object.keys(t.params)[0],i=t.params[a];if("object"!==typeof i||null===i)return;if(!(a in s)||!("enabled"in i))return;!0===s[a]&&(s[a]={enabled:!0}),"object"!==typeof s[a]||"enabled"in s[a]||(s[a].enabled=!0),s[a]||(s[a]={enabled:!1})}}));var d=V.extend({},et);l.useModulesParams(d),l.params=V.extend({},d,at,s),l.originalParams=V.extend({},l.params),l.passedParams=V.extend({},s),l.$=i;var p=i(l.params.el);if(r=p[0],r){if(p.length>1){var u=[];return p.each((function(e,a){var i=V.extend({},s,{el:a});u.push(new t(i))})),u}r.swiper=l,p.data("swiper",l);var c=p.children("."+l.params.wrapperClass);return V.extend(l,{$el:p,el:r,$wrapperEl:c,wrapperEl:c[0],classNames:[],slides:i(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===l.params.direction},isVertical:function(){return"vertical"===l.params.direction},rtl:"rtl"===r.dir.toLowerCase()||"rtl"===p.css("direction"),rtlTranslate:"horizontal"===l.params.direction&&("rtl"===r.dir.toLowerCase()||"rtl"===p.css("direction")),wrongRTL:"-webkit-box"===c.css("display"),activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEvents:function(){var e=["touchstart","touchmove","touchend"],t=["mousedown","mousemove","mouseup"];return Y.pointerEvents?t=["pointerdown","pointermove","pointerup"]:Y.prefixedPointerEvents&&(t=["MSPointerDown","MSPointerMove","MSPointerUp"]),l.touchEventsTouch={start:e[0],move:e[1],end:e[2]},l.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},Y.touch||!l.params.simulateTouch?l.touchEventsTouch:l.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,formElements:"input, select, option, textarea, button, video",lastClickTime:V.now(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.useModules(),l.params.init&&l.init(),l}}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var a={extendedDefaults:{configurable:!0},defaults:{configurable:!0},Class:{configurable:!0},$:{configurable:!0}};return t.prototype.slidesPerViewDynamic=function(){var e=this,t=e.params,a=e.slides,i=e.slidesGrid,r=e.size,s=e.activeIndex,n=1;if(t.centeredSlides){for(var o,l=a[s].swiperSlideSize,d=s+1;d<a.length;d+=1)a[d]&&!o&&(l+=a[d].swiperSlideSize,n+=1,l>r&&(o=!0));for(var p=s-1;p>=0;p-=1)a[p]&&!o&&(l+=a[p].swiperSlideSize,n+=1,l>r&&(o=!0))}else for(var u=s+1;u<a.length;u+=1)i[u]-i[s]<r&&(n+=1);return n},t.prototype.update=function(){var e=this;if(e&&!e.destroyed){var t,a=e.snapGrid,i=e.params;i.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode?(r(),e.params.autoHeight&&e.updateAutoHeight()):(t=("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),t||r()),i.watchOverflow&&a!==e.snapGrid&&e.checkOverflow(),e.emit("update")}function r(){var t=e.rtlTranslate?-1*e.translate:e.translate,a=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}},t.prototype.changeDirection=function(e,t){void 0===t&&(t=!0);var a=this,i=a.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(a.$el.removeClass(""+a.params.containerModifierClass+i+" wp8-"+i).addClass(""+a.params.containerModifierClass+e),(F.isIE||F.isEdge)&&(Y.pointerEvents||Y.prefixedPointerEvents)&&a.$el.addClass(a.params.containerModifierClass+"wp8-"+e),a.params.direction=e,a.slides.each((function(t,a){"vertical"===e?a.style.width="":a.style.height=""})),a.emit("changeDirection"),t&&a.update()),a},t.prototype.init=function(){var e=this;e.initialized||(e.emit("beforeInit"),e.params.breakpoints&&e.setBreakpoint(),e.addClasses(),e.params.loop&&e.loopCreate(),e.updateSize(),e.updateSlides(),e.params.watchOverflow&&e.checkOverflow(),e.params.grabCursor&&e.setGrabCursor(),e.params.preloadImages&&e.preloadImages(),e.params.loop?e.slideTo(e.params.initialSlide+e.loopedSlides,0,e.params.runCallbacksOnInit):e.slideTo(e.params.initialSlide,0,e.params.runCallbacksOnInit),e.attachEvents(),e.initialized=!0,e.emit("init"))},t.prototype.destroy=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var a=this,i=a.params,r=a.$el,s=a.$wrapperEl,n=a.slides;return"undefined"===typeof a.params||a.destroyed||(a.emit("beforeDestroy"),a.initialized=!1,a.detachEvents(),i.loop&&a.loopDestroy(),t&&(a.removeClasses(),r.removeAttr("style"),s.removeAttr("style"),n&&n.length&&n.removeClass([i.slideVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index").removeAttr("data-swiper-column").removeAttr("data-swiper-row")),a.emit("destroy"),Object.keys(a.eventsListeners).forEach((function(e){a.off(e)})),!1!==e&&(a.$el[0].swiper=null,a.$el.data("swiper",null),V.deleteProps(a)),a.destroyed=!0),null},t.extendDefaults=function(e){V.extend(at,e)},a.extendedDefaults.get=function(){return at},a.defaults.get=function(){return et},a.Class.get=function(){return e},a.$.get=function(){return i},Object.defineProperties(t,a),t}(j),rt={name:"device",proto:{device:Ae},static:{device:Ae}},st={name:"support",proto:{support:Y},static:{support:Y}},nt={name:"browser",proto:{browser:F},static:{browser:F}},ot={name:"resize",create:function(){var e=this;V.extend(e,{resize:{resizeHandler:function(){e&&!e.destroyed&&e.initialized&&(e.emit("beforeResize"),e.emit("resize"))},orientationChangeHandler:function(){e&&!e.destroyed&&e.initialized&&e.emit("orientationchange")}}})},on:{init:function(){var e=this;t.addEventListener("resize",e.resize.resizeHandler),t.addEventListener("orientationchange",e.resize.orientationChangeHandler)},destroy:function(){var e=this;t.removeEventListener("resize",e.resize.resizeHandler),t.removeEventListener("orientationchange",e.resize.orientationChangeHandler)}}},lt={func:t.MutationObserver||t.WebkitMutationObserver,attach:function(e,a){void 0===a&&(a={});var i=this,r=lt.func,s=new r((function(e){if(1!==e.length){var a=function(){i.emit("observerUpdate",e[0])};t.requestAnimationFrame?t.requestAnimationFrame(a):t.setTimeout(a,0)}else i.emit("observerUpdate",e[0])}));s.observe(e,{attributes:"undefined"===typeof a.attributes||a.attributes,childList:"undefined"===typeof a.childList||a.childList,characterData:"undefined"===typeof a.characterData||a.characterData}),i.observer.observers.push(s)},init:function(){var e=this;if(Y.observer&&e.params.observer){if(e.params.observeParents)for(var t=e.$el.parents(),a=0;a<t.length;a+=1)e.observer.attach(t[a]);e.observer.attach(e.$el[0],{childList:e.params.observeSlideChildren}),e.observer.attach(e.$wrapperEl[0],{attributes:!1})}},destroy:function(){var e=this;e.observer.observers.forEach((function(e){e.disconnect()})),e.observer.observers=[]}},dt={name:"observer",params:{observer:!1,observeParents:!1,observeSlideChildren:!1},create:function(){var e=this;V.extend(e,{observer:{init:lt.init.bind(e),attach:lt.attach.bind(e),destroy:lt.destroy.bind(e),observers:[]}})},on:{init:function(){var e=this;e.observer.init()},destroy:function(){var e=this;e.observer.destroy()}}},pt={update:function(e){var t=this,a=t.params,i=a.slidesPerView,r=a.slidesPerGroup,s=a.centeredSlides,n=t.params.virtual,o=n.addSlidesBefore,l=n.addSlidesAfter,d=t.virtual,p=d.from,u=d.to,c=d.slides,h=d.slidesGrid,f=d.renderSlide,v=d.offset;t.updateActiveIndex();var m,g,w,b=t.activeIndex||0;m=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",s?(g=Math.floor(i/2)+r+o,w=Math.floor(i/2)+r+l):(g=i+(r-1)+o,w=r+l);var y=Math.max((b||0)-w,0),x=Math.min((b||0)+g,c.length-1),E=(t.slidesGrid[y]||0)-(t.slidesGrid[0]||0);function T(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.lazy&&t.params.lazy.enabled&&t.lazy.load()}if(V.extend(t.virtual,{from:y,to:x,offset:E,slidesGrid:t.slidesGrid}),p===y&&u===x&&!e)return t.slidesGrid!==h&&E!==v&&t.slides.css(m,E+"px"),void t.updateProgress();if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:E,from:y,to:x,slides:function(){for(var e=[],t=y;t<=x;t+=1)e.push(c[t]);return e}()}),void T();var C=[],S=[];if(e)t.$wrapperEl.find("."+t.params.slideClass).remove();else for(var M=p;M<=u;M+=1)(M<y||M>x)&&t.$wrapperEl.find("."+t.params.slideClass+'[data-swiper-slide-index="'+M+'"]').remove();for(var P=0;P<c.length;P+=1)P>=y&&P<=x&&("undefined"===typeof u||e?S.push(P):(P>u&&S.push(P),P<p&&C.push(P)));S.forEach((function(e){t.$wrapperEl.append(f(c[e],e))})),C.sort((function(e,t){return t-e})).forEach((function(e){t.$wrapperEl.prepend(f(c[e],e))})),t.$wrapperEl.children(".swiper-slide").css(m,E+"px"),T()},renderSlide:function(e,t){var a=this,r=a.params.virtual;if(r.cache&&a.virtual.cache[t])return a.virtual.cache[t];var s=r.renderSlide?i(r.renderSlide.call(a,e,t)):i('<div class="'+a.params.slideClass+'" data-swiper-slide-index="'+t+'">'+e+"</div>");return s.attr("data-swiper-slide-index")||s.attr("data-swiper-slide-index",t),r.cache&&(a.virtual.cache[t]=s),s},appendSlide:function(e){var t=this;if("object"===typeof e&&"length"in e)for(var a=0;a<e.length;a+=1)e[a]&&t.virtual.slides.push(e[a]);else t.virtual.slides.push(e);t.virtual.update(!0)},prependSlide:function(e){var t=this,a=t.activeIndex,i=a+1,r=1;if(Array.isArray(e)){for(var s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.unshift(e[s]);i=a+e.length,r=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){var n=t.virtual.cache,o={};Object.keys(n).forEach((function(e){o[parseInt(e,10)+r]=n[e]})),t.virtual.cache=o}t.virtual.update(!0),t.slideTo(i,0)},removeSlide:function(e){var t=this;if("undefined"!==typeof e&&null!==e){var a=t.activeIndex;if(Array.isArray(e))for(var i=e.length-1;i>=0;i-=1)t.virtual.slides.splice(e[i],1),t.params.virtual.cache&&delete t.virtual.cache[e[i]],e[i]<a&&(a-=1),a=Math.max(a,0);else t.virtual.slides.splice(e,1),t.params.virtual.cache&&delete t.virtual.cache[e],e<a&&(a-=1),a=Math.max(a,0);t.virtual.update(!0),t.slideTo(a,0)}},removeAllSlides:function(){var e=this;e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),e.virtual.update(!0),e.slideTo(0,0)}},ut={name:"virtual",params:{virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,addSlidesBefore:0,addSlidesAfter:0}},create:function(){var e=this;V.extend(e,{virtual:{update:pt.update.bind(e),appendSlide:pt.appendSlide.bind(e),prependSlide:pt.prependSlide.bind(e),removeSlide:pt.removeSlide.bind(e),removeAllSlides:pt.removeAllSlides.bind(e),renderSlide:pt.renderSlide.bind(e),slides:e.params.virtual.slides,cache:{}}})},on:{beforeInit:function(){var e=this;if(e.params.virtual.enabled){e.classNames.push(e.params.containerModifierClass+"virtual");var t={watchSlidesProgress:!0};V.extend(e.params,t),V.extend(e.originalParams,t),e.params.initialSlide||e.virtual.update()}},setTranslate:function(){var e=this;e.params.virtual.enabled&&e.virtual.update()}}},ct={handle:function(a){var i=this,r=i.rtlTranslate,s=a;s.originalEvent&&(s=s.originalEvent);var n=s.keyCode||s.charCode;if(!i.allowSlideNext&&(i.isHorizontal()&&39===n||i.isVertical()&&40===n||34===n))return!1;if(!i.allowSlidePrev&&(i.isHorizontal()&&37===n||i.isVertical()&&38===n||33===n))return!1;if(!(s.shiftKey||s.altKey||s.ctrlKey||s.metaKey)&&(!e.activeElement||!e.activeElement.nodeName||"input"!==e.activeElement.nodeName.toLowerCase()&&"textarea"!==e.activeElement.nodeName.toLowerCase())){if(i.params.keyboard.onlyInViewport&&(33===n||34===n||37===n||39===n||38===n||40===n)){var o=!1;if(i.$el.parents("."+i.params.slideClass).length>0&&0===i.$el.parents("."+i.params.slideActiveClass).length)return;var l=t.innerWidth,d=t.innerHeight,p=i.$el.offset();r&&(p.left-=i.$el[0].scrollLeft);for(var u=[[p.left,p.top],[p.left+i.width,p.top],[p.left,p.top+i.height],[p.left+i.width,p.top+i.height]],c=0;c<u.length;c+=1){var h=u[c];h[0]>=0&&h[0]<=l&&h[1]>=0&&h[1]<=d&&(o=!0)}if(!o)return}i.isHorizontal()?(33!==n&&34!==n&&37!==n&&39!==n||(s.preventDefault?s.preventDefault():s.returnValue=!1),(34!==n&&39!==n||r)&&(33!==n&&37!==n||!r)||i.slideNext(),(33!==n&&37!==n||r)&&(34!==n&&39!==n||!r)||i.slidePrev()):(33!==n&&34!==n&&38!==n&&40!==n||(s.preventDefault?s.preventDefault():s.returnValue=!1),34!==n&&40!==n||i.slideNext(),33!==n&&38!==n||i.slidePrev()),i.emit("keyPress",n)}},enable:function(){var t=this;t.keyboard.enabled||(i(e).on("keydown",t.keyboard.handle),t.keyboard.enabled=!0)},disable:function(){var t=this;t.keyboard.enabled&&(i(e).off("keydown",t.keyboard.handle),t.keyboard.enabled=!1)}},ht={name:"keyboard",params:{keyboard:{enabled:!1,onlyInViewport:!0}},create:function(){var e=this;V.extend(e,{keyboard:{enabled:!1,enable:ct.enable.bind(e),disable:ct.disable.bind(e),handle:ct.handle.bind(e)}})},on:{init:function(){var e=this;e.params.keyboard.enabled&&e.keyboard.enable()},destroy:function(){var e=this;e.keyboard.enabled&&e.keyboard.disable()}}};function ft(){var t="onwheel",a=t in e;if(!a){var i=e.createElement("div");i.setAttribute(t,"return;"),a="function"===typeof i[t]}return!a&&e.implementation&&e.implementation.hasFeature&&!0!==e.implementation.hasFeature("","")&&(a=e.implementation.hasFeature("Events.wheel","3.0")),a}var vt={lastScrollTime:V.now(),event:function(){return t.navigator.userAgent.indexOf("firefox")>-1?"DOMMouseScroll":ft()?"wheel":"mousewheel"}(),normalize:function(e){var t=10,a=40,i=800,r=0,s=0,n=0,o=0;return"detail"in e&&(s=e.detail),"wheelDelta"in e&&(s=-e.wheelDelta/120),"wheelDeltaY"in e&&(s=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(r=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(r=s,s=0),n=r*t,o=s*t,"deltaY"in e&&(o=e.deltaY),"deltaX"in e&&(n=e.deltaX),(n||o)&&e.deltaMode&&(1===e.deltaMode?(n*=a,o*=a):(n*=i,o*=i)),n&&!r&&(r=n<1?-1:1),o&&!s&&(s=o<1?-1:1),{spinX:r,spinY:s,pixelX:n,pixelY:o}},handleMouseEnter:function(){var e=this;e.mouseEntered=!0},handleMouseLeave:function(){var e=this;e.mouseEntered=!1},handle:function(e){var a=e,i=this,r=i.params.mousewheel;if(!i.mouseEntered&&!r.releaseOnEdges)return!0;a.originalEvent&&(a=a.originalEvent);var s=0,n=i.rtlTranslate?-1:1,o=vt.normalize(a);if(r.forceToAxis)if(i.isHorizontal()){if(!(Math.abs(o.pixelX)>Math.abs(o.pixelY)))return!0;s=o.pixelX*n}else{if(!(Math.abs(o.pixelY)>Math.abs(o.pixelX)))return!0;s=o.pixelY}else s=Math.abs(o.pixelX)>Math.abs(o.pixelY)?-o.pixelX*n:-o.pixelY;if(0===s)return!0;if(r.invert&&(s=-s),i.params.freeMode){i.params.loop&&i.loopFix();var l=i.getTranslate()+s*r.sensitivity,d=i.isBeginning,p=i.isEnd;if(l>=i.minTranslate()&&(l=i.minTranslate()),l<=i.maxTranslate()&&(l=i.maxTranslate()),i.setTransition(0),i.setTranslate(l),i.updateProgress(),i.updateActiveIndex(),i.updateSlidesClasses(),(!d&&i.isBeginning||!p&&i.isEnd)&&i.updateSlidesClasses(),i.params.freeModeSticky&&(clearTimeout(i.mousewheel.timeout),i.mousewheel.timeout=V.nextTick((function(){i.slideToClosest()}),300)),i.emit("scroll",a),i.params.autoplay&&i.params.autoplayDisableOnInteraction&&i.autoplay.stop(),l===i.minTranslate()||l===i.maxTranslate())return!0}else{if(V.now()-i.mousewheel.lastScrollTime>60)if(s<0)if(i.isEnd&&!i.params.loop||i.animating){if(r.releaseOnEdges)return!0}else i.slideNext(),i.emit("scroll",a);else if(i.isBeginning&&!i.params.loop||i.animating){if(r.releaseOnEdges)return!0}else i.slidePrev(),i.emit("scroll",a);i.mousewheel.lastScrollTime=(new t.Date).getTime()}return a.preventDefault?a.preventDefault():a.returnValue=!1,!1},enable:function(){var e=this;if(!vt.event)return!1;if(e.mousewheel.enabled)return!1;var t=e.$el;return"container"!==e.params.mousewheel.eventsTarged&&(t=i(e.params.mousewheel.eventsTarged)),t.on("mouseenter",e.mousewheel.handleMouseEnter),t.on("mouseleave",e.mousewheel.handleMouseLeave),t.on(vt.event,e.mousewheel.handle),e.mousewheel.enabled=!0,!0},disable:function(){var e=this;if(!vt.event)return!1;if(!e.mousewheel.enabled)return!1;var t=e.$el;return"container"!==e.params.mousewheel.eventsTarged&&(t=i(e.params.mousewheel.eventsTarged)),t.off(vt.event,e.mousewheel.handle),e.mousewheel.enabled=!1,!0}},mt={name:"mousewheel",params:{mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarged:"container"}},create:function(){var e=this;V.extend(e,{mousewheel:{enabled:!1,enable:vt.enable.bind(e),disable:vt.disable.bind(e),handle:vt.handle.bind(e),handleMouseEnter:vt.handleMouseEnter.bind(e),handleMouseLeave:vt.handleMouseLeave.bind(e),lastScrollTime:V.now()}})},on:{init:function(){var e=this;e.params.mousewheel.enabled&&e.mousewheel.enable()},destroy:function(){var e=this;e.mousewheel.enabled&&e.mousewheel.disable()}}},gt={update:function(){var e=this,t=e.params.navigation;if(!e.params.loop){var a=e.navigation,i=a.$nextEl,r=a.$prevEl;r&&r.length>0&&(e.isBeginning?r.addClass(t.disabledClass):r.removeClass(t.disabledClass),r[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](t.lockClass)),i&&i.length>0&&(e.isEnd?i.addClass(t.disabledClass):i.removeClass(t.disabledClass),i[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](t.lockClass))}},onPrevClick:function(e){var t=this;e.preventDefault(),t.isBeginning&&!t.params.loop||t.slidePrev()},onNextClick:function(e){var t=this;e.preventDefault(),t.isEnd&&!t.params.loop||t.slideNext()},init:function(){var e,t,a=this,r=a.params.navigation;(r.nextEl||r.prevEl)&&(r.nextEl&&(e=i(r.nextEl),a.params.uniqueNavElements&&"string"===typeof r.nextEl&&e.length>1&&1===a.$el.find(r.nextEl).length&&(e=a.$el.find(r.nextEl))),r.prevEl&&(t=i(r.prevEl),a.params.uniqueNavElements&&"string"===typeof r.prevEl&&t.length>1&&1===a.$el.find(r.prevEl).length&&(t=a.$el.find(r.prevEl))),e&&e.length>0&&e.on("click",a.navigation.onNextClick),t&&t.length>0&&t.on("click",a.navigation.onPrevClick),V.extend(a.navigation,{$nextEl:e,nextEl:e&&e[0],$prevEl:t,prevEl:t&&t[0]}))},destroy:function(){var e=this,t=e.navigation,a=t.$nextEl,i=t.$prevEl;a&&a.length&&(a.off("click",e.navigation.onNextClick),a.removeClass(e.params.navigation.disabledClass)),i&&i.length&&(i.off("click",e.navigation.onPrevClick),i.removeClass(e.params.navigation.disabledClass))}},wt={name:"navigation",params:{navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}},create:function(){var e=this;V.extend(e,{navigation:{init:gt.init.bind(e),update:gt.update.bind(e),destroy:gt.destroy.bind(e),onNextClick:gt.onNextClick.bind(e),onPrevClick:gt.onPrevClick.bind(e)}})},on:{init:function(){var e=this;e.navigation.init(),e.navigation.update()},toEdge:function(){var e=this;e.navigation.update()},fromEdge:function(){var e=this;e.navigation.update()},destroy:function(){var e=this;e.navigation.destroy()},click:function(e){var t,a=this,r=a.navigation,s=r.$nextEl,n=r.$prevEl;!a.params.navigation.hideOnClick||i(e.target).is(n)||i(e.target).is(s)||(s?t=s.hasClass(a.params.navigation.hiddenClass):n&&(t=n.hasClass(a.params.navigation.hiddenClass)),!0===t?a.emit("navigationShow",a):a.emit("navigationHide",a),s&&s.toggleClass(a.params.navigation.hiddenClass),n&&n.toggleClass(a.params.navigation.hiddenClass))}}},bt={update:function(){var e=this,t=e.rtl,a=e.params.pagination;if(a.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var r,s=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,n=e.pagination.$el,o=e.params.loop?Math.ceil((s-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(r=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup),r>s-1-2*e.loopedSlides&&(r-=s-2*e.loopedSlides),r>o-1&&(r-=o),r<0&&"bullets"!==e.params.paginationType&&(r=o+r)):r="undefined"!==typeof e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===a.type&&e.pagination.bullets&&e.pagination.bullets.length>0){var l,d,p,u=e.pagination.bullets;if(a.dynamicBullets&&(e.pagination.bulletSize=u.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),n.css(e.isHorizontal()?"width":"height",e.pagination.bulletSize*(a.dynamicMainBullets+4)+"px"),a.dynamicMainBullets>1&&void 0!==e.previousIndex&&(e.pagination.dynamicBulletIndex+=r-e.previousIndex,e.pagination.dynamicBulletIndex>a.dynamicMainBullets-1?e.pagination.dynamicBulletIndex=a.dynamicMainBullets-1:e.pagination.dynamicBulletIndex<0&&(e.pagination.dynamicBulletIndex=0)),l=r-e.pagination.dynamicBulletIndex,d=l+(Math.min(u.length,a.dynamicMainBullets)-1),p=(d+l)/2),u.removeClass(a.bulletActiveClass+" "+a.bulletActiveClass+"-next "+a.bulletActiveClass+"-next-next "+a.bulletActiveClass+"-prev "+a.bulletActiveClass+"-prev-prev "+a.bulletActiveClass+"-main"),n.length>1)u.each((function(e,t){var s=i(t),n=s.index();n===r&&s.addClass(a.bulletActiveClass),a.dynamicBullets&&(n>=l&&n<=d&&s.addClass(a.bulletActiveClass+"-main"),n===l&&s.prev().addClass(a.bulletActiveClass+"-prev").prev().addClass(a.bulletActiveClass+"-prev-prev"),n===d&&s.next().addClass(a.bulletActiveClass+"-next").next().addClass(a.bulletActiveClass+"-next-next"))}));else{var c=u.eq(r);if(c.addClass(a.bulletActiveClass),a.dynamicBullets){for(var h=u.eq(l),f=u.eq(d),v=l;v<=d;v+=1)u.eq(v).addClass(a.bulletActiveClass+"-main");h.prev().addClass(a.bulletActiveClass+"-prev").prev().addClass(a.bulletActiveClass+"-prev-prev"),f.next().addClass(a.bulletActiveClass+"-next").next().addClass(a.bulletActiveClass+"-next-next")}}if(a.dynamicBullets){var m=Math.min(u.length,a.dynamicMainBullets+4),g=(e.pagination.bulletSize*m-e.pagination.bulletSize)/2-p*e.pagination.bulletSize,w=t?"right":"left";u.css(e.isHorizontal()?w:"top",g+"px")}}if("fraction"===a.type&&(n.find("."+a.currentClass).text(a.formatFractionCurrent(r+1)),n.find("."+a.totalClass).text(a.formatFractionTotal(o))),"progressbar"===a.type){var b;b=a.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";var y=(r+1)/o,x=1,E=1;"horizontal"===b?x=y:E=y,n.find("."+a.progressbarFillClass).transform("translate3d(0,0,0) scaleX("+x+") scaleY("+E+")").transition(e.params.speed)}"custom"===a.type&&a.renderCustom?(n.html(a.renderCustom(e,r+1,o)),e.emit("paginationRender",e,n[0])):e.emit("paginationUpdate",e,n[0]),n[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](a.lockClass)}},render:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var a=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el,r="";if("bullets"===t.type){for(var s=e.params.loop?Math.ceil((a-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length,n=0;n<s;n+=1)t.renderBullet?r+=t.renderBullet.call(e,n,t.bulletClass):r+="<"+t.bulletElement+' class="'+t.bulletClass+'"></'+t.bulletElement+">";i.html(r),e.pagination.bullets=i.find("."+t.bulletClass)}"fraction"===t.type&&(r=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):'<span class="'+t.currentClass+'"></span> / <span class="'+t.totalClass+'"></span>',i.html(r)),"progressbar"===t.type&&(r=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):'<span class="'+t.progressbarFillClass+'"></span>',i.html(r)),"custom"!==t.type&&e.emit("paginationRender",e.pagination.$el[0])}},init:function(){var e=this,t=e.params.pagination;if(t.el){var a=i(t.el);0!==a.length&&(e.params.uniqueNavElements&&"string"===typeof t.el&&a.length>1&&1===e.$el.find(t.el).length&&(a=e.$el.find(t.el)),"bullets"===t.type&&t.clickable&&a.addClass(t.clickableClass),a.addClass(t.modifierClass+t.type),"bullets"===t.type&&t.dynamicBullets&&(a.addClass(""+t.modifierClass+t.type+"-dynamic"),e.pagination.dynamicBulletIndex=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&a.addClass(t.progressbarOppositeClass),t.clickable&&a.on("click","."+t.bulletClass,(function(t){t.preventDefault();var a=i(this).index()*e.params.slidesPerGroup;e.params.loop&&(a+=e.loopedSlides),e.slideTo(a)})),V.extend(e.pagination,{$el:a,el:a[0]}))}},destroy:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var a=e.pagination.$el;a.removeClass(t.hiddenClass),a.removeClass(t.modifierClass+t.type),e.pagination.bullets&&e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&a.off("click","."+t.bulletClass)}}},yt={name:"pagination",params:{pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",modifierClass:"swiper-pagination-",currentClass:"swiper-pagination-current",totalClass:"swiper-pagination-total",hiddenClass:"swiper-pagination-hidden",progressbarFillClass:"swiper-pagination-progressbar-fill",progressbarOppositeClass:"swiper-pagination-progressbar-opposite",clickableClass:"swiper-pagination-clickable",lockClass:"swiper-pagination-lock"}},create:function(){var e=this;V.extend(e,{pagination:{init:bt.init.bind(e),render:bt.render.bind(e),update:bt.update.bind(e),destroy:bt.destroy.bind(e),dynamicBulletIndex:0}})},on:{init:function(){var e=this;e.pagination.init(),e.pagination.render(),e.pagination.update()},activeIndexChange:function(){var e=this;(e.params.loop||"undefined"===typeof e.snapIndex)&&e.pagination.update()},snapIndexChange:function(){var e=this;e.params.loop||e.pagination.update()},slidesLengthChange:function(){var e=this;e.params.loop&&(e.pagination.render(),e.pagination.update())},snapGridLengthChange:function(){var e=this;e.params.loop||(e.pagination.render(),e.pagination.update())},destroy:function(){var e=this;e.pagination.destroy()},click:function(e){var t=this;if(t.params.pagination.el&&t.params.pagination.hideOnClick&&t.pagination.$el.length>0&&!i(e.target).hasClass(t.params.pagination.bulletClass)){var a=t.pagination.$el.hasClass(t.params.pagination.hiddenClass);!0===a?t.emit("paginationShow",t):t.emit("paginationHide",t),t.pagination.$el.toggleClass(t.params.pagination.hiddenClass)}}}},xt={setTranslate:function(){var e=this;if(e.params.scrollbar.el&&e.scrollbar.el){var t=e.scrollbar,a=e.rtlTranslate,i=e.progress,r=t.dragSize,s=t.trackSize,n=t.$dragEl,o=t.$el,l=e.params.scrollbar,d=r,p=(s-r)*i;a?(p=-p,p>0?(d=r-p,p=0):-p+r>s&&(d=s+p)):p<0?(d=r+p,p=0):p+r>s&&(d=s-p),e.isHorizontal()?(Y.transforms3d?n.transform("translate3d("+p+"px, 0, 0)"):n.transform("translateX("+p+"px)"),n[0].style.width=d+"px"):(Y.transforms3d?n.transform("translate3d(0px, "+p+"px, 0)"):n.transform("translateY("+p+"px)"),n[0].style.height=d+"px"),l.hide&&(clearTimeout(e.scrollbar.timeout),o[0].style.opacity=1,e.scrollbar.timeout=setTimeout((function(){o[0].style.opacity=0,o.transition(400)}),1e3))}},setTransition:function(e){var t=this;t.params.scrollbar.el&&t.scrollbar.el&&t.scrollbar.$dragEl.transition(e)},updateSize:function(){var e=this;if(e.params.scrollbar.el&&e.scrollbar.el){var t=e.scrollbar,a=t.$dragEl,i=t.$el;a[0].style.width="",a[0].style.height="";var r,s=e.isHorizontal()?i[0].offsetWidth:i[0].offsetHeight,n=e.size/e.virtualSize,o=n*(s/e.size);r="auto"===e.params.scrollbar.dragSize?s*n:parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?a[0].style.width=r+"px":a[0].style.height=r+"px",i[0].style.display=n>=1?"none":"",e.params.scrollbar.hide&&(i[0].style.opacity=0),V.extend(t,{trackSize:s,divider:n,moveDivider:o,dragSize:r}),t.$el[e.params.watchOverflow&&e.isLocked?"addClass":"removeClass"](e.params.scrollbar.lockClass)}},getPointerPosition:function(e){var t=this;return t.isHorizontal()?"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].pageX:e.pageX||e.clientX:"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].pageY:e.pageY||e.clientY},setDragPosition:function(e){var t,a=this,i=a.scrollbar,r=a.rtlTranslate,s=i.$el,n=i.dragSize,o=i.trackSize,l=i.dragStartPos;t=(i.getPointerPosition(e)-s.offset()[a.isHorizontal()?"left":"top"]-(null!==l?l:n/2))/(o-n),t=Math.max(Math.min(t,1),0),r&&(t=1-t);var d=a.minTranslate()+(a.maxTranslate()-a.minTranslate())*t;a.updateProgress(d),a.setTranslate(d),a.updateActiveIndex(),a.updateSlidesClasses()},onDragStart:function(e){var t=this,a=t.params.scrollbar,i=t.scrollbar,r=t.$wrapperEl,s=i.$el,n=i.$dragEl;t.scrollbar.isTouched=!0,t.scrollbar.dragStartPos=e.target===n[0]||e.target===n?i.getPointerPosition(e)-e.target.getBoundingClientRect()[t.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),r.transition(100),n.transition(100),i.setDragPosition(e),clearTimeout(t.scrollbar.dragTimeout),s.transition(0),a.hide&&s.css("opacity",1),t.emit("scrollbarDragStart",e)},onDragMove:function(e){var t=this,a=t.scrollbar,i=t.$wrapperEl,r=a.$el,s=a.$dragEl;t.scrollbar.isTouched&&(e.preventDefault?e.preventDefault():e.returnValue=!1,a.setDragPosition(e),i.transition(0),r.transition(0),s.transition(0),t.emit("scrollbarDragMove",e))},onDragEnd:function(e){var t=this,a=t.params.scrollbar,i=t.scrollbar,r=i.$el;t.scrollbar.isTouched&&(t.scrollbar.isTouched=!1,a.hide&&(clearTimeout(t.scrollbar.dragTimeout),t.scrollbar.dragTimeout=V.nextTick((function(){r.css("opacity",0),r.transition(400)}),1e3)),t.emit("scrollbarDragEnd",e),a.snapOnRelease&&t.slideToClosest())},enableDraggable:function(){var t=this;if(t.params.scrollbar.el){var a=t.scrollbar,i=t.touchEventsTouch,r=t.touchEventsDesktop,s=t.params,n=a.$el,o=n[0],l=!(!Y.passiveListener||!s.passiveListeners)&&{passive:!1,capture:!1},d=!(!Y.passiveListener||!s.passiveListeners)&&{passive:!0,capture:!1};Y.touch?(o.addEventListener(i.start,t.scrollbar.onDragStart,l),o.addEventListener(i.move,t.scrollbar.onDragMove,l),o.addEventListener(i.end,t.scrollbar.onDragEnd,d)):(o.addEventListener(r.start,t.scrollbar.onDragStart,l),e.addEventListener(r.move,t.scrollbar.onDragMove,l),e.addEventListener(r.end,t.scrollbar.onDragEnd,d))}},disableDraggable:function(){var t=this;if(t.params.scrollbar.el){var a=t.scrollbar,i=t.touchEventsTouch,r=t.touchEventsDesktop,s=t.params,n=a.$el,o=n[0],l=!(!Y.passiveListener||!s.passiveListeners)&&{passive:!1,capture:!1},d=!(!Y.passiveListener||!s.passiveListeners)&&{passive:!0,capture:!1};Y.touch?(o.removeEventListener(i.start,t.scrollbar.onDragStart,l),o.removeEventListener(i.move,t.scrollbar.onDragMove,l),o.removeEventListener(i.end,t.scrollbar.onDragEnd,d)):(o.removeEventListener(r.start,t.scrollbar.onDragStart,l),e.removeEventListener(r.move,t.scrollbar.onDragMove,l),e.removeEventListener(r.end,t.scrollbar.onDragEnd,d))}},init:function(){var e=this;if(e.params.scrollbar.el){var t=e.scrollbar,a=e.$el,r=e.params.scrollbar,s=i(r.el);e.params.uniqueNavElements&&"string"===typeof r.el&&s.length>1&&1===a.find(r.el).length&&(s=a.find(r.el));var n=s.find("."+e.params.scrollbar.dragClass);0===n.length&&(n=i('<div class="'+e.params.scrollbar.dragClass+'"></div>'),s.append(n)),V.extend(t,{$el:s,el:s[0],$dragEl:n,dragEl:n[0]}),r.draggable&&t.enableDraggable()}},destroy:function(){var e=this;e.scrollbar.disableDraggable()}},Et={name:"scrollbar",params:{scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag"}},create:function(){var e=this;V.extend(e,{scrollbar:{init:xt.init.bind(e),destroy:xt.destroy.bind(e),updateSize:xt.updateSize.bind(e),setTranslate:xt.setTranslate.bind(e),setTransition:xt.setTransition.bind(e),enableDraggable:xt.enableDraggable.bind(e),disableDraggable:xt.disableDraggable.bind(e),setDragPosition:xt.setDragPosition.bind(e),getPointerPosition:xt.getPointerPosition.bind(e),onDragStart:xt.onDragStart.bind(e),onDragMove:xt.onDragMove.bind(e),onDragEnd:xt.onDragEnd.bind(e),isTouched:!1,timeout:null,dragTimeout:null}})},on:{init:function(){var e=this;e.scrollbar.init(),e.scrollbar.updateSize(),e.scrollbar.setTranslate()},update:function(){var e=this;e.scrollbar.updateSize()},resize:function(){var e=this;e.scrollbar.updateSize()},observerUpdate:function(){var e=this;e.scrollbar.updateSize()},setTranslate:function(){var e=this;e.scrollbar.setTranslate()},setTransition:function(e){var t=this;t.scrollbar.setTransition(e)},destroy:function(){var e=this;e.scrollbar.destroy()}}},Tt={setTransform:function(e,t){var a=this,r=a.rtl,s=i(e),n=r?-1:1,o=s.attr("data-swiper-parallax")||"0",l=s.attr("data-swiper-parallax-x"),d=s.attr("data-swiper-parallax-y"),p=s.attr("data-swiper-parallax-scale"),u=s.attr("data-swiper-parallax-opacity");if(l||d?(l=l||"0",d=d||"0"):a.isHorizontal()?(l=o,d="0"):(d=o,l="0"),l=l.indexOf("%")>=0?parseInt(l,10)*t*n+"%":l*t*n+"px",d=d.indexOf("%")>=0?parseInt(d,10)*t+"%":d*t+"px","undefined"!==typeof u&&null!==u){var c=u-(u-1)*(1-Math.abs(t));s[0].style.opacity=c}if("undefined"===typeof p||null===p)s.transform("translate3d("+l+", "+d+", 0px)");else{var h=p-(p-1)*(1-Math.abs(t));s.transform("translate3d("+l+", "+d+", 0px) scale("+h+")")}},setTranslate:function(){var e=this,t=e.$el,a=e.slides,r=e.progress,s=e.snapGrid;t.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((function(t,a){e.parallax.setTransform(a,r)})),a.each((function(t,a){var n=a.progress;e.params.slidesPerGroup>1&&"auto"!==e.params.slidesPerView&&(n+=Math.ceil(t/2)-r*(s.length-1)),n=Math.min(Math.max(n,-1),1),i(a).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((function(t,a){e.parallax.setTransform(a,n)}))}))},setTransition:function(e){void 0===e&&(e=this.params.speed);var t=this,a=t.$el;a.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((function(t,a){var r=i(a),s=parseInt(r.attr("data-swiper-parallax-duration"),10)||e;0===e&&(s=0),r.transition(s)}))}},Ct={name:"parallax",params:{parallax:{enabled:!1}},create:function(){var e=this;V.extend(e,{parallax:{setTransform:Tt.setTransform.bind(e),setTranslate:Tt.setTranslate.bind(e),setTransition:Tt.setTransition.bind(e)}})},on:{beforeInit:function(){var e=this;e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},init:function(){var e=this;e.params.parallax.enabled&&e.parallax.setTranslate()},setTranslate:function(){var e=this;e.params.parallax.enabled&&e.parallax.setTranslate()},setTransition:function(e){var t=this;t.params.parallax.enabled&&t.parallax.setTransition(e)}}},St={getDistanceBetweenTouches:function(e){if(e.targetTouches.length<2)return 1;var t=e.targetTouches[0].pageX,a=e.targetTouches[0].pageY,i=e.targetTouches[1].pageX,r=e.targetTouches[1].pageY,s=Math.sqrt(Math.pow(i-t,2)+Math.pow(r-a,2));return s},onGestureStart:function(e){var t=this,a=t.params.zoom,r=t.zoom,s=r.gesture;if(r.fakeGestureTouched=!1,r.fakeGestureMoved=!1,!Y.gestures){if("touchstart"!==e.type||"touchstart"===e.type&&e.targetTouches.length<2)return;r.fakeGestureTouched=!0,s.scaleStart=St.getDistanceBetweenTouches(e)}s.$slideEl&&s.$slideEl.length||(s.$slideEl=i(e.target).closest(".swiper-slide"),0===s.$slideEl.length&&(s.$slideEl=t.slides.eq(t.activeIndex)),s.$imageEl=s.$slideEl.find("img, svg, canvas"),s.$imageWrapEl=s.$imageEl.parent("."+a.containerClass),s.maxRatio=s.$imageWrapEl.attr("data-swiper-zoom")||a.maxRatio,0!==s.$imageWrapEl.length)?(s.$imageEl.transition(0),t.zoom.isScaling=!0):s.$imageEl=void 0},onGestureChange:function(e){var t=this,a=t.params.zoom,i=t.zoom,r=i.gesture;if(!Y.gestures){if("touchmove"!==e.type||"touchmove"===e.type&&e.targetTouches.length<2)return;i.fakeGestureMoved=!0,r.scaleMove=St.getDistanceBetweenTouches(e)}r.$imageEl&&0!==r.$imageEl.length&&(Y.gestures?i.scale=e.scale*i.currentScale:i.scale=r.scaleMove/r.scaleStart*i.currentScale,i.scale>r.maxRatio&&(i.scale=r.maxRatio-1+Math.pow(i.scale-r.maxRatio+1,.5)),i.scale<a.minRatio&&(i.scale=a.minRatio+1-Math.pow(a.minRatio-i.scale+1,.5)),r.$imageEl.transform("translate3d(0,0,0) scale("+i.scale+")"))},onGestureEnd:function(e){var t=this,a=t.params.zoom,i=t.zoom,r=i.gesture;if(!Y.gestures){if(!i.fakeGestureTouched||!i.fakeGestureMoved)return;if("touchend"!==e.type||"touchend"===e.type&&e.changedTouches.length<2&&!Ae.android)return;i.fakeGestureTouched=!1,i.fakeGestureMoved=!1}r.$imageEl&&0!==r.$imageEl.length&&(i.scale=Math.max(Math.min(i.scale,r.maxRatio),a.minRatio),r.$imageEl.transition(t.params.speed).transform("translate3d(0,0,0) scale("+i.scale+")"),i.currentScale=i.scale,i.isScaling=!1,1===i.scale&&(r.$slideEl=void 0))},onTouchStart:function(e){var t=this,a=t.zoom,i=a.gesture,r=a.image;i.$imageEl&&0!==i.$imageEl.length&&(r.isTouched||(Ae.android&&e.preventDefault(),r.isTouched=!0,r.touchesStart.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,r.touchesStart.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY))},onTouchMove:function(e){var t=this,a=t.zoom,i=a.gesture,r=a.image,s=a.velocity;if(i.$imageEl&&0!==i.$imageEl.length&&(t.allowClick=!1,r.isTouched&&i.$slideEl)){r.isMoved||(r.width=i.$imageEl[0].offsetWidth,r.height=i.$imageEl[0].offsetHeight,r.startX=V.getTranslate(i.$imageWrapEl[0],"x")||0,r.startY=V.getTranslate(i.$imageWrapEl[0],"y")||0,i.slideWidth=i.$slideEl[0].offsetWidth,i.slideHeight=i.$slideEl[0].offsetHeight,i.$imageWrapEl.transition(0),t.rtl&&(r.startX=-r.startX,r.startY=-r.startY));var n=r.width*a.scale,o=r.height*a.scale;if(!(n<i.slideWidth&&o<i.slideHeight)){if(r.minX=Math.min(i.slideWidth/2-n/2,0),r.maxX=-r.minX,r.minY=Math.min(i.slideHeight/2-o/2,0),r.maxY=-r.minY,r.touchesCurrent.x="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,r.touchesCurrent.y="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,!r.isMoved&&!a.isScaling){if(t.isHorizontal()&&(Math.floor(r.minX)===Math.floor(r.startX)&&r.touchesCurrent.x<r.touchesStart.x||Math.floor(r.maxX)===Math.floor(r.startX)&&r.touchesCurrent.x>r.touchesStart.x))return void(r.isTouched=!1);if(!t.isHorizontal()&&(Math.floor(r.minY)===Math.floor(r.startY)&&r.touchesCurrent.y<r.touchesStart.y||Math.floor(r.maxY)===Math.floor(r.startY)&&r.touchesCurrent.y>r.touchesStart.y))return void(r.isTouched=!1)}e.preventDefault(),e.stopPropagation(),r.isMoved=!0,r.currentX=r.touchesCurrent.x-r.touchesStart.x+r.startX,r.currentY=r.touchesCurrent.y-r.touchesStart.y+r.startY,r.currentX<r.minX&&(r.currentX=r.minX+1-Math.pow(r.minX-r.currentX+1,.8)),r.currentX>r.maxX&&(r.currentX=r.maxX-1+Math.pow(r.currentX-r.maxX+1,.8)),r.currentY<r.minY&&(r.currentY=r.minY+1-Math.pow(r.minY-r.currentY+1,.8)),r.currentY>r.maxY&&(r.currentY=r.maxY-1+Math.pow(r.currentY-r.maxY+1,.8)),s.prevPositionX||(s.prevPositionX=r.touchesCurrent.x),s.prevPositionY||(s.prevPositionY=r.touchesCurrent.y),s.prevTime||(s.prevTime=Date.now()),s.x=(r.touchesCurrent.x-s.prevPositionX)/(Date.now()-s.prevTime)/2,s.y=(r.touchesCurrent.y-s.prevPositionY)/(Date.now()-s.prevTime)/2,Math.abs(r.touchesCurrent.x-s.prevPositionX)<2&&(s.x=0),Math.abs(r.touchesCurrent.y-s.prevPositionY)<2&&(s.y=0),s.prevPositionX=r.touchesCurrent.x,s.prevPositionY=r.touchesCurrent.y,s.prevTime=Date.now(),i.$imageWrapEl.transform("translate3d("+r.currentX+"px, "+r.currentY+"px,0)")}}},onTouchEnd:function(){var e=this,t=e.zoom,a=t.gesture,i=t.image,r=t.velocity;if(a.$imageEl&&0!==a.$imageEl.length){if(!i.isTouched||!i.isMoved)return i.isTouched=!1,void(i.isMoved=!1);i.isTouched=!1,i.isMoved=!1;var s=300,n=300,o=r.x*s,l=i.currentX+o,d=r.y*n,p=i.currentY+d;0!==r.x&&(s=Math.abs((l-i.currentX)/r.x)),0!==r.y&&(n=Math.abs((p-i.currentY)/r.y));var u=Math.max(s,n);i.currentX=l,i.currentY=p;var c=i.width*t.scale,h=i.height*t.scale;i.minX=Math.min(a.slideWidth/2-c/2,0),i.maxX=-i.minX,i.minY=Math.min(a.slideHeight/2-h/2,0),i.maxY=-i.minY,i.currentX=Math.max(Math.min(i.currentX,i.maxX),i.minX),i.currentY=Math.max(Math.min(i.currentY,i.maxY),i.minY),a.$imageWrapEl.transition(u).transform("translate3d("+i.currentX+"px, "+i.currentY+"px,0)")}},onTransitionEnd:function(){var e=this,t=e.zoom,a=t.gesture;a.$slideEl&&e.previousIndex!==e.activeIndex&&(a.$imageEl.transform("translate3d(0,0,0) scale(1)"),a.$imageWrapEl.transform("translate3d(0,0,0)"),t.scale=1,t.currentScale=1,a.$slideEl=void 0,a.$imageEl=void 0,a.$imageWrapEl=void 0)},toggle:function(e){var t=this,a=t.zoom;a.scale&&1!==a.scale?a.out():a.in(e)},in:function(e){var t,a,r,s,n,o,l,d,p,u,c,h,f,v,m,g,w,b,y=this,x=y.zoom,E=y.params.zoom,T=x.gesture,C=x.image;(T.$slideEl||(T.$slideEl=y.clickedSlide?i(y.clickedSlide):y.slides.eq(y.activeIndex),T.$imageEl=T.$slideEl.find("img, svg, canvas"),T.$imageWrapEl=T.$imageEl.parent("."+E.containerClass)),T.$imageEl&&0!==T.$imageEl.length)&&(T.$slideEl.addClass(""+E.zoomedSlideClass),"undefined"===typeof C.touchesStart.x&&e?(t="touchend"===e.type?e.changedTouches[0].pageX:e.pageX,a="touchend"===e.type?e.changedTouches[0].pageY:e.pageY):(t=C.touchesStart.x,a=C.touchesStart.y),x.scale=T.$imageWrapEl.attr("data-swiper-zoom")||E.maxRatio,x.currentScale=T.$imageWrapEl.attr("data-swiper-zoom")||E.maxRatio,e?(w=T.$slideEl[0].offsetWidth,b=T.$slideEl[0].offsetHeight,r=T.$slideEl.offset().left,s=T.$slideEl.offset().top,n=r+w/2-t,o=s+b/2-a,p=T.$imageEl[0].offsetWidth,u=T.$imageEl[0].offsetHeight,c=p*x.scale,h=u*x.scale,f=Math.min(w/2-c/2,0),v=Math.min(b/2-h/2,0),m=-f,g=-v,l=n*x.scale,d=o*x.scale,l<f&&(l=f),l>m&&(l=m),d<v&&(d=v),d>g&&(d=g)):(l=0,d=0),T.$imageWrapEl.transition(300).transform("translate3d("+l+"px, "+d+"px,0)"),T.$imageEl.transition(300).transform("translate3d(0,0,0) scale("+x.scale+")"))},out:function(){var e=this,t=e.zoom,a=e.params.zoom,r=t.gesture;r.$slideEl||(r.$slideEl=e.clickedSlide?i(e.clickedSlide):e.slides.eq(e.activeIndex),r.$imageEl=r.$slideEl.find("img, svg, canvas"),r.$imageWrapEl=r.$imageEl.parent("."+a.containerClass)),r.$imageEl&&0!==r.$imageEl.length&&(t.scale=1,t.currentScale=1,r.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),r.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),r.$slideEl.removeClass(""+a.zoomedSlideClass),r.$slideEl=void 0)},enable:function(){var e=this,t=e.zoom;if(!t.enabled){t.enabled=!0;var a=!("touchstart"!==e.touchEvents.start||!Y.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1};Y.gestures?(e.$wrapperEl.on("gesturestart",".swiper-slide",t.onGestureStart,a),e.$wrapperEl.on("gesturechange",".swiper-slide",t.onGestureChange,a),e.$wrapperEl.on("gestureend",".swiper-slide",t.onGestureEnd,a)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.on(e.touchEvents.start,".swiper-slide",t.onGestureStart,a),e.$wrapperEl.on(e.touchEvents.move,".swiper-slide",t.onGestureChange,a),e.$wrapperEl.on(e.touchEvents.end,".swiper-slide",t.onGestureEnd,a)),e.$wrapperEl.on(e.touchEvents.move,"."+e.params.zoom.containerClass,t.onTouchMove)}},disable:function(){var e=this,t=e.zoom;if(t.enabled){e.zoom.enabled=!1;var a=!("touchstart"!==e.touchEvents.start||!Y.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1};Y.gestures?(e.$wrapperEl.off("gesturestart",".swiper-slide",t.onGestureStart,a),e.$wrapperEl.off("gesturechange",".swiper-slide",t.onGestureChange,a),e.$wrapperEl.off("gestureend",".swiper-slide",t.onGestureEnd,a)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.off(e.touchEvents.start,".swiper-slide",t.onGestureStart,a),e.$wrapperEl.off(e.touchEvents.move,".swiper-slide",t.onGestureChange,a),e.$wrapperEl.off(e.touchEvents.end,".swiper-slide",t.onGestureEnd,a)),e.$wrapperEl.off(e.touchEvents.move,"."+e.params.zoom.containerClass,t.onTouchMove)}}},Mt={name:"zoom",params:{zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}},create:function(){var e=this,t={enabled:!1,scale:1,currentScale:1,isScaling:!1,gesture:{$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},image:{isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},velocity:{x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0}};"onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out".split(" ").forEach((function(a){t[a]=St[a].bind(e)})),V.extend(e,{zoom:t});var a=1;Object.defineProperty(e.zoom,"scale",{get:function(){return a},set:function(t){if(a!==t){var i=e.zoom.gesture.$imageEl?e.zoom.gesture.$imageEl[0]:void 0,r=e.zoom.gesture.$slideEl?e.zoom.gesture.$slideEl[0]:void 0;e.emit("zoomChange",t,i,r)}a=t}})},on:{init:function(){var e=this;e.params.zoom.enabled&&e.zoom.enable()},destroy:function(){var e=this;e.zoom.disable()},touchStart:function(e){var t=this;t.zoom.enabled&&t.zoom.onTouchStart(e)},touchEnd:function(e){var t=this;t.zoom.enabled&&t.zoom.onTouchEnd(e)},doubleTap:function(e){var t=this;t.params.zoom.enabled&&t.zoom.enabled&&t.params.zoom.toggle&&t.zoom.toggle(e)},transitionEnd:function(){var e=this;e.zoom.enabled&&e.params.zoom.enabled&&e.zoom.onTransitionEnd()}}},Pt={loadInSlide:function(e,t){void 0===t&&(t=!0);var a=this,r=a.params.lazy;if("undefined"!==typeof e&&0!==a.slides.length){var s=a.virtual&&a.params.virtual.enabled,n=s?a.$wrapperEl.children("."+a.params.slideClass+'[data-swiper-slide-index="'+e+'"]'):a.slides.eq(e),o=n.find("."+r.elementClass+":not(."+r.loadedClass+"):not(."+r.loadingClass+")");!n.hasClass(r.elementClass)||n.hasClass(r.loadedClass)||n.hasClass(r.loadingClass)||(o=o.add(n[0])),0!==o.length&&o.each((function(e,s){var o=i(s);o.addClass(r.loadingClass);var l=o.attr("data-background"),d=o.attr("data-src"),p=o.attr("data-srcset"),u=o.attr("data-sizes");a.loadImage(o[0],d||l,p,u,!1,(function(){if("undefined"!==typeof a&&null!==a&&a&&(!a||a.params)&&!a.destroyed){if(l?(o.css("background-image",'url("'+l+'")'),o.removeAttr("data-background")):(p&&(o.attr("srcset",p),o.removeAttr("data-srcset")),u&&(o.attr("sizes",u),o.removeAttr("data-sizes")),d&&(o.attr("src",d),o.removeAttr("data-src"))),o.addClass(r.loadedClass).removeClass(r.loadingClass),n.find("."+r.preloaderClass).remove(),a.params.loop&&t){var e=n.attr("data-swiper-slide-index");if(n.hasClass(a.params.slideDuplicateClass)){var i=a.$wrapperEl.children('[data-swiper-slide-index="'+e+'"]:not(.'+a.params.slideDuplicateClass+")");a.lazy.loadInSlide(i.index(),!1)}else{var s=a.$wrapperEl.children("."+a.params.slideDuplicateClass+'[data-swiper-slide-index="'+e+'"]');a.lazy.loadInSlide(s.index(),!1)}}a.emit("lazyImageReady",n[0],o[0])}})),a.emit("lazyImageLoad",n[0],o[0])}))}},load:function(){var e=this,t=e.$wrapperEl,a=e.params,r=e.slides,s=e.activeIndex,n=e.virtual&&a.virtual.enabled,o=a.lazy,l=a.slidesPerView;function d(e){if(n){if(t.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]').length)return!0}else if(r[e])return!0;return!1}function p(e){return n?i(e).attr("data-swiper-slide-index"):i(e).index()}if("auto"===l&&(l=0),e.lazy.initialImageLoaded||(e.lazy.initialImageLoaded=!0),e.params.watchSlidesVisibility)t.children("."+a.slideVisibleClass).each((function(t,a){var r=n?i(a).attr("data-swiper-slide-index"):i(a).index();e.lazy.loadInSlide(r)}));else if(l>1)for(var u=s;u<s+l;u+=1)d(u)&&e.lazy.loadInSlide(u);else e.lazy.loadInSlide(s);if(o.loadPrevNext)if(l>1||o.loadPrevNextAmount&&o.loadPrevNextAmount>1){for(var c=o.loadPrevNextAmount,h=l,f=Math.min(s+h+Math.max(c,h),r.length),v=Math.max(s-Math.max(h,c),0),m=s+l;m<f;m+=1)d(m)&&e.lazy.loadInSlide(m);for(var g=v;g<s;g+=1)d(g)&&e.lazy.loadInSlide(g)}else{var w=t.children("."+a.slideNextClass);w.length>0&&e.lazy.loadInSlide(p(w));var b=t.children("."+a.slidePrevClass);b.length>0&&e.lazy.loadInSlide(p(b))}}},kt={name:"lazy",params:{lazy:{enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}},create:function(){var e=this;V.extend(e,{lazy:{initialImageLoaded:!1,load:Pt.load.bind(e),loadInSlide:Pt.loadInSlide.bind(e)}})},on:{beforeInit:function(){var e=this;e.params.lazy.enabled&&e.params.preloadImages&&(e.params.preloadImages=!1)},init:function(){var e=this;e.params.lazy.enabled&&!e.params.loop&&0===e.params.initialSlide&&e.lazy.load()},scroll:function(){var e=this;e.params.freeMode&&!e.params.freeModeSticky&&e.lazy.load()},resize:function(){var e=this;e.params.lazy.enabled&&e.lazy.load()},scrollbarDragMove:function(){var e=this;e.params.lazy.enabled&&e.lazy.load()},transitionStart:function(){var e=this;e.params.lazy.enabled&&(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&&!e.lazy.initialImageLoaded)&&e.lazy.load()},transitionEnd:function(){var e=this;e.params.lazy.enabled&&!e.params.lazy.loadOnTransitionStart&&e.lazy.load()}}},Lt={LinearSpline:function(e,t){var a,i,r=function(){var e,t,a;return function(i,r){t=-1,e=i.length;while(e-t>1)a=e+t>>1,i[a]<=r?t=a:e=a;return e}}();return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=r(this.x,e),a=i-1,(e-this.x[a])*(this.y[i]-this.y[a])/(this.x[i]-this.x[a])+this.y[a]):0},this},getInterpolateFunction:function(e){var t=this;t.controller.spline||(t.controller.spline=t.params.loop?new Lt.LinearSpline(t.slidesGrid,e.slidesGrid):new Lt.LinearSpline(t.snapGrid,e.snapGrid))},setTranslate:function(e,t){var a,i,r=this,s=r.controller.control;function n(e){var t=r.rtlTranslate?-r.translate:r.translate;"slide"===r.params.controller.by&&(r.controller.getInterpolateFunction(e),i=-r.controller.spline.interpolate(-t)),i&&"container"!==r.params.controller.by||(a=(e.maxTranslate()-e.minTranslate())/(r.maxTranslate()-r.minTranslate()),i=(t-r.minTranslate())*a+e.minTranslate()),r.params.controller.inverse&&(i=e.maxTranslate()-i),e.updateProgress(i),e.setTranslate(i,r),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(s))for(var o=0;o<s.length;o+=1)s[o]!==t&&s[o]instanceof it&&n(s[o]);else s instanceof it&&t!==s&&n(s)},setTransition:function(e,t){var a,i=this,r=i.controller.control;function s(t){t.setTransition(e,i),0!==e&&(t.transitionStart(),t.params.autoHeight&&V.nextTick((function(){t.updateAutoHeight()})),t.$wrapperEl.transitionEnd((function(){r&&(t.params.loop&&"slide"===i.params.controller.by&&t.loopFix(),t.transitionEnd())})))}if(Array.isArray(r))for(a=0;a<r.length;a+=1)r[a]!==t&&r[a]instanceof it&&s(r[a]);else r instanceof it&&t!==r&&s(r)}},zt={name:"controller",params:{controller:{control:void 0,inverse:!1,by:"slide"}},create:function(){var e=this;V.extend(e,{controller:{control:e.params.controller.control,getInterpolateFunction:Lt.getInterpolateFunction.bind(e),setTranslate:Lt.setTranslate.bind(e),setTransition:Lt.setTransition.bind(e)}})},on:{update:function(){var e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},resize:function(){var e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},observerUpdate:function(){var e=this;e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},setTranslate:function(e,t){var a=this;a.controller.control&&a.controller.setTranslate(e,t)},setTransition:function(e,t){var a=this;a.controller.control&&a.controller.setTransition(e,t)}}},It={makeElFocusable:function(e){return e.attr("tabIndex","0"),e},addElRole:function(e,t){return e.attr("role",t),e},addElLabel:function(e,t){return e.attr("aria-label",t),e},disableEl:function(e){return e.attr("aria-disabled",!0),e},enableEl:function(e){return e.attr("aria-disabled",!1),e},onEnterKey:function(e){var t=this,a=t.params.a11y;if(13===e.keyCode){var r=i(e.target);t.navigation&&t.navigation.$nextEl&&r.is(t.navigation.$nextEl)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?t.a11y.notify(a.lastSlideMessage):t.a11y.notify(a.nextSlideMessage)),t.navigation&&t.navigation.$prevEl&&r.is(t.navigation.$prevEl)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?t.a11y.notify(a.firstSlideMessage):t.a11y.notify(a.prevSlideMessage)),t.pagination&&r.is("."+t.params.pagination.bulletClass)&&r[0].click()}},notify:function(e){var t=this,a=t.a11y.liveRegion;0!==a.length&&(a.html(""),a.html(e))},updateNavigation:function(){var e=this;if(!e.params.loop){var t=e.navigation,a=t.$nextEl,i=t.$prevEl;i&&i.length>0&&(e.isBeginning?e.a11y.disableEl(i):e.a11y.enableEl(i)),a&&a.length>0&&(e.isEnd?e.a11y.disableEl(a):e.a11y.enableEl(a))}},updatePagination:function(){var e=this,t=e.params.a11y;e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.bullets.each((function(a,r){var s=i(r);e.a11y.makeElFocusable(s),e.a11y.addElRole(s,"button"),e.a11y.addElLabel(s,t.paginationBulletMessage.replace(/{{index}}/,s.index()+1))}))},init:function(){var e=this;e.$el.append(e.a11y.liveRegion);var t,a,i=e.params.a11y;e.navigation&&e.navigation.$nextEl&&(t=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(a=e.navigation.$prevEl),t&&(e.a11y.makeElFocusable(t),e.a11y.addElRole(t,"button"),e.a11y.addElLabel(t,i.nextSlideMessage),t.on("keydown",e.a11y.onEnterKey)),a&&(e.a11y.makeElFocusable(a),e.a11y.addElRole(a,"button"),e.a11y.addElLabel(a,i.prevSlideMessage),a.on("keydown",e.a11y.onEnterKey)),e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.$el.on("keydown","."+e.params.pagination.bulletClass,e.a11y.onEnterKey)},destroy:function(){var e,t,a=this;a.a11y.liveRegion&&a.a11y.liveRegion.length>0&&a.a11y.liveRegion.remove(),a.navigation&&a.navigation.$nextEl&&(e=a.navigation.$nextEl),a.navigation&&a.navigation.$prevEl&&(t=a.navigation.$prevEl),e&&e.off("keydown",a.a11y.onEnterKey),t&&t.off("keydown",a.a11y.onEnterKey),a.pagination&&a.params.pagination.clickable&&a.pagination.bullets&&a.pagination.bullets.length&&a.pagination.$el.off("keydown","."+a.params.pagination.bulletClass,a.a11y.onEnterKey)}},Dt={name:"a11y",params:{a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}"}},create:function(){var e=this;V.extend(e,{a11y:{liveRegion:i('<span class="'+e.params.a11y.notificationClass+'" aria-live="assertive" aria-atomic="true"></span>')}}),Object.keys(It).forEach((function(t){e.a11y[t]=It[t].bind(e)}))},on:{init:function(){var e=this;e.params.a11y.enabled&&(e.a11y.init(),e.a11y.updateNavigation())},toEdge:function(){var e=this;e.params.a11y.enabled&&e.a11y.updateNavigation()},fromEdge:function(){var e=this;e.params.a11y.enabled&&e.a11y.updateNavigation()},paginationUpdate:function(){var e=this;e.params.a11y.enabled&&e.a11y.updatePagination()},destroy:function(){var e=this;e.params.a11y.enabled&&e.a11y.destroy()}}},$t={init:function(){var e=this;if(e.params.history){if(!t.history||!t.history.pushState)return e.params.history.enabled=!1,void(e.params.hashNavigation.enabled=!0);var a=e.history;a.initialized=!0,a.paths=$t.getPathValues(),(a.paths.key||a.paths.value)&&(a.scrollToSlide(0,a.paths.value,e.params.runCallbacksOnInit),e.params.history.replaceState||t.addEventListener("popstate",e.history.setHistoryPopState))}},destroy:function(){var e=this;e.params.history.replaceState||t.removeEventListener("popstate",e.history.setHistoryPopState)},setHistoryPopState:function(){var e=this;e.history.paths=$t.getPathValues(),e.history.scrollToSlide(e.params.speed,e.history.paths.value,!1)},getPathValues:function(){var e=t.location.pathname.slice(1).split("/").filter((function(e){return""!==e})),a=e.length,i=e[a-2],r=e[a-1];return{key:i,value:r}},setHistory:function(e,a){var i=this;if(i.history.initialized&&i.params.history.enabled){var r=i.slides.eq(a),s=$t.slugify(r.attr("data-history"));t.location.pathname.includes(e)||(s=e+"/"+s);var n=t.history.state;n&&n.value===s||(i.params.history.replaceState?t.history.replaceState({value:s},null,s):t.history.pushState({value:s},null,s))}},slugify:function(e){return e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,"")},scrollToSlide:function(e,t,a){var i=this;if(t)for(var r=0,s=i.slides.length;r<s;r+=1){var n=i.slides.eq(r),o=$t.slugify(n.attr("data-history"));if(o===t&&!n.hasClass(i.params.slideDuplicateClass)){var l=n.index();i.slideTo(l,e,a)}}else i.slideTo(0,e,a)}},At={name:"history",params:{history:{enabled:!1,replaceState:!1,key:"slides"}},create:function(){var e=this;V.extend(e,{history:{init:$t.init.bind(e),setHistory:$t.setHistory.bind(e),setHistoryPopState:$t.setHistoryPopState.bind(e),scrollToSlide:$t.scrollToSlide.bind(e),destroy:$t.destroy.bind(e)}})},on:{init:function(){var e=this;e.params.history.enabled&&e.history.init()},destroy:function(){var e=this;e.params.history.enabled&&e.history.destroy()},transitionEnd:function(){var e=this;e.history.initialized&&e.history.setHistory(e.params.history.key,e.activeIndex)}}},Ot={onHashCange:function(){var t=this,a=e.location.hash.replace("#",""),i=t.slides.eq(t.activeIndex).attr("data-hash");if(a!==i){var r=t.$wrapperEl.children("."+t.params.slideClass+'[data-hash="'+a+'"]').index();if("undefined"===typeof r)return;t.slideTo(r)}},setHash:function(){var a=this;if(a.hashNavigation.initialized&&a.params.hashNavigation.enabled)if(a.params.hashNavigation.replaceState&&t.history&&t.history.replaceState)t.history.replaceState(null,null,"#"+a.slides.eq(a.activeIndex).attr("data-hash")||0);else{var i=a.slides.eq(a.activeIndex),r=i.attr("data-hash")||i.attr("data-history");e.location.hash=r||""}},init:function(){var a=this;if(!(!a.params.hashNavigation.enabled||a.params.history&&a.params.history.enabled)){a.hashNavigation.initialized=!0;var r=e.location.hash.replace("#","");if(r)for(var s=0,n=0,o=a.slides.length;n<o;n+=1){var l=a.slides.eq(n),d=l.attr("data-hash")||l.attr("data-history");if(d===r&&!l.hasClass(a.params.slideDuplicateClass)){var p=l.index();a.slideTo(p,s,a.params.runCallbacksOnInit,!0)}}a.params.hashNavigation.watchState&&i(t).on("hashchange",a.hashNavigation.onHashCange)}},destroy:function(){var e=this;e.params.hashNavigation.watchState&&i(t).off("hashchange",e.hashNavigation.onHashCange)}},_t={name:"hash-navigation",params:{hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}},create:function(){var e=this;V.extend(e,{hashNavigation:{initialized:!1,init:Ot.init.bind(e),destroy:Ot.destroy.bind(e),setHash:Ot.setHash.bind(e),onHashCange:Ot.onHashCange.bind(e)}})},on:{init:function(){var e=this;e.params.hashNavigation.enabled&&e.hashNavigation.init()},destroy:function(){var e=this;e.params.hashNavigation.enabled&&e.hashNavigation.destroy()},transitionEnd:function(){var e=this;e.hashNavigation.initialized&&e.hashNavigation.setHash()}}},Bt={run:function(){var e=this,t=e.slides.eq(e.activeIndex),a=e.params.autoplay.delay;t.attr("data-swiper-autoplay")&&(a=t.attr("data-swiper-autoplay")||e.params.autoplay.delay),clearTimeout(e.autoplay.timeout),e.autoplay.timeout=V.nextTick((function(){e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.isBeginning?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(e.slideTo(e.slides.length-1,e.params.speed,!0,!0),e.emit("autoplay")):(e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.params.loop?(e.loopFix(),e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")):e.isEnd?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(e.slideTo(0,e.params.speed,!0,!0),e.emit("autoplay")):(e.slideNext(e.params.speed,!0,!0),e.emit("autoplay"))}),a)},start:function(){var e=this;return"undefined"===typeof e.autoplay.timeout&&(!e.autoplay.running&&(e.autoplay.running=!0,e.emit("autoplayStart"),e.autoplay.run(),!0))},stop:function(){var e=this;return!!e.autoplay.running&&("undefined"!==typeof e.autoplay.timeout&&(e.autoplay.timeout&&(clearTimeout(e.autoplay.timeout),e.autoplay.timeout=void 0),e.autoplay.running=!1,e.emit("autoplayStop"),!0))},pause:function(e){var t=this;t.autoplay.running&&(t.autoplay.paused||(t.autoplay.timeout&&clearTimeout(t.autoplay.timeout),t.autoplay.paused=!0,0!==e&&t.params.autoplay.waitForTransition?(t.$wrapperEl[0].addEventListener("transitionend",t.autoplay.onTransitionEnd),t.$wrapperEl[0].addEventListener("webkitTransitionEnd",t.autoplay.onTransitionEnd)):(t.autoplay.paused=!1,t.autoplay.run())))}},Nt={name:"autoplay",params:{autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1}},create:function(){var e=this;V.extend(e,{autoplay:{running:!1,paused:!1,run:Bt.run.bind(e),start:Bt.start.bind(e),stop:Bt.stop.bind(e),pause:Bt.pause.bind(e),onTransitionEnd:function(t){e&&!e.destroyed&&e.$wrapperEl&&t.target===this&&(e.$wrapperEl[0].removeEventListener("transitionend",e.autoplay.onTransitionEnd),e.$wrapperEl[0].removeEventListener("webkitTransitionEnd",e.autoplay.onTransitionEnd),e.autoplay.paused=!1,e.autoplay.running?e.autoplay.run():e.autoplay.stop())}}})},on:{init:function(){var e=this;e.params.autoplay.enabled&&e.autoplay.start()},beforeTransitionStart:function(e,t){var a=this;a.autoplay.running&&(t||!a.params.autoplay.disableOnInteraction?a.autoplay.pause(e):a.autoplay.stop())},sliderFirstMove:function(){var e=this;e.autoplay.running&&(e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause())},destroy:function(){var e=this;e.autoplay.running&&e.autoplay.stop()}}},Ht={setTranslate:function(){for(var e=this,t=e.slides,a=0;a<t.length;a+=1){var i=e.slides.eq(a),r=i[0].swiperSlideOffset,s=-r;e.params.virtualTranslate||(s-=e.translate);var n=0;e.isHorizontal()||(n=s,s=0);var o=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(i[0].progress),0):1+Math.min(Math.max(i[0].progress,-1),0);i.css({opacity:o}).transform("translate3d("+s+"px, "+n+"px, 0px)")}},setTransition:function(e){var t=this,a=t.slides,i=t.$wrapperEl;if(a.transition(e),t.params.virtualTranslate&&0!==e){var r=!1;a.transitionEnd((function(){if(!r&&t&&!t.destroyed){r=!0,t.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],a=0;a<e.length;a+=1)i.trigger(e[a])}}))}}},Gt={name:"effect-fade",params:{fadeEffect:{crossFade:!1}},create:function(){var e=this;V.extend(e,{fadeEffect:{setTranslate:Ht.setTranslate.bind(e),setTransition:Ht.setTransition.bind(e)}})},on:{beforeInit:function(){var e=this;if("fade"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"fade");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};V.extend(e.params,t),V.extend(e.originalParams,t)}},setTranslate:function(){var e=this;"fade"===e.params.effect&&e.fadeEffect.setTranslate()},setTransition:function(e){var t=this;"fade"===t.params.effect&&t.fadeEffect.setTransition(e)}}},Rt={setTranslate:function(){var e,t=this,a=t.$el,r=t.$wrapperEl,s=t.slides,n=t.width,o=t.height,l=t.rtlTranslate,d=t.size,p=t.params.cubeEffect,u=t.isHorizontal(),c=t.virtual&&t.params.virtual.enabled,h=0;p.shadow&&(u?(e=r.find(".swiper-cube-shadow"),0===e.length&&(e=i('<div class="swiper-cube-shadow"></div>'),r.append(e)),e.css({height:n+"px"})):(e=a.find(".swiper-cube-shadow"),0===e.length&&(e=i('<div class="swiper-cube-shadow"></div>'),a.append(e))));for(var f=0;f<s.length;f+=1){var v=s.eq(f),m=f;c&&(m=parseInt(v.attr("data-swiper-slide-index"),10));var g=90*m,w=Math.floor(g/360);l&&(g=-g,w=Math.floor(-g/360));var b=Math.max(Math.min(v[0].progress,1),-1),y=0,x=0,E=0;m%4===0?(y=4*-w*d,E=0):(m-1)%4===0?(y=0,E=4*-w*d):(m-2)%4===0?(y=d+4*w*d,E=d):(m-3)%4===0&&(y=-d,E=3*d+4*d*w),l&&(y=-y),u||(x=y,y=0);var T="rotateX("+(u?0:-g)+"deg) rotateY("+(u?g:0)+"deg) translate3d("+y+"px, "+x+"px, "+E+"px)";if(b<=1&&b>-1&&(h=90*m+90*b,l&&(h=90*-m-90*b)),v.transform(T),p.slideShadows){var C=u?v.find(".swiper-slide-shadow-left"):v.find(".swiper-slide-shadow-top"),S=u?v.find(".swiper-slide-shadow-right"):v.find(".swiper-slide-shadow-bottom");0===C.length&&(C=i('<div class="swiper-slide-shadow-'+(u?"left":"top")+'"></div>'),v.append(C)),0===S.length&&(S=i('<div class="swiper-slide-shadow-'+(u?"right":"bottom")+'"></div>'),v.append(S)),C.length&&(C[0].style.opacity=Math.max(-b,0)),S.length&&(S[0].style.opacity=Math.max(b,0))}}if(r.css({"-webkit-transform-origin":"50% 50% -"+d/2+"px","-moz-transform-origin":"50% 50% -"+d/2+"px","-ms-transform-origin":"50% 50% -"+d/2+"px","transform-origin":"50% 50% -"+d/2+"px"}),p.shadow)if(u)e.transform("translate3d(0px, "+(n/2+p.shadowOffset)+"px, "+-n/2+"px) rotateX(90deg) rotateZ(0deg) scale("+p.shadowScale+")");else{var M=Math.abs(h)-90*Math.floor(Math.abs(h)/90),P=1.5-(Math.sin(2*M*Math.PI/360)/2+Math.cos(2*M*Math.PI/360)/2),k=p.shadowScale,L=p.shadowScale/P,z=p.shadowOffset;e.transform("scale3d("+k+", 1, "+L+") translate3d(0px, "+(o/2+z)+"px, "+-o/2/L+"px) rotateX(-90deg)")}var I=F.isSafari||F.isUiWebView?-d/2:0;r.transform("translate3d(0px,0,"+I+"px) rotateX("+(t.isHorizontal()?0:h)+"deg) rotateY("+(t.isHorizontal()?-h:0)+"deg)")},setTransition:function(e){var t=this,a=t.$el,i=t.slides;i.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.cubeEffect.shadow&&!t.isHorizontal()&&a.find(".swiper-cube-shadow").transition(e)}},Xt={name:"effect-cube",params:{cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}},create:function(){var e=this;V.extend(e,{cubeEffect:{setTranslate:Rt.setTranslate.bind(e),setTransition:Rt.setTransition.bind(e)}})},on:{beforeInit:function(){var e=this;if("cube"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"cube"),e.classNames.push(e.params.containerModifierClass+"3d");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0};V.extend(e.params,t),V.extend(e.originalParams,t)}},setTranslate:function(){var e=this;"cube"===e.params.effect&&e.cubeEffect.setTranslate()},setTransition:function(e){var t=this;"cube"===t.params.effect&&t.cubeEffect.setTransition(e)}}},Vt={setTranslate:function(){for(var e=this,t=e.slides,a=e.rtlTranslate,r=0;r<t.length;r+=1){var s=t.eq(r),n=s[0].progress;e.params.flipEffect.limitRotation&&(n=Math.max(Math.min(s[0].progress,1),-1));var o=s[0].swiperSlideOffset,l=-180*n,d=l,p=0,u=-o,c=0;if(e.isHorizontal()?a&&(d=-d):(c=u,u=0,p=-d,d=0),s[0].style.zIndex=-Math.abs(Math.round(n))+t.length,e.params.flipEffect.slideShadows){var h=e.isHorizontal()?s.find(".swiper-slide-shadow-left"):s.find(".swiper-slide-shadow-top"),f=e.isHorizontal()?s.find(".swiper-slide-shadow-right"):s.find(".swiper-slide-shadow-bottom");0===h.length&&(h=i('<div class="swiper-slide-shadow-'+(e.isHorizontal()?"left":"top")+'"></div>'),s.append(h)),0===f.length&&(f=i('<div class="swiper-slide-shadow-'+(e.isHorizontal()?"right":"bottom")+'"></div>'),s.append(f)),h.length&&(h[0].style.opacity=Math.max(-n,0)),f.length&&(f[0].style.opacity=Math.max(n,0))}s.transform("translate3d("+u+"px, "+c+"px, 0px) rotateX("+p+"deg) rotateY("+d+"deg)")}},setTransition:function(e){var t=this,a=t.slides,i=t.activeIndex,r=t.$wrapperEl;if(a.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.virtualTranslate&&0!==e){var s=!1;a.eq(i).transitionEnd((function(){if(!s&&t&&!t.destroyed){s=!0,t.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],a=0;a<e.length;a+=1)r.trigger(e[a])}}))}}},Yt={name:"effect-flip",params:{flipEffect:{slideShadows:!0,limitRotation:!0}},create:function(){var e=this;V.extend(e,{flipEffect:{setTranslate:Vt.setTranslate.bind(e),setTransition:Vt.setTransition.bind(e)}})},on:{beforeInit:function(){var e=this;if("flip"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"flip"),e.classNames.push(e.params.containerModifierClass+"3d");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};V.extend(e.params,t),V.extend(e.originalParams,t)}},setTranslate:function(){var e=this;"flip"===e.params.effect&&e.flipEffect.setTranslate()},setTransition:function(e){var t=this;"flip"===t.params.effect&&t.flipEffect.setTransition(e)}}},Ft={setTranslate:function(){for(var e=this,t=e.width,a=e.height,r=e.slides,s=e.$wrapperEl,n=e.slidesSizesGrid,o=e.params.coverflowEffect,l=e.isHorizontal(),d=e.translate,p=l?t/2-d:a/2-d,u=l?o.rotate:-o.rotate,c=o.depth,h=0,f=r.length;h<f;h+=1){var v=r.eq(h),m=n[h],g=v[0].swiperSlideOffset,w=(p-g-m/2)/m*o.modifier,b=l?u*w:0,y=l?0:u*w,x=-c*Math.abs(w),E=l?0:o.stretch*w,T=l?o.stretch*w:0;Math.abs(T)<.001&&(T=0),Math.abs(E)<.001&&(E=0),Math.abs(x)<.001&&(x=0),Math.abs(b)<.001&&(b=0),Math.abs(y)<.001&&(y=0);var C="translate3d("+T+"px,"+E+"px,"+x+"px)  rotateX("+y+"deg) rotateY("+b+"deg)";if(v.transform(C),v[0].style.zIndex=1-Math.abs(Math.round(w)),o.slideShadows){var S=l?v.find(".swiper-slide-shadow-left"):v.find(".swiper-slide-shadow-top"),M=l?v.find(".swiper-slide-shadow-right"):v.find(".swiper-slide-shadow-bottom");0===S.length&&(S=i('<div class="swiper-slide-shadow-'+(l?"left":"top")+'"></div>'),v.append(S)),0===M.length&&(M=i('<div class="swiper-slide-shadow-'+(l?"right":"bottom")+'"></div>'),v.append(M)),S.length&&(S[0].style.opacity=w>0?w:0),M.length&&(M[0].style.opacity=-w>0?-w:0)}}if(Y.pointerEvents||Y.prefixedPointerEvents){var P=s[0].style;P.perspectiveOrigin=p+"px 50%"}},setTransition:function(e){var t=this;t.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)}},jt={name:"effect-coverflow",params:{coverflowEffect:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0}},create:function(){var e=this;V.extend(e,{coverflowEffect:{setTranslate:Ft.setTranslate.bind(e),setTransition:Ft.setTransition.bind(e)}})},on:{beforeInit:function(){var e=this;"coverflow"===e.params.effect&&(e.classNames.push(e.params.containerModifierClass+"coverflow"),e.classNames.push(e.params.containerModifierClass+"3d"),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},setTranslate:function(){var e=this;"coverflow"===e.params.effect&&e.coverflowEffect.setTranslate()},setTransition:function(e){var t=this;"coverflow"===t.params.effect&&t.coverflowEffect.setTransition(e)}}},qt={init:function(){var e=this,t=e.params,a=t.thumbs,i=e.constructor;a.swiper instanceof i?(e.thumbs.swiper=a.swiper,V.extend(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),V.extend(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1})):V.isObject(a.swiper)&&(e.thumbs.swiper=new i(V.extend({},a.swiper,{watchSlidesVisibility:!0,watchSlidesProgress:!0,slideToClickedSlide:!1})),e.thumbs.swiperCreated=!0),e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",e.thumbs.onThumbClick)},onThumbClick:function(){var e=this,t=e.thumbs.swiper;if(t){var a=t.clickedIndex,r=t.clickedSlide;if((!r||!i(r).hasClass(e.params.thumbs.slideThumbActiveClass))&&"undefined"!==typeof a&&null!==a){var s;if(s=t.params.loop?parseInt(i(t.clickedSlide).attr("data-swiper-slide-index"),10):a,e.params.loop){var n=e.activeIndex;e.slides.eq(n).hasClass(e.params.slideDuplicateClass)&&(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,n=e.activeIndex);var o=e.slides.eq(n).prevAll('[data-swiper-slide-index="'+s+'"]').eq(0).index(),l=e.slides.eq(n).nextAll('[data-swiper-slide-index="'+s+'"]').eq(0).index();s="undefined"===typeof o?l:"undefined"===typeof l?o:l-n<n-o?l:o}e.slideTo(s)}}},update:function(e){var t=this,a=t.thumbs.swiper;if(a){var i="auto"===a.params.slidesPerView?a.slidesPerViewDynamic():a.params.slidesPerView;if(t.realIndex!==a.realIndex){var r,s=a.activeIndex;if(a.params.loop){a.slides.eq(s).hasClass(a.params.slideDuplicateClass)&&(a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft,s=a.activeIndex);var n=a.slides.eq(s).prevAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index(),o=a.slides.eq(s).nextAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index();r="undefined"===typeof n?o:"undefined"===typeof o?n:o-s===s-n?s:o-s<s-n?o:n}else r=t.realIndex;a.visibleSlidesIndexes&&a.visibleSlidesIndexes.indexOf(r)<0&&(a.params.centeredSlides?r=r>s?r-Math.floor(i/2)+1:r+Math.floor(i/2)-1:r>s&&(r=r-i+1),a.slideTo(r,e?0:void 0))}var l=1,d=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(l=t.params.slidesPerView),a.slides.removeClass(d),a.params.loop||a.params.virtual)for(var p=0;p<l;p+=1)a.$wrapperEl.children('[data-swiper-slide-index="'+(t.realIndex+p)+'"]').addClass(d);else for(var u=0;u<l;u+=1)a.slides.eq(t.realIndex+u).addClass(d)}}},Wt={name:"thumbs",params:{thumbs:{swiper:null,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-container-thumbs"}},create:function(){var e=this;V.extend(e,{thumbs:{swiper:null,init:qt.init.bind(e),update:qt.update.bind(e),onThumbClick:qt.onThumbClick.bind(e)}})},on:{beforeInit:function(){var e=this,t=e.params,a=t.thumbs;a&&a.swiper&&(e.thumbs.init(),e.thumbs.update(!0))},slideChange:function(){var e=this;e.thumbs.swiper&&e.thumbs.update()},update:function(){var e=this;e.thumbs.swiper&&e.thumbs.update()},resize:function(){var e=this;e.thumbs.swiper&&e.thumbs.update()},observerUpdate:function(){var e=this;e.thumbs.swiper&&e.thumbs.update()},setTransition:function(e){var t=this,a=t.thumbs.swiper;a&&a.setTransition(e)},beforeDestroy:function(){var e=this,t=e.thumbs.swiper;t&&e.thumbs.swiperCreated&&t&&t.destroy()}}},Ut=[rt,st,nt,ot,dt,ut,ht,mt,wt,yt,Et,Ct,Mt,kt,zt,Dt,At,_t,Nt,Gt,Xt,Yt,jt,Wt];return"undefined"===typeof it.use&&(it.use=it.Class.use,it.installModule=it.Class.installModule),it.use(Ut),it}))},5467:function(e,t,a){!function(t,i){e.exports=i(a(2791))}(0,(function(e){return function(e){function t(i){if(a[i])return a[i].exports;var r=a[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var a={};return t.m=e,t.c=a,t.i=function(e){return e},t.d=function(e,a,i){t.o(e,a)||Object.defineProperty(e,a,{configurable:!1,enumerable:!0,get:i})},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=4)}([function(t,a){t.exports=e},function(e,t){e.exports=function(e,t,a,i,r,s){var n,o=e=e||{},l=typeof e.default;"object"!==l&&"function"!==l||(n=e,o=e.default);var d,p="function"==typeof o?o.options:o;if(t&&(p.render=t.render,p.staticRenderFns=t.staticRenderFns,p._compiled=!0),a&&(p.functional=!0),r&&(p._scopeId=r),s?(d=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},p._ssrRegister=d):i&&(d=i),d){var u=p.functional,c=u?p.render:p.beforeCreate;u?(p._injectStyles=d,p.render=function(e,t){return d.call(t),c(e,t)}):p.beforeCreate=c?[].concat(c,d):[d]}return{esModule:n,exports:o,options:p}}},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a(5),r=a.n(i),s=a(8),n=a(1),o=n(r.a,s.a,!1,null,null,null);t.default=o.exports},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a(6),r=a.n(i),s=a(7),n=a(1),o=n(r.a,s.a,!1,null,null,null);t.default=o.exports},function(e,t,a){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.install=t.swiperSlide=t.swiper=t.Swiper=void 0;var r=a(0),s=i(r),n=a(2),o=i(n),l=a(3),d=i(l),p=window.Swiper||s.default,u=d.default,c=o.default,h=function(e,t){t&&(d.default.props.globalOptions.default=function(){return t}),e.component(d.default.name,d.default),e.component(o.default.name,o.default)},f={Swiper:p,swiper:u,swiperSlide:c,install:h};t.default=f,t.Swiper=p,t.swiper=u,t.swiperSlide=c,t.install=h},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"swiper-slide",data:function(){return{slideClass:"swiper-slide"}},ready:function(){this.update()},mounted:function(){this.update(),this.$parent&&this.$parent.options&&this.$parent.options.slideClass&&(this.slideClass=this.$parent.options.slideClass)},updated:function(){this.update()},attached:function(){this.update()},methods:{update:function(){this.$parent&&this.$parent.swiper&&this.$parent.update()}}}},function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a(0),r=function(e){return e&&e.__esModule?e:{default:e}}(i),s=window.Swiper||r.default;"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(e),i=1;i<arguments.length;i++){var r=arguments[i];if(null!=r)for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(a[s]=r[s])}return a},writable:!0,configurable:!0});var n=["beforeDestroy","slideChange","slideChangeTransitionStart","slideChangeTransitionEnd","slideNextTransitionStart","slideNextTransitionEnd","slidePrevTransitionStart","slidePrevTransitionEnd","transitionStart","transitionEnd","touchStart","touchMove","touchMoveOpposite","sliderMove","touchEnd","click","tap","doubleTap","imagesReady","progress","reachBeginning","reachEnd","fromEdge","setTranslate","setTransition","resize"];t.default={name:"swiper",props:{options:{type:Object,default:function(){return{}}},globalOptions:{type:Object,required:!1,default:function(){return{}}}},data:function(){return{swiper:null,classes:{wrapperClass:"swiper-wrapper"}}},ready:function(){this.swiper||this.mountInstance()},mounted:function(){if(!this.swiper){var e=!1;for(var t in this.classes)this.classes.hasOwnProperty(t)&&this.options[t]&&(e=!0,this.classes[t]=this.options[t]);e?this.$nextTick(this.mountInstance):this.mountInstance()}},activated:function(){this.update()},updated:function(){this.update()},beforeDestroy:function(){this.$nextTick((function(){this.swiper&&(this.swiper.destroy&&this.swiper.destroy(),delete this.swiper)}))},methods:{update:function(){this.swiper&&(this.swiper.update&&this.swiper.update(),this.swiper.navigation&&this.swiper.navigation.update(),this.swiper.pagination&&this.swiper.pagination.render(),this.swiper.pagination&&this.swiper.pagination.update())},mountInstance:function(){var e=Object.assign({},this.globalOptions,this.options);this.swiper=new s(this.$el,e),this.bindEvents(),this.$emit("ready",this.swiper)},bindEvents:function(){var e=this,t=this;n.forEach((function(a){e.swiper.on(a,(function(){t.$emit.apply(t,[a].concat(Array.prototype.slice.call(arguments))),t.$emit.apply(t,[a.replace(/([A-Z])/g,"-$1").toLowerCase()].concat(Array.prototype.slice.call(arguments)))}))}))}}}},function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"swiper-container"},[e._t("parallax-bg"),e._v(" "),a("div",{class:e.classes.wrapperClass},[e._t("default")],2),e._v(" "),e._t("pagination"),e._v(" "),e._t("button-prev"),e._v(" "),e._t("button-next"),e._v(" "),e._t("scrollbar")],2)},r=[],s={render:i,staticRenderFns:r};t.a=s},function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement;return(e._self._c||t)("div",{class:e.slideClass},[e._t("default")],2)},r=[],s={render:i,staticRenderFns:r};t.a=s}])}))}}]);