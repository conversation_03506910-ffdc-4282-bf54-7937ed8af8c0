"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[670],{6025:function(t,n,e){e.r(n),e.d(n,{default:function(){return o}});var s=function(){var t=this;t._self._c;return t._m(0)},a=[function(){var t=this,n=t._self._c;return n("div",{staticClass:"main"},[n("img",{staticClass:"bg",attrs:{src:e(257)}})])}],r={name:"InnoMatchExpo",data(){return{}},created(){},methods:{}},c=r,i=e(1656),u=(0,i.A)(c,s,a,!1,null,"47820a96",null),o=u.exports},257:function(t,n,e){t.exports=e.p+"assets/img/innomatchexpo.3fc25150.png"}}]);