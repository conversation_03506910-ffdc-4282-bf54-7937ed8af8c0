"use strict";(self["webpackChunkreception"]=self["webpackChunkreception"]||[]).push([[324],{2575:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"main"},[e("div",{staticClass:"article-detail"},[e("div",{staticClass:"article",domProps:{innerHTML:t._s(t.detail)}})])])},n=[],s=a(3397),d={name:"meetingHotel",data(){return{detail:""}},created(){s.JX().then((t=>{200==t.data.code&&(this.detail=t.data.data.detail)}))},methods:{}},l=d,r=a(1656),c=(0,r.A)(l,i,n,!1,null,"4d86cba6",null),u=c.exports}}]);