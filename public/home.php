<?php
// 简单的首页
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>浦江论坛</title>";
echo "<meta charset='utf-8'>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; }";
echo ".header { background: #f0f0f0; padding: 20px; text-align: center; }";
echo ".content { margin: 20px 0; }";
echo ".news-item { border-bottom: 1px solid #eee; padding: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='header'>";
echo "<h1>浦江论坛</h1>";
echo "<p>欢迎访问浦江论坛官方网站</p>";
echo "</div>";

echo "<div class='content'>";
echo "<h2>网站信息</h2>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";

// 测试数据库连接
echo "<h2>数据库状态</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "<p style='color: green;'>✓ 数据库连接正常</p>";
    
    // 查询新闻
    $stmt = $pdo->query("SELECT * FROM news WHERE is_del = 1 ORDER BY release_time DESC LIMIT 5");
    $news = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($news) {
        echo "<h3>最新新闻</h3>";
        foreach ($news as $item) {
            echo "<div class='news-item'>";
            echo "<h4>" . htmlspecialchars($item['title']) . "</h4>";
            echo "<p>发布时间: " . $item['release_time'] . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p>暂无新闻数据</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
    echo "<p>请确保MySQL服务已启动，并创建了pujiangforum数据库</p>";
}

echo "<h2>快速链接</h2>";
echo "<ul>";
echo "<li><a href='hello.php'>Hello World测试</a></li>";
echo "<li><a href='standalone.php'>独立测试页面</a></li>";
echo "<li><a href='simple_test.php'>简单测试</a></li>";
echo "<li><a href='error_test.php'>错误测试</a></li>";
echo "</ul>";

echo "</div>";

echo "</body>";
echo "</html>";
?> 