$(function(){
    !function(t, e) {
        var i = t.documentElement,
            n = "orientationchange" in window ? "orientationchange": "resize",
            d = navigator.userAgent.match(/iphone|ipad|ipod/i),
            a = function() {
                var e = i.clientWidth,
                    n = i.clientHeight;
                e && (e >= 750 ? (e = 750, t.body.style.width = "750px") : t.body.style.width = e + "px", i.style.fontSize = 100 * (e / 750) + "px", i.dataset.percent = 100 * (e / 750), i.dataset.width = e, i.dataset.height = n)
            };
        a(),
        d && t.documentElement.classList.add("iosx" + e.devicePixelRatio),
        t.addEventListener && e.addEventListener(n, a, !1)
    } (document, window)
})