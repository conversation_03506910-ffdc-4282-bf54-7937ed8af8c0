﻿$(function () {
	$(window).scroll(function () {
		if ($(window).scrollTop() > 1) {	
			$(".headerBox").addClass("is-fixed");
		} else if ($(window).scrollTop() < 1) {
			$(".headerBox").removeClass("is-fixed");
		}
	});

	// navBox
	$('.menu').click(function () {
		$(".headerBox").toggleClass("fixeds");
		$('.navBox').slideToggle(400);
		$('.searchBox').slideUp(400);
		$('.solid1').toggleClass('onSolid1');
		$('.solid2').toggleClass('onSolid2');
		$('.solid3').toggleClass('onSolid3');
	})
	$('.navBox .nav h3').click(function () {
		$(this).next('.sub').slideToggle(400).siblings('.sub').slideUp(400);
		$(this).siblings('h3').removeClass('on');
		$(this).toggleClass('on');
	})


	// goTop
	$(window).scroll(function(){
	    if($(window).scrollTop()>100){  //距顶部多少像素时，出现返回顶部按钮
	        $(".goTop").fadeIn();
	    } else{
	        $(".goTop").fadeOut();
	    }
	});
	$(".goTop").click(function(){
		$('html,body').animate({'scrollTop':0},900); //返回顶部动画 数值越小时间越短
	});

	// pageNav
	$(".pageNav li").each(function(index){
		if($(this).attr("class")=='on'){
			var w = 0
			for(var j = 0; j<index; j++) {
				w += $('.pageNav li').eq(j).width();
			}
			$('.pageNavBox').scrollLeft(w);
		}
	});
	
	
	// var urlstr = location.pathname;
	// if (urlstr.indexOf('index/lists') > -1 || urlstr.indexOf('lbznews/lists') > -1 || urlstr.indexOf('download') > -1 || urlstr.indexOf('media') > -1 || urlstr.indexOf('lbzbusiness/contact') > -1 || urlstr.indexOf('ylznews/lists') > -1 || urlstr.indexOf('ylzbusiness/contact') > -1) {
	// 		$(".footer_expo").remove();
	// 		$(".footer").remove();
	// }

})











