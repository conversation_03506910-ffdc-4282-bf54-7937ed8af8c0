/* navBox */
.navBox {
  width: 100%;
  height: 100%;
  padding-top: 1.2rem;
  background-color: #082CA4;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 90;
  overflow-y: auto;
  display: none;
}
.navBox .nav {
  width: 100%;
}
.navBox h3 {
  width: 100%;
  position: relative;
  padding: 0 0.4rem;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  font-weight: normal;
}
.navBox h3 a {
  color: #fff;
  font-size: 0.32rem;
  display: block;
  width: 100%;
  line-height: 1.12rem;
}
.navBox h3 .iconfont {
  display: block;
  width: 0.32rem;
  color: #fff;
  font-size: 0.32rem;
  position: absolute;
  top: 50%;
  right: 0.3rem;
  transform: translateY(-50%);
  transition: all 0.6s ease;
}
.navBox h3.on .iconfont {
  transform: translateY(-50%) rotate(90deg);
}
.navBox .sub {
  display: none;
  width: 100%;
  padding: 0 0.6rem;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}
.navBox .sub li {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}
.navBox .sub li:nth-child(1) {
  border-top: none;
}
.navBox .sub li a {
  color: #fff;
  font-size: 0.28rem;
  display: block;
  width: 100%;
  line-height: 0.96rem;
}
.navBox .SearchForm {
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding: 0.4rem;
}
.navBox .SearchForm form {
  width: 100%;
  height: 0.88rem;
  border-radius: 0.12rem;
  background-color: #FFFFFF;
}
.navBox .SearchForm form input {
  width: calc(100% - 1.08rem);
  display: block;
  padding: 0.2rem 0 0.2rem 0.24rem;
  color: #333;
  line-height: 0.48rem;
}
.navBox .SearchForm form .btn {
  width: 1.08rem;
  display: block;
}
.navBox .SearchForm form .btn img {
  width: 0.36rem;
  display: block;
  margin: 0 auto;
}
.navBox .language {
  display: block;
  width: 100%;
  border-top: 1px solid rgba(146, 147, 149, 0.15);
  line-height: 1.06rem;
  color: #fff;
  text-align: center;
  font-size: 0.32rem;
}
/* headerBox */
.headerBox {
  width: 100%;
  height: 1.2rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  align-items: center;
  padding: 0 0.3rem;
  background-color: #fff;
  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1);
  transition: all 0.6s ease;
}
.headerBox .logo {
  display: block;
  height: 0.72rem;
}
.headerBox .logo img {
  display: block;
  height: 100%;
}
.headerBox .box {
  align-items: center;
}
.headerBox .language {
  font-size: 0.32rem;
  line-height: 0.4rem;
  color: #333;
  margin-right: 0.24rem;
}
.headerBox .language a {
  color: #999;
}
.headerBox .language a.on {
  color: #333;
}
.headerBox .language span {
  margin: 0 0.1rem;
}
.headerBox .login {
  display: block;
  color: #333;
  font-size: 0.48rem;
  line-height: 0.58rem;
  margin-right: 0.24rem;
}
.headerBox .menu {
  width: 0.64rem;
  height: 0.64rem;
  border-radius: 50%;
  position: relative;
  background-color: #082CA4;
  overflow: hidden;
}
.headerBox .menu .solid {
  height: 2px;
  background-color: #fff;
  position: absolute;
  transition: all 0.6s ease;
}
.headerBox .menu .solid1 {
  width: 0.2rem;
  top: 0.18rem;
  right: 0.2rem;
}
.headerBox .menu .solid2 {
  width: 0.32rem;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.headerBox .menu .solid3 {
  width: 0.2rem;
  bottom: 0.18rem;
  left: 0.2rem;
}
.is-fixed {
  background-color: #fff;
}
.fixeds {
  background-color: #fff;
}
.onSolid1 {
  width: 0.32rem !important;
  transform: rotate(45deg);
  top: 0.3rem !important;
  left: 0.16rem !important;
}
.onSolid2 {
  transform: translateX(0.64rem);
  opacity: 0;
}
.onSolid3 {
  width: 0.32rem !important;
  transform: rotate(-45deg);
  left: 0.16rem !important;
  bottom: 0.3rem !important;
}
/* indexBanner */
.indexBanner {
  width: 100%;
  margin-top: 1.2rem;
  position: relative;
  overflow: hidden;
}
.indexBanner img {
  width: 100%;
  display: block;
}
.indexBanner .swiper-pagination {
  bottom: 0.3rem !important;
}
.indexBanner .swiper-pagination .swiper-pagination-bullet {
  width: 0.16rem;
  height: 0.16rem;
  background-color: #fff;
  opacity: 0.5;
  margin: 0 0.12rem !important;
}
.indexBanner .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}
/* indexTitle */
.indexTitle {
  width: 100%;
}
.indexTitle .title {
  font-size: 0.44rem;
  font-weight: bold;
  line-height: 0.48rem;
  color: #333333;
  position: relative;
  z-index: 1;
  display: inline-block;
  margin-left: 50%;
  transform: translateX(-50%);
}
.indexTitle .title::after {
  content: '';
  width: 0.77rem;
  height: 0.77rem;
  background: url(../images/titleBg.png) center center no-repeat;
  background-size: cover;
  position: absolute;
  top: -0.3rem;
  left: -0.3rem;
  z-index: -1;
}
.indexTitle .p {
  font-size: 0.36rem;
  line-height: 0.4rem;
  text-transform: uppercase;
  color: #999999;
  margin-top: 0.1rem;
  display: inline-block;
  margin-left: 50%;
  transform: translateX(-50%);
}
/* indexMore */
.indexMore {
  width: 2.8rem;
  height: 0.72rem;
  border-top: 1px solid rgba(8, 44, 164, 0.2);
  border-bottom: 1px solid rgba(8, 44, 164, 0.2);
  margin: 0.5rem auto 0;
}
.indexMore p {
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
}
.indexMore .iconfont {
  color: #082CA4;
  font-size: 0.32rem;
  line-height: 0.32rem;
  margin-left: 0.1rem;
  transform: rotate(180deg);
}
/* indexNews */
.indexNews {
  width: 100%;
  padding: 0.8rem 0.3rem;
  overflow: hidden;
  background: #fff url(../images/newsBg.png) top center no-repeat;
  background-size: 100% auto;
}
.indexNews .contBox {
  display: block;
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 0.4rem;
}
.indexNews .contBox img {
  width: 100%;
  display: block;
}
.indexNews .contBox .cont {
  width: calc(100% - 0.6rem);
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  bottom: 0.3rem;
  left: 0.3rem;
  padding: 0.3rem;
}
.indexNews .contBox .cont .time {
  display: inline-block;
  height: 0.48rem;
  line-height: 0.48rem;
  border-radius: 0.04rem;
  background-color: #082CA4;
  padding: 0 0.16rem;
  font-size: 0.24rem;
  color: #FFFFFF;
}
.indexNews .contBox .cont .tit {
  font-size: 0.32rem;
  line-height: 0.36rem;
  color: #333333;
  margin-top: 0.24rem;
}
.indexNews .contBox .cont .txt {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #666666;
  margin-top: 0.24rem;
}
.indexNews .newsBox {
  width: 100%;
  background-color: #082CA4;
  margin-top: 0.3rem;
  overflow: hidden;
  position: relative;
  padding-bottom: 1rem;
}
.indexNews .newsBox a {
  display: block;
  width: 100%;
  height: 100%;
  padding: 0.3rem;
}
.indexNews .newsBox a .time {
  display: inline-block;
  height: 0.48rem;
  line-height: 0.48rem;
  border-radius: 0.04rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 0.16rem;
  font-size: 0.24rem;
  color: #FFFFFF;
}
.indexNews .newsBox a .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #FFFFFF;
  margin-top: 0.2rem;
}
.indexNews .newsBox a .txt {
  font-size: 0.24rem;
  line-height: 0.36rem;
  color: #FFFFFF;
  margin-top: 0.2rem;
}
.indexNews .newsBox .swiper-pagination {
  bottom: 0.4rem !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.indexNews .newsBox .swiper-pagination .swiper-pagination-bullet {
  width: 0.14rem;
  height: 0.14rem;
  background-color: #fff;
  opacity: 0.5;
  margin: 0 0.12rem !important;
}
.indexNews .newsBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 0.18rem;
  height: 0.18rem;
  opacity: 1;
}
/* indexAd */
.indexAd {
  width: 100%;
  padding: 0.7rem 0.5rem;
  background: url(../images/adBg.jpg) center center no-repeat;
  background-size: cover;
  overflow: hidden;
}
.indexAd .t {
  font-size: 0.32rem;
  font-weight: bold;
  line-height: 0.48rem;
  color: #FFFFFF;
}
.indexAd img {
  width: 0.6rem;
  display: block;
  margin-top: 0.3rem;
}
/* indexSchedule */
.indexSchedule {
  width: 100%;
  padding: 0.8rem 0 0.6rem;
  overflow: hidden;
  background: #fff url(../images/scheduleBg.png) top center;
  background-size: 100% auto;
}
.indexSchedule .timeBox {
  width: 100%;
  margin-top: 0.4rem;
  padding: 0 0.2rem;
  position: relative;
  z-index: 1;
}
.indexSchedule .timeBox::before {
  content: '';
  width: 100%;
  height: 1px;
  background-color: #082CA4;
  position: absolute;
  top: 0.32rem;
  left: 0;
  z-index: -1;
}
.indexSchedule .timeBox li {
  width: 1.4rem;
  height: 0.64rem;
  line-height: 0.6rem;
  border-radius: 0.32rem;
  background-color: #FFFFFF;
  border: 1px solid #082CA4;
  color: #082CA4;
  font-size: 0.28rem;
  text-align: center;
  cursor: pointer;
  margin: 0 0.2rem;
  transition: all 0.6s ease;
}
.indexSchedule .timeBox li.on,
.indexSchedule .timeBox li:hover {
  background-color: #082CA4;
  color: #fff;
}
.indexSchedule .scheduleBox {
  width: 100%;
  margin-top: 0.5rem;
  padding: 0 0.3rem;
}
.indexSchedule .scheduleBox li {
  width: 100%;
  display: none;
}
.indexSchedule .scheduleBox li:first-child {
  display: block;
}
.indexSchedule .scheduleBox .liBox {
  width: 100%;
  margin-bottom: 0.3rem;
}
.indexSchedule .scheduleBox .Box {
  width: 100%;
  background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
  border: 2px solid rgba(8, 44, 164, 0.12);
  border-top: 2px solid #082CA4;
  box-shadow: 0 0.04rem 0.2rem 0 rgba(0, 0, 0, 0.08);
  padding: 0.3rem;
  transition: all 0.6s ease;
}
.indexSchedule .scheduleBox .Box .tit {
  width: calc(100% - 1.5rem);
  font-size: 0.28rem;
  font-weight: bold;
  line-height: 0.4rem;
  color: #333333;
}
.indexSchedule .scheduleBox .Box .label {
  min-width: 1.1rem;
  height: 0.42rem;
  line-height: 0.42rem;
  border-radius: 0.04rem;
  font-size: 0.2rem;
  padding: 0 0.06rem;
}
.indexSchedule .scheduleBox .Box .label.label1 {
  border: 1px solid #52A199;
  color: #52A199;
}
.indexSchedule .scheduleBox .Box .label.label2 {
  border: 1px solid #082CA4;
  color: #082CA4;
}
.indexSchedule .scheduleBox .Box .label.label3 {
  border: 1px solid #999999;
  color: #999999;
}
.indexSchedule .scheduleBox .Box .label .iconfont {
  margin-right: 0.06rem;
  font-size: 0.24rem;
}
.indexSchedule .scheduleBox .Box .address {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #999999;
}
.indexSchedule .scheduleBox .Box .address .iconfont {
  font-size: 0.28rem;
  margin-right: 0.06rem;
}
.indexSchedule .scheduleBox .Box .address .left {
  margin-top: 0.16rem;
}
.indexSchedule .scheduleBox .Box .address .left p {
  max-width: calc(100% - 0.34rem);
}
.indexSchedule .scheduleBox .Box .btnBox {
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 0.3rem;
  padding-top: 0.3rem;
}
.indexSchedule .scheduleBox .Box .btnBox .btn {
  display: block;
  width: 1.8rem;
  height: 0.56rem;
  line-height: 0.56rem;
  border-radius: 0.28rem;
  font-size: 0.24rem;
  color: #FFFFFF;
  text-align: center;
}
.indexSchedule .scheduleBox .Box .btnBox .btn.btn1 {
  background-color: #082CA4;
}
.indexSchedule .scheduleBox .Box .btnBox .btn.btn2 {
  background-color: #CDCFD6;
}
.indexSchedule .scheduleBox .Box .btnBox .iconfont {
  width: 0.56rem;
  height: 0.56rem;
  line-height: 0.56rem;
  color: #fff;
  font-size: 0.2rem;
  background-color: #262626;
  text-align: center;
  border-radius: 50%;
  transition: all 0.6s ease;
}
.indexSchedule .scheduleBox .Box.on {
  background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);
}
.indexSchedule .scheduleBox .Box.on .tit {
  color: #fff;
}
.indexSchedule .scheduleBox .Box.on .label.label1 {
  border: 1px solid #fff;
  color: #fff;
}
.indexSchedule .scheduleBox .Box.on .label.label2 {
  border: 1px solid #fff;
  color: #fff;
}
.indexSchedule .scheduleBox .Box.on .label.label3 {
  border: 1px solid #fff;
  color: #fff;
}
.indexSchedule .scheduleBox .Box.on .address {
  color: #fff;
}
.indexSchedule .scheduleBox .Box.on .btnBox {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}
.indexSchedule .scheduleBox .Box.on .btnBox .btn {
  color: #082CA4;
}
.indexSchedule .scheduleBox .Box.on .btnBox .btn.btn1 {
  background-color: #fff;
}
.indexSchedule .scheduleBox .Box.on .btnBox .btn.btn2 {
  background-color: #CDCFD6;
}
.indexSchedule .scheduleBox .Box.on .btnBox .iconfont {
  background-color: #fff;
  color: #082CA4;
  transform: rotate(90deg);
}
.indexSchedule .scheduleBox .contBox {
  width: 100%;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-top: none;
  display: none;
}
.indexSchedule .scheduleBox .contBox.sel {
  display: block;
}
.indexSchedule .scheduleBox .contBox .cont {
  width: 100%;
  background-color: #FAFBFF;
  padding: 0.2rem 0.3rem 0.3rem;
}
.indexSchedule .scheduleBox .contBox .cont .t {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #3D3D3D;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 0.1rem 0;
}
.indexSchedule .scheduleBox .contBox .cont .t p {
  margin: 0.1rem 0;
}
.indexSchedule .scheduleBox .contBox .cont .t p:nth-child(1) {
  width: 1.44rem;
}
.indexSchedule .scheduleBox .contBox .cont .t p:nth-child(2) {
  width: calc(100% - 1.44rem);
}
.indexSchedule .scheduleBox .contBox .cont .t p.p {
  width: 1.84rem;
}
.indexSchedule .scheduleBox .contBox .cont .t p.p1 {
  width: calc(100% - 1.84rem);
}
.indexSchedule .scheduleBox .contBox .cont .t p span:last-child em {
  display: none;
}
.indexSchedule .scheduleBox .contBox .cont .t1 {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #3D3D3D;
  padding: 0.2rem 0 0.1rem;
}
.indexSchedule .scheduleBox .contBox .cont .t2 {
  font-size: 0.24rem;
  line-height: 0.4rem;
  color: #666666;
  overflow: hidden;
}
.indexSchedule .scheduleBox .contBox .cont .t2.on {
  height: auto;
}
.indexSchedule .scheduleBox .contBox .cont .show {
  margin-top: 0.24rem;
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  cursor: pointer;
}
.indexSchedule .scheduleBox .contBox .cont .show .iconfont {
  width: 0.32rem;
  height: 0.32rem;
  line-height: 0.32rem;
  border-radius: 0.16rem;
  background-color: #082CA4;
  color: #fff;
  transform: rotate(90deg);
  text-align: center;
  font-size: 0.12rem;
  margin-left: 0.1rem;
  transition: all 0.6s ease;
}
.indexSchedule .scheduleBox .contBox .cont .show.sel .iconfont {
  transform: rotate(-90deg);
}
.indexSchedule .scheduleBox .contBox .dl {
  width: 100%;
  padding: 0.3rem;
  position: relative;
}
.indexSchedule .scheduleBox .contBox .dl::after {
  content: '';
  width: 1px;
  height: calc(100% - 1.4rem);
  border-left: 1px dashed #082CA4;
  position: absolute;
  top: 0.4rem;
  left: 2.18rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt {
  width: 100%;
  margin-bottom: 0.2rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .time {
  width: 2.08rem;
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  text-align: right;
  position: relative;
  padding-right: 0.5rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .time::after {
  content: '';
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  background-color: rgba(8, 44, 164, 0.1);
  position: absolute;
  top: 0;
  right: 0.04rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .time::before {
  content: '';
  width: 0.14rem;
  height: 0.14rem;
  border-radius: 50%;
  background-color: #082CA4;
  position: absolute;
  top: 0.08rem;
  right: 0.12rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd {
  width: calc(100% - 2.16rem);
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .t3 {
  font-size: 0.28rem;
  line-height: 0.32rem;
  color: #333333;
  margin-bottom: 0.16rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li {
  width: 100%;
  margin-bottom: 0.2rem;
  align-items: center;
  background-color: #FAFBFF;
  padding: 0.2rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li .img {
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.14rem;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li .img img {
  width: 100%;
  display: block;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li .t4 {
  width: calc(100% - 0.74rem);
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li .t4 .name {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #333333;
}
.indexSchedule .scheduleBox .contBox .dl .dt .dd .li .t4 .t5 {
  font-size: 0.2rem;
  line-height: 0.32rem;
  color: #666;
  margin-top: 0.06rem;
}
/* indexVideo */
.indexVideo {
  width: 100%;
  padding: 0.8rem 0;
  background: #fff url(../images/videoBg.png) center center no-repeat;
  background-size: cover;
  overflow: hidden;
}
.indexVideo .tabList {
  width: 4.96rem;
  height: 0.8rem;
  border-radius: 0.4rem;
  background-color: rgba(8, 44, 164, 0.06);
  margin: 0.4rem auto 0;
}
.indexVideo .tabList li {
  width: 2.4rem;
  height: 0.64rem;
  border-radius: 0.32rem;
  font-size: 0.28rem;
  line-height: 0.64rem;
  color: #082CA4;
  text-align: center;
}
.indexVideo .tabList li.on {
  background-color: #082CA4;
  color: #FFFFFF;
}
.indexVideo .lists {
  width: 100%;
  margin-top: 0.5rem;
}
.indexVideo .videoBox {
  width: 100%;
  position: relative;
  overflow: hidden;
}
.indexVideo .videoBox .swiper-slide {
  width: 5.8rem !important;
  transform: scale(0.9);
}
.indexVideo .videoBox .swiper-slide a {
  display: block;
  width: 100%;
}
.indexVideo .videoBox .swiper-slide a .img {
  width: 100%;
  overflow: hidden;
  position: relative;
  background-color: #000;
}
.indexVideo .videoBox .swiper-slide a .img .pic {
  width: 100%;
  display: block;
  opacity: 0.6;
}
.indexVideo .videoBox .swiper-slide a .img .play {
  width: 1rem;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.indexVideo .videoBox .swiper-slide a .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333333;
  border: 1px solid rgba(8, 44, 164, 0.12);
  padding: 0.4rem;
  border-top: none;
  opacity: 0;
}
.indexVideo .videoBox .swiper-slide.swiper-slide-active {
  transform: scale(1);
}
.indexVideo .videoBox .swiper-slide.swiper-slide-active a .tit {
  opacity: 1;
}
.indexVideo .videoBox .swiper-pagination {
  position: static !important;
  margin-top: 0.4rem;
}
.indexVideo .videoBox .swiper-pagination .swiper-pagination-bullet {
  width: 0.14rem;
  height: 0.14rem;
  background-color: #082CA4;
  opacity: 0.5;
  margin: 0 0.12rem !important;
}
.indexVideo .videoBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}
/* indexPartner */
.indexPartner {
  width: 100%;
  background: url(../images/partnerBg.jpg) center center no-repeat;
  background-size: cover;
  padding: 0.8rem 0.3rem 0.2rem;
}
.indexPartner .box {
  width: 100%;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.18rem;
  margin-bottom: 0.3rem;
}
.indexPartner .box:last-child {
  border-bottom: none;
}
.indexPartner .box .p {
  font-size: 0.28rem;
  font-weight: bold;
  line-height: 0.32rem;
  color: #FFFFFF;
  margin-bottom: 0.3rem;
}
.indexPartner .box ul {
  width: 100%;
}
.indexPartner .box ul li {
  width: 2.2rem;
  height: 1.2rem;
  background-color: #fff;
  margin: 0 0.15rem 0.15rem 0;
}
.indexPartner .box ul li:nth-child(3n) {
  margin: 0 0 0.15rem 0;
}
.indexPartner .box ul li a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.indexPartner .box ul li img {
  max-width: 100%;
  max-height: 100%;
}
/* indexFooter */
.indexFooter {
  width: 100%;
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.indexFooter .box {
  width: 100%;
  padding: 0.7rem 0.45rem 0;
}
.indexFooter .box .t {
  font-size: 0.32rem;
  line-height: 0.36rem;
  color: #333333;
  margin-bottom: 0.3rem;
}
.indexFooter .box .rwm {
  width: 2rem;
}
.indexFooter .box .rwm img {
  width: 100%;
  display: block;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.indexFooter .box .rwm p {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #666666;
  text-align: center;
  margin-top: 0.2rem;
}
.indexFooter .copy {
  width: 100%;
  padding: 0.3rem;
  margin-top: 0.6rem;
  font-size: 0.2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: #999;
  line-height: 0.32rem;
}
.indexFooter .copy a {
  color: #999;
}
/* goTop */
.goTop {
  width: 0.76rem;
  height: 0.76rem;
  line-height: 0.76rem;
  border-radius: 50%;
  background-color: var(--color);
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
  position: fixed;
  bottom: 10%;
  right: 0.2rem;
  z-index: 100;
  display: none;
}
/* pageBanner */
.pageBanner {
  width: 100%;
  height: 3.6rem;
  position: relative;
  background-size: cover !important;
}
.pageBanner .location {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 0.2rem 0.3rem;
  line-height: 0.4rem;
}
.pageBanner .location .sel {
  color: #fff;
  font-size: 0.32rem;
  margin-right: 0.1rem;
}
.pageBanner .location .icon-jiantou {
  color: #fff;
  font-size: 0.16rem;
  margin: 0 0.1rem;
}
.pageBanner .location p {
  font-size: 0.24rem;
  color: #fff;
}
.pageBanner .location a {
  font-size: 0.24rem;
  color: #fff;
}
/* pageNavBox */
.pageNavBox {
  width: 100%;
  overflow-y: hidden;
  overflow-x: auto;
  white-space: nowrap;
  position: sticky;
  top: 1.2rem;
  z-index: 20;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}
.pageNavBox::-webkit-scrollbar {
  display: none;
}
/* pageNav */
.pageNav {
  width: 100%;
}
.pageNav li {
  padding-left: 0.3rem;
}
.pageNav li:last-child {
  padding-right: 0.3rem;
}
.pageNav li a {
  display: inline-block;
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #333333;
  padding: 0.22rem 0;
  border-bottom: 0.04rem solid rgba(63, 170, 141, 0);
}
.pageNav li.on a {
  border-bottom: 0.04rem solid #082CA4;
  color: #082CA4;
  font-weight: bold;
}
/* pageTab */
.pageTab {
  width: calc(100% - 0.6rem);
  margin: 0.4rem auto 0;
  background-color: #FAFBFF;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.36rem;
  overflow: hidden;
}
.pageTab .t {
  width: 100%;
  height: 0.72rem;
  line-height: 0.72rem;
  position: relative;
  padding-left: 0.4rem;
  display: none;
}
.pageTab .t .iconfont {
  position: absolute;
  top: 50%;
  right: 0.4rem;
  transform: translateY(-50%) rotate(90deg);
  font-size: 0.24rem;
  color: #999999;
  transition: all 0.6s ease;
}
.pageTab .t.on .iconfont {
  transform: translateY(-50%) rotate(-90deg);
}
.pageTab .t.sel {
  display: block;
}
.pageTab ul {
  width: 100%;
  background-color: #FFFFFF;
  display: none;
}
.pageTab ul li {
  height: 0.7rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.pageTab ul li a {
  display: block;
  width: 100%;
  height: 100%;
  font-size: 0.28rem;
  line-height: 0.7rem;
  color: #333333;
  padding: 0 0.4rem;
}
.pageTab ul li.on {
  background-color: #082CA4;
}
.pageTab ul li.on a {
  color: #fff;
}
/* pageTitle */
.pageTitle {
  font-size: 0.32rem;
  line-height: 0.36rem;
  color: #333333;
  position: relative;
  padding-bottom: 0.2rem;
  font-weight: bold;
}
.pageTitle::after {
  content: '';
  width: 0.32rem;
  height: 0.06rem;
  background-color: #082CA4;
  position: absolute;
  bottom: 0;
  left: 0;
}
/* pageAbout */
.pageAbout {
  width: 100%;
  padding: 0.5rem 0.3rem;
}
.pageAbout .text {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #666666;
  text-align: justify;
  margin-top: 0.3rem;
}
.pageAbout .text img {
  max-width: 100%;
  height: auto;
}
/* pageOrganization */
.pageOrganization {
  width: 100%;
  padding: 0.4rem 0.3rem;
}
.pageOrganization ul {
  width: 100%;
  margin-top: 0.4rem;
}
.pageOrganization ul li {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 0.04rem solid #082CA4;
  margin-bottom: 0.3rem;
  padding: 0.3rem;
}
.pageOrganization ul li .t1 {
  font-size: 0.32rem;
  line-height: 0.4rem;
  color: #666666;
  font-weight: bold;
}
.pageOrganization ul li .t2 {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #666666;
  margin-top: 0.2rem;
}
.pageOrganization ul li .t2 p {
  margin-right: 0.3rem;
}
/* pageNews */
.pageNews {
  width: 100%;
  padding: 0.3rem 0 0.5rem;
}
.pageNews .newsBox {
  width: 100%;
  padding: 0 0.3rem 0.5rem;
  position: relative;
  overflow: hidden;
  margin-bottom: 0.5rem;
}
.pageNews .newsBox .swiper-slide {
  background-color: #FAFBFF;
}
.pageNews .newsBox a {
  width: 100%;
  align-items: center;
}
.pageNews .newsBox a .img {
  width: 100%;
  overflow: hidden;
}
.pageNews .newsBox a .img img {
  width: 100%;
  display: block;
}
.pageNews .newsBox a .cont {
  width: 100%;
  padding: 0.3rem;
}
.pageNews .newsBox a .cont .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333;
}
.pageNews .newsBox a .cont .txt {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #999;
  margin-top: 0.2rem;
}
.pageNews .newsBox a .cont .time {
  font-size: 0.28rem;
  line-height: 0.32rem;
  color: #666;
  margin-top: 0.3rem;
}
.pageNews .newsBox .swiper-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 0 !important;
}
.pageNews .newsBox .swiper-pagination .swiper-pagination-bullet {
  width: 0.1rem;
  height: 0.1rem;
  opacity: 0.5;
  background-color: #082CA4;
  margin: 0 0.2rem 0 0;
}
.pageNews .newsBox .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 0.16rem;
  height: 0.16rem;
  opacity: 1;
}
.pageNews .lists {
  width: 100%;
  margin: 0 0 0.4rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.pageNews .lists li {
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageNews .lists li a {
  background-color: #ffffff;
  padding: 0.4rem 0.3rem;
  align-items: center;
}
.pageNews .lists li .time {
  font-size: 0.28rem;
  line-height: 0.52rem;
  color: #999999;
}
.pageNews .lists li .time p {
  font-size: 0.48rem;
  color: #333333;
}
.pageNews .lists li .img {
  width: 2.6rem;
  overflow: hidden;
}
.pageNews .lists li .img img {
  width: 100%;
  display: block;
}
.pageNews .lists li .cont {
  width: calc(100% - 1.5rem);
}
.pageNews .lists li .cont.cont1 {
  width: calc(100% - 2.8rem);
}
.pageNews .lists li .cont .tit {
  font-size: 0.32rem;
  line-height: 0.36rem;
  color: #333333;
}
.pageNews .lists li .cont .txt {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #999999;
  margin-top: 0.1rem;
}
.pageNews .lists li .cont .a {
  align-items: center;
  line-height: 0.32rem;
  margin-top: 0.2rem;
}
.pageNews .lists li .cont .a p {
  font-size: 0.28rem;
  color: #999999;
}
.pageNews .lists li .cont .a .iconfont {
  color: #082CA4;
  font-size: 0.32rem;
  transform: rotate(180deg);
  margin-left: 0.1rem;
}
/* pageActivity */
.pageActivity {
  width: 100%;
  padding: 0.3rem 0.3rem 0.5rem;
}
.pageActivity .lists {
  width: 100%;
  margin: 0 0 0.1rem;
}
.pageActivity .lists li {
  width: 100%;
  margin: 0 0 0.3rem;
}
.pageActivity .lists li .img {
  width: 100%;
  overflow: hidden;
  position: relative;
}
.pageActivity .lists li .img img {
  width: 100%;
  display: block;
}
.pageActivity .lists li .img .label {
  height: 0.6rem;
  background-color: #082CA4;
  font-size: 0.24rem;
  line-height: 0.6rem;
  color: #FFFFFF;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 0.2rem;
}
.pageActivity .lists li .cont {
  width: 100%;
  padding: 0.3rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top: none;
  background-color: #fff;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333333;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .txt {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #666;
  margin-top: 0.2rem;
}
.pageActivity .lists li .cont .time {
  margin-top: 0.2rem;
}
.pageActivity .lists li .cont .time .p {
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #999999;
  margin-top: 0.2rem;
  transition: all 0.6s ease;
}
.pageActivity .lists li .cont .time .p .iconfont {
  font-size: 0.28rem;
  margin-right: 0.1rem;
}
.pageActivity .lists li:hover .cont {
  background-color: #082CA4;
}
.pageActivity .lists li:hover .cont .tit,
.pageActivity .lists li:hover .cont .txt {
  color: #fff;
}
.pageActivity .lists li:hover .cont .time .p {
  color: #fff;
}
/* pageVideo */
.pageVideo {
  width: 100%;
  padding: 0.3rem 0.3rem 0.5rem;
}
.pageVideo .lists {
  width: 100%;
  margin: 0 0 0.1rem;
}
.pageVideo .lists li {
  width: 100%;
  margin: 0 0 0.3rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.pageVideo .lists li a {
  display: block;
  padding-bottom: 0.3rem;
}
.pageVideo .lists li .img {
  width: 100%;
  overflow: hidden;
  background-color: #000;
  position: relative;
}
.pageVideo .lists li .img img {
  width: 100%;
  display: block;
  opacity: 0.6;
}
.pageVideo .lists li .img .iconfont {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 0.8rem;
}
.pageVideo .lists li .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333333;
  margin-top: 0.2rem;
}
.pageVideo .lists li .t {
  font-size: 0.24rem;
  line-height: 0.44rem;
  color: #999999;
  margin-top: 0.2rem;
}
.pageVideo.pageVideo1 .lists li .img img {
  opacity: 1;
}
.pageVideo.pageVideo1 .lists li .img .iconfont {
  display: none;
}
/* pageNewsD */
.pageNewsD {
  width: 100%;
  padding: 0.4rem 0.3rem;
}
.pageNewsD .title {
  text-align: center;
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333;
  font-weight: bold;
}
.pageNewsD .date {
  color: #999;
  font-size: 0.24rem;
  text-align: center;
  border-bottom: 1px dashed #ddd;
  padding: 0.3rem 0;
  margin-bottom: 0.3rem;
}
.pageNewsD .date span {
  margin: 0 0.1rem;
}
.pageNewsD .text {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #333;
}
.pageNewsD .text img {
  max-width: 100%;
  height: auto;
}
.pageNewsD .text video {
  display: block;
  width: 100%;
}
.pageNewsD .text iframe {
  width: 7.1rem;
  height: 4.1rem;
  margin: 0 auto;
  display: block;
}
.pageNewsD .box {
  width: 100%;
  border-top: 1px dashed #ddd;
  padding: 0.3rem 0 0;
  margin-top: 0.3rem;
}
.pageNewsD .box a {
  color: #999;
  font-size: 0.24rem;
  line-height: 0.44rem;
  display: inline-block;
  margin-bottom: 0.3rem;
  transition: all 0.6s ease;
}
.pageNewsD .box a:hover {
  color: #082CA4;
}
/* pageForum */
.pageForum {
  width: 100%;
  padding: 0.3rem;
}
.pageForum .liBox {
  width: 100%;
  margin-bottom: 0.3rem;
}
.pageForum .Box {
  width: 100%;
  background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
  border: 2px solid rgba(8, 44, 164, 0.12);
  border-top: 2px solid #082CA4;
  box-shadow: 0 0.04rem 0.2rem 0 rgba(0, 0, 0, 0.08);
  padding: 0.3rem;
  transition: all 0.6s ease;
}
.pageForum .Box .tit {
  width: calc(100% - 1.5rem);
  font-size: 0.28rem;
  font-weight: bold;
  line-height: 0.4rem;
  color: #333333;
}
.pageForum .Box .label {
  min-width: 1.1rem;
  height: 0.42rem;
  line-height: 0.42rem;
  border-radius: 0.04rem;
  font-size: 0.2rem;
  padding: 0 0.06rem;
}
.pageForum .Box .label.label1 {
  border: 1px solid #52A199;
  color: #52A199;
}
.pageForum .Box .label.label2 {
  border: 1px solid #082CA4;
  color: #082CA4;
}
.pageForum .Box .label.label3 {
  border: 1px solid #999999;
  color: #999999;
}
.pageForum .Box .label .iconfont {
  margin-right: 0.06rem;
  font-size: 0.24rem;
}
.pageForum .Box .address {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #999999;
}
.pageForum .Box .address .iconfont {
  font-size: 0.28rem;
  margin-right: 0.06rem;
}
.pageForum .Box .address .left {
  margin-top: 0.16rem;
}
.pageForum .Box .address .left p {
  max-width: calc(100% - 0.34rem);
}
.pageForum .Box .btnBox {
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 0.3rem;
  padding-top: 0.3rem;
}
.pageForum .Box .btnBox .btn {
  display: block;
  width: 1.8rem;
  height: 0.56rem;
  line-height: 0.56rem;
  border-radius: 0.28rem;
  font-size: 0.24rem;
  color: #FFFFFF;
  text-align: center;
}
.pageForum .Box .btnBox .btn.btn1 {
  background-color: #082CA4;
}
.pageForum .Box .btnBox .btn.btn2 {
  background-color: #CDCFD6;
}
.pageForum .Box .btnBox .iconfont {
  width: 0.56rem;
  height: 0.56rem;
  line-height: 0.56rem;
  color: #fff;
  font-size: 0.2rem;
  background-color: #262626;
  text-align: center;
  border-radius: 50%;
  transition: all 0.6s ease;
}
.pageForum .Box.on {
  background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);
}
.pageForum .Box.on .tit {
  color: #fff;
}
.pageForum .Box.on .label.label1 {
  border: 1px solid #fff;
  color: #fff;
}
.pageForum .Box.on .label.label2 {
  border: 1px solid #fff;
  color: #fff;
}
.pageForum .Box.on .label.label3 {
  border: 1px solid #fff;
  color: #fff;
}
.pageForum .Box.on .address {
  color: #fff;
}
.pageForum .Box.on .btnBox {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}
.pageForum .Box.on .btnBox .btn {
  color: #082CA4;
}
.pageForum .Box.on .btnBox .btn.btn1 {
  background-color: #fff;
}
.pageForum .Box.on .btnBox .btn.btn2 {
  background-color: #CDCFD6;
}
.pageForum .Box.on .btnBox .iconfont {
  background-color: #fff;
  color: #082CA4;
  transform: rotate(90deg);
}
.pageForum .contBox {
  width: 100%;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-top: none;
  display: none;
}
.pageForum .contBox.sel {
  display: block;
}
.pageForum .contBox .cont {
  width: 100%;
  background-color: #FAFBFF;
  padding: 0.2rem 0.3rem 0.3rem;
}
.pageForum .contBox .cont .t {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #3D3D3D;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 0.1rem 0;
}
.pageForum .contBox .cont .t p {
  margin: 0.1rem 0;
}
.pageForum .contBox .cont .t p:nth-child(1) {
  width: 1.44rem;
}
.pageForum .contBox .cont .t p:nth-child(2) {
  width: calc(100% - 1.44rem);
}
.pageForum .contBox .cont .t p.p {
  width: 1.84rem;
}
.pageForum .contBox .cont .t p.p1 {
  width: calc(100% - 1.84rem);
}
.pageForum .contBox .cont .t p span:last-child em {
  display: none;
}
.pageForum .contBox .cont .t1 {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #3D3D3D;
  padding: 0.2rem 0 0.1rem;
}
.pageForum .contBox .cont .t2 {
  font-size: 0.24rem;
  line-height: 0.4rem;
  color: #666666;
  overflow: hidden;
}
.pageForum .contBox .cont .t2.on {
  height: auto;
}
.pageForum .contBox .cont .show {
  margin-top: 0.24rem;
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  cursor: pointer;
}
.pageForum .contBox .cont .show .iconfont {
  width: 0.32rem;
  height: 0.32rem;
  line-height: 0.32rem;
  border-radius: 0.16rem;
  background-color: #082CA4;
  color: #fff;
  transform: rotate(90deg);
  text-align: center;
  font-size: 0.12rem;
  margin-left: 0.1rem;
  transition: all 0.6s ease;
}
.pageForum .contBox .cont .show.sel .iconfont {
  transform: rotate(-90deg);
}
.pageForum .contBox .dl {
  width: 100%;
  padding: 0.3rem;
  position: relative;
}
.pageForum .contBox .dl::after {
  content: '';
  width: 1px;
  height: calc(100% - 1.4rem);
  border-left: 1px dashed #082CA4;
  position: absolute;
  top: 0.4rem;
  left: 2.18rem;
}
.pageForum .contBox .dl .dt {
  width: 100%;
  margin-bottom: 0.2rem;
}
.pageForum .contBox .dl .dt .time {
  width: 2.08rem;
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  text-align: right;
  position: relative;
  padding-right: 0.5rem;
}
.pageForum .contBox .dl .dt .time::after {
  content: '';
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 50%;
  background-color: rgba(8, 44, 164, 0.1);
  position: absolute;
  top: 0;
  right: 0.04rem;
}
.pageForum .contBox .dl .dt .time::before {
  content: '';
  width: 0.14rem;
  height: 0.14rem;
  border-radius: 50%;
  background-color: #082CA4;
  position: absolute;
  top: 0.08rem;
  right: 0.12rem;
}
.pageForum .contBox .dl .dt .dd {
  width: calc(100% - 2.16rem);
}
.pageForum .contBox .dl .dt .dd .t3 {
  font-size: 0.28rem;
  line-height: 0.32rem;
  color: #333333;
  margin-bottom: 0.16rem;
}
.pageForum .contBox .dl .dt .dd .li {
  width: 100%;
  margin-bottom: 0.2rem;
  align-items: center;
  background-color: #FAFBFF;
  padding: 0.2rem;
}
.pageForum .contBox .dl .dt .dd .li .img {
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.14rem;
}
.pageForum .contBox .dl .dt .dd .li .img img {
  width: 100%;
  display: block;
}
.pageForum .contBox .dl .dt .dd .li .t4 {
  width: calc(100% - 0.74rem);
}
.pageForum .contBox .dl .dt .dd .li .t4 .name {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #333333;
}
.pageForum .contBox .dl .dt .dd .li .t4 .t5 {
  font-size: 0.2rem;
  line-height: 0.32rem;
  color: #666;
  margin-top: 0.06rem;
}
/* pageCharacter */
.pageCharacter {
  width: 100%;
  padding: 0.3rem 0.3rem 0.5rem;
}
.pageCharacter .lists {
  width: 100%;
  margin-bottom: 0.1rem;
}
.pageCharacter .lists li {
  width: 100%;
  margin: 0 0 0.3rem 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
.pageCharacter .lists li a {
  align-items: center;
}
.pageCharacter .lists li .img {
  width: 40%;
  overflow: hidden;
}
.pageCharacter .lists li .img img {
  width: 100%;
  display: block;
}
.pageCharacter .lists li .cont {
  width: 60%;
  padding: 0.3rem;
}
.pageCharacter .lists li .cont .name {
  font-size: 0.32rem;
  font-weight: bold;
  line-height: 0.4rem;
  color: #333;
}
.pageCharacter .lists li .cont .p {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #999999;
  margin-top: 0.1rem;
}
.pageCharacter .lists li .cont .a {
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  align-items: center;
  margin-top: 0.3rem;
}
.pageCharacter .lists li .cont .a .iconfont {
  font-size: 0.28rem;
  color: #082CA4;
  transform: rotate(180deg);
  margin-left: 0.1rem;
}
/* pageCharacterD */
.pageCharacterD {
  width: 100%;
  padding: 0.3rem 0 0.5rem;
}
.pageCharacterD .gallery-top {
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 0 0.3rem;
}
.pageCharacterD .gallery-top .between {
  width: 100%;
  background-color: #FAFBFF;
  align-items: center;
}
.pageCharacterD .gallery-top .between .img {
  width: 2.4rem;
  overflow: hidden;
}
.pageCharacterD .gallery-top .between .img img {
  width: 100%;
  display: block;
}
.pageCharacterD .gallery-top .between .cont {
  width: calc(100% - 2.4rem);
  padding: 0.3rem;
}
.pageCharacterD .gallery-top .between .cont .name {
  font-size: 0.32rem;
  font-weight: bold;
  line-height: 0.4rem;
  color: #333333;
}
.pageCharacterD .gallery-top .between .cont .t {
  font-size: 0.24rem;
  line-height: 0.4rem;
  color: #999999;
  margin-top: 0.2rem;
}
.pageCharacterD .gallery-top .arrow {
  width: 2.4rem;
  height: 0.8rem;
  position: relative;
  margin: 0.4rem auto 0;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next {
  right: 0;
}
.pageCharacterD .gallery-top .arrow .swiper-button-prev {
  left: 0;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next,
.pageCharacterD .gallery-top .arrow .swiper-button-prev {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 50%;
  background-color: #252525;
  margin: 0;
  top: 0;
  transition: all 0.6s ease;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next::after,
.pageCharacterD .gallery-top .arrow .swiper-button-prev::after {
  color: #fff;
  font-size: 0.24rem;
}
.pageCharacterD .gallery-top .arrow .swiper-button-next:hover,
.pageCharacterD .gallery-top .arrow .swiper-button-prev:hover {
  background-color: #082CA4;
}
/* pageHotel */
.pageHotel {
  width: 100%;
  padding: 0.5rem 0.3rem;
}
.pageHotel .t {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #666666;
  margin-top: 0.3rem;
}
.pageHotel .cont {
  background-color: #FAFBFF;
  margin-top: 0.3rem;
}
.pageHotel .cont .img {
  width: 100%;
  overflow: hidden;
}
.pageHotel .cont .img img {
  width: 100%;
  display: block;
}
.pageHotel .cont .txt {
  width: 100%;
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #333333;
  padding: 0.3rem;
}
.pageHotel .cont .txt p {
  margin: 0.1rem 0;
}
.pageHotel .t1 {
  font-size: 0.32rem;
  line-height: 0.4rem;
  color: #333333;
  margin-top: 0.4rem;
}
.pageHotel table {
  width: 100%;
  margin-top: 0.2rem;
  text-align: center;
}
.pageHotel table tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageHotel table th {
  background-color: #082CA4;
  color: #fff;
  font-weight: normal;
  font-size: 0.24rem;
  line-height: 0.32rem;
  padding: 0.14rem 0;
  color: #FFFFFF;
}
.pageHotel table td {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  padding: 0.14rem 0;
  width: 18%;
}
.pageHotel table td:nth-child(2) {
  width: 25%;
}
.pageHotel table td:nth-child(4) {
  width: 39%;
  border-right: none;
}
.pageHotel .tips {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #999999;
  margin-top: 0.3rem;
}
.pageHotel .p {
  width: 2rem;
  height: 0.7rem;
  line-height: 0.7rem;
  background-color: #082CA4;
  text-align: center;
  font-size: 0.24rem;
  color: #FFFFFF;
  margin-top: 0.4rem;
}
.pageHotel .box {
  width: 100%;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.3rem;
}
.pageHotel .box .t2 {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #333333;
}
.pageHotel .box a {
  display: block;
  width: 2.2rem;
  height: 0.7rem;
  line-height: 0.7rem;
  border-radius: 0.35rem;
  background: #082CA4;
  font-size: 0.24rem;
  color: #FFFFFF;
  text-align: center;
  margin-top: 0.3rem;
}
.pageHotel .map {
  width: 100%;
  margin-top: 0.3rem;
  overflow: hidden;
}
.pageHotel .map img {
  width: 100%;
  display: block;
}
/* pageReview */
.pageReview {
  width: 100%;
  padding: 0.3rem;
}
.pageReview ul {
  width: 100%;
}
.pageReview ul li {
  width: calc(50% - 0.15rem);
  margin: 0 0.3rem 0.3rem 0;
}
.pageReview ul li:nth-child(2n) {
  margin: 0 0 0.3rem 0;
}
.pageReview ul li .img {
  width: 100%;
  background-color: #000;
  overflow: hidden;
  position: relative;
}
.pageReview ul li .img img {
  width: 100%;
  display: block;
  opacity: 0.5;
}
.pageReview ul li .img .btn {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: #082CA4;
  text-align: center;
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #FFFFFF;
  padding: 0.18rem 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.pageReview ul li .img .btn::after {
  content: '';
  width: 1.3rem;
  height: 1.3rem;
  border-radius: 50%;
  background-color: rgba(8, 44, 164, 0.5);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.pageReview ul li .t {
  width: 100%;
  text-align: center;
  background-color: #FAFBFF;
  padding: 0.3rem;
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #333333;
}
/* pageTheme */
.pageTheme {
  width: 100%;
  padding: 0.3rem;
}
.pageTheme .img {
  width: 100%;
  overflow: hidden;
}
.pageTheme .img img {
  width: 100%;
  display: block;
}
.pageTheme .cont {
  width: 100%;
  background-color: #FAFBFF;
  padding: 0.3rem;
}
.pageTheme .cont .tit {
  font-size: 0.36rem;
  line-height: 0.4rem;
  color: #333333;
}
.pageTheme .cont .txt {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #666666;
  margin-top: 0.2rem;
}
.pageTheme .text {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #666666;
  text-align: justify;
  margin-top: 0.3rem;
}
.pageTheme .text img {
  max-width: 100%;
  height: auto;
}
/* pageSchedule */
.pageSchedule {
  width: 100%;
  padding: 0.4rem 0.3rem;
}
.pageSchedule .title {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333333;
  text-align: center;
}
.pageSchedule .p {
  font-size: 0.24rem;
  line-height: 0.44rem;
  color: #666666;
  text-align: center;
  margin: 0.3rem 0;
}
.pageSchedule .p span {
  margin: 0 0.1rem;
}
.pageSchedule .time {
  width: 100%;
  height: 0.8rem;
  line-height: 0.8rem;
  background-color: #082CA4;
  color: #fff;
  font-size: 0.28rem;
  text-align: center;
}
.pageSchedule .time.time1 {
  background-color: #52A199;
}
.pageSchedule table {
  width: 100%;
  text-align: center;
}
.pageSchedule table tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.pageSchedule table th {
  background-color: #FAFBFF;
  font-size: 0.24rem;
  line-height: 0.32rem;
  font-weight: normal;
  color: #666666;
  padding: 0.24rem 0;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.pageSchedule table th:last-child {
  border-right: none;
}
.pageSchedule table td {
  font-size: 0.24rem;
  line-height: 0.32rem;
  color: #333333;
  padding: 0.24rem 0;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  width: 53%;
}
.pageSchedule table td:nth-child(1) {
  width: 20%;
}
.pageSchedule table td:nth-child(3) {
  width: 27%;
  border-right: none;
}
.pageSchedule .tips {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #999999;
  margin-top: 0.3rem;
}
/* pageGuest */
.pageGuest {
  width: 100%;
  padding: 0.4rem 0.3rem;
}
.pageGuest .lists {
  width: 100%;
}
.pageGuest .lists li {
  width: 100%;
  margin: 0 0 0.3rem 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
.pageGuest .lists li a {
  padding: 0.3rem;
  align-items: center;
}
.pageGuest .lists li a .img {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  overflow: hidden;
}
.pageGuest .lists li a .img img {
  width: 100%;
  display: block;
}
.pageGuest .lists li a .cont {
  width: calc(100% - 2.3rem);
}
.pageGuest .lists li a .cont .name {
  font-size: 0.32rem;
  line-height: 0.4rem;
  color: #3D3D3D;
}
.pageGuest .lists li a .cont .t {
  font-size: 0.28rem;
  line-height: 0.44rem;
  color: #999999;
  margin-top: 0.2rem;
}
/* pageAtlas */
.pageAtlas {
  width: 100%;
  padding: 0.3rem;
}
.pageAtlas ul {
  width: 100%;
}
.pageAtlas ul li {
  width: 100%;
  margin: 0 0 0.3rem 0;
}
.pageAtlas ul li img {
  width: 100%;
  display: block;
}
/* pageReport */
.pageReport {
  width: 100%;
  padding: 0.3rem;
}
.pageReport ul {
  width: 100%;
}
.pageReport ul li {
  width: 100%;
  margin: 0 0 0.3rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.3rem;
}
.pageReport ul li .tit {
  font-size: 0.32rem;
  line-height: 0.44rem;
  color: #333333;
}
.pageReport ul li .time {
  font-size: 0.28rem;
  line-height: 0.32rem;
  color: #999999;
  margin-top: 0.2rem;
}
.pageReport ul li .down {
  display: block;
  width: 2rem;
  height: 0.66rem;
  line-height: 0.66rem;
  border-radius: 0.33rem;
  background-color: #082CA4;
  font-size: 0.24rem;
  color: #FFFFFF;
  text-align: center;
  margin-top: 0.3rem;
}
/* pagePrint */
.pagePrint {
  width: 100%;
  padding: 0.3rem;
}
.pagePrint ul {
  width: 100%;
}
.pagePrint ul li {
  width: 100%;
  background: url(../images/printBg.jpg) center center no-repeat;
  background-size: cover;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.3rem;
  align-items: center;
  cursor: pointer;
}
.pagePrint ul li .img {
  width: 2.2rem;
  overflow: hidden;
}
.pagePrint ul li .img img {
  width: 100%;
  display: block;
}
.pagePrint ul li .cont {
  width: calc(100% - 2.4rem);
  margin-left: 0.2rem;
}
.pagePrint ul li .cont .t {
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #333333;
}
.pagePrint ul li .cont .p {
  font-size: 0.24rem;
  line-height: 0.28rem;
  color: #333333;
  margin-top: 0.2rem;
}
.pagePrint ul li .cont .open {
  display: block;
  width: 1.8rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border-radius: 0.3rem;
  background-color: #082CA4;
  text-align: center;
  font-size: 0.2rem;
  color: #FFFFFF;
  margin-top: 0.3rem;
}
/* pagePartner */
.pagePartner {
  width: 100%;
  padding: 0.3rem;
}
.pagePartner ul {
  width: 100%;
}
.pagePartner ul li {
  width: calc(50% - 0.15rem);
  height: 1.8rem;
  margin: 0 0.3rem 0.3rem 0;
  border: 1px solid rgba(51, 51, 51, 0.2);
  overflow: hidden;
  transition: all 0.6s ease;
}
.pagePartner ul li:nth-child(2n) {
  margin: 0 0 0.3rem 0;
}
.pagePartner ul li a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.pagePartner ul li img {
  max-width: 100%;
  max-height: 100%;
}
/* noData */
.noData {
  width: 100%;
  padding: 1rem 0;
  font-size: 0.32rem;
  line-height: 0.4rem;
  text-align: center;
  color: #333;
  font-weight: bold;
}
