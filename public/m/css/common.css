* {
	margin: 0;
	padding: 0;
	outline: none;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; 
}
html {
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}
body, html {
	width: 100%;
	/* height: 100%; */
	-webkit-tap-highlight-color: transparent;
	-webkit-overflow-scrolling: touch;
}
body {
	font-family: -apple-system-font, HSSM, Arial;
	font-size: 0.28rem;
	color: #333;
	background-color: #fff;
	--color: #082CA4;
	margin: 0 auto !important;
}
body, header, div, ul, li, p, a, span, form, input, textarea, button {
	margin: 0;
	padding: 0;
}
header, footer, nav, article, aside, section {
    display: block;
}
input::-ms-input-placeholder, textarea::-ms-input-placeholder {
	color: #999;
}
input, select, textarea, button {
    font: inherit;
    color: inherit;
    border: none;
    border-radius: 0;
    background-color: transparent;
    -webkit-appearance: none;
}
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
    height: auto;
}
textarea {
	resize: none;
}
table {
	width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}
img {
	border: none;
}
li {
	list-style: none;
}
a {
	border: 0;
	color: #999;
	text-decoration: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}
.clear {
	zoom: 1;
	clear: both
}
.clear:after {
	content: "";
	display: block;
	clear: both
}
.fl {
	float: left
}
.fr {
	float: right
}
.hide {
	display: none;
}
.white {
	background-color: #fff;
}
.left {
	display: flex;
	flex-wrap: wrap;
}
.right {
	display: flex;
	justify-content: flex-end;
}
.between {
	display: flex;
	justify-content: space-between;
}
.around {
	display: flex;
	justify-content: space-around;
}
.center {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.centerT {
	display: flex;
	justify-content: center;
	align-items: center;
}
.cut {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.cutTwo {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.cutThree {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
.cutFour {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 4;
	-webkit-box-orient: vertical;
}
.scrollbar::-webkit-scrollbar { 
	width:0.04rem; 
	background:#dbe7ff; 
	border-radius: 0.04rem; 
}
.scrollbar::-webkit-scrollbar-button { 
	display:none;
}
.scrollbar::-webkit-scrollbar-track { 
	display:none;
}
.scrollbar::-webkit-scrollbar-track-piece { 
	display:none;
}
.scrollbar::-webkit-scrollbar-thumb { 
	background:#169F96; 
	border-radius: 0.04rem;
}
.scrollbar::-webkit-scrollbar-corner {
	display:none;
}
.scrollbar::-webkit-resizer {
	display:none;
}
/* @font-face {
  font-family: AlimamaShuHeiTi-Bold;src: url(../fonts/Alimama_ShuHeiTi_Bold.ttf);
} */

/* 分页 */
.pagination {
	width: 100%;
	margin-bottom: 0.4rem;
	display: flex;
	align-items: center;
	justify-content: center;
}

.pagination li {
	display: none;
	margin: 0 0.2rem;
}

.pagination li:first-child, .pagination li:last-child, .pagination li.active {
	display: block;
}

.pagination li a,
.pagination li span {
	display: block;
	background-color: var(--color);
	color: #fff;
	font-size: 0.24rem;
	line-height: 0.6rem;
	width: 1.4rem;
	text-align: center
}

.pagination .active a,
.pagination .active a:focus,
.pagination .active a:hover,
.pagination .active span,
.pagination .active span:focus,
.pagination .active span:hover {
	color: #fff;
	cursor: default;
	background-color: var(--color);
}

.pagination li a:focus,
.pagination li a:hover,
.pagination li span:focus,
.pagination li span:hover {
	cursor: pointer;
	color: #fff;
	background-color: var(--color);
}


@-webkit-keyframes pul { 
    0%{-webkit-transform: translate(0, 0);transform: translate(0, 0);} 
    50%{-webkit-transform: translate(0, 10px);transform: translate(0, 10px);} 
    100%{-webkit-transform: translate(0, 0); transform: translate(0, 0);} 
}
@-moz-keyframes pul { 
    0%{-webkit-transform: translate(0, 0);transform: translate(0, 0);} 
    50%{-webkit-transform: translate(0, 10px);transform: translate(0, 10px);} 
    100%{-webkit-transform: translate(0, 0); transform: translate(0, 0);} 
}
@keyframes pul {
    0%{-webkit-transform: translate(0, 0); transform: translate(0, 0);} 
    50%{-webkit-transform: translate(0, 10px); transform: translate(0, 10px);} 
    100%{-webkit-transform: translate(0, 0); transform: translate(0, 0);} 
}
