/* navBox */
.navBox {
    width: 100%;
    height: 100%;
    padding-top: 1.2rem;
    background-color: #082CA4;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 90;
    overflow-y: auto;
    display: none;

    .nav {
        width: 100%;
    }

    h3 {
        width: 100%;
        position: relative;
        padding: 0 0.4rem;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        font-weight: normal;

        a {
            color: #fff;
            font-size: 0.32rem;
            display: block;
            width: 100%;
            line-height: 1.12rem;
        }

        .iconfont {
            display: block;
            width: 0.32rem;
            color: #fff;
            font-size: 0.32rem;
            position: absolute;
            top: 50%;
            right: 0.3rem;
            transform: translateY(-50%);
            transition: all 0.6s ease;
        }

        &.on .iconfont {
            transform: translateY(-50%) rotate(90deg);
        }
    }

    .sub {
        display: none;
        width: 100%;
        padding: 0 0.6rem;
        border-top: 1px solid rgba(255, 255, 255, 0.15);

        li {
            width: 100%;
            position: relative;
            box-sizing: border-box;
            border-top: 1px solid rgba(255, 255, 255, 0.15);

            &:nth-child(1) {
                border-top: none;
            }

            a {
                color: #fff;
                font-size: 0.28rem;
                display: block;
                width: 100%;
                line-height: 0.96rem;
            }
        }
    }

    .SearchForm {
        width: 100%;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        padding: 0.4rem;

        form {
            width: 100%;
            height: 0.88rem;
            border-radius: 0.12rem;
            background-color: #FFFFFF;

            input {
                width: calc(100% - 1.08rem);
                display: block;
                padding: 0.2rem 0 0.2rem 0.24rem;
                color: #333;
                line-height: 0.48rem;
            }

            .btn {
                width: 1.08rem;
                display: block;

                img {
                    width: 0.36rem;
                    display: block;
                    margin: 0 auto;
                }
            }
        }
    }

    .language {
        display: block;
        width: 100%;
        border-top: 1px solid rgba(146, 147, 149, 0.15);
        line-height: 1.06rem;
        color: #fff;
        text-align: center;
        font-size: 0.32rem;
    }
}

/* headerBox */
.headerBox {
    width: 100%;
    height: 1.2rem;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    align-items: center;
    padding: 0 0.3rem;
    background-color: #fff;
    box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1);
    transition: all 0.6s ease;

    .logo {
        display: block;
        height: 0.72rem;

        img {
            display: block;
            height: 100%;
        }
    }

    .box {
        align-items: center;
    }

    .language {
        font-size: 0.32rem;
        line-height: 0.4rem;
        color: #333;
        margin-right: 0.24rem;

        a {
            color: #999;

            &.on {
                color: #333;
            }
        }

        span {
            margin: 0 0.1rem;
        }
    }

    .login {
        display: block;
        color: #333;
        font-size: 0.48rem;
        line-height: 0.58rem;
        margin-right: 0.24rem;
    }

    // .menu {
    //     width: 0.48rem;
    //     height: 0.36rem;
    //     position: relative;

    //     // display: inline-block;
    //     // height: 0.6rem;
    //     // line-height: 0.6rem;
    //     // background-color: #3FAA8D;
    //     // color: #fff;
    //     // font-size: 0.28rem;
    //     // padding: 0 0.3rem;
    //     // border-radius: 0.3rem;

    //     .solid {
    //         width: 0.48rem;
    //         height: 0.04rem;
    //         background-color: #3FAA8D;
    //         border-radius: 0.02rem;
    //         transition: all 0.6s ease;
    //         position: absolute;
    //     }

    //     .solid1 {
    //         top: 0;
    //         left: 0;
    //     }

    //     .solid2 {
    //         top: 0;
    //         left: 0;
    //         bottom: 0;
    //         margin: auto;
    //     }

    //     .solid3 {
    //         bottom: 0;
    //         left: 0;
    //         width: 0.48rem;
    //     }
    // }

    .menu {
        width: 0.64rem;
        height: 0.64rem;
        border-radius: 50%;
        position: relative;
        background-color: #082CA4;
        overflow: hidden;

        .solid {
            height: 2px;
            background-color: #fff;
            position: absolute;
            transition: all 0.6s ease;
        }

        .solid1 {
            width: 0.2rem;
            top: 0.18rem;
            right: 0.2rem;
        }

        .solid2 {
            width: 0.32rem;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .solid3 {
            width: 0.2rem;
            bottom: 0.18rem;
            left: 0.2rem;
        }
    }
}

.is-fixed {
    // background-color: var(--color);
    background-color: #fff;
}

.fixeds {
    // background-color: var(--color);
    background-color: #fff;
    // border-bottom: 1px solid rgba(146, 147, 149, 0.15);
}

// .onSolid1 {
//     transform: rotate(45deg) translateY(0.2rem);
//     top: 0.02rem !important;
//     left: 0.14rem !important;
// }

// .onSolid2 {
//     transform: translateX(0.48rem);
//     opacity: 0;
// }

// .onSolid3 {
//     transform: rotate(-45deg) translateY(-0.2rem);
//     left: 0.14rem !important;
//     bottom: 0.02rem !important;
// }

.onSolid1 {
    width: 0.32rem !important;
    transform: rotate(45deg);
    top: 0.3rem !important;
    left: 0.16rem !important;
}

.onSolid2 {
    transform: translateX(0.64rem);
    opacity: 0;
}

.onSolid3 {
    width: 0.32rem !important;
    transform: rotate(-45deg);
    left: 0.16rem !important;
    bottom: 0.3rem !important;
}

/* indexBanner */
.indexBanner {
    width: 100%;
    margin-top: 1.2rem;
    position: relative;
    overflow: hidden;

    img {
        width: 100%;
        display: block;
    }

    .swiper-pagination {
        bottom: 0.3rem !important;

        .swiper-pagination-bullet {
            width: 0.16rem;
            height: 0.16rem;
            background-color: #fff;
            opacity: 0.5;
            margin: 0 0.12rem !important;

            &.swiper-pagination-bullet-active {
                opacity: 1;
            }
        }
    }
}

/* indexTitle */
.indexTitle {
    width: 100%;

    .title {
        font-size: 0.44rem;
        font-weight: bold;
        line-height: 0.48rem;
        color: #333333;
        position: relative;
        z-index: 1;
        display: inline-block;
        margin-left: 50%;
        transform: translateX(-50%);

        &::after {
            content: '';
            width: 0.77rem;
            height: 0.77rem;
            background: url(../images/titleBg.png) center center no-repeat;
            background-size: cover;
            position: absolute;
            top: -0.3rem;
            left: -0.3rem;
            z-index: -1;
        }
    }

    .p {
        font-size: 0.36rem;
        line-height: 0.4rem;
        text-transform: uppercase;
        color: #999999;
        margin-top: 0.1rem;
        display: inline-block;
        margin-left: 50%;
        transform: translateX(-50%);
    }
}

/* indexMore */
.indexMore {
    width: 2.8rem;
    height: 0.72rem;
    border-top: 1px solid rgba(8, 44, 164, 0.2);
    border-bottom: 1px solid rgba(8, 44, 164, 0.2);
    margin: 0.5rem auto 0;

    p {
        font-size: 0.24rem;
        line-height: 0.32rem;
        color: #333333;
    }

    .iconfont {
        color: #082CA4;
        font-size: 0.32rem;
        line-height: 0.32rem;
        margin-left: 0.1rem;
        transform: rotate(180deg);
    }
}

/* indexNews */
.indexNews {
    width: 100%;
    padding: 0.8rem 0.3rem;
    overflow: hidden;
    background: #fff url(../images/newsBg.png) top center no-repeat;
    background-size: 100% auto;

    .contBox {
        display: block;
        width: 100%;
        overflow: hidden;
        position: relative;
        margin-top: 0.4rem;

        img {
            width: 100%;
            display: block;
        }

        .cont {
            width: calc(100% - 0.6rem);
            background: rgba(255, 255, 255, 0.9);
            position: absolute;
            bottom: 0.3rem;
            left: 0.3rem;
            padding: 0.3rem;

            .time {
                display: inline-block;
                height: 0.48rem;
                line-height: 0.48rem;
                border-radius: 0.04rem;
                background-color: #082CA4;
                padding: 0 0.16rem;
                font-size: 0.24rem;
                color: #FFFFFF;
            }

            .tit {
                font-size: 0.32rem;
                line-height: 0.36rem;
                color: #333333;
                margin-top: 0.24rem;
            }

            .txt {
                font-size: 0.24rem;
                line-height: 0.28rem;
                color: #666666;
                margin-top: 0.24rem;
            }
        }
    }

    .newsBox {
        width: 100%;
        background-color: #082CA4;
        margin-top: 0.3rem;
        overflow: hidden;
        position: relative;
        padding-bottom: 1rem;

        a {
            display: block;
            width: 100%;
            height: 100%;
            padding: 0.3rem;

            .time {
                display: inline-block;
                height: 0.48rem;
                line-height: 0.48rem;
                border-radius: 0.04rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 0 0.16rem;
                font-size: 0.24rem;
                color: #FFFFFF;
            }

            .tit {
                font-size: 0.32rem;
                line-height: 0.44rem;
                color: #FFFFFF;
                margin-top: 0.2rem;
            }

            .txt {
                font-size: 0.24rem;
                line-height: 0.36rem;
                color: #FFFFFF;
                margin-top: 0.2rem;
            }
        }

        .swiper-pagination {
            bottom: 0.4rem !important;
            display: flex;
            align-items: center;
            justify-content: center;
    
            .swiper-pagination-bullet {
                width: 0.14rem;
                height: 0.14rem;
                background-color: #fff;
                opacity: 0.5;
                margin: 0 0.12rem !important;
    
                &.swiper-pagination-bullet-active {
                    width: 0.18rem;
                    height: 0.18rem;
                    opacity: 1;
                }
            }
        }
    }
}

/* indexAd */
.indexAd {
    width: 100%;
    padding: 0.7rem 0.5rem;
    background: url(../images/adBg.jpg) center center no-repeat;
    background-size: cover;
    overflow: hidden;

    .t {
        font-size: 0.32rem;
        font-weight: bold;
        line-height: 0.48rem;
        color: #FFFFFF;
    }

    img {
        width: 0.6rem;
        display: block;
        margin-top: 0.3rem;
    }
}

/* indexSchedule */
.indexSchedule {
    width: 100%;
    padding: 0.8rem 0 0.6rem;
    overflow: hidden;
    background: #fff url(../images/scheduleBg.png) top center;
    background-size: 100% auto;

    .timeBox {
        width: 100%;
        margin-top: 0.4rem;
        padding: 0 0.2rem;
        position: relative;
        z-index: 1;

        &::before {
            content: '';
            width: 100%;
            height: 1px;
            background-color: #082CA4;
            position: absolute;
            top: 0.32rem;
            left: 0;
            z-index: -1;
        }

        li {
            width: 1.4rem;
            height: 0.64rem;
            line-height: 0.6rem;
            border-radius: 0.32rem;
            background-color: #FFFFFF;
            border: 1px solid #082CA4;
            color: #082CA4;
            font-size: 0.28rem;
            text-align: center;
            cursor: pointer;
            margin: 0 0.2rem;
            transition: all 0.6s ease;

            &.on, &:hover {
                background-color: #082CA4;
                color: #fff;
            }
        }
    }

    .scheduleBox {
        width: 100%;
        margin-top: 0.5rem;
        padding: 0 0.3rem;

        li {
            width: 100%;
            display: none;

            &:first-child {
                display: block;
            }
        }

        .liBox {
            width: 100%;
            margin-bottom: 0.3rem;
        }

        .Box {
            width: 100%;
            background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
            border: 2px solid rgba(8, 44, 164, 0.12);
            border-top: 2px solid #082CA4;
            box-shadow: 0 0.04rem 0.2rem 0 rgba(0, 0, 0, 0.08);
            padding: 0.3rem;
            transition: all 0.6s ease;
         
            .tit {
                width: calc(100% - 1.5rem);
                font-size: 0.28rem;
                font-weight: bold;
                line-height: 0.4rem;
                color: #333333;
            }

            .label {
                min-width: 1.1rem;
                height: 0.42rem;
                line-height: 0.42rem;
                border-radius: 0.04rem;
                font-size: 0.2rem;
                padding: 0 0.06rem;

                &.label1 {
                    border: 1px solid #52A199;
                    color: #52A199;
                }

                &.label2 {
                    border: 1px solid #082CA4;
                    color: #082CA4;
                }

                &.label3 {
                    border: 1px solid #999999;
                    color: #999999;
                }
                
                .iconfont {
                    margin-right: 0.06rem;
                    font-size: 0.24rem;
                }
            }

            .address {
                font-size: 0.24rem;
                line-height: 0.28rem;
                color: #999999;
                // margin-top: 0.16rem;

                .iconfont {
                    font-size: 0.28rem;
                    margin-right: 0.06rem;
                }

                .left {
                    margin-top: 0.16rem;

                    p {
                        max-width: calc(100% - 0.34rem);
                    }
                }
            }

            .btnBox {
                align-items: center;
                border-top: 1px solid rgba(0, 0, 0, 0.08);
                margin-top: 0.3rem;
                padding-top: 0.3rem;

                .btn {
                    display: block;
                    width: 1.8rem;
                    height: 0.56rem;
                    line-height: 0.56rem;
                    border-radius: 0.28rem;
                    font-size: 0.24rem;
                    color: #FFFFFF;
                    text-align: center;

                    &.btn1 {
                        background-color: #082CA4;
                    }

                    &.btn2 {
                        background-color: #CDCFD6;
                    }
                }

                .iconfont {
                    width: 0.56rem;
                    height: 0.56rem;
                    line-height: 0.56rem;
                    color: #fff;
                    font-size: 0.2rem;
                    background-color: #262626;
                    text-align: center;
                    border-radius: 50%;
                    transition: all 0.6s ease;
                }
            }

            &.on {
                background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);
    
                .tit {
                    color: #fff;
                }

                .label {
                    &.label1 {
                        border: 1px solid #fff;
                        color: #fff;
                    }

                    &.label2 {
                        border: 1px solid #fff;
                        color: #fff;
                    }

                    &.label3 {
                        border: 1px solid #fff;
                        color: #fff;
                    }
                }

                .address {
                    color: #fff;
                }

                .btnBox {
                    border-top: 1px solid rgba(255, 255, 255, 0.08);

                    .btn {
                        color: #082CA4;
    
                        &.btn1 {
                            background-color: #fff;
                        }
    
                        &.btn2 {
                            background-color: #CDCFD6;
                        }
                    }

                    .iconfont {
                        background-color: #fff;
                        color: #082CA4;
                        transform: rotate(90deg);
                    }
                }
            }
        }

        .contBox {
            width: 100%;
            background-color: #FFFFFF;
            border: 1px solid rgba(0, 0, 0, 0.12);
            border-top: none;
            display: none;

            &.sel {
                display: block;
            }

            .cont {
                width: 100%;
                background-color: #FAFBFF;
                padding: 0.2rem 0.3rem 0.3rem;

                .t {
                    font-size: 0.28rem;
                    line-height: 0.4rem;
                    color: #3D3D3D;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                    padding: 0.1rem 0;

                    p {
                        margin: 0.1rem 0;
                        
                        &:nth-child(1) {
                            width: 1.44rem;
                        }
    
                        &:nth-child(2) {
                            width: calc(100% - 1.44rem);
                        }
    
                        &.p {
                            width: 1.84rem;
                        }
    
                        &.p1 {
                            width: calc(100% - 1.84rem);
                        }

                        span {
                            &:last-child em {
                                display: none;
                            }
                        }
                    }
                }

                .t1 {
                    font-size: 0.28rem;
                    line-height: 0.4rem;
                    color: #3D3D3D;
                    padding: 0.2rem 0 0.1rem;
                }

                .t2 {
                    font-size: 0.24rem;
                    line-height: 0.4rem;
                    color: #666666;
                    // height: 0.8rem;
                    overflow: hidden;

                    &.on {
                        height: auto;
                    }
                }

                .show {
                    margin-top: 0.24rem;
                    font-size: 0.24rem;
                    line-height: 0.32rem;
                    color: #333333;
                    cursor: pointer;

                    .iconfont {
                        width: 0.32rem;
                        height: 0.32rem;
                        line-height: 0.32rem;
                        border-radius: 0.16rem;
                        background-color: #082CA4;
                        color: #fff;
                        transform: rotate(90deg);
                        text-align: center;
                        font-size: 0.12rem;
                        margin-left: 0.1rem;
                        transition: all 0.6s ease;
                    }

                    &.sel .iconfont {
                        transform: rotate(-90deg);
                    }
                }
            }

            .dl {
                width: 100%;
                padding: 0.3rem;
                position: relative;

                &::after {
                    content: '';
                    width: 1px;
                    height: calc(100% - 1.4rem);
                    border-left: 1px dashed #082CA4;
                    position: absolute;
                    top: 0.4rem;
                    left: 2.18rem;
                }

                .dt {
                    width: 100%;
                    margin-bottom: 0.2rem;

                    .time {
                        width: 2.08rem;
                        font-size: 0.24rem;
                        line-height: 0.32rem;
                        color: #333333;
                        text-align: right;
                        position: relative;
                        padding-right: 0.5rem;

                        &::after {
                            content: '';
                            width: 0.3rem;
                            height: 0.3rem;
                            border-radius: 50%;
                            background-color: rgba(8, 44, 164, 0.1);
                            position: absolute;
                            top: 0;
                            right: 0.04rem;
                        }

                        &::before {
                            content: '';
                            width: 0.14rem;
                            height: 0.14rem;
                            border-radius: 50%;
                            background-color: #082CA4;
                            position: absolute;
                            top: 0.08rem;
                            right: 0.12rem;
                        }
                    }

                    .dd {
                        width: calc(100% - 2.16rem);

                        .t3 {
                            font-size: 0.28rem;
                            line-height: 0.32rem;
                            color: #333333;
                            margin-bottom: 0.16rem;
                        }

                        .li {
                            width: 100%;
                            margin-bottom: 0.2rem;
                            align-items: center;
                            background-color: #FAFBFF;
                            padding: 0.2rem;

                            .img {
                                width: 0.6rem;
                                height: 0.6rem;
                                border-radius: 50%;
                                overflow: hidden;
                                margin-right: 0.14rem;

                                img {
                                    width: 100%;
                                    display: block;
                                }
                            }

                            .t4 {
                                width: calc(100% - 0.74rem);

                                .name {
                                    font-size: 0.24rem;
                                    line-height: 0.28rem;
                                    color: #333333;
                                }
    
                                .t5 {
                                    font-size: 0.2rem;
                                    line-height: 0.32rem;
                                    color: #666;
                                    margin-top: 0.06rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/* indexVideo */
.indexVideo {
    width: 100%;
    padding: 0.8rem 0;
    background: #fff url(../images/videoBg.png) center center no-repeat;
    background-size: cover;
    overflow: hidden;

    .tabList {
        width: 4.96rem;
        height: 0.8rem;
        border-radius: 0.4rem;
        background-color: rgba(8, 44, 164, 0.06);
        margin: 0.4rem auto 0;

        li {
            width: 2.4rem;
            height: 0.64rem;
            border-radius: 0.32rem; 
            font-size: 0.28rem;
            line-height: 0.64rem;
            color: #082CA4;
            text-align: center;

            &.on {
                background-color: #082CA4;
                color: #FFFFFF;
            }
        }
    }

    .lists {
        width: 100%;
        margin-top: 0.5rem;
    }

    .videoBox {
        width: 100%;
        position: relative;
        overflow: hidden;

        .swiper-slide {
            width: 5.8rem !important;
            transform: scale(0.9);

            a {
                display: block;
                width: 100%;

                .img {
                    width: 100%;
                    overflow: hidden;
                    position: relative;
                    background-color: #000;

                    .pic {
                        width: 100%;
                        display: block;
                        opacity: 0.6;
                    }

                    .play {
                        width: 1rem;
                        display: block;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }

                .tit {
                    font-size: 0.32rem;
                    line-height: 0.44rem;
                    color: #333333;
                    border: 1px solid rgba(8, 44, 164, 0.12);
                    padding: 0.4rem;
                    border-top: none;
                    opacity: 0;
                }
            }

            &.swiper-slide-active{
                transform: scale(1);

                a .tit {
                    opacity: 1;
                }
            }
        }

        .swiper-pagination {
            position: static !important;
            margin-top: 0.4rem;
    
            .swiper-pagination-bullet {
                width: 0.14rem;
                height: 0.14rem;
                background-color: #082CA4;
                opacity: 0.5;
                margin: 0 0.12rem !important;
    
                &.swiper-pagination-bullet-active {
                    opacity: 1;
                }
            }
        }
    }
}

/* indexPartner */
.indexPartner {
    width: 100%;
    background: url(../images/partnerBg.jpg) center center no-repeat;
    background-size: cover;
    padding: 0.8rem 0.3rem 0.2rem;

    .box {
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 0.18rem;
        margin-bottom: 0.3rem;

        &:last-child {
            border-bottom: none;
        }

        .p {
            font-size: 0.28rem;
            font-weight: bold;
            line-height: 0.32rem;
            color: #FFFFFF;
            margin-bottom: 0.3rem;
        }

        ul {
            width: 100%;

            li {
                width: 2.2rem;
                height: 1.2rem;
                // background-color: rgba(255, 255, 255, 0.5);
                // border: 1px solid rgba(255, 255, 255, 0.2);
                background-color: #fff;
                margin: 0 0.15rem 0.15rem 0;

                &:nth-child(3n) {
                    margin: 0 0 0.15rem 0;
                }

                a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                }

                img {
                    max-width: 100%;
                    max-height: 100%;
                }
            }
        }
    }
}

/* indexFooter */
.indexFooter {
    width: 100%;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    .box {
        width: 100%;
        padding: 0.7rem 0.45rem 0;

        .t {
            font-size: 0.32rem;
            line-height: 0.36rem;
            color: #333333;
            margin-bottom: 0.3rem;
        }

        .rwm {
            width: 2rem;

            img {
                width: 100%;
                display: block;
                border: 1px solid rgba(0, 0, 0, 0.08);
            }

            p {
                font-size: 0.24rem;
                line-height: 0.28rem;
                color: #666666;
                text-align: center;
                margin-top: 0.2rem;
            }
        }
    }

    .copy {
        width: 100%;
        padding: 0.3rem;
        margin-top: 0.6rem;
        font-size: 0.2rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        color: #999;
        line-height: 0.32rem;

        a {
            color: #999;
        }
    }
}

/* goTop */
.goTop {
    width: 0.76rem;
    height: 0.76rem;
    line-height: 0.76rem;
    border-radius: 50%;
    background-color: var(--color);
    color: #fff;
    font-size: 0.32rem;
    text-align: center;
    position: fixed;
    bottom: 10%;
    right: 0.2rem;
    z-index: 100;
    display: none;
}

/* pageBanner */
.pageBanner {
    width: 100%;
    height: 3.6rem;
    position: relative;
    background-size: cover !important;

    .location {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        align-items: center;
        padding: 0.2rem 0.3rem;
        line-height: 0.4rem;

        .sel {
            color: #fff;
            font-size: 0.32rem;
            margin-right: 0.1rem;
        }

        .icon-jiantou {
            color: #fff;
            font-size: 0.16rem;
            margin: 0 0.1rem;
        }

        p {
            font-size: 0.24rem;
            color: #fff;
        }

        a {
            font-size: 0.24rem;
            color: #fff;
        }
    }
}

/* pageNavBox */
.pageNavBox {
    width: 100%;
    overflow-y: hidden;
    overflow-x: auto;
    white-space: nowrap;
    position: sticky;
    top: 1.2rem;
    z-index: 20;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    &::-webkit-scrollbar {
        display: none;
    }
}

/* pageNav */
.pageNav {
    width: 100%;

    li {
        padding-left: 0.3rem;
        
        &:last-child {
            padding-right: 0.3rem;
        }

        a {
            display: inline-block;
            font-size: 0.28rem;
            line-height: 0.4rem;
            color: #333333;
            padding: 0.22rem 0;
            border-bottom: 0.04rem solid rgba(63, 170, 141, 0);
        }

        &.on a {
            border-bottom: 0.04rem solid #082CA4;
            color: #082CA4;
            font-weight: bold;
        }
    }
}

/* pageTab */
.pageTab {
    width: calc(100% - 0.6rem);
    margin: 0.4rem auto 0;
    background-color: #FAFBFF;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.36rem;
    overflow: hidden;

    .t {
        width: 100%;
        height: 0.72rem;
        line-height: 0.72rem;
        position: relative;
        padding-left: 0.4rem;
        display: none;

        .iconfont {
            position: absolute;
            top: 50%;
            right: 0.4rem;
            transform: translateY(-50%) rotate(90deg);
            font-size: 0.24rem;
            color: #999999;
            transition: all 0.6s ease;
        }

        &.on .iconfont {
            transform: translateY(-50%) rotate(-90deg);
        }

        &.sel {
            display: block;
        }
    }

    ul {
        width: 100%;
        background-color: #FFFFFF;
        display: none;

        li {
            height: 0.7rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);

            a {
                display: block;
                width: 100%;
                height: 100%;
                font-size: 0.28rem;
                line-height: 0.7rem;
                color: #333333;
                padding: 0 0.4rem;
            }

            &.on {
                background-color: #082CA4;

                a {
                    color: #fff;
                }
            }
        }
    }
}

/* pageTitle */
.pageTitle {
    font-size: 0.32rem;
    line-height: 0.36rem;
    color: #333333;
    position: relative;
    padding-bottom: 0.2rem;
    font-weight: bold;

    &::after {
        content: '';
        width: 0.32rem;
        height: 0.06rem;
        background-color: #082CA4;
        position: absolute;
        bottom: 0;
        left: 0;
    }
}

/* pageAbout */
.pageAbout {
    width: 100%;
    padding: 0.5rem 0.3rem;

    .text {
        font-size: 0.28rem;
        line-height: 0.44rem;
        color: #666666;
        text-align: justify;
        margin-top: 0.3rem;

        img {
            max-width: 100%;
            height: auto;
        }
    }
}

/* pageOrganization */
.pageOrganization {
    width: 100%;
    padding: 0.4rem 0.3rem;

    ul {
        width: 100%;
        margin-top: 0.4rem;

        li {
            width: 100%;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-left: 0.04rem solid #082CA4;
            margin-bottom: 0.3rem;
            padding: 0.3rem;

            .t1 {
                font-size: 0.32rem;
                line-height: 0.4rem;
                color: #666666;
                font-weight: bold;
            }

            .t2 {
                font-size: 0.28rem;
                line-height: 0.4rem;
                color: #666666;
                margin-top: 0.2rem;

                p {
                    margin-right: 0.3rem;
                }
            }
        }
    }
}

/* pageNews */
.pageNews {
    width: 100%;
    padding: 0.3rem 0 0.5rem;

    .newsBox {
        width: 100%;
        padding: 0 0.3rem 0.5rem;
        position: relative;
        overflow: hidden;
        margin-bottom: 0.5rem;

        .swiper-slide {
            background-color: #FAFBFF;
        }

        a {
            width: 100%;
            align-items: center;

            .img {
                width: 100%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: 100%;
                padding: 0.3rem;

                .tit {
                    font-size: 0.32rem;
                    line-height: 0.44rem;
                    color: #333;
                }
    
                .txt {
                    font-size: 0.28rem;
                    line-height: 0.44rem;
                    color: #999;
                    margin-top: 0.2rem;
                }
    
                .time {
                    font-size: 0.28rem;
                    line-height: 0.32rem;
                    color: #666;
                    margin-top: 0.3rem;
                }
            }
        }

        .swiper-pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            bottom: 0 !important;

            .swiper-pagination-bullet {
                width: 0.1rem;
                height: 0.1rem;
                opacity: 0.5;
                background-color: #082CA4;
                margin: 0 0.2rem 0 0;

                &.swiper-pagination-bullet-active {
                    width: 0.16rem;
                    height: 0.16rem;
                    opacity: 1;
                }
            }
        }
    }

    .lists {
        width: 100%;
        margin: 0 0 0.4rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);

        li {
            width: 100%;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);

            a {
                background-color: #ffffff;
                padding: 0.4rem 0.3rem;
                align-items: center;
            }

            .time {
                font-size: 0.28rem;
                line-height: 0.52rem;
                color: #999999;

                p {
                    font-size: 0.48rem;
                    color: #333333;
                }
            }

            .img {
                width: 2.6rem;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: calc(100% - 1.5rem);

                &.cont1 {
                    width: calc(100% - 2.8rem);
                }

                .tit {
                    font-size: 0.32rem;
                    line-height: 0.36rem;
                    color: #333333;
                }

                .txt {
                    font-size: 0.28rem;
                    line-height: 0.4rem;
                    color: #999999;
                    margin-top: 0.1rem;
                }

                .a {
                    align-items: center;
                    line-height: 0.32rem;
                    margin-top: 0.2rem;
                    
                    p {
                        font-size: 0.28rem;
                        color: #999999;
                    }

                    .iconfont {
                        color: #082CA4;
                        font-size: 0.32rem;
                        transform: rotate(180deg);
                        margin-left: 0.1rem;
                    }
                }
            }
        }
    }

    // .release {
    //     width: 100%;
    //     position: relative;

    //     &::after {
    //         content: '';
    //         width: 1px;
    //         height: 100%;
    //         background-color: rgba(0, 0, 0, 0.1);
    //         position: absolute;
    //         top: 0;
    //         left: 50%;
    //         transform: translateX(-50%);
    //     }

    //     li {
    //         width: 46%;

    //         &:nth-child(2n-1) {
    //             margin-right: 8%;
    //         }

    //         .img {
    //             width: 100%;
    //             overflow: hidden;

    //             img {
    //                 width: 100%;
    //                 display: block;
    //             }
    //         }

    //         .cont {
    //             width: 100%;
    //             padding: 30px;

    //             .tit {
    //                 font-size: 30px;
    //                 line-height: 40px;
    //                 color: #333333;
    //             }

    //             .txt {
    //                 font-size: 20px;
    //                 line-height: 24px;
    //                 color: #999999;
    //                 margin-top: 30px;
    //             }
    //         }
    //     }
    // }
}

/* pageActivity */
.pageActivity {
    width: 100%;
    padding: 0.3rem 0.3rem 0.5rem;

    // .activityBox {
    //     width: 100%;
    //     position: relative;

    //     .gallery-top {
    //         width: calc(100% - 250px);
    //         margin: 0;

    //         a {
    //             width: 100%;

    //             .img {
    //                 width: 700px;
    //                 overflow: hidden;

    //                 img {
    //                     width: 100%;
    //                     display: block;
    //                 }
    //             }

    //             .cont {
    //                 width: calc(100% - 700px);
    //                 padding: 30px 0 0 30px;

    //                 .label {
    //                     display: inline-block;
    //                     width: 120px;
    //                     height: 40px;
    //                     border-radius: 3px;
    //                     background-color: #082CA4;
    //                     font-size: 18px;
    //                     line-height: 40px;
    //                     color: #FFFFFF;
    //                     text-align: center;
    //                 }

    //                 .tit {
    //                     font-size: 30px;
    //                     line-height: 44px;
    //                     color: #333333;
    //                     margin-top: 30px;
    //                 }

    //                 .time {
    //                     margin-top: 50px;

    //                     .p {
    //                         font-size: 18px;
    //                         line-height: 22px;
    //                         color: #999999;
    //                         margin-top: 20px;

    //                         .iconfont {
    //                             font-size: 22px;
    //                             margin-right: 10px;
    //                         }
    //                     }
    //                 }
    //             }
    //         }

    //         .arrow {
    //             width: 158px;
    //             height: 64px;
    //             position: absolute;
    //             bottom: 30px;
    //             left: 730px;

    //             .swiper-button-next {
    //                 right: 0;
    //             }

    //             .swiper-button-prev {
    //                 left: 0;
    //             }

    //             .swiper-button-next, .swiper-button-prev {
    //                 width: 64px;
    //                 height: 64px;
    //                 border-radius: 50%;
    //                 background-color: #252525;
    //                 margin: 0;
    //                 top: 0;
    //                 transition: all 0.6s ease;

    //                 &::after {
    //                     color: #fff;
    //                     font-size: 14px;
    //                 }

    //                 &:hover {
    //                     background-color: #082CA4;
    //                 }
    //             }
    //         }
    //     }

    //     .gallery-thumbs {
    //         width: 216px;
    //         height: 100%;
    //         position: absolute;
    //         top: 0;
    //         right: 0;

    //         .swiper-wrapper {
    //             justify-content: space-between;
    //             transform: none !important;

    //             .swiper-slide {
    //                 height: auto !important;
    //                 background-color: #000;

    //                 img {
    //                     width: 100%;
    //                     display: block;
    //                     opacity: 0.6;
    //                     transition: all 0.6s ease;
    //                 }

    //                 &.swiper-slide-thumb-active img {
    //                     opacity: 1;
    //                 }
    //             }
    //         }
    //     }
    // }

    .lists {
        width: 100%;
        margin: 0 0 0.1rem;

        li {
            width: 100%;
            margin: 0 0 0.3rem;

            .img {
                width: 100%;
                overflow: hidden;
                position: relative;

                img {
                    width: 100%;
                    display: block;
                }

                .label {
                    height: 0.6rem;
                    background-color: #082CA4;
                    font-size: 0.24rem;
                    line-height: 0.6rem;
                    color: #FFFFFF;
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding: 0 0.2rem;
                }
            }

            .cont {
                width: 100%;
                padding: 0.3rem;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-top: none;
                background-color: #fff;
                transition: all 0.6s ease;

                .tit {
                    font-size: 0.32rem;
                    line-height: 0.44rem;
                    color: #333333;
                    transition: all 0.6s ease;
                }

                .txt {
                    font-size: 0.28rem;
                    line-height: 0.4rem;
                    color: #666;
                    margin-top: 0.2rem;
                }

                .time {
                    margin-top: 0.2rem;

                    .p {
                        font-size: 0.24rem;
                        line-height: 0.32rem;
                        color: #999999;
                        margin-top: 0.2rem;
                        transition: all 0.6s ease;

                        .iconfont {
                            font-size: 0.28rem;
                            margin-right: 0.1rem;
                        }
                    }
                }
            }

            &:hover .cont {
                background-color: #082CA4;

                .tit, .txt {
                    color: #fff;
                }

                .time {
                    .p {
                        color: #fff;
                    }
                }
            }
        }
    }
}

/* pageVideo */
.pageVideo {
    width: 100%;
    padding: 0.3rem 0.3rem 0.5rem;

    // .videoBox {
    //     width: 100%;
    //     position: relative;

    //     .gallery-top {
    //         width: 58%;
    //         margin: 0;

    //         .img {
    //             width: 100%;
    //             display: block;
    //             overflow: hidden;
    //             background-color: #000;
    //             position: relative;

    //             img {
    //                 width: 100%;
    //                 display: block;
    //                 opacity: 0.6;
    //             }

    //             .iconfont {
    //                 position: absolute;
    //                 top: 50%;
    //                 left: 50%;
    //                 transform: translate(-50%, -50%);
    //                 color: #fff;
    //                 font-size: 70px;
    //             }
    //         }
    //     }

    //     .gallery-thumbs {
    //         width: 42%;
    //         height: 100%;
    //         position: absolute;
    //         top: 0;
    //         right: 0;
    //         background-color: #FAFBFF;
    //         overflow: visible;

    //         .swiper-wrapper {
    //             justify-content: space-between;
    //             transform: none !important;

    //             .swiper-slide {
    //                 height: 33.3333% !important;
    //                 font-size: 32px;
    //                 line-height: 50px;
    //                 color: #333333;
    //                 position: relative;
    //                 display: flex;
    //                 align-items: center;
    //                 justify-content: center;
    //                 border-bottom: 1px solid rgba(8, 44, 164, 0.2);
    //                 cursor: pointer;

    //                 &::after {
    //                     content: '';
    //                     width: calc(100% + 30px);
    //                     height: 100%;
    //                     background-color: #082CA4;
    //                     position: absolute;
    //                     top: 0;
    //                     right: 0;
    //                     z-index: 1;
    //                     opacity: 0;
    //                     transition: all 0.6s ease;
    //                 }

    //                 &:last-child {
    //                     border-bottom: none;
    //                 }

    //                 p {
    //                     width: 100%;
    //                     position: relative;
    //                     z-index: 2;
    //                     padding: 0 5%;
    //                     transition: all 0.6s ease;
    //                 }

    //                 .iconfont {
    //                     position: relative;
    //                     z-index: 2;
    //                     color: #fff;
    //                     font-size: 32px;
    //                     position: absolute;
    //                     bottom: 5%;
    //                     right: 5%;
    //                     opacity: 0;
    //                     transition: all 0.6s ease;
    //                 }

    //                 &.swiper-slide-thumb-active {
    //                     color: #fff;

    //                     p {
    //                         padding: 0 5% 0 0;
    //                     }

    //                     &::after {
    //                         opacity: 1;
    //                     }

    //                     .iconfont {
    //                         opacity: 1;
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    .lists {
        width: 100%;
        margin: 0 0 0.1rem;

        li {
            width: 100%;
            margin: 0 0 0.3rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);

            a {
                display: block;
                padding-bottom: 0.3rem;
            }

            .img {
                width: 100%;
                overflow: hidden;
                background-color: #000;
                position: relative;

                img {
                    width: 100%;
                    display: block;
                    opacity: 0.6;
                }

                .iconfont {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #fff;
                    font-size: 0.8rem;
                }
            }

            .tit {
                font-size: 0.32rem;
                line-height: 0.44rem;
                color: #333333;
                margin-top: 0.2rem;
            }

            .t {
                font-size: 0.24rem;
                line-height: 0.44rem;
                color: #999999;
                margin-top: 0.2rem;
            }
        }
    }

    &.pageVideo1 {
        .lists {
            li {
                .img {
                    img {
                        opacity: 1;
                    }
    
                    .iconfont {
                        display: none;
                    }
                }
            }
        }
    }
}

/* pageNewsD */
.pageNewsD {
    width: 100%;
    padding: 0.4rem 0.3rem;

    .title {
        text-align: center;
        font-size: 0.32rem;
        line-height: 0.44rem;
        color: #333;
        font-weight: bold;
    }

    .date {
        color: #999;
        font-size: 0.24rem;
        text-align: center;
        border-bottom: 1px dashed #ddd;
        padding: 0.3rem 0;
        margin-bottom: 0.3rem;

        span {
            margin: 0 0.1rem;
        }
    }

    .text {
        font-size: 0.28rem;
        line-height: 0.44rem;
        color: #333;

        img {
            max-width: 100%;
            height: auto;
        }

        video {
            display: block;
            width: 100%;
        }

        iframe {
            width: 7.1rem;
            height: 4.1rem;
            margin: 0 auto;
            display: block;
        }
    }

    .box {
        width: 100%;
        border-top: 1px dashed #ddd;
        padding: 0.3rem 0 0;
        margin-top: 0.3rem;

        a {
            color: #999;
            font-size: 0.24rem;
            line-height: 0.44rem;
            display: inline-block;
            margin-bottom: 0.3rem;
            transition: all 0.6s ease;

            &:hover {
                color: #082CA4;
            }
        }
    }
}

/* pageForum */
.pageForum {
    width: 100%;
    padding: 0.3rem;

    .liBox {
        width: 100%;
        margin-bottom: 0.3rem;
    }

    .Box {
        width: 100%;
        background: linear-gradient(180deg, #EEF2FF 0%, #FFFFFF 100%);
        border: 2px solid rgba(8, 44, 164, 0.12);
        border-top: 2px solid #082CA4;
        box-shadow: 0 0.04rem 0.2rem 0 rgba(0, 0, 0, 0.08);
        padding: 0.3rem;
        transition: all 0.6s ease;
        
        .tit {
            width: calc(100% - 1.5rem);
            font-size: 0.28rem;
            font-weight: bold;
            line-height: 0.4rem;
            color: #333333;
        }

        .label {
            min-width: 1.1rem;
            height: 0.42rem;
            line-height: 0.42rem;
            border-radius: 0.04rem;
            font-size: 0.2rem;
            padding: 0 0.06rem;

            &.label1 {
                border: 1px solid #52A199;
                color: #52A199;
            }

            &.label2 {
                border: 1px solid #082CA4;
                color: #082CA4;
            }

            &.label3 {
                border: 1px solid #999999;
                color: #999999;
            }
            
            .iconfont {
                margin-right: 0.06rem;
                font-size: 0.24rem;
            }
        }

        .address {
            font-size: 0.24rem;
            line-height: 0.28rem;
            color: #999999;
            // margin-top: 0.16rem;

            .iconfont {
                font-size: 0.28rem;
                margin-right: 0.06rem;
            }

            .left {
                margin-top: 0.16rem;

                p {
                    max-width: calc(100% - 0.34rem);
                }
            }
        }

        .btnBox {
            align-items: center;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            margin-top: 0.3rem;
            padding-top: 0.3rem;

            .btn {
                display: block;
                width: 1.8rem;
                height: 0.56rem;
                line-height: 0.56rem;
                border-radius: 0.28rem;
                font-size: 0.24rem;
                color: #FFFFFF;
                text-align: center;

                &.btn1 {
                    background-color: #082CA4;
                }

                &.btn2 {
                    background-color: #CDCFD6;
                }
            }

            .iconfont {
                width: 0.56rem;
                height: 0.56rem;
                line-height: 0.56rem;
                color: #fff;
                font-size: 0.2rem;
                background-color: #262626;
                text-align: center;
                border-radius: 50%;
                transition: all 0.6s ease;
            }
        }

        &.on {
            background: linear-gradient(180deg, #082CA4 0%, #082CA4 100%);

            .tit {
                color: #fff;
            }

            .label {
                &.label1 {
                    border: 1px solid #fff;
                    color: #fff;
                }

                &.label2 {
                    border: 1px solid #fff;
                    color: #fff;
                }

                &.label3 {
                    border: 1px solid #fff;
                    color: #fff;
                }
            }

            .address {
                color: #fff;
            }

            .btnBox {
                border-top: 1px solid rgba(255, 255, 255, 0.08);

                .btn {
                    color: #082CA4;

                    &.btn1 {
                        background-color: #fff;
                    }

                    &.btn2 {
                        background-color: #CDCFD6;
                    }
                }

                .iconfont {
                    background-color: #fff;
                    color: #082CA4;
                    transform: rotate(90deg);
                }
            }
        }
    }

    .contBox {
        width: 100%;
        background-color: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-top: none;
        display: none;

        &.sel {
            display: block;
        }

        .cont {
            width: 100%;
            background-color: #FAFBFF;
            padding: 0.2rem 0.3rem 0.3rem;

            .t {
                font-size: 0.28rem;
                line-height: 0.4rem;
                color: #3D3D3D;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                padding: 0.1rem 0;

                p {
                    margin: 0.1rem 0;

                    &:nth-child(1) {
                        width: 1.44rem;
                    }

                    &:nth-child(2) {
                        width: calc(100% - 1.44rem);
                    }

                    &.p {
                        width: 1.84rem;
                    }

                    &.p1 {
                        width: calc(100% - 1.84rem);
                    }

                    span {
                        &:last-child em {
                            display: none;
                        }
                    }
                }
            }

            .t1 {
                font-size: 0.28rem;
                line-height: 0.4rem;
                color: #3D3D3D;
                padding: 0.2rem 0 0.1rem;
            }

            .t2 {
                font-size: 0.24rem;
                line-height: 0.4rem;
                color: #666666;
                // height: 0.8rem;
                overflow: hidden;

                &.on {
                    height: auto;
                }
            }

            .show {
                margin-top: 0.24rem;
                font-size: 0.24rem;
                line-height: 0.32rem;
                color: #333333;
                cursor: pointer;

                .iconfont {
                    width: 0.32rem;
                    height: 0.32rem;
                    line-height: 0.32rem;
                    border-radius: 0.16rem;
                    background-color: #082CA4;
                    color: #fff;
                    transform: rotate(90deg);
                    text-align: center;
                    font-size: 0.12rem;
                    margin-left: 0.1rem;
                    transition: all 0.6s ease;
                }

                &.sel .iconfont {
                    transform: rotate(-90deg);
                }
            }
        }

        .dl {
            width: 100%;
            padding: 0.3rem;
            position: relative;

            &::after {
                content: '';
                width: 1px;
                height: calc(100% - 1.4rem);
                border-left: 1px dashed #082CA4;
                position: absolute;
                top: 0.4rem;
                left: 2.18rem;
            }

            .dt {
                width: 100%;
                margin-bottom: 0.2rem;

                .time {
                    width: 2.08rem;
                    font-size: 0.24rem;
                    line-height: 0.32rem;
                    color: #333333;
                    text-align: right;
                    position: relative;
                    padding-right: 0.5rem;

                    &::after {
                        content: '';
                        width: 0.3rem;
                        height: 0.3rem;
                        border-radius: 50%;
                        background-color: rgba(8, 44, 164, 0.1);
                        position: absolute;
                        top: 0;
                        right: 0.04rem;
                    }

                    &::before {
                        content: '';
                        width: 0.14rem;
                        height: 0.14rem;
                        border-radius: 50%;
                        background-color: #082CA4;
                        position: absolute;
                        top: 0.08rem;
                        right: 0.12rem;
                    }
                }

                .dd {
                    width: calc(100% - 2.16rem);

                    .t3 {
                        font-size: 0.28rem;
                        line-height: 0.32rem;
                        color: #333333;
                        margin-bottom: 0.16rem;
                    }

                    .li {
                        width: 100%;
                        margin-bottom: 0.2rem;
                        align-items: center;
                        background-color: #FAFBFF;
                        padding: 0.2rem;

                        .img {
                            width: 0.6rem;
                            height: 0.6rem;
                            border-radius: 50%;
                            overflow: hidden;
                            margin-right: 0.14rem;

                            img {
                                width: 100%;
                                display: block;
                            }
                        }

                        .t4 {
                            width: calc(100% - 0.74rem);

                            .name {
                                font-size: 0.24rem;
                                line-height: 0.28rem;
                                color: #333333;
                            }

                            .t5 {
                                font-size: 0.2rem;
                                line-height: 0.32rem;
                                color: #666;
                                margin-top: 0.06rem;
                            }
                        }
                    }
                }
            }
        }
    }
}

/* pageCharacter */
.pageCharacter {
    width: 100%;
    padding: 0.3rem 0.3rem 0.5rem;

    .lists {
        width: 100%;
        margin-bottom: 0.1rem;

        li {
            width: 100%;
            margin: 0 0 0.3rem 0;
            border: 1px solid rgba(0, 0, 0, 0.12);

            a {
                align-items: center;
            }

            .img {
                width: 40%;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: 60%;
                padding: 0.3rem;

                .name {
                    font-size: 0.32rem;
                    font-weight: bold;
                    line-height: 0.4rem;
                    color: #333;
                }

                .p {
                    font-size: 0.28rem;
                    line-height: 0.44rem;
                    color: #999999;
                    margin-top: 0.1rem;
                }

                .a {
                    font-size: 0.24rem;
                    line-height: 0.32rem;
                    color: #333333;
                    align-items: center;
                    margin-top: 0.3rem;

                    .iconfont {
                        font-size: 0.28rem;
                        color: #082CA4;
                        transform: rotate(180deg);
                        margin-left: 0.1rem;
                    }
                }
            }
        }
    }
}

/* pageCharacterD */
.pageCharacterD {
    width: 100%;
    padding: 0.3rem 0 0.5rem;

    .gallery-top {
        width: 100%;
        position: relative;
        overflow: hidden;
        padding: 0 0.3rem;

        .between {
            width: 100%;
            background-color: #FAFBFF;
            align-items: center;

            .img {
                width: 2.4rem;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: calc(100% - 2.4rem);
                padding: 0.3rem;

                .name {
                    font-size: 0.32rem;
                    font-weight: bold;
                    line-height: 0.4rem;
                    color: #333333;
                }

                .t {
                    font-size: 0.24rem;
                    line-height: 0.4rem;
                    color: #999999;
                    margin-top: 0.2rem;
                }
            }
        }

        .arrow {
            width: 2.4rem;
            height: 0.8rem;
            position: relative;
            margin: 0.4rem auto 0;

            .swiper-button-next {
                right: 0;
            }

            .swiper-button-prev {
                left: 0;
            }

            .swiper-button-next, .swiper-button-prev {
                width: 0.8rem;
                height: 0.8rem;
                border-radius: 50%;
                background-color: #252525;
                margin: 0;
                top: 0;
                transition: all 0.6s ease;

                &::after {
                    color: #fff;
                    font-size: 0.24rem;
                }

                &:hover {
                    background-color: #082CA4;
                }
            }
        }
    }

    // .gallery-thumbs {
    //     width: 100%;
    //     margin-top: 40px;

    //     .swiper-slide {
    //         background-color: #000;

    //         img {
    //             width: 100%;
    //             display: block;
    //             opacity: 0.6;
    //             transition: all 0.6s ease;
    //         }

    //         &.swiper-slide-thumb-active img {
    //             opacity: 1;
    //         }
    //     }
    // }
}

/* pageHotel */
.pageHotel {
    width: 100%;
    padding: 0.5rem 0.3rem;

    .t {
        font-size: 0.28rem;
        line-height: 0.44rem;
        color: #666666;
        margin-top: 0.3rem;
    }

    .cont {
        background-color: #FAFBFF;
        margin-top: 0.3rem;

        .img {
            width: 100%;
            overflow: hidden;
    
            img {
                width: 100%;
                display: block;
            }
        }

        .txt {
            width: 100%;
            font-size: 0.28rem;
            line-height: 0.44rem;
            color: #333333;
            padding: 0.3rem;
            
            p {
                margin: 0.1rem 0;
            }
        }
    }

    .t1 {
        font-size: 0.32rem;
        line-height: 0.4rem;
        color: #333333;
        margin-top: 0.4rem;
    }

    table {
        width: 100%;
        margin-top: 0.2rem;
        text-align: center;

        tr {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: #082CA4;
            color: #fff;
            font-weight: normal;
            font-size: 0.24rem;
            line-height: 0.32rem;
            padding: 0.14rem 0;
            color: #FFFFFF;
        }

        td {
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 0.24rem;
            line-height: 0.32rem;
            color: #333333;
            padding: 0.14rem 0;
            width: 18%;

            &:nth-child(2) {
                width: 25%;
            }

            &:nth-child(4) {
                width: 39%;
                border-right: none;
            }
        }
    }

    .tips {
        font-size: 0.28rem;
        line-height: 0.4rem;
        color: #999999;
        margin-top: 0.3rem;
    }

    .p {
        width: 2rem;
        height: 0.7rem;
        line-height: 0.7rem;
        background-color: #082CA4;
        text-align: center;
        font-size: 0.24rem;
        color: #FFFFFF;
        margin-top: 0.4rem;
    }

    .box {
        width: 100%;
        background-color: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 0.3rem;

        .t2 {
            font-size: 0.28rem;
            line-height: 0.44rem;
            color: #333333;
        }

        a {
            display: block;
            width: 2.2rem;
            height: 0.7rem;
            line-height: 0.7rem;
            border-radius: 0.35rem;
            background: #082CA4;
            font-size: 0.24rem;
            color: #FFFFFF;
            text-align: center;
            margin-top: 0.3rem;
        }
    }

    .map {
        width: 100%;
        margin-top: 0.3rem;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
        }
    }
}

/* pageReview */
.pageReview {
    width: 100%;
    padding: 0.3rem;

    ul {
        width: 100%;
        
        li {
            width: calc(50% - 0.15rem);
            margin: 0 0.3rem 0.3rem 0;

            &:nth-child(2n) {
                margin: 0 0 0.3rem 0;
            }

            .img {
                width: 100%;
                background-color: #000;
                overflow: hidden;
                position: relative;

                img {
                    width: 100%;
                    display: block;
                    opacity: 0.5;
                }

                .btn {
                    width: 1rem;
                    height: 1rem;
                    border-radius: 50%;
                    background-color: #082CA4;
                    text-align: center;
                    font-size: 0.24rem;
                    line-height: 0.32rem;
                    color: #FFFFFF;
                    padding: 0.18rem 0;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1;

                    &::after {
                        content: '';
                        width: 1.3rem;
                        height: 1.3rem;
                        border-radius: 50%;
                        background-color: rgba(8, 44, 164, 0.5);
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        z-index: -1;
                    }
                }
            }

            .t {
                width: 100%;
                text-align: center;
                background-color: #FAFBFF;
                padding: 0.3rem;
                font-size: 0.28rem;
                line-height: 0.4rem;
                color: #333333;
            }
        }
    }
}

/* pageTheme */
.pageTheme {
    width: 100%;
    padding: 0.3rem;

    .img {
        width: 100%;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
        }
    }

    .cont {
        width: 100%;
        background-color: #FAFBFF;
        padding: 0.3rem;

        .tit {
            font-size: 0.36rem;
            line-height: 0.4rem;
            color: #333333;
        }

        .txt {
            font-size: 0.28rem;
            line-height: 0.44rem;
            color: #666666;
            margin-top: 0.2rem;
        }
    }    
    
    .text {
        font-size: 0.28rem;
        line-height: 0.44rem;
        color: #666666;
        text-align: justify;
        margin-top: 0.3rem;

        img {
            max-width: 100%;
            height: auto;
        }
    }
}

/* pageSchedule */
.pageSchedule {
    width: 100%;
    padding: 0.4rem 0.3rem;

    .title {
        font-size: 0.32rem;
        line-height: 0.44rem;
        color: #333333;
        text-align: center;
    }

    .p {
        font-size: 0.24rem;
        line-height: 0.44rem;
        color: #666666;
        text-align: center;
        margin: 0.3rem 0;

        span {
            margin: 0 0.1rem;
        }
    }

    .time {
        width: 100%;
        height: 0.8rem;
        line-height: 0.8rem;
        background-color: #082CA4;
        color: #fff;
        font-size: 0.28rem;
        text-align: center;

        &.time1 {
            background-color: #52A199;
        }
    }

    table {
        width: 100%;
        text-align: center;

        tr {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        th {
            background-color: #FAFBFF;
            font-size: 0.24rem;
            line-height: 0.32rem;
            font-weight: normal;
            color: #666666;
            padding: 0.24rem 0;
            border-right: 1px solid rgba(0, 0, 0, 0.1);

            &:last-child {
                border-right: none;
            }
        }

        td {
            font-size: 0.24rem;
            line-height: 0.32rem;
            color: #333333;
            padding: 0.24rem 0;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            width: 53%;

            &:nth-child(1) {
                width: 20%;
            }
            
            &:nth-child(3) {
                width: 27%;
                border-right: none;
            }
        }
    }

    .tips {
        font-size: 0.28rem;
        line-height: 0.4rem;
        color: #999999;
        margin-top: 0.3rem;
    }
}

/* pageGuest */
.pageGuest {
    width: 100%;
    padding: 0.4rem 0.3rem;

    .lists {
        width: 100%;

        li {
            width: 100%;
            margin: 0 0 0.3rem 0;
            border: 1px solid rgba(0, 0, 0, 0.12);

            a {
                padding: 0.3rem;
                align-items: center;

                .img {
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    overflow: hidden;

                    img {
                        width: 100%;
                        display: block;
                    }
                }

                .cont {
                    width: calc(100% - 2.3rem);

                    .name {
                        font-size: 0.32rem;
                        line-height: 0.4rem;
                        color: #3D3D3D;
                    }

                    .t {
                        font-size: 0.28rem;
                        line-height: 0.44rem;
                        color: #999999;
                        margin-top: 0.2rem;
                    }
                }
            }
        }
    }
}

/* pageAtlas */
.pageAtlas {
    width: 100%;
    padding: 0.3rem; 

    ul {
        width: 100%;

        li {
            width: 100%;
            margin: 0 0 0.3rem 0;

            img {
                width: 100%;
                display: block;
            }
        }
    }
}

/* pageReport */
.pageReport {
    width: 100%;
    padding: 0.3rem;

    ul {
        width: 100%;

        li {
            width: 100%;
            margin: 0 0 0.3rem;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.3rem;

            .tit {
                font-size: 0.32rem;
                line-height: 0.44rem;
                color: #333333;
            }

            .time {
                font-size: 0.28rem;
                line-height: 0.32rem;
                color: #999999;
                margin-top: 0.2rem;
            }

            .down {
                display: block;
                width: 2rem;
                height: 0.66rem;
                line-height: 0.66rem;
                border-radius: 0.33rem;
                background-color: #082CA4;
                font-size: 0.24rem;
                color: #FFFFFF;
                text-align: center;
                margin-top: 0.3rem;
            }
        }
    }
}

/* pagePrint */
.pagePrint {
    width: 100%;
    padding: 0.3rem;

    ul {
        width: 100%;

        li {
            width: 100%;
            background: url(../images/printBg.jpg) center center no-repeat;
            background-size: cover;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.3rem;
            align-items: center;
            cursor: pointer;

            .img {
                width: 2.2rem;
                overflow: hidden;

                img {
                    width: 100%;
                    display: block;
                }
            }

            .cont {
                width: calc(100% - 2.4rem);
                margin-left: 0.2rem;

                .t {
                    font-size: 0.28rem;
                    line-height: 0.4rem;
                    color: #333333;
                }

                .p {
                    font-size: 0.24rem;
                    line-height: 0.28rem;
                    color: #333333;
                    margin-top: 0.2rem;
                }

                .open {
                    display: block;
                    width: 1.8rem;
                    height: 0.6rem;
                    line-height: 0.6rem;
                    border-radius: 0.3rem;
                    background-color: #082CA4;
                    text-align: center;
                    font-size: 0.2rem;
                    color: #FFFFFF;
                    margin-top: 0.3rem;
                }
            }
        }
    }
}

/* pagePartner */
.pagePartner {
    width: 100%;
    padding: 0.3rem;

    ul {
        width: 100%;

        li {
            width: calc(50% - 0.15rem);
            height: 1.8rem;
            margin: 0 0.3rem 0.3rem 0;
            border: 1px solid rgba(51, 51, 51, 0.2);
            overflow: hidden;
            transition: all 0.6s ease;

            &:nth-child(2n) {
                margin: 0 0 0.3rem 0;
            }

            a {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
            }
            
            img {
                // width: 100%;
                // display: block;
                max-width: 100%;
                max-height: 100%;
            }
        }
    }
}

/* noData */
.noData {
    width: 100%;
    padding: 1rem 0;
    font-size: 0.32rem;
    line-height: 0.4rem;
    text-align: center;
    color: #333;
    font-weight: bold;
}


