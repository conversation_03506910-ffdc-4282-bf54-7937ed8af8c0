<?php
// 直接调用控制器的入口文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>直接调用控制器测试</h1>";

// 定义必要的常量
define('APP_DEBUG', true);
define('APP_PATH', __DIR__ . '/../application/');
define('THINK_PATH', __DIR__ . '/../thinkphp/5.0.24/');

// 检查控制器文件是否存在
$controller_file = APP_PATH . 'web/controller/Index.php';
if (!file_exists($controller_file)) {
    die('控制器文件不存在: ' . $controller_file);
}

echo "✓ 控制器文件存在<br>";

// 尝试直接包含控制器文件
try {
    // 先加载ThinkPHP基础文件
    require THINK_PATH . 'base.php';
    
    echo "✓ ThinkPHP基础文件加载成功<br>";
    
    // 创建控制器实例
    $controller = new \app\web\controller\Index();
    
    echo "✓ 控制器实例创建成功<br>";
    
    // 调用index方法
    $result = $controller->index();
    
    echo "✓ 控制器方法调用成功<br>";
    
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    
    // 显示详细的错误堆栈
    echo "<h3>错误堆栈:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 