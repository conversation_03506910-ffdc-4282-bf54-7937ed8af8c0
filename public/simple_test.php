<?php
// 最简单的测试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>简单测试</h1>";

// 步骤1: 检查文件
echo "<h2>步骤1: 检查文件</h2>";
$files = [
    '../thinkphp/5.0.24/base.php',
    '../thinkphp/5.0.24/start.php',
    '../application/config.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✓ " . $file . " 存在<br>";
    } else {
        echo "✗ " . $file . " 不存在<br>";
    }
}

// 步骤2: 尝试加载base.php
echo "<h2>步骤2: 加载base.php</h2>";
try {
    require '../thinkphp/5.0.24/base.php';
    echo "✓ base.php 加载成功<br>";
} catch (Throwable $e) {
    echo "✗ base.php 加载失败: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    exit;
}

echo "<h2>测试完成</h2>";
?> 