<?php
// 独立的入口文件，不依赖ThinkPHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>浦江论坛 - 独立页面</h1>";
echo "<p>这是一个不依赖ThinkPHP框架的独立页面</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";

// 测试数据库连接
echo "<h2>数据库连接测试</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 测试查询
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>数据库表数量: " . count($tables) . "</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<h2>文件系统测试</h2>";
$test_files = [
    '../application/config.php',
    '../thinkphp/5.0.24/start.php',
    '../application/web/controller/Index.php'
];

foreach ($test_files as $file) {
    if (file_exists($file)) {
        echo "<p>✓ " . $file . " 存在</p>";
    } else {
        echo "<p style='color: red;'>✗ " . $file . " 不存在</p>";
    }
}

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='hello.php'>Hello World测试</a></li>";
echo "<li><a href='minimal.php'>最小化测试</a></li>";
echo "<li><a href='debug.php'>详细调试</a></li>";
echo "</ul>";
?> 