$(function(){
    var script = document.getElementsByTagName("script");
    eval(script[script.length-1].innerHTML);

    addArea = function (type, num) { //第几个
        var index = layer.open({
            type: 1,
            title: '区域信息',
            area: '820px',
            btnAlign: 'c',
            content: $('.popArea')
        });

        var chooseP = []; //页面编辑的时候存放的省份id集合
        var chooseC = []; //页面编辑的时候存放的城市id集合
        var editP = []; //某一项编辑的时候存放的省份集合
        var editC = []; //某一项编辑的时候存放的城市集合
        $("." + type + " tr." + type + "_express").each(function(){
            chooseP = chooseP.concat($(this).find("input.province_id").val().split(','));
            if (chooseP) {
                chooseC = chooseC.concat($(this).find("input.city_ids").val().split(','));
            }
        })
        
        if (chooseP) {
            province.forEach(item => {
                item.list.forEach(ele => {
                    ele.checked = false;
                    chooseC.forEach(pro => {
                        if (ele.code == pro) {
                            ele.checked = true;
                        }
                    })
                })
            })
        }

        //编辑某一项的时候，第几个记录下来
        $("#type").val(type);
        if (num) {
            $("#editIndex").val(num);
            editC = $("." + type + " ." + type + "_" + num + " .city_ids").val().split(',');
            editP = $("." + type + " ." + type + "_" + num + " .province_id").val().split(',');
        }


        var html = '';
        var shtml = '';
        province.forEach(item => {
            var style = '';
            let array = item.list.filter(ele => {
                if (ele.checked) {
                    return ele;
                }
            })
            if (array.length == item.list.length) {
                style = 'style="display: none;"';
            }
            var status = `" onclick="chooseProvince(this,${item.code})`;
            if (chooseP) {
                chooseP.forEach(res => { //已选择的省份不能再被选择
                    if (res == item.code) {
                        status = ' disabled';
                    }
                })
            }

            html += `<li class="province left province_${item.code}" ${style}>
                <div class="nameP between">
                    <p onclick="showCity(this)"><span class="iconfont icon-right"></span>${item.name}</p>
                    <div class="choose${status}">选择</div>
                </div>
                <ul class="province-sub left">`;
            item.list.forEach(ele => {
                var showLeft = '';
                if (ele.checked) {
                    showLeft = 'style="display: none;"';
                }
                html += `<li class="province-city left city_${ele.code}" ${showLeft}>
                    <div class="nameC between">
                        <p>${ele.name}</p>
                        <input type="hidden" class="province_id" value="${ele.code}">
                        <div class="choose" onclick="chooseCity(this,${ele.code},${item.code})">选择</div>
                    </div>
                </li>`;
            })
            html += `</ul>
            </li>`;
            
            var display = 'style="display: none;"';
            if (editP) {
                editP.forEach(res => { //编辑某一项的时候 省份id
                    if (res == item.code) {
                        display = '';
                    }
                })
            }
            shtml += `<li class="province left province_${item.code}" ${display}>
                <div class="nameP between">
                    <p onclick="showCity(this)"><span class="iconfont icon-right"></span>${item.name}</p>
                    <div class="choose" onclick="cancelProvince(this,${item.code})">取消</div>
                    <input type="hidden" class="province_id" value="${item.code}">
                    <input type="hidden" class="province_name" value="${item.name}">
                </div>
                <ul class="province-sub left">`;
            item.list.forEach(ele => {
                var show = 'style="display: none;"';
                if (editC) {
                    editC.forEach(res => {
                        if (res == ele.code) {
                            show = '';
                        }
                    })
                }
                shtml += `<li class="province-city left city_${ele.code}" ${show}>
                    <div class="nameC between">
                        <p>${ele.name}</p>
                        <div class="choose" onclick="cancelCity(this,${ele.code},${item.code})">取消</div>
                        <input type="hidden" class="city_id" value="${ele.code}">
                        <input type="hidden" class="city_name" value="${ele.name}">
                    </div>
                </li>`;
            })
            shtml += `</ul>
            </li>`;
        })
        $(".region-all .region-list").html(html);
        $(".region-selected .region-list").html(shtml);

        $(".cancel").click(function () {
            layer.close(index);
            $("#editIndex").val('');
            $("#type").val('');
        })
    }
    
    showCity = function (that) {
        $(that).parents('.province').toggleClass('active');
    }
    //选择省份
    chooseProvince = function (that,id) {
        $(".region-all .province_" + id).hide();
        $(".region-selected .province_" + id).show();
        var provinceId = [];
        $(".region-all .province_" + id + " .province-city").each(function(index) {
            provinceId.push($(this).find("input.province_id").val());
        });
        provinceId.forEach(ele => {
            $(".region-selected .province_" + id + " .city_" + ele).show();
        })
    }
    //取消省份
    cancelProvince = function (that,id) {
        $(".region-all .province_" + id).show();
        $(".region-all .province_" + id + " .province-city").show();
        $(".region-selected .province_" + id).hide();
    }

    //选择城市
    chooseCity = function (that,id,fid) {
        $(".region-all .city_" + id).hide();
        $(".region-selected .province_" + fid).show();
        $(".region-selected .province_" + fid + " .city_" + id).show();
        
        var hiddenLength = $(".region-all .province_" + fid + " .province-city:hidden").length;
        var childLength = $(".region-all .province_" + fid + " .province-city").length;
        if (hiddenLength == childLength) { //如果省下面的市都被选择了，那这个省也隐藏
            $(".region-all .province_" + fid).hide();
        }
    }

    //取消城市
    cancelCity = function (that,id,fid) {
        $(".region-selected .province_" + fid  + " .city_" + id).hide();
        $(".region-all .province_" + fid).show();
        $(".region-all .province_" + fid  + " .city_" + id).show();
        var hiddenLength = $(".region-selected .province_" + fid + " .province-city:hidden").length;
        var childLength = $(".region-selected .province_" + fid + " .province-city").length;
        if (hiddenLength == childLength) { //如果省下面的市都被取消了，那这个省也隐藏
            $(".region-selected .province_" + fid).hide();
            //$(".region-all .province_" + fid + " .nameP").find(".choose").hide(); //修复取消为选择
            //$(".region-all .province_" + fid + " .nameP").append('<div class="choose" onclick="chooseProvince(this,'+ fid +')">选择</div>');
        }
    }

    //删除已选择的区域
    deleteArea = function (obj,index,id) {
        $(obj + '_' + index).remove();
        if (id) {
            var delStr = $("#del").val();
            var str = '';
            delStr ? str += delStr + ',' + id : str += id;
            $("#del").val(str);
        }
    }
})