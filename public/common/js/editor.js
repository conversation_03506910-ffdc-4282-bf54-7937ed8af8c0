editFun("editorText", "#content-editor", "#toolbar-editor")
function editFun(editorVal<PERSON>, editor<PERSON>ontent, editorToolbar) {
	const html = document.getElementById(editorValue).value
	const { createEditor, createToolbar } = window.wangEditor

	const editor = createEditor({
		selector: editorContent,
		html: html,
		mode: "default",
		config: {
			MENU_CONF: {
				uploadImage: {
					server: host + "/wangeditor/wangeditor/upload_img",
					maxFileSize: 10 * 1024 * 1024, // 30M
					onFailed(file, res) {
						alert(res.message)
					},
					onError(file, err, res) {
						alert(err.message)
					}
				},
				insertVideo: {
					onInsertedVideo(videoNode) { }
				},
				uploadVideo: {
					server: host + "/wangeditor/wangeditor/upload_video",
					maxFileSize: 30 * 1024 * 1024, // 30M
					onFailed(file, res) {
						alert(res.message)
					},
					onError(file, err, res) {
						alert(err.message)
					}
				}
			},
			on<PERSON><PERSON><PERSON>(editor) {
				const html = editor.getHtml()
				document.getElementById(editorValue).value = html
			}
		}
	})

	const toolbar = createToolbar({
		editor,
		selector: editorToolbar,
		mode: "default",
		config: {
			excludeKeys: ['emotion', 'fullScreen']
		}
	})
}