$(function(){
    //添加多规格
    addMultiple = function () {
        var num = Number($("#numM").val()) + 1;
        var sku = `<tr class="param_${num}">
            <td><input type="hidden" class="param_id" value="${num}"><input style="width:200px" type="text" class="layui-input param"></td>
            <td>
                <div class="layui-form-upload param-logo">
                    <input type="hidden" class="logo">
                    <div class="uploadBtn" id="param-logo_${num}"></div>
                </div>
            </td>
            <td><input type="number" class="layui-input sale_price" lay-verify="digit"></td>
            <td><input type="number" class="layui-input market_price" lay-verify="digit"></td>
            <td><input type="number" class="layui-input stock" lay-verify="integer"></td>
            <td><input type="number" class="layui-input warn" lay-verify="integer"></td>
            <td class="operate">
                <div class="centerT">
                    <a href="javascript:;" class="green" onclick="copyParam(${num})">复制</a>
                    <a href="javascript:;" class="red" onclick="deleteParam(${num})">删除</a>
                </div>
            </td>
        </tr>`;
        $(".paramMultiple tbody").append(sku);
        uploadParamLogo('param-logo_'+num);
        $("#numM").val(num);
    }

    //复制多规格
    copyParam = function (id) {
        var name = $('.param_'+id).find("input.param").val();
        var logo = $('.param_'+id).find("input.logo").val();
        var sale_price = $('.param_'+id).find("input.sale_price").val();
        var market_price = $('.param_'+id).find("input.market_price").val();
        var stock = $('.param_'+id).find("input.stock").val();
        var warn = $('.param_'+id).find("input.warn").val();
        if ( name=='' || sale_price=='' || market_price=='' || stock=='' || warn=='') {
            layer.msg('请先完善该规格，然后才能复制', {icon: 2, shade: 0.3, time: 1500});
        } else {
            var num = Number($('#numM').val()) + 1;
            var sku = `<tr class="param_${num}">
                <td><input type="hidden" class="param_id" value="${num}"><input style="width:200px" type="text" value="${name}" class="layui-input param"></td>
                <td>
                    <div class="layui-form-upload param-logo">
                        <input type="hidden" class="logo" value="${logo}">
                        <div class="uploadBtn" style="background: none;" id="param-logo_${num}">
                            <img src="${logo}">
                        </div>
                    </div>
                </td>
                <td><input type="number" value="${sale_price}" class="layui-input sale_price" lay-verify="digit"></td>
                <td><input type="number" value="${market_price}" class="layui-input market_price" lay-verify="digit"></td>
                <td><input type="number" value="${stock}" class="layui-input stock" value="0" lay-verify="integer"></td>
                <td><input type="number" value="${warn}" class="layui-input warn" value="0" lay-verify="integer"></td>
                <td class="operate">
                    <div class="centerT">
                        <a href="javascript:;" class="green" onclick="copyParam(${num})">复制</a>
                        <a href="javascript:;" class="red" onclick="deleteParam(${num})">删除</a>
                    </div>
                </td>
            </tr>`;
            $(".paramMultiple tbody").append(sku);
            uploadParamLogo('param-logo_'+num);
            $("#numM").val(num);
        }
    }

    //删除规格
    deleteParam = function (index,id) {
        $(".param_"+index).remove();
        if( id ) {
            var delStr = $("#delM").val();
            var str = '';
            delStr ? str += delStr + ',' + id : str += id;
            $("#delM").val(str);
        }
    }

    //添加交叉规格头部
    addCross = function (index,id) {
        var skuItemLen = $(".sku-list .sku-item").length;
        if (skuItemLen > 0) {
            $(".sku-list .sku-item:eq(0) .layui-btn-danger").show();
        }
        if (skuItemLen > 3) {
            layer.msg("规格项最多为4项！", { icon: 2, shade: 0.3, time: 1800 });
            return false;
        }
        var num = Number($("#numC").val()) + 1;
        var sku = `<div class="sku-item skuHead_${num}">
                <div class="sku-item-head left">
                    <div class="layui-form-item">
                        <label class="layui-form-label">规格项：</label>
                        <div class="layui-input-block input">
                            <input type="text" name="param_${num}" class="layui-input">
                        </div>
                    </div>
                    <button type="button" class="layui-btn layui-btn-danger" onclick="delete_param(this, 'sku-item')">删除</button>
                </div>
                <div class="sku-item-param left">
                    <div class="hr"><span class="iconfont icon-angle"></span></div>
                    <div class="list left"></div>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="addParamItem(${num})">+ 添加规格值</button>
                </div>
            </div>`;
        $(".sku-list").append(sku);
        $("#numC").val(num);
    }

    //弹出层填写规格项
    addParamItem = function(num,edit) {  //编辑页的时候加上第二个参数
        layer.prompt({ title: '输入规格值，多个换行即可', formType: 2 }, function (text, index) {
            if (text != '') {
                var value = text.replace(/\n/g, ",")
                var params = value.split(',');
                var nums = Number($("#params_nums").val());
                for (var i = 0; i < params.length; i++) {
                    if (params[i] == '') continue;
                    var id = i + nums;
                    if (edit) {
                        id = '';
                    }
                    var sku = `<div class="sku-item-name">
                        <input type="text" data-id="${id}" value="${params[i]}" class="sku-item-value">
                        <span class="iconfont icon-delete" onclick="delete_param(this, 'sku-item-name')"></span>
                    </div>`;
                    $(".skuHead_" + num + " .sku-item-param .list").append(sku);
                }
                $("#params_nums").val(params.length + nums);
            }
            sku_table_generate();
            if($(".sku-setting").css('display') == 'none'){
                $(".sku-setting").show();
            }
            layer.close(index);
            $(".sku-list").on('keyup','.sku-item-value',function(){
                sku_table_generate();
            });
        });
    }
    //生成表格头
    sku_data_head = function() {
        var arr = []; //完整的规格数据
        var obj = {}; //单个规格数据
        $('.sku-list .sku-item').each(function () {
            obj = {}; //清空数据
            obj.sku_name = $(this).find('input.layui-input').val();
            if ($(this).find('input.layui-input').data('id')) {
                obj.sku_id = $(this).find('input.layui-input').data('id');
            }
            var sku_arr = [];
            $(this).find('.sku-item-name').each(function (index) {
                var sku_obj = {};
                sku_obj.name = obj.sku_name;
                sku_obj.sku_id = $(this).find('input.sku-item-value').data('id');
                sku_obj.value = $(this).find('input.sku-item-value').val();
                sku_arr.push(sku_obj);
            })
            if (sku_arr.length > 0) { //如果值为空的话规格名也不取
                obj.sku_value = sku_arr;
                arr.push(obj);
            }
        })
        return arr;
    }
    //规格项封装函数
    sku_data_list = function() {
        var arr = sku_data_head();
        var prop_value_arr = [];
        arr.forEach(ele_1 => {
            var item_prop_arr = [];
            if (prop_value_arr.length > 0) {
                prop_value_arr.forEach(ele_2 => {
                    ele_1['sku_value'].forEach(ele_3 => {
                        obj = {};
                        obj.sku_name = `${ele_2.sku_name}♀${ele_3.value}`;
                        obj.sku_spec_name = `${ele_2.sku_spec_name},${ele_1['sku_name']}:${ele_3.value}`;
                        obj.sku_id = `${ele_2.sku_id}_${ele_3.sku_id}`;
                        item_prop_arr.push(obj);
                    })
                })
            } else {
                ele_1['sku_value'].forEach(ele_3 => {
                    obj = {};
                    obj.sku_name = ele_3.value;
                    obj.sku_spec_name = `${ele_1['sku_name']}:${ele_3.value}`;
                    obj.sku_id = `${ele_3.sku_id}`;
                    item_prop_arr.push(obj);
                })
            }
            prop_value_arr = item_prop_arr.length > 0 ? item_prop_arr : prop_value_arr;
        });
        return prop_value_arr;
    }
    //表格数据
    sku_table_data = function () {
        var sku_arr = [];
        $(".sku-table .table tbody tr").each(function () {
            var obj = $(this);
            var sku_obj = {};
            sku_obj.attr_value_items = obj.attr('sku_id');
            sku_obj.logo = obj.find('input.logo').val();
            sku_obj.sale_price = obj.find('input.sale_price').val();
            sku_obj.market_price = obj.find('input.market_price').val();
            sku_obj.stock = obj.find('input.stock').val();
            sku_obj.warn = obj.find('input.warn').val();
            sku_arr.push(sku_obj);
        })
        return sku_arr;
    }
    //表格左侧名称 rowspan
    sku_table_left = function(r_c) {
        var html_arr = [];
        var arr = sku_data_head();
        var c_n = 1;
        for (var x = arr.length - 1; x >= 0; x--) {
            for (var i = 0; i < r_c;) {
                for (ele of arr[x]['sku_value']) {
                    $(".sku-table .table").find('tbody tr:eq(' + i + ')').prepend('<td rowspan="' + c_n + '">' + ele.value + '</td>');
                    i = i + c_n;
                }
            }
            c_n = c_n * arr[x]['sku_value'].length;
        }
    }
    //删除单个规格或者删除整个规格项 根据parentName来决定（父级class名称）
    delete_param = function(e, parentName) {
        var delObj = $(e).parents('.' + parentName);
        delObj.remove();
        sku_table_generate();
    }
    //生成规格table
    sku_table_generate = function(num) {
        var arr = sku_data_head();
        var sku_data = sku_table_data(); //必须放在前面，不然数据会被重置为空
        var length = arr.length;
        colspan = length == 0 ? 1 : length;
        rowspan = length == 1 ? 1 : 2;
        var tableHead = `<tr>
            <th colspan="${colspan}">规格</th>
            <th rowspan="${rowspan}">缩略图</th>
            <th rowspan="${rowspan}">售卖价</th>
            <th rowspan="${rowspan}">划线价</th>
            <th rowspan="${rowspan}">库存</th>
            <th rowspan="${rowspan}">库存预警</th>
        </tr>`;
        if (colspan > 1) {
            tableHead += `<tr>`;
            for (ele of arr) {
                tableHead += `<th>${ele.sku_name}</th>`;
            }
            tableHead += `</tr>`;
        }
        $(".sku-table .table thead").html(tableHead);

        var arr_list = sku_data_list();

        var tableList = '';
        arr_list.forEach((item,index) => {
            tableList += `<tr sku_id="${item.sku_id}">
                <td><div class="layui-form-upload sku-logo"><input type="hidden" class="logo" value=""><div class="uploadBtn"></div></div></td>
                <td><input type="number" class="layui-input sale_price" value="" lay-verify="digit"></td>
                <td><input type="number" class="layui-input market_price" value="" lay-verify="digit"></td>
                <td><input type="number" class="layui-input stock" value="" lay-verify="integer"></td>
                <td><input type="number" class="layui-input warn" value="" lay-verify="integer"></td>
            </tr>`;
        });
        $(".sku-table .table tbody").html(tableList);
        uploadCrossLogo();
        sku_table_left(arr_list.length);
        if (sku_data.length > 0) {
            for (ele of sku_data) {
                obj = $(`.sku-table .table tbody tr[sku_id="${ele.attr_value_items}"]`);
                if( ele.logo ) {
                    obj.find('input.logo').val(ele.logo);
                    obj.find('.uploadBtn').html('<img src="' + ele.logo + '">');
                }
                obj.find('input.sale_price').val(ele.sale_price);
                obj.find('input.market_price').val(ele.market_price);
                obj.find('input.stock').val(ele.stock);
                obj.find('input.warn').val(ele.warn);
            }
        }
    }

    //交叉规格批量设置
    var priceReg = /^\d+(.{0,1})\d{0,2}$/;	//价格正则表达式
    var stockReg = /^\d+$/;	//库存正则表达式
    var $input = $(".sku-setting .layui-input");
    $(".sku-setting a").each(function () {
        $(this).click(function () {
            var tag = $(this).data("tag");
            var placeholder = $(this).text();
            $(".sku-setting a").hide();
            $(".sku-setting .operat").show();
            $input.data("tag", tag).attr("placeholder", placeholder).focus();
        })
    })
    skuSettingConfirm = function () {
        var tag = $input.data("tag");
        var placeholder = $input.attr("placeholder");
        var stockReg = /^\d+$/; //库存正则表达式
        var is_update = true; //是否更新
        var changeVal = $(".sku-setting .layui-input").val();
        //修改对应的值
        if (changeVal == '') {
            layer.msg("请输入" + placeholder, { icon: 2, shade: 0.3, time: 1800 });
        } else {
            if (tag == 'sale_price' || tag == 'market_price') {
                if (!priceReg.test(changeVal)) {
                    layer.msg(placeholder + "格式错误", { icon: 2, shade: 0.3, time: 1800 });
                    $input.focus();
                    is_update = false;
                }
            } else if (tag == 'stock' || tag == 'warn') {
                if (!stockReg.test(changeVal)) {
                    layer.msg(placeholder + "格式错误", { icon: 2, shade: 0.3, time: 1800 });
                    is_update = false;
                    $input.focus();
                }
            }
            if (is_update) {
                //更新价格、库存
                if (tag == "sale_price") {
                    $(".sku-table .table .sale_price").val(changeVal);
                } else if (tag == "market_price") {
                    $(".sku-table .table .market_price").val(changeVal);
                } else if (tag == "stock") {
                    $(".sku-table .table .stock").val(changeVal);
                } else if (tag == "warn") {
                    $(".sku-table .table .warn").val(changeVal);
                }
            }
            skuSettingCancel();
        }
    }
    skuSettingCancel = function () {
        $(".sku-setting .layui-input").val('');
        $(".sku-setting a").show();
        $(".sku-setting .operat").hide();
    }

    skuSettingUpload = function () {
        var changeLogo = $("#skuLogo").val();
        if (changeLogo == '') {
            layer.msg("请上传缩略图", { icon: 2, shade: 0.3, time: 1800 });
        } else {
            $(".sku-table .table .logo").val(changeLogo);
            $(".sku-table .table .uploadBtn").html('').append('<img src="' + changeLogo+ '">').css('background', 'none');
        }
    }
})