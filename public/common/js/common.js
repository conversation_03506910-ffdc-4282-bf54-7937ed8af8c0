$(function () {
    //全选框的选中状态
    $(".table input[name='checkAll']").change(function () {
        var check = $(this).is(":checked");
        if (check) {
            $(".table input[name='check']").each(function (index, item) {
                item.checked = true;
            });
        } else {
            $(".table input[name='check']").each(function (index, item) {
                item.checked = false;
            });
        }
    })
    $(".table input[name='check']").change(function () {
        var size = $(".table input[name='check']").length; //获取当前页面有多少个
        var len = $(".table input[name='check']:checked").length;
        if (len == size){
            $(".table input[name='checkAll']").prop("checked", true);
        } else {
            $(".table input[name='checkAll']").prop("checked", false);
        }
    });
    
    // sidebar收缩展开
    if (!$("body").hasClass("shrink")) {
        $(".sidenav .nav li.item").each(function () {
            if (!$(this).hasClass("show")) {
                $(this).find("ul").css("display", "none");
            } else {
                $(this).find("ul").css("display", "block");
            }
        });
    } else {
        $(".sidenav .nav li.item").each(function () {
            $(".sidenav .nav ul").hide();
        });
    }

	$(".sidenav .nav a").on('click', function () {
		if (!$("body").hasClass("shrink")) {
			$(this).parent("li").siblings("li.item").children('ul').slideUp(400);
			if ($(this).next().css("display") == "none") {
				$(this).next("ul").slideDown(400);
                $(this).parent("li").addClass("show").siblings('li').removeClass("show");    
			} else {
				$(this).next('ul').slideUp(400);
                $(this).parent('li').removeClass("show");
			}
		}
	});

	$(".shrinkBtn").on("click", function () {
		if (!$("body").hasClass("shrink")) {
            $(".sidenav .nav ul").hide();
            $("body").addClass("shrink");
            $.ajax({
                type: 'post',
                url: "/index.php/admin/index/shrink.html",
                data: {
                    shrink: 2
                },
                success: function (res) {}
            });
		} else {
            $("body").removeClass("shrink");
            $.ajax({
                type: 'post',
                url: "/index.php/admin/index/shrink.html",
                data: {
                    shrink: 1
                },
                success: function (res) {}
            });
		}
	});

	$(document).on("mouseover", ".shrink .sidenav ul:first > li", function () {
		$(".nav_popup.third").hide();
		$(".nav_popup.second").length == 0 && ($(".sidenav").append("<div class='second nav_popup'><div></div></div>"));
        $(".nav_popup.second > div").html($(this).html());
        $(".nav_popup.second > div").children("ul").removeAttr("style");
		$(".nav_popup.second").show();
		$(".nav_popup.third").hide();
		var top = $(this).offset().top;
		var d = $(window).height() - $(".nav_popup.second > div").height();
		if (d - top <= 0) {
			top = d >= 0 ? d - 8 : 0;
		}
		$(".nav_popup.second").stop().animate({ "top": top }, 100);
	});

	$(document).on("mouseover", ".second.nav_popup > div > ul > li", function () {
		if (!$(this).hasClass("item")) {
			$(".nav_popup.third").hide();
			return;
		}
		$(".nav_popup.third").length == 0 && ($(".sidenav").append("<div class='third nav_popup'><div></div></div>"));
		$(".nav_popup.third > div").html($(this).html());
		$(".nav_popup.third").show();
		var top = $(this).offset().top;
		var d = $(window).height() - $(".nav_popup.third").height();
		if (d - top <= 0) {
			top = d >= 0 ? d - 8 : 0;
		}
		$(".nav_popup.third").stop().animate({ "top": top }, 100);
	});

	$(document).on("mouseleave", ".shrink .sidenav ul:first, .second.nav_popup, .third.nav_popup", function () {
		$(".nav_popup.second").hide();
		$(".nav_popup.third").hide();
	});

	$(document).on("mouseover", ".second.nav_popup", function () {
		$(".nav_popup.second").show();
	});

	$(document).on("mouseover", ".third.nav_popup", function () {
		$(".nav_popup.second").show();
		$(".nav_popup.third").show();
	});
})

layui.use(['upload'], function () {
    var upload = layui.upload;

    //上传商品缩略图
    uploadlogo = function(elem,url,obj) {
        upload.render({
            elem: elem,
            url: url,
            accept: 'images',
            acceptMime: 'image/*',
            done: function (res) {
                var item = this.item;
                $(obj).val(res.data);
                $(item).css({
                    'background': 'url('+ res.data +')',
                    'background-repeat': 'no-repeat',
                    'background-size': '100% 100%'
                });
            }
        });
    }
    
    //上传商品轮播图
    uploadAlbum = function(elem,url,obj,preview) {
        upload.render({
            elem: elem,
            url: url,
            accept: 'images',
            acceptMime: 'image/*',
            multiple: true,
            done: function (res) {
                $(obj).append('<div class="img draggable-element"><img src="' + res.data + '"><input type="hidden" value="' + res.data + '"><div class="close" style="display: none;"><span class="iconfont icon-delete" onclick="deleteImg(this)"></span></div></div>');
                drag();
            }
        });

        //预览轮播图
        $(preview).click(function () {
            var json = {
                id: 0,
                start: 0,
                status: 0,
                data: []
            };
            $(obj + " .img").each(function(){
                var str = {};
                str.src = $(this).children().filter('img').attr('src');
                json.data.push(str);
            })
            layer.photos({
                photos: json,
                anim: 5,
                area: '1000px'
            });
        })
    }
    //删除图片
    deleteImg = function (that) {
        $(that).parents(".img").remove();
    }

    //轮播图的拖拽
    drag = function () {
        $(".draggable-element").arrangeable();
        $(".draggable-element").hover(function(){
            $(this).children(".close").show();
        },function(){
            $(this).children(".close").hide();
        });
    }
    
    //图片的处理
    pic('#logo');
    pic('#banner');
})

/*复制*/
function copy(obj) {
	var url = document.getElementById(obj);
	url.select();
	document.execCommand("Copy");
    layer.msg('复制成功', {icon: 1, shade: 0.3, time: 500});
}
/*修改排序*/
function sortChange(id,sort,url) {
    if (sort <= 0) {
        layer.msg('请输入正整数', {icon: 2, shade: 0.3, time: 1500});
    } else {
        $.ajax({
            type: 'post',
            url: url,
            data: {
                id: id,
                sort: sort
            },
            success: function (res) {
                if (res.code == 200) {
                    layer.msg('修改成功', {icon: 1, shade: 0.3, time: 1500}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('修改失败', {icon: 2, shade: 0.3, time: 2000});
                }
            }
        });
    }
}
//单个操作
function singleOperate(json, msg, url) {
    layer.open({
        title: '提示',
        content: msg,
        btn: ['确认', '取消'],
        btnAlign: 'c',
        yes: function(){
            $.ajax({
                type: 'post',
                url: url,
                data: json,
                success: function (res) {
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
                            location.reload();
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2, shade: 0.3, time: 2000});
                    }
                }
            });
        }
    });
}
//批量操作
function multipleOperate(json, msg, url) {
    var arr = '';
    $(".table input[name='check']:checked").each(function () {
        arr ? arr += ',' + $(this).val() : arr += $(this).val();
    });
    if (arr == '') {
        layer.msg('请先勾选', {icon: 2, shade: 0.3, time: 1000});
    } else {
        json.id = arr //合并
        layer.open({
            title: '提示',
            content: msg,
            btn: ['确认', '取消'],
            btnAlign: 'c',
            yes: function(){
                $.ajax({
                    type: 'post',
                    url: url,
                    data: json,
                    success: function (res) {
                        if (res.code == 200) {
                            layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2, shade: 0.3, time: 2000});
                        }
                    }
                });
            }
        });
    }
}

function pic(obj) {
    if (obj) {
        if ($(obj).val()) $(obj).next().css({
            'background': 'url('+ $(obj).val() +')',
            'background-repeat': 'no-repeat',
            'background-size': '100% 100%'
        });
    }
}

/**验证*****************************/
//判断是否为数字（整数、2位小数）
function isNum(val) {
	var reg = /^\d+(.{0,1})\d{0,2}$/;
	var val = $.trim(val);
	if (reg.test(val) == false) {
		return false;
	}
	return true;
}

//判断是否为正整数
function isPositiveInteger(val) {
    var reg = /^\d+$/;
    var val = $.trim(val);
    if (reg.test(val) == false) {
        return false;
    }
    return true;
}