* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
*:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
html {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none;
}
body {
    font-family: "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
    width: 100%;
    height: 100%;
    font-size: 14px;
    line-height: 20px;
    color: #666;
    background-color: #f4f6f8;
}
body,div,dl,dd,dt,ul,li,input,select,option,button,textarea,form,p,a {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
div::-webkit-scrollbar {
    display: none;
}
button,input,select,option,textarea {
    outline: none;
    background: none;
    border: none;
    font-family: inherit;
    font-size: inherit;
    font-style: inherit;
}
input[type="number"] {
    -moz-appearance: textfield;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input:-webkit-autofill {
    box-shadow: 0 0 0px 1000px white inset !important;
}
::-webkit-input-placeholder {
    color: #babbc3;
}
:-moz-placeholder {
    color: #babbc3;
}
::-moz-placeholder {
    color: #babbc3;
}
:-ms-input-placeholder {
    color: #babbc3;
}
ul,li {
    list-style: none;
}
em,i {
    font-style: normal;
}
textarea {
    resize: none;
}
a,a:hover,a:focus {
    text-decoration: none;
    outline: none;
}
.left {
    display: flex;
    flex-wrap: wrap;
}
.right {
    display: flex;
    justify-content: flex-end;
}
.between {
    display: flex;
    justify-content: space-between;
}
.around {
    display: flex;
    justify-content: space-around;
}
.center {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.centerT {
    display: flex;
    justify-content: center;
    align-items: center;
}
.cut {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.cutTwo {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
/*登录*/
.loginWrap {
    width: 100%;
    height: 100vh;
    position: absolute;
    background: url(../images/admin-bg.jpg) center;
}
.loginWrap .login {
    width: 468px;
    padding: 36px 60px;
    background-color: #fff;
    border-radius: 4px;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.loginWrap .name {
    font-size: 38px;
    color: #1e2633;
    font-weight: bold;
    padding-bottom: 20px;
    line-height: 72px;
    text-align: center;
}
.loginWrap .layui-form-item {
    height: 42px;
    line-height: 42px;
    position: relative;
    margin-bottom: 24px;
}
.loginWrap .icon {
    width: 42px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
}
.loginWrap .iconfont {
    font-size: 24px;
    color: #babbc3;
}
.loginWrap .layui-input {
    height: 42px;
    line-height: 42px;
    padding-left: 42px;
}
.loginWrap .layui-btn {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 50px;
    font-size: 20px;
    color: #fff;
    margin-top: 18px;
    background-color: #3385ff;
}
/*头部*/
.header {
    width: 100%;
    height: 56px;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    padding-right: 16px;
    z-index: 120;
}
.header .logo {
    font-size: 18px;
    color: #fff;
    width: 200px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    background-color: #171e2a;
    -webkit-transition: width 0.3s ease;
    -o-transition: width 0.3s ease;
    transition: width 0.3s ease;
}
.header .logo .iconfont {
    display: none;
    padding-right: 0;
}
.header .navbar {
    flex: 1;
}
.header .shrinkBtn {
    height: 56px;
    line-height: 56px;
    text-align: center;
    margin-left: 16px;
    cursor: pointer;
}
.header .shrinkBtn .iconfont {
    font-size: 24px;
    color: #3385ff;
    font-weight: bold;
}
.header .shrinkBtn .text {
    width: 100%;
}
.header .web {
    font-size: 14px;
    color: #fff;
    height: 32px;
    line-height: 32px;
    padding: 0 22px;
    margin: 12px 0 12px 30px;
    border-radius: 4px;
    background-color: #2faf6e;
}
.header .user {
    height: 56px;
    position: relative;
}
.header .user:hover .sub {
    display: block;
}
.header .user:hover .bar {
    min-width: 100%;
    opacity: 1;
}
.header .user:hover .icon-caret {
    transform: rotate(180deg);
}
.header .user .username {
    display: block;
    color: #000;
    line-height: 56px;
    padding: 0 4px;
}
.header .user .icon-user {
    font-size: 18px;
}
.header .user .icon-caret {
    display: inline-block;
    transition: transform 0.3s;
}
.header .sub {
    display: none;
    position: absolute;
    right: 0;
    top: 58px;
    width: 108px;
    padding: 5px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    border: 1px solid #d2d2d2;
    background-color: #fff;
    z-index: 100;
    border-radius: 2px;
    white-space: nowrap;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: upbit;
    animation-name: upbit;
}
.header .sub li {
    height: 36px;
    line-height: 36px;
}
.header .sub li a {
    display: block;
    color: #333;
    padding: 0 20px;
}
.header .sub li a:hover {
    background-color: #f2f2f2;
    color: #000;
}
.header .bar {
    position: absolute;
    left: 0;
    top: 52px;
    width: 0;
    height: 4px;
    opacity: 0;
    background-color: #3385ff;
    transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
}
/*侧边栏*/
.sidenav {
    width: 200px;
    background-color: #171e2a;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 112;
    overflow: auto;
    -webkit-transition: width 0.3s ease;
    -o-transition: width 0.3s ease;
    transition: width 0.3s ease;
}
.sidenav .nav {
    padding-top: 66px;
}
.sidenav .nav > .item ul li a.active {
    color: #fff;
    background-color: #242d3d;
}
.sidenav .nav > .item > a {
    height: 50px;
    line-height: 50px;
}
.sidenav .nav > .item > a:hover {
    color: #fff;
}
.sidenav .nav > .item > a:hover:before {
    opacity: 1;
}
.sidenav .nav > .item > a:before {
    height: 50px;
}
.sidenav .nav > .item > ul > li.item > a:hover {
    color: #fff;
}
.sidenav .nav > .item > ul > li.item > a:hover:before {
    opacity: 1;
}
.sidenav .nav > .item > ul > li:not(.item):hover,.sidenav .nav > .item > ul > li.item > ul li:hover {
    color: #fff;
    background-color: #242d3d;
}
.sidenav .nav > .item > a span {
    height: 50px;
    line-height: 50px;
}
.sidenav .nav > .item > a span.text {
    font-size: 15px;
}
.sidenav .nav .item.show > ul {
    display: block;
}
.sidenav .nav .item.show > a:before {
    opacity: 1;
}
.sidenav .nav .item.show > a > .icon-caret {
    transform: rotate(0);
}
.sidenav .nav ul {
    background-color: rgba(0, 0, 0, 0.3);
}
.sidenav a {
    display: block;
    overflow: hidden;
    padding-left: 20px;
    height: 42px;
    line-height: 42px;
    color: rgba(255, 255, 255, 0.7);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    position: relative;
}
.sidenav a:before {
    content: "";
    position: absolute;
    left: 0;
    width: 3px;
    height: 42px;
    opacity: 0;
    background-color: #999ea7;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}
.sidenav .icon:first-of-type {
    font-size: 20px;
    position: absolute;
    margin-left: -1px;
}
.sidenav .text {
    padding-left: 30px;
}
.sidenav .icon-caret {
    font-size: 14px;
    position: absolute;
    top: 0;
    right: 20px;
    transform: rotate(-90deg);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}
.sidenav .item .item > ul > li a .text {
    padding-left: 42px;
}
.sidenav .item > ul {
    display: none;
    background-color: rgba(0, 0, 0, 0.6);
}
/*正文区域*/
.content {
    width: calc(100% - 232px);
    min-height: calc(100vh - 72px);
    margin-left: 216px;
    padding-top: 72px;
    overflow-y: auto;
    position: relative;
    -webkit-transition: width 0.3s ease, margin 0.3s ease;
    -o-transition: width 0.3s ease, margin 0.3s ease;
    transition: width 0.3s ease, margin 0.3s ease;
}
/*侧边栏收缩*/
.shrink .header .logo {
    width: 60px;
    background-color: #3385ff;
}
.shrink .header .logo .text {
    display: none;
}
.shrink .header .logo .iconfont {
    font-size: 20px;
    color: #fff;
    display: block;
}
.shrink .sidenav {
    width: 60px;
    overflow: visible;
}
.shrink .sidenav .nav {
    width: 60px;
}
.shrink .sidenav .nav .item > a .text {
    display: none;
}
.shrink .sidenav .nav .item > a .icon-caret {
    display: none;
}
.shrink .sidenav .nav > li > ul {
    display: none;
}
.shrink .nav_popup {
    display: block;
    position: absolute;
    border: 3px solid rgba(60, 71, 76, 0);
}
.shrink .nav_popup div {
    border-radius: 5px;
    background-color: #20222a;
}
.shrink .nav_popup > div > a > .icon-caret {
    transform: rotate(0);
}
.shrink .nav_popup.second {
    left: 60px;
}
.shrink .nav_popup.third {
    left: 242px;
}
.shrink .nav_popup.third div > ul {
    display: block;
}
.shrink .nav_popup ul {
    width: 180px;
}
.shrink .nav_popup ul li {
    width: 180px;
    background-color: rgba(0, 0, 0, 0.6);
}
.shrink .nav_popup ul li:last-child {
    border-radius: 0 0 5px 5px;
}
.shrink .nav_popup ul li a.active {
    background-color: #2e3c4d;
}
.shrink .nav_popup a span {
    margin-left: 0px;
}
.shrink .nav_popup > div > ul > li:last-child > a {
    border-radius: 0 0 5px 5px;
}
.shrink .nav_popup > div > ul > li.item > ul {
    position: absolute;
    top: 0;
    left: 180px;
    width: 180px;
    z-index: 99;
}
.shrink .nav_popup .icon {
    display: none;
}
.shrink .nav_popup > div > ul > li > a:hover {
    color: #fff;
    background-color: #2e3c4d;
}
.shrink .content {
    width: calc(100% - 92px);
    margin-left: 76px;
}
/*checkbox勾选美化*/
.checkBox {
    width: 16px;
    height: 16px;
}
.checkBox input[type="checkbox"] {
    display: none;
}
.checkBox span {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    cursor: pointer;
}
.checkBox input[type="checkbox"] + span {
    background-color: #fff;
    border: 1px solid #dadde7;
    border-radius: 2px;
}
.checkBox input[type="checkbox"]:checked + span {
    border: none;
    background: url(../images/check.png) no-repeat;
}
/*分页*/
.pages {
    height: 72px;
    padding: 30px 0 10px;
}
.pages .total {
    color: #333;
    height: 32px;
    line-height: 32px;
    margin-right: 10px;
}
.pages .pagination {
    display: inline-block;
    padding-left: 0;
    border-radius: 4px;
}
.pages .pagination li {
    display: inline;
}
.pages .pagination li > a,
.pages .pagination li > span {
    position: relative;
    float: left;
    padding: 0 12px;
    height: 32px;
    margin-right: 4px;
    line-height: 32px;
    color: #333;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 2px;
}
.pages .pagination li:first-child > a,
.pages .pagination li:first-child > span {
    margin-left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
.pages .pagination li > a:hover,
.pages .pagination li > span:hover,
.pages .pagination li > a:focus,
.pages .pagination li > span:focus {
    z-index: 2;
    color: #fff;
    background-color: #3385ff;
    border-color: #3385ff;
}
.pages .pagination .active > a,
.pages .pagination .active > span,
.pages .pagination .active > a:hover,
.pages .pagination .active > span:hover,
.pages .pagination .active > a:focus,
.pages .pagination .active > span:focus {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #3385ff;
    border-color: #3385ff;
}
.pages .pagination .disabled > span,
.pages .pagination .disabled > span:hover,
.pages .pagination .disabled > span:focus,
.pages .pagination .disabled > a,
.pages .pagination .disabled > a:hover,
.pages .pagination .disabled > a:focus {
    color: #777;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #ddd;
}
.pages .layui-input-block {
    width: 98px;
}
.pages .layui-input {
    height: 32px;
    line-height: 32px;
}
/*正文区域*/
.wrap {
    padding: 0 20px 20px;
    border-radius: 2px;
    background-color: #fff;
    position: relative;
}
/*工具栏*/
.tools {
    width: 100%;
    border-radius: 2px;
    background-color: #fff;
    padding: 16px 20px 0;
    margin-bottom: 10px;
}
.tools .layui-form-item {
    margin-right: 20px;
    margin-bottom: 16px;
}
.tools .layui-form-label {
    width: auto;
    padding: 6px 12px 6px 0;
}
.tools .layui-input {
    height: 32px;
    line-height: 32px;
}
.tools .select {
    width: 142px;
}
.tools .classify {
    width: 218px;
}
.tools .time {
    width: 218px;
}
.tools .time .layui-input {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
.tools .time .icon-calendar {
    font-size: 18px;
    height: 32px;
    line-height: 32px;
    position: absolute;
    top: 0;
    right: 6px;
    z-index: 9;
}
.tools .input {
    width: 218px;
}
.tools .layui-form .add {
    padding-bottom: 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid #e9edef;
}
.tools .layui-form.between .add {
    padding-bottom: 0;
    margin-bottom: 16px;
    border-bottom: none;
}
.tools .add {
    padding-bottom: 16px;
}
.tools .add .layui-btn {
    padding: 0 16px;
}
.tools .add .layui-form-item {
    margin: 0 16px;
}
.tools xm-select {
    min-height: 32px;
    line-height: 32px;
}
.tools xm-select > .xm-label .scroll .label-content {
    padding: 0 30px 0 10px !important;
}
/*操作按钮*/
.operate a {
    font-size: 12px;
    color: #fff;
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    margin: 0 4px;
    border-radius: 3px;
}
.operate a:hover {
    opacity: 0.8;
    filter: alpha(opacity=80);
    color: #fff;
}
.operate span {
    padding-right: 2px;
}
.operate .green {
    background-color: #2faf6e;
}
.operate .blue {
    background-color: #2d6df2;
}
.operate .red {
    background-color: #ff4949;
}
.operate .yellow {
    background-color: #f49539;
}
.operate .orange {
    background-color: #ff5722;
}
.operate .gray {
    background-color: #999ea7;
}
.operate .purple {
    background-color: #748fe6;
}
/*layui-table*/
.layui-table thead tr {
    border-bottom: 1px solid #e9edef;
}
.layui-table thead th {
    font-family: "Hiragino Sans GB";
    font-weight: bold;
    text-align: center;
    padding: 20px 0;
    line-height: 20px;
}
.layui-table tbody tr {
    border-bottom: 1px solid #e9edef;
}
.layui-table tbody td {
    text-align: center;
    border: none;
}
.layui-table tbody::-webkit-scrollbar {
    display: none;
}
.layui-table .check_box {
    width: 50px;
}
.layui-table .title {
    width: 300px;
}
.layui-table .title p {
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.layui-table .banner img {
    width: 132px;
    height: 48px;
    border-radius: 2px;
}
.layui-table .logo img {
    width: 36px;
    height: 36px;
    border-radius: 2px;
}
.layui-table .verify img {
    width: 132px;
    height: 58px;
    border-radius: 2px;
    margin: 0 3px;
}
.layui-table .state {
    display: inline-block;
    font-size: 12px;
    color: #fff;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
    border-radius: 3px;
}
.layui-table .state.green {
    background-color: #2faf6e;
}
.layui-table .state.blue {
    background-color: #426ab3;
}
.layui-table .state.red {
    background-color: #ff4d4f;
}
.layui-table .state.yellow {
    background-color: #f49539;
}
.layui-table .state.orange {
    background-color: #ff5722;
}
.layui-table .state.gray {
    background-color: #999ea7;
}
.layui-table .state.purple {
    background-color: #748fe6;
}
.layui-table .plus {
    font-weight: bold;
    color: #2ec770;
}
.layui-table .minus {
    font-weight: bold;
    color: #ff4229;
}
.layui-table .sequence {
    width: 52px;
    height: 26px;
    line-height: 24px;
    text-align: center;
    border: 1px solid #dcdee2;
    border-radius: 3px;
    color: #333;
}
/*商品*/
.layui-table .goods-info {
    width: 316px;
    text-align: left;
}
.layui-table .goods-info .pic {
    width: 64px;
    height: 64px;
    margin-right: 10px;
}
.layui-table .goods-info .pic img {
    width: 64px;
    height: 64px;
    border-radius: 2px;
}
.layui-table .goods-info .info {
    width: 216px;
}
.layui-table .goods-info .info .goods_name {
    line-height: 20px;
}
.layui-table .goods-info .info .param {
    display: inline-block;
    font-size: 12px;
    color: #3b7ae1;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    background-color: #f0faff;
    padding: 0 10px;
    margin-top: 2px;
}
.layui-table .goods-info .info .price {
    font-size: 12px;
    color: #ff4d4f;
    line-height: 20px;
}
.layui-table .order-line:hover, .layui-table .order-info:hover, .layui-table .order-detail:hover {
    background: none;
}
.layui-table .order-line {
    height: 20px;
}
.layui-table .order-info {
    height: 50px;
    background-color: #f4f6f8;
    border-bottom: none;
    position: relative;
}
.layui-table .order-info td {
    height: 50px;
    padding: 16px 0;
    border: 1px solid #eaeaea;
    text-align: left;
}
.layui-table .order-info .li {
    font-size: 13px;
    display: inline-block;
    height: 18px;
    line-height: 18px;
    padding: 0 16px;
}
.layui-table .order-info .li input {
    width: 128px;
    color: #333;
}
.layui-table .order-info .li a {
    font-size: 12px;
    color: #2d8cf0;
}
.layui-table .order-info .btns {
    height: 18px;
    padding-right: 10px;
}
.layui-table .order-info .btns .btn {
    font-size: 13px;
    color: #2d8cf0;
    padding: 0 12px;
    height: 18px;
    line-height: 18px;
}
.layui-table .order-info .btns .btn:not(:last-of-type) {
    border-right: 1px solid #b8b9bd;
}
.layui-table .order-detail {
    border-bottom: none;
}
.layui-table .order-detail td {
    border: 1px solid #eaeaea;
}
.layui-table .order-detail .order-num span {
    color: #333;
    font-weight: bold;
    padding-left: 2px;
}
.layui-table .order-detail .order-total {
    font-size: 16px;
    color: #ff4d4f;
    font-weight: bold;
}
.layui-table .order-detail .order-buyer {
    line-height: 24px;
}
.layui-table .order-detail .order-address {
    width: 200px;
    text-align: left;
}
.layui-table .order-detail .order-address li {
    width: 180px;
    font-size: 12px;
    color: #666;
    line-height: 18px;
}
.layui-table .order-detail .order-address li span {
    color: #333;
}
.layui-table .order-detail .order-state {
    min-width: 108px;
}
.layui-table .order-remark, .layui-table .order-reason {
    width: 300px;
    text-align: left;
}
.layui-table .order-remark p, .layui-table .order-reason p {
    font-size: 13px;
    line-height: 18px;
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.layui-table .order-start {
    border-left: none;
}
.layui-table .order-start .iconfont {
    font-size: 24px;
    color: #3385ff;
}
.layui-table .order-replay {
    border-right: 1px solid #e9edef;
}
.layui-table .order-replay td {
    text-align: left;
}
.layui-table .order-replay .tit {
    color: #3b7ae1;
    text-align: right;
    padding-right: 6px;
    line-height: 22px;
}
.layui-table .order-replay .desc {
    line-height: 22px;
}
.layui-table .order-replay .imgs {
    margin-top: 10px;
}
.layui-table .order-replay .imgs img {
    width: 50px;
    height: 50px;
    border: 1px solid #eaeaea;
    border-radius: 2px;
    margin-right: 8px;
    cursor: pointer;
}
/*编辑*/
.edit_wrap {
    width: 100%;
    height: calc(100vh - 132px);
    padding: 20px;
    border-radius: 2px;
    overflow-y: auto;
    background-color: #fff;
    position: relative;
}
.edit_wrap .title {
    font-size: 16px;
    color: #333;
    font-weight: bold;
    line-height: 40px;
    padding: 0 20px;
    background-color: #f4f6f8;
    border-radius: 2px;
    margin-bottom: 24px;
}
.edit_wrap .layui-form-item + .title {
    margin-top: 32px;
}
.edit_wrap .layui-input-block {
    width: 388px;
    display: flex;
}
.edit_wrap .layui-input-block .layui-input, .edit_wrap .layui-input-block .layui-textarea, .edit_wrap .layui-input-block .layui-form-select {
    flex: 1;
}
.edit_wrap .layui-input-block .unit {
    width: 42px;
    height: 36px;
    line-height: 36px;
    background-color: #f4f8f9;
    text-align: center;
    border: 1px solid #e6e6e6;
    margin-left: -1px;
}
.edit_wrap .layui-input-block.time {
    width: 388px;
    position: relative;
}
.edit_wrap .layui-input-block.time .layui-input {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
.edit_wrap .layui-input-block.time .icon-calendar {
    font-size: 18px;
    height: 36px;
    line-height: 36px;
    position: absolute;
    top: 0;
    right: 6px;
    z-index: 9;
}
.edit_wrap .layui-form-upload {
    position: relative;
    border: 1px solid #eaeaea;
    box-sizing: unset;
}
.edit_wrap .layui-form-upload.logo {
    width: 100px;
    height: 100px;
    line-height: 100px;
}
.edit_wrap .layui-form-upload.banner {
    width: 386px;
    height: 130px;
    line-height: 130px;
}
.edit_wrap .layui-form-upload .uploadBtn {
    width: inherit;
    height: inherit;
    line-height: inherit;
    background: url(../images/uploadBtn.png) no-repeat center;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
}
.edit_wrap .layui-form-upload .uploadBtn .icon-camera {
    font-size: 36px;
    color: #999;
}
.edit_wrap .layui-form-upload .uploadBtn img {
    display: block;
    width: inherit;
    height: inherit;
}
.edit_wrap .layui-upload-list {
    min-height: 160px;
    width: 762px;
    padding: 10px 10px 0 10px;
    border: 1px dotted #aeaeae;
    position: relative;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.edit_wrap .layui-upload-list .img {
    width: 140px;
    height: 140px;
    margin: 0 10px 10px 0;
    position: relative;
}
.edit_wrap .layui-upload-list .img:nth-child(5n) {
    margin-right: 0;
}
.edit_wrap .layui-upload-list .img img {
    width: 140px;
    height: 140px;
    border-radius: 2px;
    border: 1px solid #e6e6e6;
    box-sizing: border-box;
}
.edit_wrap .layui-upload-list .img .close {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
}
.edit_wrap .layui-upload-list .img .close span {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 24px;
    border-radius: 50%;
    cursor: pointer;
}
.edit_wrap .editor {
    width: 1000px;
    border: 1px solid #e6e6e6;
}
#toolbar-editor, #toolbar-editor2, #toolbar-editor3 {
    border-bottom: 1px solid #e6e6e6;
}
#content-editor, #content-editor2, #content-editor3 {
    height: 600px;
}
.edit_wrap .xmSelect {
    width: 388px;
    min-height: 36px;
}
.edit_wrap .layui-wrap {
    padding: 20px 20px 10px;
    border-radius: 2px;
    background-color: #f4f6f8;
    margin: -10px 0 20px 120px;
}
.edit_wrap .layui-wrap .layui-form-item {
    margin-bottom: 10px;
}
.edit_wrap .layui-wrap .layui-input-block {
    width: 228px;
}
.edit_wrap .layui-wrap .textarea {
    width: 258px;
}
.edit_wrap .layui-wrap .layui-input-block .layui-input,
.edit_wrap .layui-wrap .layui-input-block .layui-textarea {
    background-color: #fff;
}
.edit_wrap .layui-wrap .layui-table {
    background-color: #fff;
    margin-bottom: 10px;
}
.edit_wrap .layui-wrap .layui-table thead tr {
    height: 52px;
    background-color: #eaf0f7 !important;
}
.edit_wrap .layui-wrap .layui-table thead th {
    padding: 10px;
    border: 1px solid #e6e6e6;
}
.edit_wrap .layui-wrap .layui-table tbody tr td {
    padding: 10px;
    border: 1px solid #e6e6e6;
}
.edit_wrap .layui-wrap .layui-table tbody tr:hover {
    background: none;
}
.edit_wrap .layui-wrap .layui-table .layui-input {
    height: 30px;
    line-height: 30px;
}
.edit_wrap .layui-wrap .layui-btn.add {
    margin-bottom: 20px;
}
.edit_wrap .layui-wrap .layui-form-item .layui-input-block + .layui-btn {
    margin: 2px 18px;
}
.edit_wrap .layui-preview {
    background-color: #2faf6e;
    margin-left: 10px;
}
.edit_wrap .layui-video {
    width: 352px;
    height: 206px;
}
.edit_wrap .tip {
    font-size: 12px;
    line-height: 18px;
    color: #999;
    margin-top: -18px;
    padding-left: 120px;
    margin-bottom: 12px;
}
.layui-form-btns {
    width: 100%;
    height: 60px;
    padding: 14px 0 14px 174px;
    background-color: #fff;
    border-top: 1px solid #e9edef;
}
.layui-form-btns .layui-btn {
    padding: 0 22px;
}
/*订单详情页*/
.order-detail {
    width: 100%;
    border-radius: 2px;
    background-color: #fff;
    padding: 0 20px;
}
.order-detail .orderInfo {
    padding: 20px 0;
}
.order-detail .order-tit {
    padding: 32px 0 20px;
    border-bottom: 1px solid #e9edef;
}
.order-detail .order-tit .tit {
    color: #000;
    font-weight: bold;
    height: 32px;
    line-height: 32px;
    margin-right: 28px;
}
.order-detail .order-tit .tit span {
    font-size: 18px;
    color: #000;
    font-weight: bold;
}
.order-detail .orderInfo .order_li {
    width: 33.333%;
}
.order-detail .orderInfo .tit {
    font-size: 16px;
    color: #000;
    font-weight: bold;
    line-height: 22px;
    padding-bottom: 20px;
}
.order-detail .orderInfo li {
    display: flex;
    margin-bottom: 8px;
}
.order-detail .orderInfo li .t {
    width: 72px;
    text-align: right;
}
.order-detail .orderInfo li .text {
    flex: 1;
}
.order-detail .orderInfo .money li {
    color: #333;
    margin-bottom: 12px;
}
.order-detail .orderInfo .money li .t {
    width: 82px;
}
.order-detail .orderInfo .money li .text {
    font-size: 16px;
    color: #ff4229;
    font-weight: bold;
}
.order-detail .orderInfo:not(:last-of-type) {
    border-bottom: 1px solid #e9edef;
}
.order-goods {
    margin-top: 16px;
    padding: 20px;
    border-radius: 2px;
    background-color: #fff;
}
.order-goods .table thead tr {
    background-color: #eaf0f7 !important;
}
/*弹出层*/
.popUp {
    display: none;
    padding: 32px 20px 0;
}
.popUp .layui-input-block {
    width: 300px;
    display: flex;
}
.popUp .layui-input-block .layui-input, .popUp .layui-input-block .layui-textarea, .popUp .layui-input-block .layui-form-select {
    flex: 1;
}
.popUp .layui-input-block .unit {
    width: 42px;
    height: 36px;
    line-height: 36px;
    background-color: #f4f8f9;
    text-align: center;
    border: 1px solid #e6e6e6;
    margin-left: -1px;
}
.popUp .tip {
    font-size: 12px;
    line-height: 18px;
    color: #999;
    margin-top: -18px;
    padding-left: 120px;
    margin-bottom: 12px;
}
.popUp .xmSelect {
    width: 300px;
    min-height: 36px;
}
.popUp .layui-form-upload {
    position: relative;
    border: 1px solid #eaeaea;
    box-sizing: unset;
}
.popUp .layui-form-upload.logo {
    width: 80px;
    height: 80px;
}
.popUp .layui-form-upload .uploadBtn {
    width: inherit;
    height: inherit;
    border-radius: 2px;
    background: url(../images/uploadBtn.png) no-repeat center;
    cursor: pointer;
}
.popUp .layui-form-upload .uploadBtn img {
    display: block;
    width: inherit;
    height: inherit;
}
.popUp .layui-btns {
    border-top: 1px solid #eee;
    padding: 16px 0;
    margin-top: 32px;
}
/*用户详情页*/
.layui-user {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 28px;
}
.layui-user a {
    color: #2d8cf0;
    line-height: 22px;
    padding: 0 10px;
}
.layui-user .avatar {
    width: 90px;
    height: 90px;
    border: 4px solid #fff;
    border-radius: 100%;
    box-shadow: 0 4px 10px rgba(0, 82, 169, 0.2);
    margin: 0 20px 0 40px;
}
.layui-user .avatar img {
    width: 82px;
    height: 82px;
    border-radius: 100%;
}
.layui-user .user-info {
    display: flex;
    flex-wrap: wrap;
    width: calc(100% - 150px);
    padding-top: 20px;
}
.layui-user .user-info .layui-block {
    width: 28%;
}
.layui-user .user-info .layui-form-item {
    margin-bottom: 12px;
}
.layui-user .user-info .layui-form-label {
    width: 120px;
    padding: 1px 0;
}
.layui-user .user-info .layui-form-content {
    color: #333;
    line-height: 22px;
}
.layui-user .layui-main {
    width: 28%;
    padding-right: 30px;
}
.layui-user .layui-main .layui-main-block {
    border: 1px solid #e9edef;
    text-align: center;
    padding: 20px 0;
    border-radius: 2px;
    color: #333;
}
.layui-user .layui-main .tit {
    line-height: 22px;
}
.layui-user .layui-main .data {
    font-size: 24px;
    font-weight: bold;
    line-height: 34px;
    padding: 10px 0;
}
.layui-user .layui-main .bg {
    width: 74px;
    height: 74px;
    line-height: 74px;
    text-align: center;
    margin: 0 20px;
    border-radius: 100%;
}
.layui-user .layui-main .bg.blue {
    background-color: #f0faff;
}
.layui-user .layui-main .bg.blue span {
    font-size: 50px;
    color: #2d8cf0;
}
.layui-user .layui-main .bg.yellow {
    background-color: #fff9e6;
}
.layui-user .layui-main .bg.yellow span {
    font-size: 50px;
    color: #ff9900;
}
.layui-user .layui-main .total {
    text-align: left;
}
.layui-user .layui-main .total p {
    padding-bottom: 10px;
}
.layui-user .layui-main .total span {
    font-size: 24px;
    line-height: 34px;
    font-weight: bold;
}
/*首页数据统计*/
.data-total {
    padding: 30px;
    border-radius: 2px;
    background-color: #fff;
    margin-bottom: 10px;
}
.data-total a {
    color: #636669;
    width: 16.666%;
    height: 58px;
}
.data-total .icon {
    width: 58px;
    height: 58px;
    line-height: 58px;
    text-align: center;
    border-radius: 50%;
}
.data-total .icon .iconfont {
    color: #fff;
    font-size: 36px;
}
.data-total .icon.green {
    background: linear-gradient(90deg, #2cdd8f, #0ecd83);
}
.data-total .icon.blue {
    background: linear-gradient(90deg, #51cdfd, #3bb8ff);
}
.data-total .icon.yellow {
    background: linear-gradient(90deg, #f9dc1c, #ffc524);
}
.data-total .icon.orange {
    background: linear-gradient(90deg, #f8daa3, #ffa500);
}
.data-total .icon.red {
    background: linear-gradient(90deg, #f67172, #ff3c3d);
}
.data-total .icon.purple {
    background: linear-gradient(90deg, #af7efc, #907df7);
}
.data-total .text {
    padding-left: 14px;
}
.data-total .tit {
    line-height: 20px;
}
.data-total .num {
    font-size: 20px;
    color: #333;
    font-weight: 500;
    line-height: 34px;
    padding-top: 4px;
}
.data-list {
    margin-bottom: 10px;
}
.data-list .data-card {
    width: 33.333%;
}
.data-list .card {
    border-radius: 2px;
    background-color: #fff;
}
.data-list .data-card:not(:last-of-type) .card {
    margin-right: 16px;
}
.data-list .t {
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px solid #f6f6f6;
    color: #333;
}
.data-list .list {
    padding: 10px;
}
.data-list .list li {
    padding: 5px;
}
.data-list .list a {
    display: block;
    padding: 10px 15px;
    background-color: #f8f8f8;
    color: #999;
    border-radius: 2px;
}
.data-list .list .two {
    width: 50%;
}
.data-list .list .three {
    width: 33.333%;
}
.data-list .list .tit {
    line-height: 24px;
    padding-bottom: 6px;
}
.data-list .list .num {
    font-size: 18px;
    color: #3385ff;
    font-weight: bold;
    line-height: 34px;
}
.data-sales {
    border-radius: 2px;
    background-color: #fff;
    margin-bottom: 10px;
}
.data-sales .t {
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px solid #f6f6f6;
    color: #333;
}
.data-sales .list {
    padding: 12px 15px 0;
    height: 118px;
}
.data-sales .li {
    width: 33.333%;
}
.data-sales .sales-tit {
    width: 32%;
    height: 90px;
    line-height: 90px;
    text-align: center;
    background-color: #f8f8f8;
}
.data-sales .sales-info {
    width: 68%;
}
.data-sales .sales-info li {
    height: 45px;
    line-height: 45px;
    border-top: 1px solid #f8f8f8;
    border-right: 1px solid #f8f8f8;
    border-bottom: 1px solid #f8f8f8;
}
.data-sales .sales-info li:last-of-type {
    border-top: none;
}
.data-sales .sales-info .tit {
    display: inline-block;
    width: 100px;
    text-align: right;
    color: #999;
}
.data-sales .sales-info .num {
    display: inline-block;
    font-size: 16px;
    padding-left: 12px;
}
.data-sales .li:not(:last-of-type) ul {
    padding-right: 10px;
}
.data-charts {
    padding: 20px 15px;
    border-radius: 2px;
    background-color: #fff;
    margin-bottom: 10px;
}
.data-charts .filter {
    height: 32px;
}
.data-charts .time-range {
    margin-right: 20px;
}
.data-charts .time-range li {
    width: 46px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
    border: 1px solid #e6e6e6;
    border-right: none;
}
.data-charts .time-range li.on {
    border-radius: 2px !important;
    background-color: #e8eeff;
    color: #2d8cf0;
    border: 1px solid #2d8cf0;
}
.data-charts .time-range li:first-child {
    border-radius: 2px 0 0 2px;
}
.data-charts .time-range li:last-child {
    border-right: 1px solid #e6e6e6;
    border-radius: 0 2px 2px 0;
}
.data-charts .time-range li:last-child.on {
    border-right-color: #2d8cf0;
}
.data-charts .time-filter {
    width: 242px;
    height: 32px;
    position: relative;
}
.data-charts .time-filter .layui-input {
    height: 32px;
    line-height: 32px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
}
.data-charts .time-filter .icon-calendar {
    height: 32px;
    line-height: 32px;
    position: absolute;
    top: 0;
    right: 6px;
    z-index: 9;
}
.data-charts .echart-type {
    margin: 20px 0;
}
.data-charts .echart-type .type-li {
    width: 220px;
    height: 92px;
    padding: 16px 20px;
    margin-right: 16px;
    border-radius: 2px;
    background: linear-gradient(180deg, #e8eeff 0%, #fff 100%);
    border: 1px solid #e6e6e6;
    position: relative;
    cursor: pointer;
}
.data-charts .echart-type .type-li:hover {
    border-color: #2d8cf0;
}
.data-charts .echart-type .type-li:hover .num {
    color: #2d8cf0;
}
.data-charts .echart-type .type-li.on {
    border-color: #2d8cf0;
}
.data-charts .echart-type .type-li.on .num {
    color: #2d8cf0;
}
.data-charts .echart-type .type-li.on .angle {
    display: inline-block;
    background: #fbfcff;
}
.data-charts .echart-type .type-li .tit {
    color: #636669;
    line-height: 20px;
}
.data-charts .echart-type .type-li .num {
    font-size: 24px;
    color: #333;
    font-weight: 500;
    line-height: 32px;
    padding-top: 8px;
}
.data-charts .echart-type .type-li .angle {
    position: absolute;
    bottom: -8px;
    left: 50%;
    -webkit-transform: translateX(-50%) rotate(-45deg);
    transform: translateX(-50%) rotate(-45deg);
    width: 15px;
    height: 15px;
    border-style: solid;
    border-width: 0 0 1px 1px;
    border-color: #2d8cf0;
    border-radius: 0 0 0 2px;
    background: #fff;
    display: none;
}
.data-charts .echarts {
    width: 100%;
    height: 500px;
    position: relative;
    background-color: #fff;
}
.data-charts .echarts .chart {
    width: 100%;
    height: 480px;
}
:root,
:host {
    --w-e-textarea-bg-color: #fff;
    --w-e-textarea-color: #333;
    --w-e-textarea-border-color: #ccc;
    --w-e-textarea-slight-border-color: #e8e8e8;
    --w-e-textarea-slight-color: #d4d4d4;
    --w-e-textarea-slight-bg-color: #f5f2f0;
    --w-e-textarea-selected-border-color: #b4d5ff;
    --w-e-textarea-handler-bg-color: #4290f7;
    --w-e-toolbar-color: #595959;
    --w-e-toolbar-bg-color: #fff;
    --w-e-toolbar-active-color: #333;
    --w-e-toolbar-active-bg-color: #f1f1f1;
    --w-e-toolbar-disabled-color: #999;
    --w-e-toolbar-border-color: #e8e8e8;
    --w-e-modal-button-bg-color: #fafafa;
    --w-e-modal-button-border-color: #d9d9d9;
}

.w-e-text-container *,
.w-e-toolbar * {
    box-sizing: border-box;
    margin: 0;
    outline: none;
    padding: 0;
}
.w-e-text-container blockquote,
.w-e-text-container li,
.w-e-text-container p,
.w-e-text-container td,
.w-e-text-container th,
.w-e-toolbar * {
    line-height: 1.5;
}
.w-e-text-container {
    background-color: var(--w-e-textarea-bg-color);
    color: var(--w-e-textarea-color);
    height: 100%;
    position: relative;
}
.w-e-text-container .w-e-scroll {
    -webkit-overflow-scrolling: touch;
    height: 100%;
}
.w-e-text-container [data-slate-editor] {
    word-wrap: break-word;
    border-top: 1px solid transparent;
    min-height: 100%;
    outline: 0;
    padding: 0 10px;
    white-space: pre-wrap;
}
.w-e-text-container [data-slate-editor] p {
    margin: 15px 0;
}
.w-e-text-container [data-slate-editor] h1,
.w-e-text-container [data-slate-editor] h2,
.w-e-text-container [data-slate-editor] h3,
.w-e-text-container [data-slate-editor] h4,
.w-e-text-container [data-slate-editor] h5 {
    margin: 20px 0;
}
.w-e-text-container [data-slate-editor] img {
    cursor: default;
    display: inline !important;
    max-width: 100%;
    min-height: 20px;
    min-width: 20px;
}
.w-e-text-container [data-slate-editor] [data-selected="true"] {
    box-shadow: 0 0 0 2px var(--w-e-textarea-selected-border-color);
}
.w-e-text-placeholder {
    font-style: italic;
    left: 10px;
    top: 17px;
    width: 90%;
}
.w-e-max-length-info,
.w-e-text-placeholder {
    color: var(--w-e-textarea-slight-color);
    pointer-events: none;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.w-e-max-length-info {
    bottom: 0.5em;
    right: 1em;
}
.w-e-bar {
    background-color: var(--w-e-toolbar-bg-color);
    color: var(--w-e-toolbar-color);
    font-size: 14px;
    padding: 0 5px;
}
.w-e-bar svg {
    fill: var(--w-e-toolbar-color);
    height: 14px;
    width: 14px;
}
.w-e-bar-show {
    display: flex;
}
.w-e-bar-hidden {
    display: none;
}
.w-e-hover-bar {
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px;
    box-shadow: 0 2px 5px #0000001f;
    position: absolute;
}
.w-e-toolbar {
    flex-wrap: wrap;
    position: relative;
}
.w-e-bar-divider {
    background-color: var(--w-e-toolbar-border-color);
    display: inline-flex;
    height: 40px;
    margin: 0 5px;
    width: 1px;
}
.w-e-bar-item {
    display: flex;
    height: 40px;
    padding: 4px;
    position: relative;
    text-align: center;
}
.w-e-bar-item,
.w-e-bar-item button {
    align-items: center;
    justify-content: center;
}
.w-e-bar-item button {
    background: transparent;
    border: none;
    color: var(--w-e-toolbar-color);
    cursor: pointer;
    display: inline-flex;
    height: 32px;
    overflow: hidden;
    padding: 0 8px;
    white-space: nowrap;
}
.w-e-bar-item button:hover {
    background-color: var(--w-e-toolbar-active-bg-color);
    color: var(--w-e-toolbar-active-color);
}
.w-e-bar-item button .tit {
    margin-left: 5px;
}
.w-e-bar-item .active {
    background-color: var(--w-e-toolbar-active-bg-color);
    color: var(--w-e-toolbar-active-color);
}
.w-e-bar-item .disabled {
    color: var(--w-e-toolbar-disabled-color);
    cursor: not-allowed;
}
.w-e-bar-item .disabled svg {
    fill: var(--w-e-toolbar-disabled-color);
}
.w-e-bar-item .disabled:hover {
    background-color: var(--w-e-toolbar-bg-color);
    color: var(--w-e-toolbar-disabled-color);
}
.w-e-bar-item .disabled:hover svg {
    fill: var(--w-e-toolbar-disabled-color);
}
.w-e-menu-tooltip-v5:before {
    background-color: var(--w-e-toolbar-active-color);
    border-radius: 5px;
    color: var(--w-e-toolbar-bg-color);
    content: attr(data-tooltip);
    font-size: 0.75em;
    opacity: 0;
    padding: 5px 10px;
    position: absolute;
    text-align: center;
    top: 40px;
    transition: opacity 0.6s;
    visibility: hidden;
    white-space: pre;
    z-index: 1;
}
.w-e-menu-tooltip-v5:after {
    border: 5px solid transparent;
    border-bottom: 5px solid var(--w-e-toolbar-active-color);
    content: "";
    opacity: 0;
    position: absolute;
    top: 30px;
    transition: opacity 0.6s;
    visibility: hidden;
}
.w-e-menu-tooltip-v5:hover:after,
.w-e-menu-tooltip-v5:hover:before {
    opacity: 1;
    visibility: visible;
}
.w-e-menu-tooltip-v5.tooltip-right:before {
    left: 100%;
    top: 10px;
}
.w-e-menu-tooltip-v5.tooltip-right:after {
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-right-color: var(--w-e-toolbar-active-color);
    border-top-color: transparent;
    left: 100%;
    margin-left: -10px;
    top: 16px;
}
.w-e-bar-item-group .w-e-bar-item-menus-container {
    background-color: var(--w-e-toolbar-bg-color);
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px;
    box-shadow: 0 2px 10px #0000001f;
    display: none;
    left: 0;
    margin-top: 40px;
    position: absolute;
    top: 0;
    z-index: 1;
}
.w-e-bar-item-group:hover .w-e-bar-item-menus-container {
    display: block;
}
.w-e-select-list {
    background-color: var(--w-e-toolbar-bg-color);
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px;
    box-shadow: 0 2px 10px #0000001f;
    left: 0;
    margin-top: 40px;
    max-height: 350px;
    min-width: 100px;
    overflow-y: auto;
    position: absolute;
    top: 0;
    z-index: 1;
}
.w-e-select-list ul {
    line-height: 1;
    list-style: none;
}
.w-e-select-list ul .selected {
    background-color: var(--w-e-toolbar-active-bg-color);
}
.w-e-select-list ul li {
    cursor: pointer;
    padding: 7px 0 7px 25px;
    position: relative;
    text-align: left;
    white-space: nowrap;
}
.w-e-select-list ul li:hover {
    background-color: var(--w-e-toolbar-active-bg-color);
}
.w-e-select-list ul li svg {
    left: 0;
    margin-left: 5px;
    margin-top: -7px;
    position: absolute;
    top: 50%;
}
.w-e-bar-bottom .w-e-select-list {
    bottom: 0;
    margin-bottom: 40px;
    margin-top: 0;
    top: inherit;
}
.w-e-drop-panel {
    background-color: var(--w-e-toolbar-bg-color);
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px;
    box-shadow: 0 2px 10px #0000001f;
    margin-top: 40px;
    min-width: 200px;
    padding: 10px;
    position: absolute;
    top: 0;
    z-index: 1;
}
.w-e-bar-bottom .w-e-drop-panel {
    bottom: 0;
    margin-bottom: 40px;
    margin-top: 0;
    top: inherit;
}
.w-e-modal {
    background-color: var(--w-e-toolbar-bg-color);
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px;
    box-shadow: 0 2px 10px #0000001f;
    color: var(--w-e-toolbar-color);
    font-size: 14px;
    min-height: 40px;
    min-width: 100px;
    padding: 20px 15px 0;
    position: absolute;
    text-align: left;
    z-index: 1;
}
.w-e-modal .btn-close {
    cursor: pointer;
    line-height: 1;
    padding: 5px;
    position: absolute;
    right: 8px;
    top: 7px;
}
.w-e-modal .btn-close svg {
    fill: var(--w-e-toolbar-color);
    height: 10px;
    width: 10px;
}
.w-e-modal .babel-container {
    display: block;
    margin-bottom: 15px;
}
.w-e-modal .babel-container span {
    display: block;
    margin-bottom: 10px;
}
.w-e-modal .button-container {
    margin-bottom: 15px;
}
.w-e-modal button {
    background-color: var(--w-e-modal-button-bg-color);
    border: 1px solid var(--w-e-modal-button-border-color);
    border-radius: 4px;
    color: var(--w-e-toolbar-color);
    cursor: pointer;
    font-weight: 400;
    height: 32px;
    padding: 4.5px 15px;
    text-align: center;
    touch-action: manipulation;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap;
}
.w-e-modal input[type="number"],
.w-e-modal input[type="text"],
.w-e-modal textarea {
    font-feature-settings: "tnum";
    background-color: var(--w-e-toolbar-bg-color);
    border: 1px solid var(--w-e-modal-button-border-color);
    border-radius: 4px;
    color: var(--w-e-toolbar-color);
    font-variant: tabular-nums;
    padding: 4.5px 11px;
    transition: all 0.3s;
    width: 100%;
}
.w-e-modal textarea {
    min-height: 60px;
}
body .w-e-modal,
body .w-e-modal * {
    box-sizing: border-box;
}
.w-e-progress-bar {
    background-color: var(--w-e-textarea-handler-bg-color);
    height: 1px;
    position: absolute;
    transition: width 0.3s;
    width: 0;
}
.w-e-full-screen-container {
    bottom: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: fixed;
    right: 0 !important;
    top: 0 !important;
    width: 100% !important;
}
.w-e-full-screen-container [data-w-e-textarea="true"] {
    flex: 1 !important;
}
.w-e-text-container [data-slate-editor] code {
    background-color: var(--w-e-textarea-slight-bg-color);
    border-radius: 3px;
    font-family: monospace;
    padding: 3px;
}
.w-e-panel-content-color {
    list-style: none;
    text-align: left;
    width: 230px;
}
.w-e-panel-content-color li {
    border: 1px solid var(--w-e-toolbar-bg-color);
    border-radius: 3px 3px;
    cursor: pointer;
    display: inline-block;
    padding: 2px;
}
.w-e-panel-content-color li:hover {
    border-color: var(--w-e-toolbar-color);
}
.w-e-panel-content-color li .color-block {
    border: 1px solid var(--w-e-toolbar-border-color);
    border-radius: 3px 3px;
    height: 17px;
    width: 17px;
}
.w-e-panel-content-color .active {
    border-color: var(--w-e-toolbar-color);
}
.w-e-panel-content-color .clear {
    line-height: 1.5;
    margin-bottom: 5px;
    width: 100%;
}
.w-e-panel-content-color .clear svg {
    height: 16px;
    margin-bottom: -4px;
    width: 16px;
}
.w-e-text-container [data-slate-editor] blockquote {
    background-color: var(--w-e-textarea-slight-bg-color);
    border-left: 8px solid var(--w-e-textarea-selected-border-color);
    display: block;
    font-size: 100%;
    line-height: 1.5;
    margin: 10px 0;
    padding: 10px;
}
.w-e-panel-content-emotion {
    font-size: 20px;
    list-style: none;
    text-align: left;
    width: 300px;
}
.w-e-panel-content-emotion li {
    border-radius: 3px 3px;
    cursor: pointer;
    display: inline-block;
    padding: 0 5px;
}
.w-e-panel-content-emotion li:hover {
    background-color: var(--w-e-textarea-slight-bg-color);
}
.w-e-textarea-divider {
    border-radius: 3px;
    margin: 20px auto;
    padding: 20px;
}
.w-e-textarea-divider hr {
    background-color: var(--w-e-textarea-border-color);
    border: 0;
    display: block;
    height: 1px;
}
.w-e-text-container [data-slate-editor] pre > code {
    background-color: var(--w-e-textarea-slight-bg-color);
    border: 1px solid var(--w-e-textarea-slight-border-color);
    border-radius: 4px 4px;
    display: block;
    font-size: 14px;
    padding: 10px;
    text-indent: 0;
}
.w-e-text-container [data-slate-editor] .w-e-image-container {
    display: inline-block;
    margin: 0 10px;
}
.w-e-text-container [data-slate-editor] .w-e-image-container:hover {
    box-shadow: 0 0 0 2px var(--w-e-textarea-selected-border-color);
}
.w-e-text-container [data-slate-editor] .w-e-selected-image-container {
    overflow: hidden;
    position: relative;
}
.w-e-text-container
    [data-slate-editor]
    .w-e-selected-image-container
    .w-e-image-dragger {
    background-color: var(--w-e-textarea-handler-bg-color);
    height: 7px;
    position: absolute;
    width: 7px;
}
.w-e-text-container
    [data-slate-editor]
    .w-e-selected-image-container
    .left-top {
    cursor: nwse-resize;
    left: 0;
    top: 0;
}
.w-e-text-container
    [data-slate-editor]
    .w-e-selected-image-container
    .right-top {
    cursor: nesw-resize;
    right: 0;
    top: 0;
}
.w-e-text-container
    [data-slate-editor]
    .w-e-selected-image-container
    .left-bottom {
    bottom: 0;
    cursor: nesw-resize;
    left: 0;
}
.w-e-text-container
    [data-slate-editor]
    .w-e-selected-image-container
    .right-bottom {
    bottom: 0;
    cursor: nwse-resize;
    right: 0;
}
.w-e-text-container [data-slate-editor] .w-e-selected-image-container:hover {
    box-shadow: none;
}
.w-e-text-container [contenteditable="false"] .w-e-image-container:hover {
    box-shadow: none;
}
.w-e-text-container [data-slate-editor] ol,
.w-e-text-container [data-slate-editor] ul {
    padding-left: 20px;
}
.w-e-text-container [data-slate-editor] li {
    line-height: inherit;
    margin: 10px 0;
}
.w-e-text-container [data-slate-editor] table {
    border-collapse: collapse;
}
.w-e-text-container [data-slate-editor] table td,
.w-e-text-container [data-slate-editor] table th {
    border: 1px solid var(--w-e-textarea-border-color);
    line-height: 1.5;
    min-width: 50px;
    padding: 3px 5px;
    text-align: left;
}
.w-e-text-container [data-slate-editor] table th {
    background-color: var(--w-e-textarea-slight-bg-color);
    font-weight: 700;
    text-align: center;
}
.w-e-text-container [data-slate-editor] table.full-width {
    width: 100%;
}
.w-e-text-container [data-slate-editor] table.full-width td.th {
    min-width: 0;
}
.w-e-panel-content-table {
    background-color: var(--w-e-toolbar-bg-color);
}
.w-e-panel-content-table table {
    border-collapse: collapse;
}
.w-e-panel-content-table td {
    border: 1px solid var(--w-e-toolbar-border-color);
    cursor: pointer;
    height: 15px;
    padding: 3px 5px;
    width: 20px;
}
.w-e-panel-content-table td.active {
    background-color: var(--w-e-toolbar-active-bg-color);
}
.w-e-textarea-video-container {
    border: 1px solid var(--w-e-textarea-border-color);
    margin: 0 auto;
    padding: 10px 0;
    text-align: center;
    width: 480px;
}
.w-e-textarea-video-container iframe {
    height: 245px;
    width: 450px;
}
.w-e-textarea-video-container video {
    width: 450px;
}

.w-e-text-container [data-slate-editor] pre > code {
    word-wrap: normal;
    font-family: Consolas, Monaco, Andale Mono, Ubuntu Mono, monospace;
    -webkit-hyphens: none;
    hyphens: none;
    line-height: 1.5;
    margin: 0.5em 0;
    overflow: auto;
    padding: 1em;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    text-align: left;
    text-shadow: 0 1px #fff;
    white-space: pre;
    word-break: normal;
    word-spacing: normal;
}
.w-e-text-container [data-slate-editor] pre > code .token.cdata,
.w-e-text-container [data-slate-editor] pre > code .token.comment,
.w-e-text-container [data-slate-editor] pre > code .token.doctype,
.w-e-text-container [data-slate-editor] pre > code .token.prolog {
    color: #708090;
}
.w-e-text-container [data-slate-editor] pre > code .token.punctuation {
    color: #999;
}
.w-e-text-container [data-slate-editor] pre > code .token.namespace {
    opacity: 0.7;
}
.w-e-text-container [data-slate-editor] pre > code .token.boolean,
.w-e-text-container [data-slate-editor] pre > code .token.constant,
.w-e-text-container [data-slate-editor] pre > code .token.deleted,
.w-e-text-container [data-slate-editor] pre > code .token.number,
.w-e-text-container [data-slate-editor] pre > code .token.property,
.w-e-text-container [data-slate-editor] pre > code .token.symbol,
.w-e-text-container [data-slate-editor] pre > code .token.tag {
    color: #905;
}
.w-e-text-container [data-slate-editor] pre > code .token.attr-name,
.w-e-text-container [data-slate-editor] pre > code .token.builtin,
.w-e-text-container [data-slate-editor] pre > code .token.char,
.w-e-text-container [data-slate-editor] pre > code .token.inserted,
.w-e-text-container [data-slate-editor] pre > code .token.selector,
.w-e-text-container [data-slate-editor] pre > code .token.string {
    color: #690;
}
.w-e-text-container [data-slate-editor] pre > code .language-css .token.string,
.w-e-text-container [data-slate-editor] pre > code .style .token.string,
.w-e-text-container [data-slate-editor] pre > code .token.entity,
.w-e-text-container [data-slate-editor] pre > code .token.operator,
.w-e-text-container [data-slate-editor] pre > code .token.url {
    color: #9a6e3a;
}
.w-e-text-container [data-slate-editor] pre > code .token.atrule,
.w-e-text-container [data-slate-editor] pre > code .token.attr-value,
.w-e-text-container [data-slate-editor] pre > code .token.keyword {
    color: #07a;
}
.w-e-text-container [data-slate-editor] pre > code .token.class-name,
.w-e-text-container [data-slate-editor] pre > code .token.function {
    color: #dd4a68;
}
.w-e-text-container [data-slate-editor] pre > code .token.important,
.w-e-text-container [data-slate-editor] pre > code .token.regex,
.w-e-text-container [data-slate-editor] pre > code .token.variable {
    color: #e90;
}
.w-e-text-container [data-slate-editor] pre > code .token.bold,
.w-e-text-container [data-slate-editor] pre > code .token.important {
    font-weight: 700;
}
.w-e-text-container [data-slate-editor] pre > code .token.italic {
    font-style: italic;
}
.w-e-text-container [data-slate-editor] pre > code .token.entity {
    cursor: help;
}
@-webkit-keyframes upbit {
    from {
        -webkit-transform: translate3d(0, 30px, 0);
        opacity: 0.3;
    }
    to {
        -webkit-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}
@keyframes upbit {
    from {
        transform: translate3d(0, 30px, 0);
        opacity: 0.3;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}