/*升级标准*/
.grade {
    padding: 20px;
    border-radius: 2px;
    background-color: #fff;
    margin-bottom: 10px;
}
.grade .layui-form-item {
    margin-bottom: 0;
}
.grade .layui-form-label {
    width: auto;
}
.grade .tip {
    line-height: 28px;
}
.grade .tip span {
    color: #f94004;
}

/*三级分类*/
.sort tbody tr {
    border-left: 1px solid #e9edef;
    border-right: 1px solid #e9edef;
}
.sort .sortOne {
    background-color: #fafdff;
}
.sort .sortThree .sort_info .hr {
    width: 220px;
    padding-left: 184px;
}
.sort .sort_info {
    width: 558px;
    padding: 0 30px;
    text-align: left;
    color: #262b30;
}
.sort .sort_info .show_sub {
    width: 18px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
}
.sort .sort_info .show_sub .iconfont {
    display: inline-block;
    color: #b8b9bd;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
}
.sort .sort_info .show_sub:hover span {
    color: #3385ff;
}
.sort .sort_info .rotate .iconfont {
    transform: rotate(-90deg);
}
.sort .sort_info .hr {
    width: 110px;
    height: 32px;
    line-height: 32px;
    padding-left: 70px;
}
.sort .sort_info .hr span {
    font-size: 24px;
    color: #b8b9bd;
}
.sort .sort_info .tit {
    padding: 0 14px;
    line-height: 32px;
}
.sort .sort_info .name {
    width: 108px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 3px;
    background-color: #f4f6f8;
}
.sort .sort_info a {
    color: #2d8cf0;
    line-height: 32px;
    margin-left: 20px;
}
/*交叉规格*/
.edit_wrap .sku-list .sku-item {
    padding: 16px 0;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 2px;
}
.edit_wrap .sku-list .sku-item-head {
    height: 34px;
    position: relative;
}
.edit_wrap .sku-list .layui-input {
    height: 34px;
    line-height: 34px;
}
.edit_wrap .sku-list .layui-btn {
    height: 32px;
    line-height: 32px;
}
.edit_wrap .sku-list .layui-form-label {
    padding: 7px 4px 7px 0;
}
.edit_wrap .sku-list .layui-btn {
    padding: 0 10px;
}
.edit_wrap .sku-list .layui-btn.layui-btn-danger {
    position: absolute;
    top: 0;
    right: 20px;
}
.edit_wrap .sku-list .sku-item-param {
    padding-top: 10px;
    padding-left: 84px;
}
.edit_wrap .sku-list .sku-item-param .hr {
    padding-right: 12px;
    height: 32px;
    line-height: 32px;
}
.edit_wrap .sku-list .sku-item-param .iconfont {
    font-size: 24px;
    color: #999;
}
.edit_wrap .sku-list .sku-item-param .sku-item-name {
    height: 32px;
    margin-right: 16px;
    border-radius: 2px;
    border: 1px solid #e6e6e6;
    position: relative;
}
.edit_wrap .sku-list .sku-item-param .sku-item-name:hover .iconfont {
    display: block;
}
.edit_wrap .sku-list .sku-item-param .sku-item-name .sku-item-value {
    display: block;
    width: 120px;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
}
.edit_wrap .sku-list .sku-item-param .sku-item-name .iconfont {
    display: none;
    cursor: pointer;
    position: absolute;
    top: -6px;
    right: 0;
}
.edit_wrap .sku-setting {
    padding: 14px 20px;
    margin-bottom: 10px;
    border-radius: 2px;
    background-color: #fff;
}
.edit_wrap .sku-setting a {
    display: inline-block;
    color: #3385ff;
    height: 32px;
    line-height: 32px;
    margin-right: 16px;
}
.edit_wrap .sku-setting .sku-setting-avatar {
    margin-top: 16px;
}
.edit_wrap .sku-setting .sku-setting-avatar .layui-form-upload {
    width: 40px;
    height: 40px;
    border: none;
}
.edit_wrap .sku-setting .sku-upload-logo {
    height: 28px;
    line-height: 28px;
    padding: 0 15px;
    margin: 6px 10px;
}
.edit_wrap .sku-setting .operat {
    display: none;
}
.edit_wrap .sku-setting .layui-input {
    width: 188px;
    height: 32px;
    line-height: 32px;
}
.edit_wrap .sku-setting .layui-form-label {
    padding: 6px 12px 6px 0;
}
.edit_wrap .sku-setting .layui-btn {
    margin-left: 10px;
}
.edit_wrap .layui-wrap .layui-table.param .layui-input {
    width: 88px;
    margin: 0 auto;
    text-align: center;
}
.edit_wrap .layui-wrap .layui-table.param .sku-logo,
.edit_wrap .layui-wrap .layui-table.param .param-logo {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    border: none;
}
/*edit_wrap*/
.edit_wrap .layui-wrap .suggest {
    color: #ff5722;
    line-height: 36px;
    padding-left: 20px;
}
.edit_wrap .layui-wrap .layui-input-block:hover .icon-delete {
    display: block;
}
.edit_wrap .layui-wrap .layui-input-block .icon-delete {
    display: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    position: absolute;
    right: -10px;
    top: -10px;
}
.edit_wrap .layui-wrap .layui-input-block .icon-delete:hover {
    color: #333;
}
.edit_wrap .layui-wrap.attribute table tbody tr td:first-of-type {
    width: 200px;
}
.edit_wrap .layui-wrap.attribute table th,
.edit_wrap .layui-wrap.attribute table tbody tr td {
    text-align: left;
    padding: 10px 20px;
}
.edit_wrap .layui-wrap.attribute .layui-form-checkbox[lay-skin="primary"] {
    height: 30px !important;
    line-height: 30px !important;
    padding-left: 22px;
}
.edit_wrap .layui-wrap.attribute .layui-form-checkbox[lay-skin="primary"] span {
    line-height: 30px;
    padding-right: 20px;
}
.edit_wrap .layui-wrap.attribute .layui-form-checkbox[lay-skin="primary"] i {
    top: 6px;
}
.edit_wrap .layui-wrap.attribute .layui-form-radio {
    height: 30px !important;
    line-height: 30px !important;
}
.edit_wrap .layui-wrap.attribute .attr_name .layui-input {
    width: 158px;
}
.edit_wrap .layui-wrap.attribute .attr_val .layui-input {
    width: 288px;
}
.edit_wrap .layui-wrap.attribute .attr_val p {
    line-height: 30px;
    position: absolute;
    left: 328px;
    top: 10px;
}
.edit_wrap .layui-wrap.attribute .attr_val .layui-btn {
    height: 30px;
    line-height: 30px;
    position: absolute;
    right: 20px;
    top: 10px;
}
.edit_wrap .layui-wrap.left .layui-form-item {
    margin-right: 20px;
}
/*筛选商品*/
.layui-table.filter {
    width: 830px;
}
.layui-table.filter + .layui-btns {
    margin-top: 0;
}
.layui-table.filter thead {
    display: block;
}
.layui-table.filter thead tr {
    background: none !important;
}
.layui-table.filter tbody {
    height: 350px;
    overflow: auto;
    display: block;
}
.layui-table.filter .price {
    width: 214px;
}
.layui-table.filter .sale {
    width: 150px;
}
.layui-table.filter .check_box {
    width: 150px;
}
.layui-table.filter .operate {
    width: 150px;
}
/*优惠券图标*/
.coupon {
    display: inline-block;
    width: 72px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    position: relative;
    border-radius: 2px;
}
.coupon.blue {
    background-color: #2d8cf0;
}
.coupon.yellow {
    background-color: #ff9900;
}
.coupon:after {
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: -4px;
    width: 8px;
    height: 100%;
    background: radial-gradient(circle, #fff, #fff 2px, transparent 3px);
    background-size: 8px 8px;
}
.icon-rec {
    display: inline-block;
    font-size: 12px;
    color: #fff;
    width: 32px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    margin-left: 4px;
    border-radius: 2px;
    position: relative;
    background-color: #ff1f2c;
    margin-top: 9px;
}
.icon-rec span {
    position: absolute;
    top: 0;
    left: -3px;
    border-width: 3px;
    border-style: solid;
    border-color: #ff1f2c #ff1f2c transparent transparent;
    margin: 0;
}
.icon-rec.green {
    background-color: #2ec770;
}
.icon-rec.green span {
    border-color: #2ec770 #2ec770 transparent transparent;
}

/*地区弹窗*/
.region .region-transfor {
    width: 350px;
}
.region .region-transfor .region-tit {
    color: #333;
    line-height: 32px;
}
.region .region-transfor .region-list {
    height: 500px;
    padding: 16px;
    overflow-y: auto;
    border: 1px solid #ededed;
}
.region .region-transfor .region-list::-webkit-scrollbar {
    display: block;
    width: 6px;
    height: 6px;
}
.region .region-transfor .region-list::-webkit-scrollbar-thumb {
    width: 6px;
    background: #d9e0ee;
    border-radius: 4px;
}
.region .region-transfor .province {
    color: #333;
    line-height: 14px;
    font-family: "Hiragino Sans GB";
    margin-bottom: 10px;
}
.region .region-transfor .province .icon-right {
    font-size: 12px;
    padding-right: 4px;
    display: inline-block;
    transition: transform 0.3s;
}
.region .region-transfor .province.active .province-sub {
    height: auto;
    overflow: visible;
    display: block;
}
.region .region-transfor .province.active .icon-right {
    transform: rotate(90deg);
}
.region .region-transfor .province p {
    cursor: pointer;
}
.region .region-transfor .nameP,
.region .region-transfor .nameC {
    width: 100%;
}
.region .region-transfor .choose {
    font-size: 12px;
    line-height: 12px;
    color: #2d8cf0;
    cursor: pointer;
}
.region .region-transfor .choose.disabled {
    color: #999;
}
.region .region-transfor .province-sub {
    width: 100%;
    height: 0;
    overflow: hidden;
    display: none;
    padding-top: 12px;
}
.region .region-transfor .province-city {
    font-size: 12px;
    color: #333;
    line-height: 12px;
    text-indent: 18px;
    margin-bottom: 10px;
}
.region .region-icon {
    width: 48px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #fbfbfb;
    border: 1px solid #e6e6e6;
    margin: 0 16px;
}
.region .region-icon span {
    font-size: 18px;
    color: #c9c9c9;
}
/*权限管理*/
.collapse {
    margin-left: 120px;
}
.collapse .collapse-item {
    border: 1px solid #e9edef;
    margin-bottom: 20px;
}
.collapse .collapse-title {
    position: relative;
    height: 42px;
    line-height: 42px;
    padding: 0 20px;
    color: #333;
    background-color: #f4f6f8;
    overflow: hidden;
}
.collapse .collapse-title .icon-caret {
    width: 20px;
    text-align: center;
    position: absolute;
    top: 0;
    right: 12px;
    cursor: pointer;
}
.collapse .collapse-content {
    display: none;
}
.collapse .collapse-content.show {
    display: block;
}
.collapse .collapse-li {
    border-bottom: 1px solid #e9edef;
}
.collapse .collapse-li:last-of-type {
    border-bottom: none;
}
.collapse .collapse-li:first-of-type {
    border-top: 1px solid #e9edef;
}
.collapse .collapse-li .collapse-name {
    width: 192px;
    padding-left: 20px;
    border-right: 1px solid #e9edef;
    display: flex;
    align-items: center;
}
.collapse .collapse-li .collapse-checks {
    flex: 1;
    padding: 10px 15px;
}
.collapse .collapse-li .collapse-checks .layui-form-checkbox[lay-skin="primary"] {
    width: 24%;
    margin: 6px 0;
}