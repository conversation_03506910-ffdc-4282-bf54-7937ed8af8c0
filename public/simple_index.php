<?php
// 最简单的入口文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('APP_DEBUG', true);
define('APP_PATH', __DIR__ . '/../application/');
define('THINK_PATH', __DIR__ . '/../thinkphp/5.0.24/');

// 创建运行时目录
$runtime_dirs = ['../runtime/cache', '../runtime/temp', '../runtime/log'];
foreach ($runtime_dirs as $dir) {
    if (!is_dir($dir)) mkdir($dir, 0755, true);
}

// 直接加载ThinkPHP
require THINK_PATH . 'start.php'; 