<?php
// 简化的测试入口文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>浦江论坛 - 简化测试页面</h1>";

// 测试基本PHP功能
echo "<h2>1. PHP环境测试</h2>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
echo "服务器信息: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// 测试文件包含
echo "<h2>2. 文件包含测试</h2>";
if (file_exists('../application/config.php')) {
    echo "✓ 配置文件存在<br>";
} else {
    echo "✗ 配置文件不存在<br>";
}

if (file_exists('../thinkphp/5.0.24/start.php')) {
    echo "✓ ThinkPHP启动文件存在<br>";
} else {
    echo "✗ ThinkPHP启动文件不存在<br>";
}

// 测试数据库连接
echo "<h2>3. 数据库连接测试</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "✓ 数据库连接成功<br>";
    
    // 测试查询
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "数据库表数量: " . count($tables) . "<br>";
    
} catch (PDOException $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
    echo "请确保MySQL服务已启动，并创建了pujiangforum数据库<br>";
}

// 测试ThinkPHP框架
echo "<h2>4. ThinkPHP框架测试</h2>";
try {
    // 定义必要的常量
    define('APP_DEBUG', true);
    define('APP_PATH', __DIR__ . '/../application/');
    
    // 加载框架
    require __DIR__ . '/../thinkphp/5.0.24/start.php';
    
    echo "✓ ThinkPHP框架加载成功<br>";
} catch (Exception $e) {
    echo "✗ ThinkPHP框架加载失败: " . $e->getMessage() . "<br>";
}

echo "<h2>5. 下一步操作</h2>";
echo "<p>如果以上测试都通过，请访问：</p>";
echo "<ul>";
echo "<li><a href='http://localhost:8000/'>首页</a></li>";
echo "<li><a href='http://localhost:8000/admin'>管理后台</a></li>";
echo "<li><a href='http://localhost:8000/web/index/index'>直接访问控制器</a></li>";
echo "</ul>";
?> 