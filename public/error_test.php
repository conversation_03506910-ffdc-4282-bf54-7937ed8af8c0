<?php
// 错误测试文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>ThinkPHP错误测试</h1>";

// 步骤1: 定义常量
echo "<h2>步骤1: 定义常量</h2>";
define('APP_DEBUG', true);
define('APP_PATH', __DIR__ . '/../application/');
define('THINK_PATH', __DIR__ . '/../thinkphp/5.0.24/');

echo "✓ 常量定义完成<br>";

// 步骤2: 检查文件
echo "<h2>步骤2: 检查文件</h2>";
$files = [
    THINK_PATH . 'base.php',
    THINK_PATH . 'start.php',
    APP_PATH . 'config.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✓ " . $file . " 存在<br>";
    } else {
        echo "✗ " . $file . " 不存在<br>";
    }
}

// 步骤3: 尝试加载base.php
echo "<h2>步骤3: 加载base.php</h2>";
try {
    require THINK_PATH . 'base.php';
    echo "✓ base.php 加载成功<br>";
} catch (Exception $e) {
    echo "✗ base.php 加载失败: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    exit;
} catch (Error $e) {
    echo "✗ base.php 加载失败 (Fatal Error): " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    exit;
}

// 步骤4: 检查Loader类
echo "<h2>步骤4: 检查Loader类</h2>";
if (class_exists('think\Loader')) {
    echo "✓ Loader类存在<br>";
} else {
    echo "✗ Loader类不存在<br>";
    exit;
}

// 步骤5: 检查App类
echo "<h2>步骤5: 检查App类</h2>";
if (class_exists('think\App')) {
    echo "✓ App类存在<br>";
} else {
    echo "✗ App类不存在<br>";
    exit;
}

// 步骤6: 尝试运行应用（不发送响应）
echo "<h2>步骤6: 尝试运行应用</h2>";
try {
    $app = \think\App::run();
    echo "✓ 应用运行成功<br>";
    echo "应用类型: " . get_class($app) . "<br>";
} catch (Exception $e) {
    echo "✗ 应用运行失败: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    
    // 显示错误堆栈
    echo "<h3>错误堆栈:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "✗ 应用运行失败 (Fatal Error): " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
    
    // 显示错误堆栈
    echo "<h3>错误堆栈:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>测试完成</h2>";
echo "<p>如果没有错误，说明ThinkPHP框架加载正常</p>";
?> 