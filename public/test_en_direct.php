<?php
// 直接测试en模块
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>直接测试en模块</h1>";

try {
    define('APP_PATH', realpath(__DIR__ . '/../application/') . DIRECTORY_SEPARATOR);
    define('THINK_PATH', realpath(__DIR__ . '/../thinkphp/5.0.24/') . DIRECTORY_SEPARATOR);
    
    require THINK_PATH . 'base.php';
    
    echo "<p style='color: green;'>✓ ThinkPHP 加载成功</p>";
    
    // 直接尝试加载en模块的Index控制器
    $controller_class = 'app\\en\\controller\\Index';
    
    if (class_exists($controller_class)) {
        echo "<p style='color: green;'>✓ en\\Index 控制器类存在</p>";
        
        $controller = new $controller_class();
        echo "<p style='color: green;'>✓ en\\Index 控制器实例化成功</p>";
        
        if (method_exists($controller, 'index')) {
            echo "<p style='color: green;'>✓ index 方法存在</p>";
            
            // 尝试调用index方法
            $result = $controller->index();
            echo "<p style='color: green;'>✓ index 方法调用成功</p>";
            echo "<p>返回结果类型: " . gettype($result) . "</p>";
            
        } else {
            echo "<p style='color: red;'>✗ index 方法不存在</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ en\\Index 控制器类不存在</p>";
        
        // 检查文件是否存在
        $controller_file = APP_PATH . 'en/controller/Index.php';
        if (file_exists($controller_file)) {
            echo "<p style='color: green;'>✓ 控制器文件存在: " . $controller_file . "</p>";
            
            // 尝试手动包含文件
            include_once $controller_file;
            
            if (class_exists($controller_class)) {
                echo "<p style='color: green;'>✓ 手动包含后控制器类存在</p>";
            } else {
                echo "<p style='color: red;'>✗ 手动包含后控制器类仍不存在</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ 控制器文件不存在: " . $controller_file . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
}

echo "<p><a href='/'>返回首页</a></p>";
?> 