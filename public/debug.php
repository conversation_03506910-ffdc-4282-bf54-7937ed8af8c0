<?php
// 调试入口文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>ThinkPHP 调试页面</h1>";

// 步骤1: 检查基本环境
echo "<h2>步骤1: 基本环境检查</h2>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
echo "当前目录: " . __DIR__ . "<br>";

// 步骤2: 检查文件存在性
echo "<h2>步骤2: 文件存在性检查</h2>";
$files_to_check = [
    '../application/config.php',
    '../thinkphp/5.0.24/start.php',
    '../thinkphp/5.0.24/base.php',
    '../thinkphp/5.0.24/library/think/App.php',
    '../thinkphp/5.0.24/library/think/Loader.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ " . $file . "<br>";
    } else {
        echo "✗ " . $file . " (不存在)<br>";
    }
}

// 步骤3: 检查目录权限
echo "<h2>步骤3: 目录权限检查</h2>";
$dirs_to_check = [
    '../runtime',
    '../runtime/cache',
    '../runtime/temp',
    '../runtime/log'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✓ " . $dir . " (可写)<br>";
        } else {
            echo "⚠ " . $dir . " (存在但不可写)<br>";
        }
    } else {
        echo "✗ " . $dir . " (不存在)<br>";
        // 尝试创建目录
        if (mkdir($dir, 0755, true)) {
            echo "  → 已创建目录<br>";
        } else {
            echo "  → 创建目录失败<br>";
        }
    }
}

// 步骤4: 尝试加载基础文件
echo "<h2>步骤4: 基础文件加载测试</h2>";
try {
    // 定义基本常量
    define('THINK_VERSION', '5.0.24');
    define('THINK_START_TIME', microtime(true));
    define('THINK_START_MEM', memory_get_usage());
    define('EXT', '.php');
    define('DS', DIRECTORY_SEPARATOR);
    define('THINK_PATH', __DIR__ . '/../thinkphp/5.0.24/');
    define('LIB_PATH', THINK_PATH . 'library/');
    define('CORE_PATH', LIB_PATH . 'think/');
    define('TRAIT_PATH', LIB_PATH . 'traits/');
    define('APP_PATH', __DIR__ . '/../application/');
    define('ROOT_PATH', __DIR__ . '/../');
    define('RUNTIME_PATH', ROOT_PATH . 'runtime/');
    define('LOG_PATH', RUNTIME_PATH . 'log/');
    define('CACHE_PATH', RUNTIME_PATH . 'cache/');
    define('TEMP_PATH', RUNTIME_PATH . 'temp/');
    define('CONF_PATH', APP_PATH);
    define('CONF_EXT', EXT);
    define('ENV_PREFIX', 'PHP_');
    define('IS_CLI', PHP_SAPI == 'cli' ? true : false);
    define('IS_WIN', strpos(PHP_OS, 'WIN') !== false);
    
    echo "✓ 基本常量定义成功<br>";
    
    // 尝试加载Loader类
    if (file_exists(CORE_PATH . 'Loader.php')) {
        require CORE_PATH . 'Loader.php';
        echo "✓ Loader类加载成功<br>";
        
        // 尝试注册自动加载
        if (class_exists('think\Loader')) {
            \think\Loader::register();
            echo "✓ 自动加载注册成功<br>";
        } else {
            echo "✗ Loader类不存在<br>";
        }
    } else {
        echo "✗ Loader.php文件不存在<br>";
    }
    
} catch (Exception $e) {
    echo "✗ 基础文件加载失败: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
}

// 步骤5: 尝试加载配置文件
echo "<h2>步骤5: 配置文件加载测试</h2>";
try {
    if (file_exists(APP_PATH . 'config.php')) {
        $config = include APP_PATH . 'config.php';
        if (is_array($config)) {
            echo "✓ 配置文件加载成功<br>";
            echo "默认模块: " . ($config['default_module'] ?? '未设置') . "<br>";
            echo "默认控制器: " . ($config['default_controller'] ?? '未设置') . "<br>";
            echo "默认操作: " . ($config['default_action'] ?? '未设置') . "<br>";
        } else {
            echo "✗ 配置文件格式错误<br>";
        }
    } else {
        echo "✗ 配置文件不存在<br>";
    }
} catch (Exception $e) {
    echo "✗ 配置文件加载失败: " . $e->getMessage() . "<br>";
}

// 步骤6: 尝试加载App类
echo "<h2>步骤6: App类加载测试</h2>";
try {
    if (class_exists('think\App')) {
        echo "✓ App类存在<br>";
        
        // 尝试创建App实例
        $app = new \think\App();
        echo "✓ App实例创建成功<br>";
    } else {
        echo "✗ App类不存在<br>";
    }
} catch (Exception $e) {
    echo "✗ App类加载失败: " . $e->getMessage() . "<br>";
}

// 步骤7: 数据库连接测试
echo "<h2>步骤7: 数据库连接测试</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "✓ 数据库连接成功<br>";
    
    // 测试查询
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "数据库表数量: " . count($tables) . "<br>";
    
} catch (PDOException $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
}

echo "<h2>调试完成</h2>";
echo "<p>如果以上测试都通过，请尝试访问：</p>";
echo "<ul>";
echo "<li><a href='http://localhost:8000/'>主页面</a></li>";
echo "<li><a href='http://localhost:8000/simple.php'>简化测试</a></li>";
echo "</ul>";
?> 