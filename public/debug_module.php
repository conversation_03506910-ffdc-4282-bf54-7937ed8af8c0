<?php
// 调试模块加载
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试模块加载</h1>";

try {
    define('APP_PATH', realpath(__DIR__ . '/../application/') . '/');
    define('THINK_PATH', realpath(__DIR__ . '/../thinkphp/5.0.24/') . '/');
    
    echo "<p>APP_PATH: " . APP_PATH . "</p>";
    echo "<p>THINK_PATH: " . THINK_PATH . "</p>";
    
    require THINK_PATH . 'base.php';
    
    echo "<p style='color: green;'>✓ ThinkPHP 加载成功</p>";
    
    // 模拟ThinkPHP的模块检查逻辑
    $module = 'en';
    $config = [
        'app_multi_module' => true,
        'deny_module_list' => ['common'],
        'default_module' => 'web'
    ];
    
    echo "<h3>模块检查详情:</h3>";
    echo "<p>模块名: " . $module . "</p>";
    echo "<p>app_multi_module: " . ($config['app_multi_module'] ? 'true' : 'false') . "</p>";
    echo "<p>deny_module_list: " . json_encode($config['deny_module_list']) . "</p>";
    
    $bind = \think\Route::getBind('module');
    echo "<p>绑定模块: " . ($bind ?: '无') . "</p>";
    
    $available = false;
    
    if ($bind) {
        // 绑定模块逻辑
        list($bindModule) = explode('/', $bind);
        echo "<p>绑定模块名: " . $bindModule . "</p>";
        
        if (empty($module)) {
            $module = $bindModule;
            $available = true;
            echo "<p>使用绑定模块: " . $module . "</p>";
        } elseif ($module == $bindModule) {
            $available = true;
            echo "<p>模块匹配绑定模块</p>";
        }
    } elseif (!in_array($module, $config['deny_module_list']) && is_dir(APP_PATH . $module)) {
        $available = true;
        echo "<p>模块通过常规检查</p>";
    }
    
    echo "<p>最终available结果: " . ($available ? 'true' : 'false') . "</p>";
    
    if (!$available) {
        echo "<h3>失败原因分析:</h3>";
        echo "<p>模块在deny_module_list中: " . (in_array($module, $config['deny_module_list']) ? '是' : '否') . "</p>";
        echo "<p>模块目录存在: " . (is_dir(APP_PATH . $module) ? '是' : '否') . "</p>";
        echo "<p>完整模块路径: " . APP_PATH . $module . "</p>";
        
        // 检查目录权限
        $module_path = APP_PATH . $module;
        echo "<p>目录可读: " . (is_readable($module_path) ? '是' : '否') . "</p>";
        echo "<p>目录可执行: " . (is_executable($module_path) ? '是' : '否') . "</p>";
        
        // 列出目录内容
        if (is_dir($module_path)) {
            echo "<p>目录内容:</p>";
            $files = scandir($module_path);
            echo "<ul>";
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    echo "<li>" . $file . " (" . (is_dir($module_path . '/' . $file) ? '目录' : '文件') . ")</li>";
                }
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
}

echo "<p><a href='/'>返回首页</a></p>";
?> 