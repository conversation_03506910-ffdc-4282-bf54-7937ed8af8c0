<?php
// 测试自动加载机制
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>测试自动加载机制</h1>";

try {
    define('APP_PATH', realpath(__DIR__ . '/../application/') . DIRECTORY_SEPARATOR);
    define('THINK_PATH', realpath(__DIR__ . '/../thinkphp/5.0.24/') . DIRECTORY_SEPARATOR);
    
    echo "<p>APP_PATH: " . APP_PATH . "</p>";
    echo "<p>THINK_PATH: " . THINK_PATH . "</p>";
    
    require THINK_PATH . 'base.php';
    
    echo "<p style='color: green;'>✓ ThinkPHP 加载成功</p>";
    
    // 检查自动加载器
    $autoloaders = spl_autoload_functions();
    echo "<h3>已注册的自动加载器:</h3>";
    foreach ($autoloaders as $i => $autoloader) {
        if (is_array($autoloader)) {
            if (is_object($autoloader[0])) {
                echo "<p>" . $i . ": " . get_class($autoloader[0]) . "::" . $autoloader[1] . "</p>";
            } else {
                echo "<p>" . $i . ": " . $autoloader[0] . "::" . $autoloader[1] . "</p>";
            }
        } else {
            echo "<p>" . $i . ": " . (is_string($autoloader) ? $autoloader : 'Closure') . "</p>";
        }
    }
    
    // 测试自动加载
    echo "<h3>测试自动加载:</h3>";
    
    $controller_class = 'app\\en\\controller\\Index';
    echo "<p>尝试加载类: " . $controller_class . "</p>";
    
    if (class_exists($controller_class, true)) {
        echo "<p style='color: green;'>✓ 类加载成功</p>";
        
        $controller = new $controller_class();
        echo "<p style='color: green;'>✓ 控制器实例化成功</p>";
        
    } else {
        echo "<p style='color: red;'>✗ 类加载失败</p>";
        
        // 检查文件路径
        $expected_file = APP_PATH . 'en/controller/Index.php';
        echo "<p>期望文件路径: " . $expected_file . "</p>";
        echo "<p>文件是否存在: " . (file_exists($expected_file) ? '是' : '否') . "</p>";
        
        if (file_exists($expected_file)) {
            echo "<p>文件内容前100字符:</p>";
            echo "<pre>" . htmlspecialchars(substr(file_get_contents($expected_file), 0, 100)) . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
}

echo "<p><a href='/'>返回首页</a></p>";
?> 