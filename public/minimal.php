<?php
// 最简单的测试页面
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>浦江论坛 - 最小化测试</h1>";
echo "<p>如果您能看到这个页面，说明PHP环境正常。</p>";

// 测试数据库连接
echo "<h2>数据库连接测试</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', '');
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 测试查询
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>数据库表数量: " . count($tables) . "</p>";
    
    if (count($tables) > 0) {
        echo "<p>数据库表列表:</p><ul>";
        foreach ($tables as $table) {
            echo "<li>" . $table . "</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
    echo "<p>请确保MySQL服务已启动，并创建了pujiangforum数据库</p>";
}

// 测试文件系统
echo "<h2>文件系统测试</h2>";
$test_dirs = [
    '../application',
    '../thinkphp',
    '../runtime',
    '../public'
];

foreach ($test_dirs as $dir) {
    if (is_dir($dir)) {
        echo "<p>✓ " . $dir . " 目录存在</p>";
    } else {
        echo "<p style='color: red;'>✗ " . $dir . " 目录不存在</p>";
    }
}

// 测试PHP扩展
echo "<h2>PHP扩展测试</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✓ " . $ext . " 扩展已加载</p>";
    } else {
        echo "<p style='color: red;'>✗ " . $ext . " 扩展未加载</p>";
    }
}

echo "<h2>下一步操作</h2>";
echo "<p>如果以上测试都通过，请尝试：</p>";
echo "<ul>";
echo "<li><a href='debug.php'>运行详细调试</a></li>";
echo "<li><a href='direct.php'>直接调用控制器</a></li>";
echo "<li><a href='simple.php'>简化框架测试</a></li>";
echo "</ul>";

echo "<h2>快速修复建议</h2>";
echo "<ol>";
echo "<li>确保MySQL服务已启动</li>";
echo "<li>创建数据库：CREATE DATABASE pujiangforum;</li>";
echo "<li>执行SQL脚本：mysql -u root -p pujiangforum < ../init_database.sql</li>";
echo "<li>确保runtime目录可写</li>";
echo "</ol>";
?> 