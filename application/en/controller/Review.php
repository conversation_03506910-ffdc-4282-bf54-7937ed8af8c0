<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：往届回顾 **/
class Review extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：往届回顾 **/
	/** 作者：穠@20230328 **/
    public function review() {

		/** 查询往届回顾信息 **/
		$row = Db::table('review')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 20)->find();

		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：论坛主题 **/
	/** 作者：穠@20230328 **/
    public function theme() {

		/** 对外接收参数 **/
		$id = input('id', '');	//分类id

		/** 查询论坛主题信息 **/
		$row = Db::table('review')->where('language', 2)->where('genre', 1)->order('sort, id')->where('id', $id)->where('is_del', 1)->column('id,title,tdk_key,desc,theme_detail');

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 21)->find();

		$this->assign('id', $id);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：论坛日程 **/
	/** 作者：穠@20230328 **/
    public function schedule() {

		/** 对外接收参数 **/
		$id = input('id', '');	//分类id

		/** 查询论坛日程信息 **/
		$data = Db::table('review')->where('language', 2)->where('genre', 1)->order('sort, id')->where('id', $id)->where('is_del', 1)->column('id,title,tdk_key,desc,schedule_title,schedule_day,schedule_main,schedule_exhibit');

		/** 查询论坛日程 **/
		$sort = Db::table('review_schedule')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', $id)->where('is_del', 1)->select();

		foreach ($sort as $k => $v) {
			$sort[$k]['row'] = Db::table('review_schedule_time')->where('schedule_id', $v['id'])->order('sort, id')->where('is_del', 1)->select();
		}

		foreach ($sort as $k => $v) {

			$time_i = '';	//时间下标
			$place_i = '';	//地址下标

			foreach ($v['row'] as $kk => $vv) {
				if ($vv['time']) {
					$time_i = $kk;
				} else {
					if ($time_i !== '') {
						if (isset($sort[$k]['row'][$time_i]['time_merge'])) {
							$sort[$k]['row'][$time_i]['time_merge'] += 1;
						} else {
							$sort[$k]['row'][$time_i]['time_merge'] = 2;
						}
					}
				}

				if ($vv['place']) {
					$place_i = $kk;
				} else {
					if ($place_i !== '') {
						if (isset($sort[$k]['row'][$place_i]['place_merge'])) {
							$sort[$k]['row'][$place_i]['place_merge'] += 1;
						} else {
							$sort[$k]['row'][$place_i]['place_merge'] = 2;
						}
					}
				}
			}
		}

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 22)->find();

		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('sort', $sort);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：演讲嘉宾 **/
	/** 作者：穠@20230328 **/
    public function guest() {

		/** 对外接收参数 **/
		$id  = input('id', '');	    //分类id		
		$num = input('num/d', 12);	//每页显示多少条

		/** 查询演讲嘉宾 **/
		$row  = Db::table('review_guest')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', $id)->where('is_del', 1);
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
		$page = $row->render();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 23)->find();
		
		$this->assign('id', $id);		
		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：图片集锦 **/
	/** 作者：穠@20230328 **/
    public function atlas() {

		/** 对外接收参数 **/
		$id = input('id', '');	//分类id		
		
		/** 查询图片集锦 **/
		$row = Db::table('review_picture')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', $id)->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 24)->find();
		
		$this->assign('id', $id);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：专题报告集 **/
	/** 作者：穠@20230328 **/
    public function report() {

		/** 对外接收参数 **/
		$id = input('id', '');	//分类id		
		
		/** 查询专题报告集 **/
		$row = Db::table('review_report')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', $id)->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 25)->find();
		
		$this->assign('id', $id);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：会后刊 **/
	/** 作者：穠@20230328 **/
    public function prints() {

		/** 对外接收参数 **/
		$id = input('id', '');	//分类id		
		
		/** 查询会后刊 **/
		$row = Db::table('review_prints')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', $id)->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 26)->find();
		
		$this->assign('id', $id);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}
}