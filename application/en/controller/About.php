<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：论坛介绍 **/
class About extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：论坛简介 **/
	/** 作者：穠@20230328 **/
    public function about() {		
		
		/** 查询论坛简介信息 **/
		$data = Db::table('forum_blurb')->where('language', 2)->where('genre', 1)->order('id desc')->find();

		$this->assign('data', $data);
		$this->assign('public_title', $data['title']);
		$this->assign('public_key', $data['tdk_key']);
		$this->assign('public_desc', $data['desc']);
        return $this->fetch('en@public/public');
	}

	/** 功能：组织机构 **/
	/** 作者：穠@20230328 **/
    public function organization() {

		/** 查询分类 **/
		$sort = Db::table('forum_agency_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

		foreach ($sort as $k => $v) {
			$sort[$k]['row'] = Db::table('forum_agency')->where('language', 2)->where('genre', 1)->where('sort_id', $v['id'])->where('is_del', 1)->order('sort, id')->select();
		}
		
		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 15)->find();

		$this->assign('sort', $sort);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

}