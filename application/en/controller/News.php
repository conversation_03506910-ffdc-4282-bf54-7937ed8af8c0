<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：新闻中心 **/
class News extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：论坛动态 **/
	/** 作者：穠@20230328 **/
    public function news() {

        /** 对外接收参数 **/
		$num	 = input('num/d', 9);				//每页显示多少条
		$keyword = trim(input('keyword', ''));		//检索条件
		$pid     = input('pid', '');				//二级分类id
		$sort_id = input('sort_id', '');			//分类id
		
		if (!$pid) {
			
			/** 查询父级分类 **/
			$pid = Db::table('news_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('pid', 0)->where('is_del', 1)->value('id');
		}

		/** 查询分类 **/
		$sort = Db::table('news_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('pid', $pid)->where('is_del', 1)->column('id,name,tdk_key,desc,pid');

		foreach ($sort as $k => $v) {
			if(!$sort_id) {
				$sort_id = $v['id'];
			}
		}

		/** 查询新闻信息 **/
		$row  = Db::table('news')->order('sort desc, release_time desc')->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('sort', $sort);
		$this->assign('keyword', $keyword);
		$this->assign('sort_id', $sort_id);
		$this->assign('pid', $pid);
		$this->assign('public_title', $sort[$sort_id]['name']);
		$this->assign('public_key', $sort[$sort_id]['tdk_key']);
		$this->assign('public_desc', $sort[$sort_id]['desc']);
        return $this->fetch('en@public/public');
	}
	
	/** 功能：浦江发布 **/
	/** 作者：穠@20230328 **/
    public function release() {

		$this->assign('public_title', '浦江发布');
		$this->assign('public_key', '浦江发布');
		$this->assign('public_desc', '浦江发布');
        return $this->fetch('en@public/public');
	}
	
	/** 功能：会议季活动 **/
	/** 作者：穠@20230328 **/
    public function activity() {

		$this->assign('public_title', '会议季活动');
		$this->assign('public_key', '会议季活动');
		$this->assign('public_desc', '会议季活动');
        return $this->fetch('en@public/public');
	}
	
	/** 功能：访谈栏目 **/
	/** 作者：穠@20230328 **/
    public function interview() {

		$this->assign('public_title', '访谈栏目');
		$this->assign('public_key', '访谈栏目');
		$this->assign('public_desc', '访谈栏目');
        return $this->fetch('en@public/public');
	}
	
	/** 功能：平行未来的N次元 **/
	/** 作者：穠@20230328 **/
    public function video() {

		$this->assign('public_title', '平行未来的N次元');
		$this->assign('public_key', '平行未来的N次元');
		$this->assign('public_desc', '平行未来的N次元');
        return $this->fetch('en@public/public');
	}

	/** 功能：详情 **/
	/** 作者：穠@20230328 **/
    public function detail() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	  //新闻id

		/** 添加浏览量 **/
		Db::table('news')->where('id', $id)->setInc('visit_num');

		/** 查询新闻信息 **/
		$data = Db::table('news')->where('language', 2)->where('genre', 1)->where('id', $id)->find();

		/** 查询新闻分类 **/
		$sort_find = Db::table('news_sort')->where('id', $data['sort_id'])->where('is_del', 1)->find();
		
		/** 查询分类 **/
		$sort = Db::table('news_sort')->where('language', 2)->where('genre', 1)->where('pid', $sort_find['pid'])->where('is_del', 1)->select();

		/** 查询上一条 **/
		$shang = Db::table('news')->where('sort_id', $sort_find['id'])->order('id')->where('release_time', $data['release_time'])->where('id', '>', $data['id'])->where('is_del', 1)->find();
		if (!$shang) {
			$shang = Db::table('news')->where('sort_id', $sort_find['id'])->order('release_time')->where('release_time', '>', $data['release_time'])->where('is_del', 1)->find();
		}

		/** 查询下一条 **/
		$xiayt = Db::table('news')->where('sort_id', $sort_find['id'])->order('id desc')->where('release_time', $data['release_time'])->where('id', '<', $data['id'])->where('is_del', 1)->find();
		if (!$xiayt) {
			$xiayt = Db::table('news')->where('sort_id', $sort_find['id'])->order('release_time desc')->where('release_time', '<', $data['release_time'])->where('is_del', 1)->find();
		}

		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('shang', $shang);
		$this->assign('xiayt', $xiayt);
		$this->assign('sort', $sort);
		$this->assign('pid', $sort_find['pid']);
		$this->assign('sort_id', $sort_find['id']);
		$this->assign('public_title', $data['title']);
		$this->assign('public_key', $data['tdk_key']);
		$this->assign('public_desc', $data['desc']);
        return $this->fetch('en@public/public');
	}
}