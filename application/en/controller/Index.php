<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：首页 **/
class Index extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：首页 **/
	/** 作者：穠@20230328 **/
    public function index() {

		/** 查询Banner信息 **/
		$banner = Db::table('banner')->where('language', 2)->where('genre', 1)->order('sort')->where('is_del', 1)->select();

		/** 查询新闻 **/
		$news = Db::table('news')->where('language', 2)->where('genre', 1)->order('sort desc, release_time desc')->limit(7)->where('is_del', 1)->select();

		/** 查询新闻-浦江发布 **/
		$release = Db::table('news')->where('language', 2)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 15)->limit(6)->where('is_del', 1)->select();

		/** 查询往届回顾2023年-专题报告集 **/
		$report = Db::table('review_report')->where('language', 2)->where('genre', 1)->order('sort, id')->where('review_id', 7)->limit(6)->where('is_del', 1)->select();

		/** 查询新闻-会议季活动 **/
		$activity = Db::table('news')->where('language', 2)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 16)->limit(5)->where('is_del', 1)->select();

		/** 查询新闻-平行未来的N次元 **/
		$videos = Db::table('news')->where('language', 2)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 19)->limit(5)->where('is_del', 1)->select();

		/** 查询新闻-视频会客厅 **/
		$video = Db::table('news')->where('language', 2)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 21)->limit(5)->where('is_del', 1)->select();

		/** 查询合作伙伴分类信息 **/
		$cooperatesort = Db::table('cooperate_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('id', 'in', [26,24,6,7,9])->where('is_del', 1)->select();

		/** 查询合作伙伴信息 **/
		foreach ($cooperatesort as $k => $v) {
			$cooperatesort[$k]['cooperate'] = Db::table('cooperate')->order('sort, id')->where('language', 2)->where('genre', 1)->where('sort_id', $v['id'])->where('is_del', 1)->select();
		}

		/** 查询合作伙伴-合作媒体分类信息 **/
		$medium_id = Db::table('cooperate_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('type', 3)->where('is_del', 1)->column('id');

		$medium = Db::table('cooperate')->order('sort, id')->where('language', 2)->where('genre', 1)->where('sort_id', 'in', $medium_id)->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 14)->find();

		$this->assign('banner', $banner);
		$this->assign('news', $news);
		$this->assign('release', $release);
		$this->assign('report', $report);
		$this->assign('activity', $activity);
		$this->assign('videos', $videos);
		$this->assign('video', $video);
		$this->assign('medium', $medium);
		$this->assign('cooperatesort', $cooperatesort);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

}