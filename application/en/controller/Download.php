<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：文件下载 **/
class Download extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：文件下载 **/
	/** 作者：穠@20230328 **/
    public function download() {

		$num = input('num/d', 10);				//每页显示多少条

		/** 查询文件下载信息 **/
		$row  = Db::table('download')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1);
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
		$page = $row->render();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 19)->find();

		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

}