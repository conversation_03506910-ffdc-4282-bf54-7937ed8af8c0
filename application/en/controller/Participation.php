<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：报名参会 **/
class Participation extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：参会说明 **/
	/** 作者：穠@20230328 **/
    public function participation() {
		
		/** 查询参会说明信息 **/
		$data = Db::table('participation_explain')->where('language', 2)->where('genre', 1)->order('id desc')->find();

		$this->assign('data', $data);
		$this->assign('public_title', $data['title']);
		$this->assign('public_key', $data['tdk_key']);
		$this->assign('public_desc', $data['desc']);
        return $this->fetch('en@public/public');
	}
}