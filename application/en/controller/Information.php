<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：会务信息 **/
class Information extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：班车信息 **/
	/** 作者：穠@20230328 **/
    public function service() {
		
		/** 查询班车信息信息 **/
		$data = Db::table('meeting_vehicle')->where('language', 2)->where('genre', 1)->order('id desc')->find();

		$this->assign('data', $data);
		$this->assign('public_title', $data['title']);
		$this->assign('public_key', $data['tdk_key']);
		$this->assign('public_desc', $data['desc']);
        return $this->fetch('en@public/public');
	}

    /** 功能：推荐酒店 **/
	/** 作者：穠@20230328 **/
    public function hotel() {

		/** 查询推荐酒店信息 **/
		$data = Db::table('meeting_hotel')->where('language', 2)->where('genre', 1)->order('id desc')->find();

		/** 查询交通环境信息 **/
		$row = Db::table('meeting_hotel_traffic')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 16)->find();

		$this->assign('data', $data);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}

    /** 功能：会场地图 **/
	/** 作者：穠@20230328 **/
    public function map() {

		/** 查询会场地图信息 **/
		$data = Db::table('meeting_hotel')->where('language', 2)->where('genre', 1)->order('id desc')->find();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 2)->where('id', 17)->find();

		$this->assign('data', $data);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('en@public/public');
	}
}