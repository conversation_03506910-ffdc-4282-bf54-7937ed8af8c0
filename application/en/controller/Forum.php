<?php
namespace app\en\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：论坛日程 **/
class Forum extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：论坛日程 **/
	/** 作者：穠@20230328 **/
    public function forum() {

		$this->assign('public_title', 'Agenda');
		$this->assign('public_key', 'Agenda');
		$this->assign('public_desc', 'Agenda');
        return $this->fetch('en@public/public');
	}

}