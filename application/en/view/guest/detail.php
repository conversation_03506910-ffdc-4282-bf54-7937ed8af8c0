    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">Speakers</a>
                </div>
                <ul class="tabBox right">
                    <li class="on"><a href="<?=url('en/guest/guest')?>">Speakers</a></li>
                    <li><a href="<?=$public_site['guest_uid']?>" target="_blank">Speaker Registration</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- pageCharacterD -->
    <div class="pageCharacterD">
        <div class="wrap">
            <div class="swiper-container gallery-top">
                <div class="swiper-wrapper">
                    <?php if (!count($row)) { ?>

                    <?php } else { foreach ($row as $k => $v) { ?>
                        <div class="swiper-slide" data-hash="<?=$v['id']?>">
                            <div class="between">
                                <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                                <div class="cont">
                                    <?php if ($v['StringField22'] != '') { ?>
                                        <div class="name"><?=$v['StringField22']?></div>
                                    <?php } else { ?>
                                        <div class="name"><?=$v['FullName']?></div>
                                    <?php } ?>
                                    <div class="t"><?=$v['Position']?></div>
                                </div>
                            </div>
                        </div>
                    <?php } } ?>
                </div>
                <div class="arrow">
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
            <div class="swiper-container gallery-thumbs">
                <div class="swiper-wrapper">
                    <?php if (!count($row)) { ?>
                        
                    <?php } else { foreach ($row as $k => $v) { ?>
                        <div class="swiper-slide" data-hash="<?=$v['id']?>">
                            <img src="<?=$v['Avatar']?>" alt="">
                        </div>
                    <?php } } ?>
                </div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/swiper.min.css" />
    <script type="text/javascript" src="<?=$dir_public?>/web/js/swiper.min.js"></script>
    <script>
        var galleryThumbs = new Swiper('.gallery-thumbs', {
            spaceBetween: 20,
            slidesPerView: 6,
            loop: true,
            freeMode: true,
            loopedSlides: 7, //looped slides should be the same
            watchSlidesVisibility: true,
            watchSlidesProgress: true,
        });
        var galleryTop = new Swiper('.gallery-top', {
            hashNavigation: true,
            spaceBetween: 0,
            loop:true,
            loopedSlides: 7, //looped slides should be the same
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            thumbs: {
                swiper: galleryThumbs,
            },
        });
    </script>