    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">About</a>
                </div>
                <ul class="tabBox right">
                    <li><a href="<?=url('en/about/about')?>">About Us</a></li>
                    <li class="on"><a href="<?=url('en/about/organization')?>">Organization</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- pageOrganization -->
    <div class="pageOrganization">
        <div class="wrap">
            <div class="pageTitle">Organization</div>
            <ul>
                <?php if (!count($sort)) { ?>

                <?php } else { foreach ($sort as $k => $v) { ?>
                    <li>
                        <div class="t1"><?=$v['name']?></div>
                        <div class="left t2">
                            <?php if (!count($v['row'])) { ?>

                            <?php } else { foreach ($v['row'] as $kk => $vv) { ?>
                                <p><?=$vv['title']?></p>
                            <?php } } ?>
                        </div>
                    </li>
                <?php } } ?>
            </ul>
        </div>
    </div>