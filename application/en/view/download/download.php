    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">File Download</a>
                </div>
            </div>
        </div>
    </div>

    <!-- pageReport -->
    <div class="pageReport">
        <div class="wrap">
            <ul class="lists left">
                <?php if (!count($row)) { ?>

                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <div class="tit"><?=$v['title']?></div>
                        <div class="time"><?=$v['time']?></div>
                        <a href="<?=$v['url']?>" download="<?=$v['url_name']?>" class="down">Download</a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', 'PREV', $page);
                $page = str_replace('&raquo;', 'NEXT', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    </div>