<?php
	use \think\Cookie;
	use \think\Session;
	use \think\Request;
	use \think\Db;
	$request    = Request::instance();
	$dir_public = str_replace('/index.php', '', $request->root());
	$dir_view   = "../application/" . $request->module() . "/view/" . strtolower($request->controller()) . '/' . $request->action();

	/** 查询网站设置信息 **/
	$public_site = Db::table('rule_site')->where('language', 2)->where('genre', 1)->order('id desc')->find();

    /** 查询合作伙伴分类 **/
	$cooperate_sort = Db::table('cooperate_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

    /** 查询新闻分类 **/
	$news_sort = Db::table('news_sort')->where('language', 2)->where('genre', 1)->where('pid', 0)->order('sort, id')->where('is_del', 1)->select();
    
    $web_public_url = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

	$web_public_url_index[0] = 'http://' . $_SERVER['HTTP_HOST'];
	$web_public_url_index[1] = 'http://' . $_SERVER['HTTP_HOST'] . '/';
	$web_public_url_index[2] = 'http://' . $_SERVER['HTTP_HOST'] . '/index/index';
	$web_public_url_index[3] = 'http://' . $_SERVER['HTTP_HOST'] . '/index/index.html';
	
	if (in_array($web_public_url, $web_public_url_index)) {
		$m_public_url = 'http://' . $_SERVER['HTTP_HOST'] . "/m/index/index";
	} else {
        $m_public_url = str_replace('/en/', "/enm/", $web_public_url);
	}
?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<title><?=isset($public_title) ? $public_title : '';?></title>
	<meta name="keywords" content="<?=isset($public_key) ? $public_key : '';?>" />
    <meta name="description" content="<?=isset($public_desc) ? $public_desc : '';?>" />
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/common.css"/>
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/iconfont/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/web/css/style.css?v=' . date('YmdHis')?>">
	<script type="text/javascript" src="<?=$dir_public?>/web/js/jquery.min.js"></script>
	<script type="text/javascript" src="<?=$dir_public?>/web/js/jquery.SuperSlide.2.1.3.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/vue.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/axios.min.js"></script>
	<script type="text/javascript" src="<?=$dir_public?>/web/js/suncher.js"></script>
    <script>
		if(/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){
			window.location.href = "<?=$m_public_url?>";
		}
	</script>
</head>
<body>

    <!--headerBox-->
    <div class="headerBox headerBox1">
        <div class="wrap1 between">
            <a href="<?=url('en/index/index')?>" class="logo">
                <!-- <img src="<?=$dir_public?>/web/images/logo.png" /> -->
            </a>
            <div class="box right">
                <ul id="headerNav" class="navBox right">
                    <li class="nLi">
                        <h3><a href="<?=url('en/index/index')?>" data-href="index">Home</a></h3>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="about">About</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('en/about/about')?>">About Us</a></li>
                                <li><a href="<?=url('en/about/organization')?>">Organization</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="news">News</a></h3>
                        <div class="sub">
                            <ul>
                                <?php if (!count($news_sort)) { ?>
                                    
                                <?php } else { foreach ($news_sort as $k => $v) { ?>
                                    <li><a href="<?=url('en/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a></li>
                                <?php } } ?>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="<?=url('en/forum/forum')?>" data-href="forum">Agenda</a></h3>
                    </li>
                    <!-- <li class="nLi">
                        <h3><a href="javascript:;" data-href="guest">Speakers</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('en/guest/guest')?>">Speakers</a></li>
                                <li><a href="<?=$public_site['guest_uid']?>" target="_blank">Speaker Registration</a></li>
                            </ul>
                        </div>
                    </li> -->
                    <!-- <li class="nLi">
                        <h3><a href="javascript:;" data-href="participation">Registration</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('en/participation/participation')?>">Guidelines</a></li>
                                <li><a href="<?=$public_site['participation_uid']?>" target="_blank">Delegate Registration</a></li>
                            </ul>
                        </div>
                    </li> -->
                    <!-- <li class="nLi">
                        <h3><a href="javascript:;" data-href="information">Information</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('en/information/service')?>">Shuttle Bus</a></li>
                                <li><a href="<?=url('en/information/hotel')?>">Reservation</a></li>
                                <li><a href="<?=url('en/information/map')?>">Map</a></li>
                            </ul>
                        </div>
                    </li> -->
                    <li class="nLi">
                        <h3><a href="<?=url('en/live/live')?>" data-href="live">Live</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('en/live/review')?>">Live Review</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="<?=url('en/review/review')?>" data-href="review">Review</a></h3>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="partner">Partners</a></h3>
                        <div class="sub">
                            <ul>
                                <?php if (!count($cooperate_sort)) { ?>
                                    
                                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                                    <?php if ($v['type'] == 1) { ?>
                                        <li><a href="<?=url('en/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>   
                                    <?php } ?>
                                <?php } } ?>
                                <li><a href="<?=url('en/partner/medium')?>">Media Partners</a></li>    
                                <?php if (!count($cooperate_sort)) { ?>
                                    
                                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                                    <?php if ($v['type'] == 2) { ?>
                                        <li><a href="<?=url('en/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li> 
                                    <?php } ?>
                                <?php } } ?>
                            </ul>
                        </div>
                    </li>
                    <!-- <li class="nLi">
                        <h3><a href="<?=url('en/download/download')?>" data-href="download">File Download</a></h3>
                    </li> -->
                </ul>
                <a href="/" class="language">中文</a>
                <a href="<?=$public_site['sign_uid']?>" target="_blank" class="login login1 centerT">
                    <div class="iconfont icon-login"></div>
                    <p>Register/Login</p>
                </a>
            </div>
        </div>
    </div>

	<?php require_once("{$dir_view}.php");?>

    <!-- footer -->
    <div class="footer" id="footer">
        <div class="wrap between">
            <dl>
                <dt>Academic Contact</dt>
                <dd>Contact: <?=$public_site['learning_contacts']?></dd>
                <dd>Telephone: <?=$public_site['learning_phone']?></dd>
                <dd>Fax: <?=$public_site['learning_fax']?></dd>
                <dd>E-mail: <?=$public_site['learning_mail']?></dd>
                <dd>Address: <?=$public_site['learning_address']?></dd>
            </dl>
            <dl>
                <dt>Conference Contact</dt>
                <dd>Contact: <?=$public_site['business_contacts']?></dd>
                <dd>Telephone: <?=$public_site['business_phone']?></dd>
                <dd>Fax: <?=$public_site['business_fax']?></dd>
                <dd>E-mail: <?=$public_site['business_mail']?></dd>
                <dd>Address: <?=$public_site['business_address']?></dd>
            </dl>
            <div class="codeBox">
                <div class="p">Scan to follow</div>
                <div class="right">
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['service']?>" />
                        <p>WeChat Service</p>
                    </div>
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['subscribe']?>" />
                        <p>WeChat Subscription</p>
                    </div>
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['blog']?>" />
                        <p>Weibo</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="copyRight">
            <div class="wrap"><span>All rights reserved. Pujiang Innovation Forum</span> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备18034787号-1</a> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备05040256号-8</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402009841" target="_blank">沪公网安备 31010402009841号</a></div>
        </div>
    </div>

    <!-- sidebarBox -->
    <ul class="sidebarBox sidebarBox1">
        <li>
            <a href="<?=url('en/partner/cooperation', ['sort_id' => 11])?>">
                <div class="iconfont icon-hezuo"></div>
                <div class="t">Business Cooperation</div>
            </a>
        </li>
        <li>
            <a href="#footer">
                <div class="iconfont icon-dianhua"></div>
                <div class="t">Contact us</div>
            </a>
        </li>
        <!-- <li id="subscribe">
            <a href="javascript:;">
                <div class="iconfont icon-dingyue"></div>
                <div class="t">Subscribe E-Newsletter</div>
            </a>
        </li> -->
        <li>
            <a href="javascript:;">
                <div class="iconfont icon-weixin"></div>
                <div class="t">WeChat Service</div>
                <div class="img"><img src="<?=$dir_public . $public_site['service']?>" alt=""></div>
            </a>
        </li>
        <li>
            <a href="javascript:;">
                <div class="iconfont icon-weixin"></div>
                <div class="t">WeChat Subscription</div>
                <div class="img"><img src="<?=$dir_public . $public_site['subscribe']?>" alt=""></div>
            </a>
        </li>
        <li class="goTop">
            <a href="javascript:;">
                <div class="iconfont icon-dingbu"></div>
            </a>
        </li>
    </ul>

    <!-- popupBox -->
    <div class="popupBox"></div>
    <div class="popupForm">
        <form autocomplete="off">
            <div class="close"></div>
            <div class="title">Subscribe E-Newsletter</div>
            <div class="tit">Please leave your information to get our lastest Newsfeed</div>
            <div class="row">
                <div class="p">Name</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <div class="row">
                <div class="p">Company</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <div class="row">
                <div class="p">Email</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <button type="button" class="ajaxformbtn">Submit</button>
        </form>
    </div>

    <script>
        $("#subscribe").click(function() {
            $(".popupBox").show();
            $(".popupForm").slideDown(300);
        })

        $(".popupForm .close").click(function() {
            $(".popupBox").hide();
            $(".popupForm").slideUp(300);
        })
    </script>

    
</body>
</html>