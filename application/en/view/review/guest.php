    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">Review</a>
                </div>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <li><a href="<?=url('en/review/theme', ['id' => $id])?>">Theme</a></li>
            <li><a href="<?=url('en/review/schedule', ['id' => $id])?>">Agenda</a></li>
            <li class="on"><a href="<?=url('en/review/guest', ['id' => $id])?>">Speakers</a></li>
            <li><a href="<?=url('en/review/atlas', ['id' => $id])?>">Photo Gallery</a></li>
            <li><a href="<?=url('en/review/report', ['id' => $id])?>">Forum Bulletin</a></li>
            <li><a href="<?=url('en/review/prints', ['id' => $id])?>">Conference Review</a></li>
        </ul>
    </div>

    <!-- pageGuest -->
    <div class="pageGuest">
        <div class="wrap">
            <ul class="lists left">
                <?php if (!count($row)) { ?>
					<div class="noData">No Data</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="javascript:;" class="between">
                            <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                            <div class="cont">
                                <div class="name"><?=$v['name']?></div>
                                <div class="t"><?=$v['identity']?></div>
                            </div>
                        </a>
                    </li>         
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', 'PREV', $page);
                $page = str_replace('&raquo;', 'NEXT', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    </div>