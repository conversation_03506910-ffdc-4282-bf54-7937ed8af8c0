    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">Review</a>
                </div>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <li><a href="<?=url('en/review/theme', ['id' => $id])?>">Theme</a></li>
            <li class="on"><a href="<?=url('en/review/schedule', ['id' => $id])?>">Agenda</a></li>
            <li><a href="<?=url('en/review/guest', ['id' => $id])?>">Speakers</a></li>
            <li><a href="<?=url('en/review/atlas', ['id' => $id])?>">Photo Gallery</a></li>
            <li><a href="<?=url('en/review/report', ['id' => $id])?>">Forum Bulletin</a></li>
            <li><a href="<?=url('en/review/prints', ['id' => $id])?>">Conference Review</a></li>
        </ul>
    </div>

    <!-- pageSchedule -->
    <div class="pageSchedule">
        <div class="wrap">
            <div class="title"><?=$data[$id]['schedule_title']?></div>
            <div class="p">
                <?php if ($data[$id]['schedule_day']) {?>
                    Main session: <?=$data[$id]['schedule_day']?><span>│</span>
                <?php } ?>
                Conference Venue: <?=$data[$id]['schedule_main']?>
                <?php if ($data[$id]['schedule_exhibit']) {?>
                    <span>│</span>Exhibition Venue: <?=$data[$id]['schedule_exhibit']?>
                <?php } ?>
            </div>

            <?php if (!count($sort)) { ?>

            <?php } else { foreach ($sort as $k => $v) { ?>
                <!-- <?php if (strpos($v['title'], '月') !== false && strpos($v['title'], '日') !== false) {?>
                    <div class="time"><?=$v['title']?></div>
                <?php } else { ?>
                    <div class="time time1"><?=$v['title']?></div>
                <?php } ?> -->
                <div class="time"><?=$v['title']?></div>
                <table>
                    <tr>
                        <th>Time</th>
                        <th>Content</th>
                        <th>Address</th>
                    </tr>
                    <?php if (!count($v['row'])) { ?>

                    <?php } else { foreach ($v['row'] as $kk => $vv) { ?>
                        <tr>
                            <?php if ($vv['time']) {?>
                                <?php if (isset($vv['time_merge'])) { ?>
                                    <td rowspan="<?=$vv['time_merge']?>"><?=$vv['time']?></td>
                                <?php } else { ?>
                                    <td><?=$vv['time']?></td>
                                <?php } ?>
                            <?php } ?>
                            <td><?=$vv['content']?></td>
                            <?php if ($vv['place']) {?>
                                <?php if (isset($vv['place_merge'])) { ?>
                                    <td rowspan="<?=$vv['place_merge']?>"><?=$vv['place']?></td>
                                <?php } else { ?>
                                    <td><?=$vv['place']?></td>
                                <?php } ?>
                            <?php } ?>
                        </tr>
                    <?php } } ?>
                </table>
            <?php } } ?>
            <div class="tips">* The agenda will be updated and finalized by the Secretariat.</div>
        </div>
    </div>