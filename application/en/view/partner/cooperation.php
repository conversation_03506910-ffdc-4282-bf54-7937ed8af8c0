    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <!-- <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">Partners</a>
                </div> -->
                <ul class="tabBox tabBox1 left">
                    <?php if (!count($sort)) { ?>
					
                    <?php } else { foreach ($sort as $k => $v) { ?>
                        <?php if ($v['type'] == 1) { ?>
                            <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                                <a href="<?=url('en/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                            </li>
                        <?php } ?>
                    <?php } } ?>
                    <li class="mom">
                        <a href="<?=url('en/partner/medium')?>">Media Partners</a>
                        <div class="ul ul1">
                            <?php if (!count($sort)) { ?>
                        
                            <?php } else { foreach ($sort as $k => $v) { ?>
                                <?php if ($v['type'] == 3) { ?>
                                    <a href="<?=url('en/partner/partner', ['sort_id' => $v['id']])?>" class="li"><?=$v['name']?></a>
                                <?php } ?>
                            <?php } } ?>
                        </div>
                    </li>
                    <?php if (!count($sort)) { ?>
					
                    <?php } else { foreach ($sort as $k => $v) { ?>
                        <?php if ($v['type'] == 2) { ?>
                            <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                                <a href="<?=url('en/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                            </li>
                        <?php } ?>
                    <?php } } ?>
                </ul>
            </div>
        </div>
    </div>
    <script>
        $(".pageBanner .tabBox .mon").hover(function() {
            $(this).children('.ul').show();
        }, function() {
            $(this).children('.ul').hide();
        })
    </script>

    <!-- pageAbout -->
    <div class="pageAbout">
        <div class="wrap">
            <div class="pageTitle">Business Cooperation</div>
            <div class="text"><?=str_replace("&nbsp;", ' ', $sort[$sort_id]['detail'])?></div>
        </div>
    </div>