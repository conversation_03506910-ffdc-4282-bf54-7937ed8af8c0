    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">News</a>
                </div>
                <ul class="tabBox right">
                    <?php if (!count($news_sort)) { ?>
                    
                    <?php } else { foreach ($news_sort as $k => $v) { ?>
                        <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                            <a href="<?=url('en/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                        </li>
                    <?php } } ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('en/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageNewsD -->
    <div class="pageNewsD">
        <div class="wrap">
            <div class="title"><?=$data['title']?></div>
            <div class="date">
                <?php if ($data['source']) { ?>
                    <span>Source: <?=$data['source']?></span> 
                <?php } ?>
                <?php if ($data['author']) { ?>
                    <span>Author: <?=$data['author']?></span>
                <?php } ?>
                <span>Time: <?=substr($data['release_time'], 0, 10)?></span>
                <!-- <span>View: <?=$data['visit_num']?></span> -->
            </div>
            <div class="text"><?=str_replace("&nbsp;", ' ', $data['detail'])?></div>
            <div class="box">
                <?php if ($shang) { ?>
                    <p><a href="<?=url('en/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $shang['id']])?>">Prev: <?=$shang['title']?></a></p>
                <?php } else { ?>
                    <p><a href="javascript:;">Prev: No More</a></p>
                <?php } ?>
                <?php if ($xiayt) { ?>
                    <p><a href="<?=url('en/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $xiayt['id']])?>">Next: <?=$xiayt['title']?></a></p>
                <?php } else { ?>
                    <p><a href="javascript:;">Next: No More</a></p>
                <?php } ?>
            </div>
        </div>
    </div>