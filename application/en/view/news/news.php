    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>Location: </p>
                    <a href="<?=url('en/index/index')?>">Home</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">News</a>
                </div>
                <ul class="tabBox right">
                    <?php if (!count($news_sort)) { ?>
                                
                    <?php } else { foreach ($news_sort as $k => $v) { ?>
                        <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                            <a href="<?=url('en/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                        </li>
                    <?php } } ?>
                    <!-- <li class="on"><a href="<?=url('en/news/news')?>">News</a></li>
                    <li><a href="<?=url('en/news/interview')?>">Video</a></li> -->
                </ul>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('en/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
            <!-- <li class="on"><a href="<?=url('en/news/news')?>">论坛动态</a></li>
            <li><a href="<?=url('en/news/release')?>">浦江发布</a></li>
            <li><a href="<?=url('en/news/activity')?>">会议季活动</a></li>
            <li><a href="<?=url('en/news/news')?>">其他</a></li> -->
        </ul>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/swiper.min.css" />
    <script type="text/javascript" src="<?=$dir_public?>/web/js/swiper.min.js"></script>

    <?php if ($sort_id == 14 || $sort_id == 15 || $sort_id == 16 || $sort_id == 17) { ?>
        <!-- pageActivity -->
        <div class="pageActivity">
            <div class="wrap">
                <?php if (count($row)) { ?>
                    <div class="activityBox between">
                        <div class="swiper-container gallery-top">
                            <div class="swiper-wrapper">
                                <?php if (!count($row)) { ?>

                                <?php } else { foreach ($row as $k => $v) { if ($k < 3) { ?>
                                    <div class="swiper-slide">
                                        <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="between">
                                            <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                                            <div class="cont">
                                                <?php if ($sort_id == 14) { ?>
                                                    <div class="label">News</div>
                                                <?php } ?>
                                                <?php if ($sort_id == 15) { ?>
                                                    <div class="label">Annual Conference</div>
                                                <?php } ?>
                                                <?php if ($sort_id == 16) { ?>
                                                    <div class="label">Conference Session Events</div>
                                                <?php } ?>
                                                <?php if ($sort_id == 17) { ?>
                                                    <div class="label">Others</div>
                                                <?php } ?>
                                                <div class="tit cutTwo"><?=$v['title']?></div>
                                                <!-- <?php if ($v['time']) { ?>
                                                    <div class="time">
                                                        <div class="p left">
                                                            <div class="iconfont icon-shijian"></div>
                                                            <p>Time: <?=$v['time']?></p>
                                                        </div>
                                                        <div class="p left">
                                                            <div class="iconfont icon-weizhi1"></div>
                                                            <p>Address: <?=$v['place']?></p>
                                                        </div>
                                                    </div>
                                                <?php } else { ?>
                                                    <div class="txt cutThree"><?=$v['desc']?></div>
                                                <?php } ?> -->
                                                <div class="txt cutThree"><?=$v['desc']?></div>
                                            </div>
                                        </a>
                                    </div>
                                <?php } } } ?>
                            </div>
                            <div class="arrow">
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </div>
                        <div class="swiper-container gallery-thumbs">
                            <div class="swiper-wrapper">
                                <?php if (!count($row)) { ?>
                                        
                                <?php } else { foreach ($row as $k => $v) { if ($k < 3) { ?>
                                    <div class="swiper-slide"><img src="<?=$dir_public . $v['logo']?>?>" alt=""></div>
                                <?php } } } ?>
                            </div>
                        </div>
                    </div>

                    <ul class="lists left">
                        <?php if (!count($row)) { ?>

                        <?php } else { foreach ($row as $k => $v) { if ($k >= 3) { ?>
                            <li>
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <?php if ($sort_id == 14) { ?>
                                            <div class="label">News</div>
                                        <?php } ?>
                                        <?php if ($sort_id == 15) { ?>
                                            <div class="label">Annual Conference</div>
                                        <?php } ?>
                                        <?php if ($sort_id == 16) { ?>
                                            <div class="label">Conference Session Events</div>
                                        <?php } ?>
                                        <?php if ($sort_id == 17) { ?>
                                            <div class="label">Others</div>
                                        <?php } ?>
                                    </div>
                                    <div class="cont">
                                        <div class="tit cutTwo"><?=$v['title']?></div>
                                        <!-- <?php if ($v['time']) { ?>
                                            <div class="time">
                                                <div class="p left">
                                                    <div class="iconfont icon-shijian"></div>
                                                    <p>Time: <?=$v['time']?></p>
                                                </div>
                                                <div class="p left">
                                                    <div class="iconfont icon-weizhi1"></div>
                                                    <p>Address: <?=$v['place']?></p>
                                                </div>
                                            </div>
                                        <?php } else { ?>
                                            <div class="txt cutTwo"><?=$v['desc']?></div>
                                        <?php } ?> -->
                                        <div class="txt cutTwo"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </li>
                        <?php } } } ?>
                    </ul>

                    <!-- 分页 -->
                    <?php
                        $page = json_encode($page);
                        $page = str_replace('&laquo;', 'PREV', $page);
                        $page = str_replace('&raquo;', 'NEXT', $page);
                        $page = json_decode($page, true);
                    ?>
                    <?=$page?>
                <?php } else { ?>
                    <div class="noData">No Data</div>
                <?php } ?>
            </div>
        </div>
        <script>
            var galleryThumbs = new Swiper('.gallery-thumbs', {
                spaceBetween: 0,
                slidesPerView: 3,
                // loop: true,
                freeMode: true,
                direction: "vertical",
                loopedSlides: 3, //looped slides should be the same
                watchSlidesVisibility: true,
                watchSlidesProgress: true,
            });
            var galleryTop = new Swiper('.gallery-top', {
                spaceBetween: 0,
                // loop:true,
                loopedSlides: 3, //looped slides should be the same
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                thumbs: {
                    swiper: galleryThumbs,
                },
            });
        </script>
    <?php } else if ($sort_id == 18) { ?>
        <!-- pageNews -->
        <div class="pageNews">
            <div class="wrap">
                <?php if (count($row)) { ?>
                    <div class="newsBox swiper-container">
                        <div class="swiper-wrapper">
                            <?php if (!count($row)) { ?>

                            <?php } else { foreach ($row as $k => $v) { if ($k < 5) { ?>
                                <div class="swiper-slide">
                                    <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="between">
                                        <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                                        <div class="cont">
                                            <div class="tit cutTwo"><?=$v['title']?></div>
                                            <div class="txt cutThree"><?=$v['desc']?></div>
                                            <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                                        </div>
                                    </a>
                                </div>
                            <?php } } } ?>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                    <ul class="lists">
                        <?php if (!count($row)) { ?>

                        <?php } else { foreach ($row as $k => $v) { if ($k >= 5) { ?>
                            <li>
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="between">
                                    <div class="img"><img src="<?=$dir_public . $v['logo']?>?>" alt=""></div>
                                    <div class="cont cont1">
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cutTwo"><?=$v['desc']?></div>
                                        <!-- <div class="time1"><?=substr($v['release_time'], 0, 10)?></div> -->
                                        <div class="left a">
                                            <p>More</p>
                                            <div class="iconfont icon-arrow"></div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        <?php } } } ?>
                    </ul>

                    <!-- 分页 -->
                    <?php
                        $page = json_encode($page);
                        $page = str_replace('&laquo;', 'PREV', $page);
                        $page = str_replace('&raquo;', 'NEXT', $page);
                        $page = json_decode($page, true);
                    ?>
                    <?=$page?>
                <?php } else { ?>
                    <div class="noData">No Data</div>
                <?php } ?>
            </div>
        </div>
        <script>
            var newsBox = new Swiper('.newsBox', {
                loop: true,
                autoplay: {
                    delay: 5000,
                },
                pagination: {
                    el: '.swiper-pagination',
	                clickable :true,
                }
            });
        </script>
    <?php } else { ?>
        <!-- pageNews -->
        <div class="pageVideo">
            <div class="wrap">
                <?php if (count($row)) { ?>
                    <div class="videoBox between">
                        <div class="swiper-container gallery-top">
                            <div class="swiper-wrapper">
                                <?php if (!count($row)) { ?>

                                <?php } else { foreach ($row as $k => $v) { if ($k < 3) { ?>
                                    <div class="swiper-slide">
                                        <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="img">
                                            <img src="<?=$dir_public . $v['logo']?>" alt="">
                                            <div class="iconfont icon-bofang"></div>
                                        </a>
                                    </div>
                                <?php } } } ?>
                            </div>
                        </div>
                        <div class="swiper-container gallery-thumbs">
                            <div class="swiper-wrapper">
                                <?php if (!count($row)) { ?>
                                        
                                <?php } else { foreach ($row as $k => $v) { if ($k < 3) { ?>
                                    <div class="swiper-slide">
                                        <p><?=$v['title']?></p>
                                        <div class="iconfont icon-jiantou2"></div>
                                    </div>
                                <?php } } } ?>
                            </div>
                        </div>
                    </div>

                    <ul class="lists left">
                        <?php if (!count($row)) { ?>

                        <?php } else { foreach ($row as $k => $v) { if ($k >= 3) { ?>
                            <li>
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <div class="t cut"><?=$v['desc']?></div>
                                </a>
                            </li>
                        <?php } } } ?>
                    </ul>

                    <!-- 分页 -->
                    <?php
                        $page = json_encode($page);
                        $page = str_replace('&laquo;', 'PREV', $page);
                        $page = str_replace('&raquo;', 'NEXT', $page);
                        $page = json_decode($page, true);
                    ?>
                    <?=$page?>
                <?php } else { ?>
                    <div class="noData">No Data</div>
                <?php } ?>
            </div>
        </div>
        <script>
            var galleryThumbs = new Swiper('.gallery-thumbs', {
                spaceBetween: 0,
                slidesPerView: 3,
                // loop: true,
                freeMode: true,
                direction: "vertical",
                loopedSlides: 3, //looped slides should be the same
                watchSlidesVisibility: true,
                watchSlidesProgress: true,
            });
            var galleryTop = new Swiper('.gallery-top', {
                spaceBetween: 0,
                // loop:true,
                loopedSlides: 3, //looped slides should be the same
                // navigation: {
                //     nextEl: '.swiper-button-next',
                //     prevEl: '.swiper-button-prev',
                // },
                thumbs: {
                    swiper: galleryThumbs,
                },
            });
        </script>
    <?php } ?>