    <!-- indexBanner -->
    <div class="indexBanner swiper-container">
        <div class="swiper-wrapper">
            <?php if (!count($banner)) { ?>
            
            <?php } else { foreach ($banner as $k => $v) { ?>
                <div class="swiper-slide">
                    <!-- <a href="javascript:;" style="background: url(<?=$dir_public . $v['logo']?>) center center no-repeat;"></a> -->
                    <a href="javascript:;"><img src="<?=$dir_public . $v['logo']?>" alt=""></a>
                </div>
            <?php } } ?>
        </div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>

    <!-- indexNews -->
    <div class="indexNews">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">News Center</div>
                <a href="<?=url('en/news/news')?>" class="more right">
                    <p>More</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <div class="box between">
                <?php if (!count($news)) { ?>
                            
                <?php } else { foreach ($news as $k => $v) { if ($k < 1) { ?>
                    <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="contBox" title="<?=$v['title']?>">
                        <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                        <div class="cont">
                            <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                            <div class="tit cut"><?=$v['title']?></div>
                            <div class="txt cutTwo"><?=$v['desc']?></div>
                        </div>
                    </a>
                <?php } } } ?>

                <div class="newsBox swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($news)) { ?>
                                
                        <?php } else { foreach ($news as $k => $v) { if (1 <= $k && $k < 4) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" title="<?=$v['title']?>">
                                    <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <div class="txt cutThree"><?=$v['desc']?></div>
                                </a>
                            </div>
                        <?php } } } ?>
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                </div>

                <ul>
                    <?php if (!count($news)) { ?>
                                
                    <?php } else { foreach ($news as $k => $v) { if ($k >= 4) { ?>
                        <li title="<?=$v['title']?>">
                            <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="between">
                                <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                                <div class="cont">
                                    <div class="tit cut"><?=$v['title']?></div>
                                    <div class="txt cutTwo"><?=$v['desc']?></div>
                                    <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                                </div>
                            </a>
                        </li>
                    <?php } } } ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- indexSchedule -->
    <div class="indexSchedule" id="app">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">Agenda</div>
                <a href="<?=url('en/forum/forum')?>" class="more right">
                    <p>More</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <ul class="timeBox centerT">
                <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07 00:00:00','2024-09-07 23:59:59', 1)">09-07</li>
                <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08 00:00:00','2024-09-08 23:59:59', 2)">09-08</li>
                <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09 00:00:00','2024-09-09 23:59:59', 3)">09-09</li>
            </ul>

            <div class="scheduleBox">
                <div v-for="(item,index) in lists" :key="index" @click="select(index)">
                    <div class="liBox" v-if="item.lt.FC1I3KSV08QWDZVDU">
                        <div class="Box between" :class="sel === index ? 'on' : ''">
                            <div class="titBox">
                                <div class="left">
                                    <div class="tit">{{item.lt.FC1I3KSV02QWDZVUF}}</div>
                                    <!-- <div class="label label1 centerT" v-if="item.lt.forumtime_type == 1">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>Not started</p>
                                    </div>
                                    <div class="label label2 centerT" v-if="item.lt.forumtime_type == 2">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>Live</p>
                                    </div>
                                    <div class="label label3 centerT" v-if="item.lt.forumtime_type == 3">
                                        <div class="iconfont icon-zhibo"></div>
                                        <p>Ended</p>
                                    </div> -->
                                </div>
                                <div class="address left">
                                    <div class="left" style="margin-right: 20px;">
                                        <div class="iconfont icon-weizhi1"></div>
                                        <p>Room: {{item.lt.FC1I3KSV05GWDZVPB_name}}</p>
                                    </div>
                                    <div class="left">
                                        <div class="iconfont icon-shijian"></div>
                                        <p>Time: {{item.lt.start_time}} - {{item.lt.end_time}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="btnBox right">
                                <!-- <a href="javascrupt:;" class="btn btn2" v-if="item.lt.forumtime_type == 1">Await</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 2">Live</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 3">Playback</a> -->
                                <div class="iconfont icon-jiantou"></div>
                            </div>
                        </div>
                        <div class="contBox" :class="sel === index ? 'sel' : ''">
                            <div class="cont">
                                <!-- <div class="t">论坛地点：中国•上海</div> -->
                                <div class="t" v-if="item.dw_data != ''">
                                    <div class="between" v-if="item.dw_data.a">
                                        <p>Organizer：</p>
                                        <p><span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div>  
                                    <div class="between" v-if="item.dw_data.b">
                                        <p style="min-width: 114px;">Co-Organizer：</p>
                                        <p style="min-width: calc(100% - 114px);"><span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div> 
                                    <div class="between" v-if="item.dw_data.c">
                                        <p>Supporter：</p>
                                        <p><span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div> 
                                    <!-- <p v-if="item.dw_data.a">Organizer：
                                        <span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.b">Co-Organizer：
                                        <span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.c">Supporter：
                                        <span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p> -->
                                </div>
                                <div class="t1" v-if="item.lt.FC1I3KSV03JWDZVUB">Theme</div>
                                <div class="t2">{{item.lt.FC1I3KSV03JWDZVUB}}</div>
                                <!-- <div class="show">
                                    <p>点击展开</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div> -->
                            </div>
                            <div class="dl" v-if="item.yc_row != ''">
                                <div v-for="(innerItem,innerIndex) in item.yc_row" :key="innerIndex">
                                    <div class="dt between" v-if="!innerItem.FC1I3KT0NT6WDZVMF">
                                        <div class="time">{{innerItem.duration_sd}}</div>
                                        <div class="dd">
                                            <div class="t3">{{innerItem.topicEn}}</div>
                                            <div class="left" v-if="innerItem.FC1I3KT0NR7WDZVAE != 'a' && innerItem.FC1I3KT0NR7WDZVAE != 'e'">
                                                <div class="li left" v-for="(inItem,inIndex) in innerItem.associatedAttendee" :key="inIndex">
                                                    <div class="img"><img :src="'https://fs.31huiyi.com/' + inItem.avatar" alt=""></div>
                                                    <div class="t4">
                                                        <div class="name">{{inItem.stringField22}}</div>
                                                        <div class="t5">{{inItem.stringField18}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        new Vue({
            el: '#app',
            data: {
                start_time: '2024-09-07 00:00:00',
                end_time: '2024-09-07 23:59:59',
                lists: [],
                sel: null, 
                sele: 1,
            },
            created() {
                this.forumList(this.start_time, this.end_time)
            },
            methods: {
                changeTime(start, end, index) {
                    this.forumList(start, end)
                    this.sele = index
                    this.sel = null
                },

                forumList(start, end){
                    var that = this
                    axios({
                        method: 'post',
                        url: '<?=url('api/schedule/schedule_zs')?>',
                        data: {
                            start_time: start,
                            end_time: end,
                        }
                    }).then((res) => {
                        // console.log(res.data.data);
                        if (res.data.code == 200) {
                            let lists = res.data.data
                            lists.forEach((item, index) => {
                                let now = new Date().getTime()
                                let startTime = new Date(item.lt.forumtime[0]).getTime()
                                let endTime = new Date(item.lt.forumtime[1]).getTime()
                                item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                                item.lt.end_time = item.lt.forumtime[1].slice(10, 16)
                                if(now < startTime) {
                                    item.lt.forumtime_type = 1  // 未开始
                                }
                                if(now > startTime && now < endTime) {
                                    item.lt.forumtime_type = 2  // 进行中
                                }
                                if(now > endTime) {
                                    item.lt.forumtime_type = 3  // 已结束
                                }
                            })
                            this.lists = lists
                        }
                    })
                },

                select(index) {
                    if (this.sel === index) {
                        this.sel = null; // 再次点击当前项时收起
                    } else {
                        this.sel = index; // 点击其他项时当前项展开，其他项收起
                    }
                    // this.sel = index
                }
            }
        })
    </script>

    <!-- indexReport -->
    <div class="indexReport">
        <div class="wrap">
            <ul class="tabList left">
                <li class="on">
                    <div class="p">Annual Conference</div>
                    <a href="<?=url('en/news/news', ['pid' => 12, 'sort_id' => 15])?>" class="more">
                        <div class="right">
                            <p>More</p>
                            <div class="iconfont icon-arrow"></div>
                        </div>
                    </a>
                </li>
                <li>
                    <div class="p">Forum Bulletin</div>
                    <!-- <div class="p">研究报告</div> -->
                    <a href="<?=url('en/review/report', ['id' => 7])?>" class="more">
                        <div class="right">
                            <p>More</p>
                            <div class="iconfont icon-arrow"></div>
                        </div>
                    </a>
                </li>
            </ul>
        </div>

        <ul class="lists">
            <li>
                <div class="reportBox reportBox1 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($release)) { ?>
                        
                        <?php } else { foreach ($release as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>">
                                    <div class="label">Annual Conference</div>
                                    <div class="tit cut"><?=$v['title']?></div>
                                    <div class="txt cutTwo"><?=$v['desc']?></div>
                                    <div class="left more">
                                        <p>More</p>
                                        <div class="iconfont icon-jiantou"></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>

                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="label">浦江发布</div>
                                <div class="tit cut">《上海科技金融生态年度观察2022》报告全文</div>
                                <div class="txt cutTwo">《上海科技金融生态年度观察2022》秉承开放协同的理念，由上海市科学学研究所、上海市科技创业中心…</div>
                                <div class="left more">
                                    <p>More</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <!-- Add Arrows -->
                    <div class="arrow">
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </li>
            <li style="display: none;">
                <div class="reportBox reportBox2 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($report)) { ?>
                        
                        <?php } else { foreach ($report as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=$v['url']?>" download="<?=$v['url_name']?>">
                                    <div class="label">Forum Bulletin</div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <!-- <div class="txt cutTwo"><?=$v['title']?></div> -->
                                    <!-- <div class="left more">
                                        <p>More</p>
                                        <div class="iconfont icon-jiantou"></div>
                                    </div> -->
                                    <div class="down">Download</div>
                                </a>
                            </div>
                        <?php } } ?>

                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="label">研究报告</div>
                                <div class="tit cut">《上海科技金融生态年度观察2022》报告全文</div>
                                <div class="txt cutTwo">《上海科技金融生态年度观察2022》秉承开放协同的理念，由上海市科学学研究所、上海市科技创业中心…</div>
                                <div class="left more">
                                    <p>More</p>
                                    <div class="iconfont icon-jiantou"></div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <!-- Add Arrows -->
                    <div class="arrow">
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <script>
        $(".indexReport .tabList li").click(function(){
            $(this).addClass('on').siblings().removeClass('on');
            $(".indexReport .lists li").eq($(".indexReport .tabList li").index(this)).show().siblings().hide();
        });
    </script>

    <!-- indexActivity -->
    <div class="indexActivity">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">Conference Session Events</div>
                <a href="<?=url('en/news/news', ['pid' => 12, 'sort_id' => 16])?>" class="more right">
                    <p>More</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </div>

            <div class="activityBox swiper-container">
                <div class="swiper-wrapper">
                    <?php if (!count($activity)) { ?>
                    
                    <?php } else { foreach ($activity as $k => $v) { ?>
                        <div class="swiper-slide">
                            <a href="<?=url('en/news/detail', ['id' => $v['id']])?>" class="between">
                                <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                                <div class="cont">
                                    <div class="label">Conference Session Events</div>
                                    <div class="tit cutTwo"><?=$v['title']?></div>
                                    <div class="txt cutThree"><?=$v['desc']?></div>
                                    <!-- <div class="t left">
                                        <div class="iconfont icon-shijian"></div>
                                        <p>Time: <?=$v['time']?></p>
                                    </div>
                                    <div class="t left">
                                        <div class="iconfont icon-weizhi1"></div>
                                        <p>Address: <?=$v['place']?></p>
                                    </div> -->
                                </div>
                            </a>
                        </div>
                    <?php } } ?>

                    <!-- <div class="swiper-slide">
                        <a href="#" class="between">
                            <div class="img"><img src="<?=$dir_public?>/web/images/activityImg.jpg" alt=""></div>
                            <div class="cont">
                                <div class="label">会议季活动</div>
                                <div class="tit cutTwo">会议季│浦江创新论坛——2024科技创新智库国际研讨会开幕</div>
                                <div class="txt cutTwo">习近平指出，当前，世界百年未有之大变局加速演进，新一轮科技革命和产业变革深入发展…</div>
                                <div class="t left">
                                    <div class="iconfont icon-shijian"></div>
                                    <p>时间：2024.05.26-2024.05.30</p>
                                </div>
                                <div class="t left">
                                    <div class="iconfont icon-weizhi1"></div>
                                    <p>地点：中国•上海</p>
                                </div>
                            </div>
                        </a>
                    </div> -->
                </div>
                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>

    <!-- indexAd -->
    <!-- <div class="indexAd">
        <div class="wrap">
            <div class="t1">Sharing Innovation and Shaping the Future</div>
            <div class="t2">Towards an Open Environment for Scientific and Technological Innovation</div>
        </div>
    </div> -->

    <!-- indexVideo -->
    <div class="indexVideo">
        <div class="wrap">
            <div class="indexTitle between">
                <div class="title">Video Gallery</div>
                <ul class="tabList right">
                    <li class="on">The Nth Dimension of Parallel Future</li>
                    <li>Video Gallery</li>
                </ul>
            </div>
        </div>

        <ul class="lists">
            <li>
                <div class="videoBox videoBox1 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($videos)) { ?>
                        
                        <?php } else { foreach ($videos as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="cont">
                                        <div class="label">The Nth Dimension of Parallel Future</div>
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cut"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>

                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="img">
                                    <img src="<?=$dir_public?>/web/images/videoImg.jpg" alt="">
                                    <div class="iconfont icon-bofang"></div>
                                </div>
                                <div class="cont">
                                    <div class="label">平行未来的N次元</div>
                                    <div class="tit cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未</div>
                                    <div class="txt cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻"脑机结合"的未来</div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <div class="arrowBox between wrap">
                        <div class="arrow">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                        <a href="<?=url('en/news/news', ['pid' => 13, 'sort_id' => 19])?>" class="more right">
                            <p>More</p>
                            <div class="iconfont icon-arrow"></div>
                        </a>
                    </div>
                </div>
            </li>
            <li style="display: none;">
                <div class="videoBox videoBox2 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($video)) { ?>
                        
                        <?php } else { foreach ($video as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('en/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                                        <div class="iconfont icon-bofang"></div>
                                    </div>
                                    <div class="cont">
                                        <div class="label">Video Gallery</div>
                                        <div class="tit cut"><?=$v['title']?></div>
                                        <div class="txt cut"><?=$v['desc']?></div>
                                    </div>
                                </a>
                            </div>
                        <?php } } ?>
                        <!-- <div class="swiper-slide">
                            <a href="#">
                                <div class="img">
                                    <img src="<?=$dir_public?>/web/images/videoImg.jpg" alt="">
                                    <div class="iconfont icon-bofang"></div>
                                </div>
                                <div class="cont">
                                    <div class="label">视频会客厅</div>
                                    <div class="tit cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未</div>
                                    <div class="txt cut">平行未来的N次元 | 跟随交大吕宝粮教授探寻"脑机结合"的未来</div>
                                </div>
                            </a>
                        </div> -->
                    </div>
                    <div class="arrowBox between wrap">
                        <div class="arrow">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                        <a href="<?=url('en/news/news', ['pid' => 13, 'sort_id' => 21])?>" class="more right">
                            <p>More</p>
                            <div class="iconfont icon-arrow"></div>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <script>
        $(".indexVideo .tabList li").click(function(){
            $(this).addClass('on').siblings().removeClass('on')
            $(".indexVideo .lists li").eq($(".indexVideo .tabList li").index(this)).show().siblings().hide();
        });
    </script>

    <!-- indexPartner -->
    <div class="indexPartner">
        <div class="wrap">
            <?php if (!count($cooperatesort)) { ?>

            <?php } else { foreach ($cooperatesort as $k => $v) { ?>
                <div class="box between">
                    <div class="tit"><?=$v['name']?></div>
                    <ul class="left">
                        <?php if (!count($v['cooperate'])) { ?>

                        <?php } else { foreach ($v['cooperate'] as $kk => $vv) { ?>
                            <li>
                                <?php if ($vv['url']) { ?>
                                    <a href="<?=$vv['url']?>" target="_blank">
                                <?php } else { ?>
                                    <a href="javascrupt:;">
                                <?php } ?>
                                    <img src="<?=$dir_public . $vv['logo']?>" alt="">
                                </a>
                            </li>
                        <?php } } ?>
                    </ul>
                </div>
            <?php } } ?>

            <div class="box between">
                <div class="tit">Media Partners</div>
                <ul class="left">
                    <?php if (!count($medium)) { ?>

                    <?php } else { foreach ($medium as $k => $v) { ?>
                        <li>
                            <?php if ($v['url']) { ?>
                                <a href="<?=$v['url']?>" target="_blank">
                            <?php } else { ?>
                                <a href="javascrupt:;">
                            <?php } ?>
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                            </a>
                        </li>
                    <?php } } ?>
                </ul>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/swiper.min.css" />
    <script type="text/javascript" src="<?=$dir_public?>/web/js/swiper.min.js"></script>
    <script>
        var indexBanner = new Swiper('.indexBanner', {
            // loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            navigation: {
                nextEl: '.indexBanner .swiper-button-next',
                prevEl: '.indexBanner .swiper-button-prev',
            }
        });

        var newsBox = new Swiper('.newsBox', {
            loop: true,
            autoplay: {
                delay: 5000,
            },
            pagination: {
                el: '.swiper-pagination',
	            clickable :true,
            }
        });

        var reportBox1 = new Swiper(".reportBox1", {
            slidesPerView: "auto",
            // centeredSlides: true,
            spaceBetween: 36,
            slidesPerView: 3,
            observer: true, 
            observeParents: true,
            // loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            navigation: {
                nextEl: '.reportBox1 .swiper-button-next',
                prevEl: '.reportBox1 .swiper-button-prev',
            }
        });

        var reportBox2 = new Swiper(".reportBox2", {
            slidesPerView: "auto",
            // centeredSlides: true,
            spaceBetween: 36,
            slidesPerView: 3,
            observer: true, 
            observeParents: true,
            // loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            navigation: {
                nextEl: '.reportBox2 .swiper-button-next',
                prevEl: '.reportBox2 .swiper-button-prev',
            }
        });

        var activityBox = new Swiper('.activityBox', {
            watchSlidesProgress: true,
            slidesPerView: 'auto',
            centeredSlides: true,
            loop: true,
            loopedSlides: 5,
            autoplay: true,
            navigation: {
                nextEl: '.activityBox .swiper-button-next',
                prevEl: '.activityBox .swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable :true,
            },
            on: {
                progress: function(progress) {
                    for (i = 0; i < this.slides.length; i++) {
                        var slide = this.slides.eq(i);
                        var slideProgress = this.slides[i].progress;
                        modify = 1;
                        if (Math.abs(slideProgress) > 1) {
                            modify = (Math.abs(slideProgress) - 1) * 0.3 + 1;
                        }
                        translate = slideProgress * modify * 260 + 'px';
                        scale = 1 - Math.abs(slideProgress) / 5;
                        zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                        slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                        slide.css('zIndex', zIndex);
                        slide.css('opacity', 1);
                        if (Math.abs(slideProgress) > 3) {
                            slide.css('opacity', 0);
                        }
                    }
                },
                setTransition: function(transition) {
                    for (var i = 0; i < this.slides.length; i++) {
                        var slide = this.slides.eq(i)
                        slide.transition(transition);
                    }
                }
            }
        })

        var videoBox1 = new Swiper(".videoBox1", {
            // slidesPerView: "auto",
            // centeredSlides: true,
            spaceBetween: 50,
            slidesPerView: 2,
            observer: true, 
            observeParents: true,
            // loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            navigation: {
                nextEl: '.videoBox1 .swiper-button-next',
                prevEl: '.videoBox1 .swiper-button-prev',
            }
        });

        var videoBox2 = new Swiper(".videoBox2", {
            // slidesPerView: "auto",
            // centeredSlides: true,
            spaceBetween: 50,
            slidesPerView: 2,
            observer: true, 
            observeParents: true,
            // loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            navigation: {
                nextEl: '.videoBox2 .swiper-button-next',
                prevEl: '.videoBox2 .swiper-button-prev',
            }
        });
    </script>