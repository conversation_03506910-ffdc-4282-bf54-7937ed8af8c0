<?php
use think\Db;
use think\Session;
use think\Cookie;
use think\Cache;
use \think\Request;
// +----------------------------------------------------------------------
// | Thoperating_ipnkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件

class YunxinServer {

}

/** 
* 产生指定长度的随机字符串 
* 
* <AUTHOR>
* @param int $arg1 随机字符串长度 
* @return string
*/
function rand_str($lenth) {                                                  
	$str = '123456789abcdefhijkmnpqrstuvwxyz'; //32种不容易混淆的字符
	$strstr = '';
	for ($i = 0; $i < $lenth; $i++) {
		$strstr .= $str[mt_rand(0, strlen($str) - 1)];
	}
	return $strstr;
}

/** 
* 读取json文件数据，返回id=>name的hash数组 
* 
* <AUTHOR>
* @param string $arg1 键
* @param string $arg2 文件路径
* @return array
*/
function json($key, $path) {
	//json_decode()对 JSON 格式的字符串进行编码,并返回数组
	//file_get_contents()将整个文件读入一个字符串
	$json = json_decode(file_get_contents($path), true)[$key];	 
	$result = array();
	
	do {
		$jsons = array();
		foreach ($json as $v) {
			$result[$v['id']] = $v['name'];
			$jsons = isset($v['son']) ? array_merge($jsons, $v['son']) : $jsons;
		}
		$json = $jsons;
	} while ($json);
	
	return $result;
}

/**字符串整理格式
  *多余空格转成一个，多余换行变成一个*/
function text_clear($str) {
	$arr    = explode("\n", $str);
	$strstr = '';
	foreach($arr as $v) {
		$vv = preg_replace('/ +/', ' ', trim($v));
		$strstr .= $vv ? "{$vv}\n" : '';
	}
	return $strstr;
}

/*** 当前毫秒字符串 ***/
/*** 日志: 创建@兰波@20161212  ***/
/*** 用法一: microtime_str(); 产生16位的当前毫秒数, 类似: 1481545059803225 ***/
/*** 如果要把16位的毫秒字符串转换成时间，可以用: date('Y-m-d H:i:s',(int)substr($mstr, 0, 10)) ***/
function microtime_str() {
	list($usec, $sec) = explode(" ", microtime());
	$usec = 'a' . (string)$usec; //目的是确保$usec一定是字符串
	$usec = substr($usec, 3, -2);
	$sec = (string)$sec;
	return $sec . $usec;
}

/**字符串输出格式化
*每一个换行全部转成<p>标签*/
function text_print($str) {	
	$arr    = explode("\n", $str);
	$strstr = '';
	foreach($arr as $v) {
		$strstr .= "<p>" . $v . "</p>";
	}
	return $strstr;
}

function uid($uid = null, $expire = 0) {
    $uid_cookie   = 't__i__m__e';
	$key          = '9273.w!:)7908axon~~5240!';
	$admin_cookie = 'ak47e293yj';
	if ($uid == 'logout') {
		cookie($uid_cookie, null);
        return true;
	}
    if ($uid == 'is_admin') {
		return uid() == $admin_cookie ? true : false;
    }
    $iv_size    = mcrypt_get_iv_size(MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB);
    $iv         = mcrypt_create_iv($iv_size, MCRYPT_RAND);

	if ($uid == null) { //获取uid
		$uid_cookie = base64_decode(cookie($uid_cookie));
		if (empty($uid_cookie)) {
			return null;
		} else {
			$uid = mcrypt_decrypt(MCRYPT_RIJNDAEL_256, $key, $uid_cookie, MCRYPT_MODE_ECB, $iv);
			return trim($uid); //去掉多余的null;
		}
	} else {
		$uid = mcrypt_encrypt(MCRYPT_RIJNDAEL_256, $key, $uid, MCRYPT_MODE_ECB, $iv);
		$uid = base64_encode($uid);
		cookie($uid_cookie, $uid, $expire);
	}
}

// 这个函数 php 5.5以上就自带了
if (!function_exists('array_column')) {
	function array_column($arr, $key) {
		$_arr = array();
		foreach ($arr as $v) {
			$_arr[] = $v[$key];
		}
		return $_arr;
	}
}

// +---------------------------------------------------------------------------------------------------------------------
// | 生成订单号/随机数     生成订单号/随机数      生成订单号/随机数      生成订单号/随机数      生成订单号/随机数
// +---------------------------------------------------------------------------------------------------------------------

/** 功能：生成随机数并返回 **/
/** 作者：籣@20231124 **/
function getnoncestr() {
	$code = "";
	for ($i=0; $i < 10; $i++) { 
		$code .= mt_rand(1000, 10000);        //获取随机数
	}
	$nonceStrTemp = md5($code);
	$nonce_str = mb_substr($nonceStrTemp, 5,37);      //MD5加密后截取32位字符
	return $nonce_str;
}

/** 功能：生成22位订单号 **/
/** 作者：籣@20231124 **/
function build_order_no($type = null) {

	$code = date('YmdHis').substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);

	/** 查询数据库中是否已有此订单号 如已有择重新生成 **/

	if ($type == 1) {
		$is = Db::table('order')->where('code', $code)->find();
	} else {
		$is = null;
	}

	if ($is) {
		return build_order_no($type);
	} else {
		return $code;
	}

	//return date('YmdHis').substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
	//return substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 12);
}

/** 功能：微信支付 / 退款 签名 **/
/** 作者：@20231124 **/
function getsign($params) {
	
	/** 将参数数组按照参数名ASCII码从小到大排序 **/
	ksort($params);

	foreach ($params as $key => $item) {
		if (!empty($item)) {				 //剔除参数值为空的参数
			$newArr[] = $key.'='.$item;     // 整合新的参数数组
		}
	}

	$stringA		= implode("&", $newArr);									//使用 & 符号连接参数
	$stringSignTemp = $stringA."&key=".'90a5b55fbef26472eee71d37574049f5';		//拼接key key是在商户平台API安全里自己设置的
	$stringSignTemp = MD5($stringSignTemp);										//将字符串进行MD5加密
	$sign			= strtoupper($stringSignTemp);								 //将所有字符转换为大写

	return $sign;
}

// +---------------------------------------------------------------------------------------------------------------------
// | 权限验证     权限验证      权限验证      权限验证      权限验证      权限验证      权限验证      权限验证
// +---------------------------------------------------------------------------------------------------------------------

/** 功能：权限验证 **/
/** 作者：穠@20231124 **/
function purview() {

	if (Session::get('sdysy_type') == 100) {
		return 200;
	}

	/** 查询用户信息 **/
	$purview_id = Db::table('admin')->where('uid', Session::get('sdysy_uid'))->value('purview');

	/** 查询权限信息 **/
	$purview = Db::table('purview')->where('id', $purview_id)->value('purview');

	/** 获取 控制器/方法 名称 **/
	$request = request();
	$url_c	 = $request->controller();
	$url_m	 = $request->action();

	if (in_array($url_c, explode('!', $purview))) {
		return 200;
	}
	
	return 1;
}

// +---------------------------------------------------------------------------------------------------------------------
// | 新闻中心二级分类     新闻中心二级分类     新闻中心二级分类     新闻中心二级分类     新闻中心二级分类
// +---------------------------------------------------------------------------------------------------------------------

/** 功能：新闻中心二级分类 **/
/** 作者：@20240726 **/
function news_sort($language, $genre) {
	
	/** 查询宿舍信息 **/
	$sort1 = Db::table('news_sort')->order('sort, id')->where('pid', 0)->where('language', $language)->where('genre', $genre)->where('is_del', 1)->field('id,name')->select();

	/** 查询房间 **/
	$sort2 = Db::table('news_sort')->order('sort, id')->where('pid', '>', 0)->where('language', $language)->where('genre', $genre)->field('id,name,pid')->where('is_del', 1)->select();

	$sort = [];

	/** 重新封装房间信息 **/
	foreach ($sort2 as $k => $v) {
		if (!isset($sort[$v['pid']])) {
			$sort[$v['pid']] = [];
		}

		$sort[$v['pid']][] = $v;
	}

	/** 封装房间信息到宿舍下 **/
	foreach ($sort1 as $k => $v) {
		if (isset($sort[$v['id']])) {
			$sort1[$k]['list'] = $sort[$v['id']];
		} else {
			$sort1[$k]['list'] = [];
		}
	}

	return $sort1;
}
?>