<?php
use think\Route;
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

//Route::rule('/:en','en');    // 投诉与建议

//Route::rule('web_cn','web');

//Route::rule('web/cn/index/index','web/index/index');
//Route::rule('m/en/index/index','en/index/index');

//Route::rule('fbas2024','web2024/index/index');

$route_url = $_SERVER['REQUEST_URI'];

if ($route_url) {
	if (strlen($route_url) == substr_count($route_url, '/')) {
		$route_url = '/';
	}
}

// 简化的路由配置
// 根路径直接路由到web模块
Route::rule('/', 'web/index/index');
Route::rule('/index', 'web/index/index');
Route::rule('/index/index', 'web/index/index');

// web模块路由
Route::rule('web/:controller/:action', 'web/:controller/:action');
Route::rule('web/:controller', 'web/:controller/index');

// admin模块路由
Route::rule('/admin', 'admin/login/login');
Route::rule('admin/:controller/:action', 'admin/:controller/:action');
Route::rule('admin/:controller', 'admin/:controller/index');

// 其他模块路由
Route::rule('m/:controller/:action', 'm/:controller/:action');
Route::rule('m/:controller', 'm/:controller/index');

Route::rule('en/:controller/:action', 'en/:controller/:action');
Route::rule('en/:controller', 'en/:controller/index');

return [
    '__pattern__' => [
        'name' => '\w+',
    ],
];
