<?php
namespace app\api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Attendee extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240722 **/
	public function _initialize() {

    }

	public function show() {

		/** 对外接收参数 **/
		$id = input('id', '');			//id
		
		$row = Db::table('ceshi')->where('id', $id)->value('txt');

		$row = json_decode($row, true);

		dump($row);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 接收参会人信息     接收参会人信息     接收参会人信息     接收参会人信息     接收参会人信息     接收参会人信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会人信息 **/
	/** 作者：@20230808 **/
	public function user_add() {
		
		/** 对外接收参数 **/
		$row = input();			//接收数据

		//Db::table('ceshi')->insert(['txt' => json_encode($row)]);

		/** 嘉宾 **/
		$data[]	= 'FullName';					//中文姓名 Name
		$data[]	= 'StringField22';				//英文姓名 English Name
		$data[]	= 'Mobile';						//手机 Mobile
		$data[]	= 'Email';						//邮箱 Email
		$data[]	= 'Phone';						//电话 Tel
		$data[]	= 'Avatar';						//照片 Photo
		$data[]	= 'IdType';						//证件类型
		$data[] = 'IdNumber';					//证件号
		$data[]	= 'Country';					//国家/地区
		$data[]	= 'Province';					//省
		$data[]	= 'City';						//市
		$data[]	= 'Area';						//区
		$data[]	= 'Address';					//地址
		$data[]	= 'Position';					//职务  Title
		$data[]	= 'Company';					//机构/单位

		$data[]	= 'StringField71';				//国籍 Nationality
		$data[]	= 'StringField72';				//证件类型 ID type
		$data[]	= 'StringField73';				//证件号码 ID NO
		$data[]	= 'StringField55';				//性别 Gender
		$data[]	= 'StringField68';				//生日 Date of Birth
		$data[]	= 'StringField70';				//年龄 Age
		$data[]	= 'StringField59';				//当前居住地
		$data[]	= 'StringField27';				//Organization / Company
		$data[]	= 'StringField2';				//单位性质 Organization Properties
		$data[]	= 'StringField1';				//地址 Address
		$data[]	= 'StringField30';				//是否副局级及以上
		$data[]	= 'StringField31';				//行政级别描述
		$data[]	= 'StringField40';				//中文简历 Chinese Resume
		$data[]	= 'StringField52';				//英文简历 English Resume
		$data[]	= 'StringField5';				//演讲题目 Speech Topic
		$data[]	= 'TextField1';					//演讲摘要 Speech Abstract
		$data[]	= 'StringField42';				//PPT资料 Presentation Slides
		$data[]	= 'StringField28';				//VVIP
		$data[]	= 'StringField29';				//VIP
		$data[]	= 'StringField13';				//报告方式: Will the presentation be conducted via:
		$data[]	= 'StringField14';				//姓名 Name
		$data[]	= 'StringField15';				//机构/单位 Organization/Company
		$data[]	= 'AlternateEmail';				//邮箱 E-mail
		$data[]	= 'StringField44';				//手机 Mobile Phone
		$data[]	= 'StringField16';				//职务 Title

		$info	 = [];
		$infos	 = [];
		$addtime = date('Y-m-d H:i:s');		//时间

		//dump($row);

		/** 获取参会人列表 **/
		$info['AttendeeId']		= $row["AttendeeId"];					//参会人id
		$info['BventId']		= $row["BventId"];						//会议id
		$info['AttendeeTypeId'] = $row["AttendeeTypeId"];				//类别id
		$info['Status']			= $row["Status"];						//审核状态
		$info['UserId']			= $row['ContactInfo']['ContactId'];		//账号id
		$info['addtime']		= $addtime;								//时间

		$infos['AttendeeId']	 = $row["AttendeeId"];					//参会人id
		$infos['BventId']		 = $row["BventId"];						//会议id
		$infos['AttendeeTypeId'] = $row["AttendeeTypeId"];				//类别id
		$infos['Status']		 = $row["Status"];						//审核状态
		$infos['UserId']		 = $row['ContactInfo']['ContactId'];	//账号id
		$infos['addtime']		 = $addtime;							//时间

		/** 封装数据 **/
		foreach ($row['AttendeeFields'] as $k => $v) {
			if ($v['FieldName'] == 'Avatar') {
				if (isset($v['Value']) && isset($v['Value'][0]) && isset($v['Value'][0]['absoluteUrl'])) {
					$info[$v['FieldName']]	= $v['Value'][0]['absoluteUrl'];
					$infos[$v['FieldName']] = $v['ShowValue'];
				} else {
					$info[$v['FieldName']]	= '';
					$infos[$v['FieldName']] = '';
				}
			} else {
				if (in_array($v['FieldName'], $data)) {
					if (isset($v['FieldName']) && isset($v['Value'])) {
						$info[$v['FieldName']]	= $v['Value'];
						$infos[$v['FieldName']] = $v['ShowValue'];
					} else {
						$info[$v['FieldName']]	= '';
						$infos[$v['FieldName']] = '';
					}
				}
			}
		}

		/** 查询是否已有报名 **/
		$is = Db::table('forum_apply')->where('AttendeeId', $row['AttendeeId'])->where('BventId', $row['BventId'])->find();

		if ($is) {
			if ($info['Status'] != 'Registered') {
				Db::table('forum_apply')->where('AttendeeId', $row['AttendeeId'])->where('BventId', $row['BventId'])->delete();
				Db::table('forum_applys')->where('AttendeeId', $row['AttendeeId'])->where('BventId', $row['BventId'])->delete();
			} else {
				Db::table('forum_apply')->where('AttendeeId', $row['AttendeeId'])->where('BventId', $row['BventId'])->update($info);
				Db::table('forum_applys')->where('AttendeeId', $row['AttendeeId'])->where('BventId', $row['BventId'])->update($infos);
			}
		} else {
		
			if ($info['Status'] != 'Registered') {
				return json_encode(['Status' => 1, 'Msg' => '审核状态未通过']);
			}

			Db::table('forum_apply')->insert($info);
			Db::table('forum_applys')->insert($infos);
		}

		return json_encode(['Status' => 1, 'Msg' => '已接收']);
	}
}