<?php
namespace app\api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Users extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@******** **/
	public function _initialize() {

    }

	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
    public function ceshi() {

		/** 对外接收参数 **/
		$account_id     = input('account_id', '');						//账号id
		$token	        = input('token', '');							//token
		$type	        = input('type', 1);								//类型 1：中文 2：英文
		$bventId		= '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

		$account_id = '010809c4-b8b6-423c-9687-e20ed2e7b758';
		$token = '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
		
		/** 获取参会人id  列别id **/
		$userType = $this->user_typeid($account_id,$token);
		
		if (!$userType) {
			return json_encode(['code' => 1, 'msg' => '没找到']);
		}
		
		$id				= $userType['id'];								//参会人id
		$attendeeTypeId = $userType['attendeeTypeId'];					//列别id
		
		$url = 'https://gateway.pujiangforum.cn/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;

		//$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;


		/** 封装参数 **/
		$row['attendeeTypeId'] = $attendeeTypeId;

		$postdata = json_encode($row);

		$type_name[1] = 'zh-CN';
		$type_name[2] = 'en-US';

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token,'Lang:' . $type_name[$type],'X-Lang:' . $type_name[$type]
		);

		dump($http_header);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		dump($result);

		//dump($result[0]['detail']['relationBvents']);

		return 200;

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data_info, 'id' => $id]);
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取登录认证code     获取登录认证code     获取登录认证code     获取登录认证code     获取登录认证code
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
	public function user_code() {

		/** 对外接收参数 **/
		$account_id = input('account_id', '');		//账号id

		$bventId = '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

		$url = 'https://31api.31huiyi.com/op/security/usercode?loginUserId=' . $account_id;

		/** 封装参数 **/
		$row = [];

		$postdata = json_encode($row);

		/** 获取token **/
		$token = $this->token();

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();


		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		//curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		//curl_setopt ($ch, CURLOPT_POSTFIELDS, null);			//参数
		//curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取登录认证code所需参数     获取登录认证code所需参数     获取登录认证code所需参数     获取登录认证code所需参数
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
	public function user_code_id() {

		/** 对外接收参数 **/
		$mobile = input('mobile', '');		//账号id

		$bventId = '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

		$url = 'https://31api.31huiyi.com/op/userresource/AccountUser/v1/createForClient';

		/** 封装参数 **/
		$row['mobile'] = $mobile;

		$postdata = json_encode($row);

		/** 获取token **/
		$token = $this->token();

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();


		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		//curl_setopt ($ch, CURLOPT_POSTFIELDS, null);			//参数
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		dump($result);

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息
	// +---------------------------------------------------------------------------------------------------------------------
    
	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
    public function getUserInfo() {

		/** 对外接收参数 **/
		$account_id     = input('account_id', '');						//账号id
		$token	        = input('token', '');							//token
		$type	        = input('type', 1);								//类型 1：中文 2：英文
		$bventId		= '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id
		
		/** 获取参会人id  列别id **/
		$userType = $this->user_typeid($account_id,$token);
		
		if (!$userType) {
			return json_encode(['code' => 1, 'msg' => '没找到']);
		}
		
		$id				= $userType['id'];								//参会人id
		$attendeeTypeId = $userType['attendeeTypeId'];					//列别id
		
		$url = 'https://gateway.pujiangforum.cn/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;

		//$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;


		/** 封装参数 **/
		$row['attendeeTypeId'] = $attendeeTypeId;

		$postdata = json_encode($row);

		$type_name[1] = 'zh-CN';
		$type_name[2] = 'en-US';

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token,'Lang:' . $type_name[$type],'X-Lang:' . $type_name[$type]
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		//dump($result);

		/** 嘉宾 **/
		$data[]	= 'InviteFromUserId';				//邀约人
		$data[]	= 'AttendStatus';					//参会人入场状态
		$data[]	= 'TicketName';						//门票名称
		$data[]	= 'CheckinStatus';					//签到状态
		$data[]	= 'ReceptionStatus';				//接待状态
		$data[]	= 'PayStatus';						//支付状态
		$data[]	= 'DataSources';					//数据来源
		$data[]	= 'AuditStatus';					//审核状态
		$data[]	= 'AttendeeRole';					//参会人角色
		$data[]	= 'AvatarFaceSyncStatus';			//头像同步状态
		$data[]	= 'CheckinCode';					//签到码
		$data[]	= 'InvitationCode';					//邀约码
		$data[]	= 'InviteName';						//邀约人
		$data[]	= 'AttendedTime';					//参会人首次入场时间
		$data[]	= 'Separator';						//基本信息 Basic Information
		$data[]	= 'FullName';						//中文姓名 Name
		$data[]	= 'FirstName';						//名
		$data[]	= 'MiddleName';						//中间名
		$data[]	= 'LastName';						//姓
		$data[]	= 'StringField22';					//英文姓名 English Name
		$data[]	= 'Mobile';							//手机 Mobile
		$data[]	= 'StringField71';					//国籍 Nationality
		$data[]	= 'StringField49';					//Overseas
		$data[]	= 'IdType';							//证件类型
		$data[]	= 'IdNumber';						//证件
		$data[]	= 'StringField72';					//证件类型 ID type
		$data[]	= 'StringField73';					//证件号码 ID NO
		$data[]	= 'StringField55';					//性别 Gender
		$data[]	= 'StringField68';					//生日 Date of Birth
		$data[]	= 'StringField70';					//年龄 Age
		$data[]	= 'Province';						//省
		$data[]	= 'City';							//市
		$data[]	= 'Area';							//区
		$data[]	= 'Address';						//地址
		$data[]	= 'Country';						//国家/地区
		$data[]	= 'StringField59';					//当前居住地
		$data[]	= 'Avatar';							//照片 Photo
		$data[]	= 'StringField23';					//邀请渠道
		$data[]	= 'StringField19';					//请选择您有兴趣参与的论坛活动
		$data[]	= 'Separator';						//单位信息 Organization Information
		$data[]	= 'Company';						//机构/单位
		$data[]	= 'StringField27';					//Organization / Company
		$data[]	= 'StringField2';					//单位性质 Organization Properties
		$data[]	= 'StringField1';					//地址 Address
		$data[]	= 'Position';						//职务
		$data[]	= 'StringField18';					//Title
		$data[]	= 'StringField30';					//是否副局级及以上
		$data[]	= 'StringField31';					//行政级别描述
		$data[]	= 'Email';							//邮箱 Email
		$data[]	= 'Phone';							//电话 Tel
		$data[]	= 'Separator';						//资料信息
		$data[]	= 'StringField40';					//中文简历 Chinese Resume
		$data[]	= 'StringField52';					//英文简历 English Resume
		$data[]	= 'StringField5';					//演讲题目 Speech Topic
		$data[]	= 'TextField1';						//演讲摘要 Speech Abstract
		$data[]	= 'StringField53';					//备注信息 Remark
		$data[]	= 'Separator';						//个人属性 Personal Attributes
		$data[]	= 'StringField7';					//您是否有安排其他学术交流活动？是否需要秘书处协助联络？Do you have other academic events planned?
		$data[]	= 'StringField54';					//需要协助联络的事项 Matters necessitating coordination support
		$data[]	= 'StringField32';					//是否参加开幕式及主论坛？Attend the Opening Ceremony & Main Forum ?
		$data[]	= 'StringField35';					//是否愿意接收采访? Do you willing to give an interview?
		$data[]	= 'StringField36';					//意向采访嘉宾
		$data[]	= 'StringField33';					//是否参加晚宴？Attend the Welcome Banquet?
		$data[]	= 'StringField78';					//是否参加晚宴？Attend the Welcome Banquet?
		$data[]	= 'StringField57';					//是否参加浦江夜游 Attend Pujiang River Cruise？
		$data[]	= 'StringField81';					//浦江夜游 Pujiang River Cruise
		$data[]	= 'StringField79';					//浦江夜游 Pujiang River Cruise
		$data[]	= 'StringField58';					//是否有禁忌饮食 Any dietary restriction?
		$data[]	= 'StringField43';					//是否有联系人? Do you have a contact person?
		$data[]	= 'StringField13';					//报告方式: Will the presentation be conducted via:
		$data[]	= 'Separator';						//联系人信息 Contact Information
		$data[]	= 'StringField14';					//姓名 Name
		$data[]	= 'StringField15';					//机构/单位 Organization/Company
		$data[]	= 'AlternateEmail';					//邮箱 E-mail
		$data[]	= 'StringField44';					//手机 Mobile Phone
		$data[]	= 'StringField16';					//职务 Title
		$data[]	= 'Separator';						//交通信息
		$data[]	= 'ArrivalTime';					//抵沪日期 Date for Arriving in Shanghai
		$data[]	= 'ArrivalTransportNumber';			//抵沪航班/车次 Flight/Train Number for Arriving in Shanghai
		$data[]	= 'ReturnDepartureTime';			//离沪日期 Date for Leaving Shanghai
		$data[]	= 'ReturnTransportNumber';			//离沪航班/车次 Flight/Train Number for Leaving Shanghai
		$data[]	= 'ArriveAtTheStation';				//抵沪地点 Address for Arriving in Shanghai
		$data[]	= 'ReturnJourneyDepartureStation';	//离沪地点 Address for Leaving Shanghai
		$data[]	= 'Remark';							//备注 Remark
		$data[]	= 'Separator';						//酒店信息
		$data[]	= 'CheckinDate';					//酒店入住时间 Check-in Time
		$data[]	= 'CheckoutDate';					//酒店离开时间 Check-out Time
		$data[]	= 'RoomType';						//房型
		$data[]	= 'StringField46';					//入住酒店名称 Hotel
		$data[]	= 'StringField47';					//酒店付费类型
		$data[]	= 'StringField48';					//酒店房号
		$data[]	= 'HotelNote';						//入住备注
		$data[]	= 'StringField77';					//座位号
		$data[]	= 'StringField45';					//座位图
		$data[]	= 'StringField80';					//魏波导出
		$data[]	= 'StringField50';					//导出批次

		$data_info = [];

		$rc = [];

		/** 封装数据 **/
		foreach ($result[0]['detail']['attendeeFieldValues'] as $k => $v) {
			//dump($v);
			if (isset($v['fieldName']) && in_array($v['fieldName'], $data)) {
				if ($v['fieldName'] == 'Avatar') {
					if (isset($v['value']) && isset($v['value'][0]) && isset($v['value'][0]['absoluteUrl'])) {
						$data_info[$v['fieldName']]['value']	  = $v['value'][0]['absoluteUrl'];
						$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
					} else {
						$data_info[$v['fieldName']]['value']	  = '';
						$data_info[$v['fieldName']]['show_value'] = '';
					}
				} else if ($v['fieldName'] == 'StringField19') {
					foreach ($v['fieldsOptions'] as $kk => $vv) {
						$rc[$vv['code']] = [];
						foreach ($vv['childrens'] as $kkk => $vvv) {
							$rc[$vv['code']][$vvv['code']] = $vvv;
						}
					}

					$data_info[$v['fieldName']]['value']	  = $v['value'];
					$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
				} else {
					if (in_array($v['fieldName'], $data)) {
						if (isset($v['fieldName']) && isset($v['value'])) {
							$data_info[$v['fieldName']]['value']	  = $v['value'];
							$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
						} else {
							$data_info[$v['fieldName']]['value']	  = '';
							$data_info[$v['fieldName']]['show_value'] = '';
						}
					}
				}
			}
		}

		/*foreach ($data_info as $k => $v) {

			if ($k == 'StringField19') {

				$StringField19 = $v['value'];
				$StringField19 = str_replace('[', '', $StringField19);
				$StringField19 = str_replace(']', '', $StringField19);

				foreach (explode(",", $StringField19) as $kk => $vv) {
					$rc_keys = explode("/", $vv);
					$rc_keys[0] = str_replace('"', '', $rc_keys[0]);
					$rc_keys[1] = str_replace('"', '', $rc_keys[1]);


					$i = $rc_keys[0] - 1;

					if (isset($rc_keys[0]) && isset($rc_keys[1]) && isset($rc[$rc_keys[0]][$rc_keys[1]])) {
						$data_info[$k]['rc'][$i][] = $rc[$rc_keys[0]][$rc_keys[1]];
					} else {
						$data_info[$k]['rc'][$i][] = [];
					}
				}
			}
		}*/

		$rc = $result[0]['detail']['relationBvents'];

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data_info, 'id' => $id, 'rc' => $rc]);
    }
    
	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
	public function user_typeid($account_id,$token) {

		/** 对外接收参数 **/
		$bventId    = '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

		$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/GetPersonalAttendeesPages/' . $account_id;

		/** 封装参数 **/
		$row['pageNo']		 = 1;
		$row['pageSize']	 = 10;
		$row['pageSizeOpts'] = [10,20,50,100];
		$row['totalCount']	 = 1;
		$row['totalPage']	 = 1;

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if (isset($result['list']) && isset($result['list'][0]) && isset($result['list'][0]['id'])) {
		    return $result['list'][0];
		} else {
		    return null;
		}
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会用户信息 **/
	/** 作者：@******** **/
	public function user() {

		/** 对外接收参数 **/
		$account_id = input('account_id', '');						//账号id
		$token	    = input('token', '');							//token
		$bventId    = '********-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

// 		$account_id		= '010809c4-b8b6-423c-9687-e20ed2e7b758';
// 		$token	= '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

		$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/GetPersonalAttendeesPages/' . $account_id;

		/** 封装参数 **/
		$row['pageNo']		 = 1;
		$row['pageSize']	 = 10;
		$row['pageSizeOpts'] = [10,20,50,100];
		$row['totalCount']	 = 1;
		$row['totalPage']	 = 1;

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if (isset($result['list']) && isset($result['list'][0]) && isset($result['list'][0]['id'])) {
			return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result['list'][0]]);
		}

		return json_encode(['code' => 1, 'msg' => '查询失败', 'data' => $result]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会信息 **/
	/** 作者：@******** **/
	public function participation() {

		/** 对外接收参数 **/
		$id				= input('id', '');												//参会人id
		$token			= input('token', '');											//token
		$attendeeTypeId = input('attendeeTypeId', '');									//列别id
		$bventId		= '********-f0b5-6a75-d4d5-08dc731b5d6e';						//会议id
		//$attendeeTypeId = '28330000-1b2e-bacb-8af5-08dbab8bfb8b';						//列别id

		//$id				= '10f00000-c716-76bb-d3aa-08dcc0f20cc3';
		//$attendeeTypeId = 'efc90000-4333-96c2-3fb2-08dcad1bf0e7';
		//$token	= '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';


		$url = 'https://gateway.pujiangforum.cn/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;

		//$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;


		/** 封装参数 **/
		$row['attendeeTypeId'] = $attendeeTypeId;

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		//dump($result);

		/** 嘉宾 **/
		$data[]	= 'InviteFromUserId';				//邀约人
		$data[]	= 'AttendStatus';					//参会人入场状态
		$data[]	= 'TicketName';						//门票名称
		$data[]	= 'CheckinStatus';					//签到状态
		$data[]	= 'ReceptionStatus';				//接待状态
		$data[]	= 'PayStatus';						//支付状态
		$data[]	= 'DataSources';					//数据来源
		$data[]	= 'AuditStatus';					//审核状态
		$data[]	= 'AttendeeRole';					//参会人角色
		$data[]	= 'AvatarFaceSyncStatus';			//头像同步状态
		$data[]	= 'CheckinCode';					//签到码
		$data[]	= 'InvitationCode';					//邀约码
		$data[]	= 'InviteName';						//邀约人
		$data[]	= 'AttendedTime';					//参会人首次入场时间
		$data[]	= 'Separator';						//基本信息 Basic Information
		$data[]	= 'FullName';						//中文姓名 Name
		$data[]	= 'FirstName';						//名
		$data[]	= 'MiddleName';						//中间名
		$data[]	= 'LastName';						//姓
		$data[]	= 'StringField22';					//英文姓名 English Name
		$data[]	= 'Mobile';							//手机 Mobile
		$data[]	= 'StringField71';					//国籍 Nationality
		$data[]	= 'StringField49';					//Overseas
		$data[]	= 'IdType';							//证件类型
		$data[]	= 'IdNumber';						//证件
		$data[]	= 'StringField72';					//证件类型 ID type
		$data[]	= 'StringField73';					//证件号码 ID NO
		$data[]	= 'StringField55';					//性别 Gender
		$data[]	= 'StringField68';					//生日 Date of Birth
		$data[]	= 'StringField70';					//年龄 Age
		$data[]	= 'Province';						//省
		$data[]	= 'City';							//市
		$data[]	= 'Area';							//区
		$data[]	= 'Address';						//地址
		$data[]	= 'Country';						//国家/地区
		$data[]	= 'StringField59';					//当前居住地
		$data[]	= 'Avatar';							//照片 Photo
		$data[]	= 'StringField23';					//邀请渠道
		$data[]	= 'StringField19';					//请选择您有兴趣参与的论坛活动
		$data[]	= 'Separator';						//单位信息 Organization Information
		$data[]	= 'Company';						//机构/单位
		$data[]	= 'StringField27';					//Organization / Company
		$data[]	= 'StringField2';					//单位性质 Organization Properties
		$data[]	= 'StringField1';					//地址 Address
		$data[]	= 'Position';						//职务
		$data[]	= 'StringField18';					//Title
		$data[]	= 'StringField30';					//是否副局级及以上
		$data[]	= 'StringField31';					//行政级别描述
		$data[]	= 'Email';							//邮箱 Email
		$data[]	= 'Phone';							//电话 Tel
		$data[]	= 'Separator';						//资料信息
		$data[]	= 'StringField40';					//中文简历 Chinese Resume
		$data[]	= 'StringField52';					//英文简历 English Resume
		$data[]	= 'StringField5';					//演讲题目 Speech Topic
		$data[]	= 'TextField1';						//演讲摘要 Speech Abstract
		$data[]	= 'StringField53';					//备注信息 Remark
		$data[]	= 'Separator';						//个人属性 Personal Attributes
		$data[]	= 'StringField7';					//您是否有安排其他学术交流活动？是否需要秘书处协助联络？Do you have other academic events planned?
		$data[]	= 'StringField54';					//需要协助联络的事项 Matters necessitating coordination support
		$data[]	= 'StringField32';					//是否参加开幕式及主论坛？Attend the Opening Ceremony & Main Forum ?
		$data[]	= 'StringField35';					//是否愿意接收采访? Do you willing to give an interview?
		$data[]	= 'StringField36';					//意向采访嘉宾
		$data[]	= 'StringField33';					//是否参加晚宴？Attend the Welcome Banquet?
		$data[]	= 'StringField57';					//是否参加浦江夜游 Attend Pujiang River Cruise？
		$data[]	= 'StringField81';					//浦江夜游 Pujiang River Cruise
		$data[]	= 'StringField58';					//是否有禁忌饮食 Any dietary restriction?
		$data[]	= 'StringField43';					//是否有联系人? Do you have a contact person?
		$data[]	= 'StringField13';					//报告方式: Will the presentation be conducted via:
		$data[]	= 'Separator';						//联系人信息 Contact Information
		$data[]	= 'StringField14';					//姓名 Name
		$data[]	= 'StringField15';					//机构/单位 Organization/Company
		$data[]	= 'AlternateEmail';					//邮箱 E-mail
		$data[]	= 'StringField44';					//手机 Mobile Phone
		$data[]	= 'StringField16';					//职务 Title
		$data[]	= 'Separator';						//交通信息
		$data[]	= 'ArrivalTime';					//抵沪日期 Date for Arriving in Shanghai
		$data[]	= 'ArrivalTransportNumber';			//抵沪航班/车次 Flight/Train Number for Arriving in Shanghai
		$data[]	= 'ReturnDepartureTime';			//离沪日期 Date for Leaving Shanghai
		$data[]	= 'ReturnTransportNumber';			//离沪航班/车次 Flight/Train Number for Leaving Shanghai
		$data[]	= 'ArriveAtTheStation';				//抵沪地点 Address for Arriving in Shanghai
		$data[]	= 'ReturnJourneyDepartureStation';	//离沪地点 Address for Leaving Shanghai
		$data[]	= 'Remark';							//备注 Remark
		$data[]	= 'Separator';						//酒店信息
		$data[]	= 'CheckinDate';					//酒店入住时间 Check-in Time
		$data[]	= 'CheckoutDate';					//酒店离开时间 Check-out Time
		$data[]	= 'RoomType';						//房型
		$data[]	= 'StringField46';					//入住酒店名称 Hotel
		$data[]	= 'StringField47';					//酒店付费类型
		$data[]	= 'StringField48';					//酒店房号
		$data[]	= 'HotelNote';						//入住备注
		$data[]	= 'StringField77';					//座位号
		$data[]	= 'StringField45';					//座位图
		$data[]	= 'StringField80';					//魏波导出
		$data[]	= 'StringField50';					//导出批次

		$data_info = [];

		$rc = [];

		/** 封装数据 **/
		foreach ($result[0]['detail']['attendeeFieldValues'] as $k => $v) {
			//dump($v);
			if (isset($v['fieldName']) && in_array($v['fieldName'], $data)) {
				if ($v['fieldName'] == 'Avatar') {
					if (isset($v['value']) && isset($v['value'][0]) && isset($v['value'][0]['absoluteUrl'])) {
						$data_info[$v['fieldName']]['value']	  = $v['value'][0]['absoluteUrl'];
						$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
					} else {
						$data_info[$v['fieldName']]['value']	  = '';
						$data_info[$v['fieldName']]['show_value'] = '';
					}
				} else if ($v['fieldName'] == 'StringField19') {
					foreach ($v['fieldsOptions'] as $kk => $vv) {
						$rc[$vv['code']] = [];
						foreach ($vv['childrens'] as $kkk => $vvv) {
							$rc[$vv['code']][$vvv['code']] = $vvv;
						}
					}

					$data_info[$v['fieldName']]['value']	  = $v['value'];
					$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
				} else {
					if (in_array($v['fieldName'], $data)) {
						if (isset($v['fieldName']) && isset($v['value'])) {
							$data_info[$v['fieldName']]['value']	  = $v['value'];
							$data_info[$v['fieldName']]['show_value'] = $v['showValue'];
						} else {
							$data_info[$v['fieldName']]['value']	  = '';
							$data_info[$v['fieldName']]['show_value'] = '';
						}
					}
				}
			}
		}

		foreach ($data_info as $k => $v) {

			if ($k == 'StringField19') {

				$StringField19 = $v['value'];
				$StringField19 = str_replace('[', '', $StringField19);
				$StringField19 = str_replace(']', '', $StringField19);

				foreach (explode(",", $StringField19) as $kk => $vv) {
					$rc_keys = explode("/", $vv);
					$rc_keys[0] = str_replace('"', '', $rc_keys[0]);
					$rc_keys[1] = str_replace('"', '', $rc_keys[1]);


					$i = $rc_keys[0] - 1;

					if (isset($rc_keys[0]) && isset($rc_keys[1]) && isset($rc[$rc_keys[0]][$rc_keys[1]])) {
						$data_info[$k]['rc'][$i][] = $rc[$rc_keys[0]][$rc_keys[1]];
					} else {
						$data_info[$k]['rc'][$i][] = [];
					}
				}
			}
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data_info]);
	}

	

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取token     获取token     获取token     获取token     获取token     获取token     获取token     获取token
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取 token **/
	/** 作者：穠@20240225 **/
	public function token() {

		if (Cache::get('31_access_token')) {
			return Cache::get('31_access_token');
		}

		$url = 'https://oauth.31huiyi.com' . '/connect/token';
		
		/** 封装参数 **/
		$row['grant_type'] = 'custom_user_code';					//授权类型(默认值：custom_user_code)
		$row['client_id']  = 'openapi';								//客户端（默认值：openapi）
		$row['scope']	   = 'offline_access OpenAppGateway';		//授权范围（默认值：offline_access OpenAppGateway）	
		$row['method']	   = 'client_secret';						//自定义登录方式（默认值：client_secret）
		$row['appKey']	   = '9021';								//颁发的appkey
		$row['appSecret']  = 'iYfZWxNyRin3rA4MZkiTsXFBPJ5HHpzs';	//颁发的appsecret
		//$row['Key']		   = '7003';								//

		$postdata = json_encode($row);

		$keydata = 'grant_type='.$row['grant_type'].'&client_id='.$row['client_id'].'&scope='.$row['scope'].'&method='.$row['method'].'&appKey='.$row['appKey'].'&appSecret='.$row['appSecret'];

		$http_header = array(
			'Content-type:application/x-www-form-urlencoded'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $keydata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);
		
		if (isset($result['access_token'])) {
			Cache::set('31_access_token', $result['access_token'], 260);

			return Cache::get('31_access_token');
		} else {
			return false;
		}
	}
}