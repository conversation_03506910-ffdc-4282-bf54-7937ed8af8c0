<?php
namespace app\api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Index extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240722 **/
	public function _initialize() {

    }
    
    /** 功能：新闻列表 **/
	/** 作者：@20230825 **/
	public function news() {

		/** 对外接收参数 **/
		$language = input('language', 1);			//语言 1：中文 2：英文

		/** 查询新闻信息 **/
		$row['pic'] = Db::table('news')->order('release_time desc')->limit(4)->where('language', $language)->where('genre', 1)->where('is_del', 1)->select();
		
		$row['list'] = Db::table('news')->order('release_time desc')->limit(5,2)->where('language', $language)->where('genre', 1)->where('is_del', 1)->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $row]);
	}
	
	/** 功能：轮播图 **/
	/** 作者：@20240820 **/
	public function banner() {

		/** 对外接收参数 **/
		$language = input('language', 1);			//语言 1：中文 2：英文
		
		/** 查询轮播图 **/
		$data = Db::table('banner')->order('sort')->where('language', $language)->where('genre', 3)->where('is_del', 1)->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：图片直播 **/
	/** 作者：@20230825 **/
	public function live_photo() {

		/** 对外接收参数 **/
		$language = input('language', 1);			//语言 1：中文 2：英文

		/** 查询图片直播 **/
		$data = Db::table('h5_broadcast_picture')->order('sort')->where('language', $language)->where('genre', 3)->where('is_del', 1)->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}
}