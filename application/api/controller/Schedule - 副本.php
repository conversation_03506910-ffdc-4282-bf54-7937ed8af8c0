<?php
namespace app\api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Schedule extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240801 **/
	public function _initialize() {

    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试     测试
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表 **/
	/** 作者：@20240801 **/
	public function schedule_time() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		$tablePkey = 'FT1I2TCF21OPJVVF';	//测试环境
		//$tablePkey = 'FT1I2VP9E4H26VVU';	//生产环境

		if (!$rangeStart) {
			$rangeStart = '2024-07-30 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-07-30 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;
		
		/** 测试环境 **/
		$url = 'https://test-gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		/** 生产环境 **/
		//$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取日程日期列表（测试环境）     获取日程日期列表（测试环境）     获取日程日期列表（测试环境）
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表（测试环境） **/
	/** 作者：@20240801 **/
	public function schedule() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		$tablePkey = 'FT1I2TCF21OPJVVF';	//测试环境
		//$tablePkey = 'FT1I2VP9E4H26VVU';	//生产环境
		
		if (!$rangeStart) {
			$rangeStart = '2024-07-30 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-07-30 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;
		
		/** 测试环境 **/
		$url = 'https://test-gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		/** 生产环境 **/
		//$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if ($result['businessCode'] != 0) {
			return json_encode(['code' => 1, 'msg' => $result['businessMessage'], 'data' => $result]);
		}

		if (!count($result['returnObj'])) {
			return json_encode(['code' => 1, 'msg' => '没有论坛日程信息', 'data' => $result]);
		}

		/** 单位类别中文 **/
		$dwmc_zh['a'] = '承办单位';
		$dwmc_zh['b'] = '协办单位';
		$dwmc_zh['c'] = '支持单位';

		/** 单位类别英文 **/
		$dwmc_en['a'] = 'Organizer';
		$dwmc_en['b'] = 'Co-Organizer';
		$dwmc_en['c'] = 'Supporter';

		$data = [];
		
		/** 循环数据重新封装 **/
		foreach ($result['returnObj'] as $k => $v) {

			$data[$k]		= [];
			$data[$k]['lt'] = [];	//论坛内容
			$data[$k]['dw'] = [];	//承办单位
			$data[$k]['yc'] = [];	//议程（时段）

			/** 封装论坛内容 **/
			if (count($v['forumContent'])) {
				$data[$k]['lt']['attendeePerResp']		= $v['forumContent']['attendeePerResp'];
				$data[$k]['lt']['auditStatus']			= $v['forumContent']['auditStatus'];
				$data[$k]['lt']['auditStatusName']		= $v['forumContent']['auditStatusName'];
				$data[$k]['lt']['createdDate']			= $v['forumContent']['createdDate'];
				$data[$k]['lt']['formNumber']			= $v['forumContent']['formNumber'];
				$data[$k]['lt']['latestUpdatedDate']	= $v['forumContent']['latestUpdatedDate'];
				$data[$k]['lt']['pkey']					= $v['forumContent']['pkey'];
				$data[$k]['lt']['staging']				= $v['forumContent']['staging'];
				
				/** 封装议程信息 **/
				foreach ($v['forumContent']['contents'] as $kk => $vv) {
					$data[$k]['lt'][$vv['fieldCode']] = $vv['fieldValue'];
				}
			}

			/** 封装承办单位 **/
			if (count($v['unitContent'])) {
				foreach ($v['unitContent'] as $kk => $vv) {
					$data[$k]['dw'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['dw'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['dw'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['dw'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['dw'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['dw'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['dw'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['dw'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['dw'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];

						if ($vvv['fieldCode'] == 'unitType') {
							$data[$k]['dw'][$kk]['unitType_zh_name'] = $dwmc_zh[$vvv['fieldValue']];
							$data[$k]['dw'][$kk]['unitType_en_name'] = $dwmc_en[$vvv['fieldValue']];
						}
					}
				}
			}

			/** 封装议程（时段） **/
			if (count($v['agendaContent'])) {
				foreach ($v['agendaContent'] as $kk => $vv) {
					$data[$k]['yc'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['yc'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['yc'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['yc'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['yc'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['yc'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['yc'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['yc'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['yc'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];

						if ($vvv['fieldCode'] == 'agendaTime') {
							if (isset($vvv['fieldValue'][0]) && isset($vvv['fieldValue'][1])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0] . ',' . $vvv['fieldValue'][1];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5) . ' - ' . substr($vvv['fieldValue'][1], 11, 5);
							} else if (isset($vvv['fieldValue'][0])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5);
							} else {
								$data[$k]['yc'][$kk]['duration_time'] = '';

								$data[$k]['yc'][$kk]['duration_sd'] = '';
							}
						}

						/*if ($vvv['fieldCode'] == 'associatedAttendee') {
							if (isset($data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'])) {
								$data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'] = 'https://test-fs.31huiyi.com/' . $data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'];
							}
						}*/
					}
				}
			}
		}
		
		/** 论坛地点中文 **/
		$area_zh['a'] = '东郊宾馆·紫金厅';
		$area_zh['b'] = '东郊宾馆·锦绣厅';
		$area_zh['d'] = '张江科学会堂张江厅';
		$area_zh['e'] = '张江科学会堂张江厅A';
		$area_zh['f'] = '张江科学会堂张江厅B';
		$area_zh['g'] = '张江科学会堂科创厅';
		$area_zh['h'] = '张江科学会堂 海科厅中心舞台';
		$area_zh['i'] = '张江科学会堂203会议室';
		$area_zh['j'] = '张江科学会堂303会议室';
		$area_zh['k'] = '张江科学会堂304会议室';
		$area_zh['l'] = '张江科学会堂305会议室';
		$area_zh['m'] = '张江科学会堂402会议室';
		$area_zh['n'] = '张江科学会堂403会议室';
		$area_zh['o'] = '张江科学会堂404会议室';
		$area_zh['p'] = '张江科学会堂405会议室';
		$area_zh['q'] = '上海远洋宾馆';
		$area_zh['r'] = '大零号湾';
		$area_zh['s'] = '外场';
		$area_zh['t'] = '线上';
		$area_zh['u'] = '国创中心总部';
		$area_zh['v'] = '松江区';

		/** 论坛地点英文 **/
		$area_en['a'] = 'Zijin Hall, Convention Center, Dongjiao Hotel';
		$area_en['b'] = 'Jinxiu Hall, Convention Center, Dongjiao Hotel';
		$area_en['c'] = 'Zhangjiang Hall, 2F, ZhangJiang Science Hall';
		$area_en['d'] = 'Zhangjiang Hall A, 2F, ZhangJiang Science Hall';
		$area_en['e'] = 'Zhangjiang Hall B, 2F, ZhangJiang Science Hall';
		$area_en['f'] = 'Inspire Hall, 2F, ZhangJiang Science Hall';
		$area_en['g'] = 'Haike Hall, 1F, ZhangJiang Science Hall';
		$area_en['h'] = 'Conference Room 203, ZhangJiang Science Hall';
		$area_en['j'] = 'Conference Room 303, ZhangJiang Science Hall';
		$area_en['k'] = 'Conference Room 304, ZhangJiang Science Hall';
		$area_en['l'] = 'Conference Room 305, ZhangJiang Science Hall';
		$area_en['m'] = 'Conference Room 402, ZhangJiang Science Hall';
		$area_en['n'] = 'Conference Room 403, ZhangJiang Science Hall';
		$area_en['o'] = 'Conference Room 404, ZhangJiang Science Hall';
		$area_en['p'] = 'Conference Room 405, ZhangJiang Science Hall';
		$area_en['q'] = 'Ocean Hotel Shanghai';
		$area_en['r'] = 'GREATER NEO BAY';
		$area_en['s'] = 'Off-site';
		$area_en['t'] = 'Online';
		$area_en['u'] = 'NATIONAL INNOVATION CENTER par EXCELLENCE (Shanghai)';
		$area_en['v'] = 'Songjiang District';

		//return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);

		/** 重新封装数据 **/
		foreach ($data as $k => $v) {

			$dw_data = [];	//单位重组

			/** 循环议程（时段） **/
			foreach ($v['dw'] as $kk => $vv) {
				if (isset($vv['unitType']) && $vv['unitType']) {
					if (!isset($dw_data[$vv['unitType']])) {
						$dw_data[$vv['unitType']] = [];
					}

					$dw_data[$vv['unitType']][] = $vv;
				}
			}

			$data[$k]['dw_data'] = $dw_data;
			
			$yc_data = [];	//第一次重组 以时间为键
			$yc_row	 = [];	//第二次重组 自增键值
			
			/** 循环议程（时段） **/
			foreach ($v['yc'] as $kk => $vv) {

				if (!isset($vv['duration_time'])) {
					$vv['duration_time'] = 'cs33';
				}

				/** 以时段为 键 设置数组结构 **/
				if (!isset($yc_data[$vv['duration_time']])) {

					$yc_data[$vv['duration_time']] = [];
					
					/** 循环信息封入新的数组 **/
					foreach ($vv as $kkk => $vvv) {
						if ($kkk != 'associatedAttendee') {
							$yc_data[$vv['duration_time']][$kkk] = $vvv;
						} else {
							$yc_data[$vv['duration_time']][$kkk][] = $vvv;
						}
					}
				} else {
					$yc_data[$vv['duration_time']]['associatedAttendee'][] = $vv['associatedAttendee'];
				}
			}

			foreach ($yc_data as $kk => $vv) {
				$yc_row[] = $vv;
			}
			
			$data[$k]['yc_row'] = $yc_row;

			if (isset($data[$k]['lt']['FC1I3CSLNBVPJVPD']) && isset($area_zh[$data[$k]['lt']['FC1I3CSLNBVPJVPD']])) {
				$data[$k]['lt']['FC1I3CSLNBVPJVPD_name'] = $area_zh[$data[$k]['lt']['FC1I3CSLNBVPJVPD']];
			}

			if (isset($data[$k]['lt']['FC1I3CSLNCVPJVWD']) && isset($area_en[$data[$k]['lt']['FC1I3CSLNCVPJVWD']])) {
				$data[$k]['lt']['FC1I3CSLNCVPJVWD_name'] = $area_en[$data[$k]['lt']['FC1I3CSLNCVPJVWD']];
			}

			unset($data[$k]['yc']);
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取日程日期列表（正式环境）     获取日程日期列表（正式环境）     获取日程日期列表（正式环境）
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取日程日期列表（正式环境） **/
	/** 作者：@20240801 **/
	public function schedule_zs() {

		/** 获取参数 **/
		$pageIndex	 = input('page', 1);			//第几页
		$pageSize	 = input('num', 100);			//每页数量
		$auditStatus = input('status', -2);			//状态：-1 审核通过的，-2全部
		$rangeStart	 = input('start_time', '');		//开始时间
		$rangeEnd	 = input('end_time', '');		//结束时间

		//$tablePkey = 'FT1I2TCF21OPJVVF';	//测试环境
		//$tablePkey = 'FT1I2VP9E4H26VVU';	//生产环境
		$tablePkey = 'FT1I196B7RRWBVVEF';	//生产环境
		
		if (!$rangeStart) {
			$rangeStart = '2024-09-07 00:00:00';
		}

		if (!$rangeEnd) {
			$rangeEnd = '2024-09-07 23:59:59';
		}

		/** 封装参数 **/
		$row['tablePkey']	= $tablePkey;
		$row['pageIndex']	= $pageIndex;
		$row['pageSize']	= $pageSize;
		$row['auditStatus'] = $auditStatus;

		$rangeFields[0]['fieldType']	= 'dateTime';
		$rangeFields[0]['fieldCode']	= 'forumtime';
		$rangeFields[0]['rangeStart']	= $rangeStart;
		$rangeFields[0]['rangeEnd']		= $rangeEnd;

		$row['rangeFields'] = $rangeFields;
		
		/** 测试环境 **/
		//$url = 'https://test-gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		/** 生产环境 **/
		$url = 'https://gateway.31huiyi.com/api/SEARCHAPI/consumer/form/center/tableView/fetchForumRecord';

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json'
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		//return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $result]);

		if ($result['businessCode'] != 0) {
			return json_encode(['code' => 1, 'msg' => $result['businessMessage'], 'data' => $result]);
		}

		if (!count($result['returnObj'])) {
			return json_encode(['code' => 1, 'msg' => '没有论坛日程信息', 'data' => $result]);
		}

		$data = [];
		
		/** 循环数据重新封装 **/
		foreach ($result['returnObj'] as $k => $v) {

			$data[$k]		= [];
			$data[$k]['lt'] = [];	//论坛内容
			$data[$k]['dw'] = [];	//承办单位
			$data[$k]['yc'] = [];	//议程（时段）

			/** 封装论坛内容 **/
			if (count($v['forumContent'])) {
				$data[$k]['lt']['attendeePerResp']		= $v['forumContent']['attendeePerResp'];
				$data[$k]['lt']['auditStatus']			= $v['forumContent']['auditStatus'];
				$data[$k]['lt']['auditStatusName']		= $v['forumContent']['auditStatusName'];
				$data[$k]['lt']['createdDate']			= $v['forumContent']['createdDate'];
				$data[$k]['lt']['formNumber']			= $v['forumContent']['formNumber'];
				$data[$k]['lt']['latestUpdatedDate']	= $v['forumContent']['latestUpdatedDate'];
				$data[$k]['lt']['pkey']					= $v['forumContent']['pkey'];
				$data[$k]['lt']['staging']				= $v['forumContent']['staging'];
				
				/** 封装议程信息 **/
				foreach ($v['forumContent']['contents'] as $kk => $vv) {
					$data[$k]['lt'][$vv['fieldCode']] = $vv['fieldValue'];
				}
			}

			/** 封装承办单位 **/
			if (count($v['unitContent'])) {
				foreach ($v['unitContent'] as $kk => $vv) {
					$data[$k]['dw'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['dw'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['dw'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['dw'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['dw'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['dw'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['dw'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['dw'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['dw'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];
					}
				}
			}

			/** 封装议程（时段） **/
			if (count($v['agendaContent'])) {
				foreach ($v['agendaContent'] as $kk => $vv) {
					$data[$k]['yc'][$kk]['attendeePerResp']		= $vv['attendeePerResp'];
					$data[$k]['yc'][$kk]['auditStatus']			= $vv['auditStatus'];
					$data[$k]['yc'][$kk]['auditStatusName']		= $vv['auditStatusName'];
					$data[$k]['yc'][$kk]['createdDate']			= $vv['createdDate'];
					$data[$k]['yc'][$kk]['formNumber']			= $vv['formNumber'];
					$data[$k]['yc'][$kk]['latestUpdatedDate']	= $vv['latestUpdatedDate'];
					$data[$k]['yc'][$kk]['pkey']				= $vv['pkey'];
					$data[$k]['yc'][$kk]['staging']				= $vv['staging'];
					
					/** 封装议程信息 **/
					foreach ($vv['contents'] as $kkk => $vvv) {
						$data[$k]['yc'][$kk][$vvv['fieldCode']] = $vvv['fieldValue'];

						if ($vvv['fieldCode'] == 'agendaTime') {
							if (isset($vvv['fieldValue'][0]) && isset($vvv['fieldValue'][1])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0] . ',' . $vvv['fieldValue'][1];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5) . ' - ' . substr($vvv['fieldValue'][1], 11, 5);
							} else if (isset($vvv['fieldValue'][0])) {
								$data[$k]['yc'][$kk]['duration_time'] = $vvv['fieldValue'][0];

								$data[$k]['yc'][$kk]['duration_sd'] = substr($vvv['fieldValue'][0], 11, 5);
							} else {
								$data[$k]['yc'][$kk]['duration_time'] = '';

								$data[$k]['yc'][$kk]['duration_sd'] = '';
							}
						}

						/*if ($vvv['fieldCode'] == 'associatedAttendee') {
							if (isset($data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'])) {
								$data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'] = 'https://test-fs.31huiyi.com/' . $data[$k]['yc'][$kk][$vvv['fieldCode']]['oldValue'];
							}
						}*/
					}
				}
			}
		}
		
		/** 论坛地点中文 **/
		$area_zh['a'] = '东郊宾馆·紫金厅';
		$area_zh['b'] = '东郊宾馆·锦绣厅';
		$area_zh['d'] = '张江科学会堂张江厅';
		$area_zh['e'] = '张江科学会堂张江厅A';
		$area_zh['f'] = '张江科学会堂张江厅B';
		$area_zh['g'] = '张江科学会堂科创厅';
		$area_zh['h'] = '张江科学会堂 海科厅中心舞台';
		$area_zh['i'] = '张江科学会堂203会议室';
		$area_zh['j'] = '张江科学会堂303会议室';
		$area_zh['k'] = '张江科学会堂304会议室';
		$area_zh['l'] = '张江科学会堂305会议室';
		$area_zh['m'] = '张江科学会堂402会议室';
		$area_zh['n'] = '张江科学会堂403会议室';
		$area_zh['o'] = '张江科学会堂404会议室';
		$area_zh['p'] = '张江科学会堂405会议室';
		$area_zh['q'] = '上海远洋宾馆';
		$area_zh['r'] = '大零号湾';
		$area_zh['s'] = '外场';
		$area_zh['t'] = '线上';
		$area_zh['u'] = '国创中心总部';
		$area_zh['v'] = '松江区';

		/** 论坛地点英文 **/
		$area_en['a'] = 'Zijin Hall, Convention Center, Dongjiao Hotel';
		$area_en['b'] = 'Jinxiu Hall, Convention Center, Dongjiao Hotel';
		$area_en['c'] = 'Zhangjiang Hall, 2F, ZhangJiang Science Hall';
		$area_en['d'] = 'Zhangjiang Hall A, 2F, ZhangJiang Science Hall';
		$area_en['e'] = 'Zhangjiang Hall B, 2F, ZhangJiang Science Hall';
		$area_en['f'] = 'Inspire Hall, 2F, ZhangJiang Science Hall';
		$area_en['g'] = 'Haike Hall, 1F, ZhangJiang Science Hall';
		$area_en['h'] = 'Conference Room 203, ZhangJiang Science Hall';
		$area_en['j'] = 'Conference Room 303, ZhangJiang Science Hall';
		$area_en['k'] = 'Conference Room 304, ZhangJiang Science Hall';
		$area_en['l'] = 'Conference Room 305, ZhangJiang Science Hall';
		$area_en['m'] = 'Conference Room 402, ZhangJiang Science Hall';
		$area_en['n'] = 'Conference Room 403, ZhangJiang Science Hall';
		$area_en['o'] = 'Conference Room 404, ZhangJiang Science Hall';
		$area_en['p'] = 'Conference Room 405, ZhangJiang Science Hall';
		$area_en['q'] = 'Ocean Hotel Shanghai';
		$area_en['r'] = 'GREATER NEO BAY';
		$area_en['s'] = 'Off-site';
		$area_en['t'] = 'Online';
		$area_en['u'] = 'NATIONAL INNOVATION CENTER par EXCELLENCE (Shanghai)';
		$area_en['v'] = 'Songjiang District';

		//return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);

		/** 重新封装数据 **/
		foreach ($data as $k => $v) {
			
			$yc_data = [];	//第一次重组 以时间为键
			$yc_row	 = [];	//第二次重组 自增键值
			
			/** 循环议程（时段） **/
			foreach ($v['yc'] as $kk => $vv) {

				if (!isset($vv['duration_time'])) {
					$vv['duration_time'] = 'cs33';
				}

				/** 以时段为 键 设置数组结构 **/
				if (!isset($yc_data[$vv['duration_time']])) {

					$yc_data[$vv['duration_time']] = [];
					
					/** 循环信息封入新的数组 **/
					foreach ($vv as $kkk => $vvv) {
						if ($kkk != 'associatedAttendee') {
							$yc_data[$vv['duration_time']][$kkk] = $vvv;
						} else {
							$yc_data[$vv['duration_time']][$kkk][] = $vvv;
						}
					}
				} else {
					$yc_data[$vv['duration_time']]['associatedAttendee'][] = $vv['associatedAttendee'];
				}
			}

			foreach ($yc_data as $kk => $vv) {
				$yc_row[] = $vv;
			}
			
			$data[$k]['yc_row'] = $yc_row;

			if (isset($data[$k]['lt']['FC1I3KSV03VWDZVUO']) && isset($area_zh[$data[$k]['lt']['FC1I3KSV03VWDZVUO']])) {
				$data[$k]['lt']['FC1I3KSV03VWDZVUO_name'] = $area_zh[$data[$k]['lt']['FC1I3KSV03VWDZVUO']];
			}

			if (isset($data[$k]['lt']['FC1I3KSV05GWDZVPB']) && isset($area_en[$data[$k]['lt']['FC1I3KSV05GWDZVPB']])) {
				$data[$k]['lt']['FC1I3KSV05GWDZVPB_name'] = $area_en[$data[$k]['lt']['FC1I3KSV05GWDZVPB']];
			}

			unset($data[$k]['yc']);
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}
}