<?php
namespace app\wangeditor\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Wangeditor extends \think\Controller {

	public function my_path_info($file) {
		$path_parts = array();
		$path_parts ['dirname'] = rtrim(substr($file, 0, strrpos($file, '/')),"/")."/";
		$path_parts ['basename'] = ltrim(substr($file, strrpos($file, '/')),"/");
		$path_parts ['extension'] = substr(strrchr($file, '.'), 1);
		$path_parts ['filename'] = ltrim(substr($path_parts ['basename'], 0, strrpos($path_parts ['basename'], '.')),"/");
		return $path_parts;
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 图片上传     图片上传      图片上传      图片上传      图片上传      图片上传      图片上传      图片上传
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：罗傲傲@20211228 **/
	public function upload_img() {

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		//$file = $_FILES['file'];//得到传输的数据

		foreach ($_FILES as $k => $v) {
			$name = $v['name'];

			/** 获取扩展名 */
			$file_ext = $this->my_path_info($name);
			$names	  = $file_ext['filename'];	
			$file_ext = $file_ext['extension'];
			$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

			$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
			//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

			if (!in_array($file_ext, $conf_ext)) {
				return ['code' => 1, 'msg' => '对不起文件格式错误'];
			}
			
			/** 获取新的文件名 */
			$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
			$file_name =  '';
			for ($i = 0; $i < 20; $i++) {
				$file_name .= $str[mt_rand(0,strlen($str) - 1)];
			}

			$file_url = 'shangcheng/kcxfzdh/' . $file_name . '.' . $file_ext;

			//上传秘钥等信息
			$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
			$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

			//地域节点
			$region = 'ap-shanghai';

			//实例化链接
			$cosClient = new \Qcloud\Cos\Client(
				array(
					'region' => $region,
					'schema' => 'https', //协议头部，默认为http
					'credentials'=> array(
						'secretId'  => $secretId ,
						'secretKey' => $secretKey
					)
				)
			);
		
			//存储桶名称 格式：BucketName-APPID
			$bucket = "partner-cos-1304859415";

			//上传文件的临时路径
			$srcPath = $v['tmp_name'];
			$file = fopen($srcPath, 'rb');
			if ($file) {
				$result = $cosClient->Upload(
					$bucket = $bucket,
					$key = $file_url,
					$body = $file);
			}

			$img['url']  = 'https://' . $result['Location'];	//图片地址
			$img['alt']  = $names;								//图片文字说明
			$img['href'] = '';									//跳转链接

			$data[] = $img;
		}
		
		return json_encode(['errno' => 0, 'msg' => '上传成功', 'data' => $data]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 视频上传     视频上传      视频上传      视频上传      视频上传      视频上传      视频上传      视频上传
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：罗傲傲@20211228 **/
	public function upload_video() {

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['wangeditor-uploaded-video'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['mp4', 'avi', 'mpeg', 'wmv', 'rmvb', 'rm', 'flv'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/kcxfzdh/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }

		$data['url'] = 'https://' . $result['Location'];

		return json_encode(['errno' => 0, 'msg' => '上传成功', 'data' => $data]);
	}
}