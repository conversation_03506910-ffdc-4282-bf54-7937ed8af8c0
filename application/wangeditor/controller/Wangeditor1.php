<?php
namespace app\wangeditor\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Wangeditor extends \think\Controller {

	public function my_path_info($file) {
		$path_parts = array();
		$path_parts ['dirname'] = rtrim(substr($file, 0, strrpos($file, '/')),"/")."/";
		$path_parts ['basename'] = ltrim(substr($file, strrpos($file, '/')),"/");
		$path_parts ['extension'] = substr(strrchr($file, '.'), 1);
		$path_parts ['filename'] = ltrim(substr($path_parts ['basename'], 0, strrpos($path_parts ['basename'], '.')),"/");
		return $path_parts;
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 图片上传     图片上传      图片上传      图片上传      图片上传      图片上传      图片上传      图片上传
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：罗傲傲@20211228 **/
	public function upload_img() {

		$request    = Request::instance();
		$dir_public = str_replace('/index.php', '', $request->root());

		foreach ($_FILES as $k => $v) {

			$name = $v['name'];

			/** 获取扩展名 */
			//$file_ext = pathinfo($name);
			$file_ext = $this->my_path_info($name);
			$names	  = $file_ext['filename'];		//获取文件名
			$file_ext = $file_ext['extension'];
			$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

			$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];

			if (!in_array($file_ext, $conf_ext)) {
				//return  '对不起文件格式错误';
				return json_encode(['errno' => 1, 'msg' => '上传失败', 'data' => '对不起文件格式错误']);
			}
			
			/** 获取新的文件名 */
			$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
			$file_name =  '';
			for ($i = 0; $i < 20; $i++) {
				$file_name .= $str[mt_rand(0,strlen($str) - 1)];
			}

			/** 设定存储目录：u/wangeditor/img */
			$dir_root = str_replace('index.php', '', $_SERVER['SCRIPT_FILENAME']);
			
			if (!file_exists("{$dir_root}u")) { mkdir("{$dir_root}u"); }
			if (!file_exists("{$dir_root}u/wangeditor")) { mkdir("{$dir_root}u/wangeditor"); }
			if (!file_exists("{$dir_root}u/wangeditor/img")) { mkdir("{$dir_root}u/wangeditor/img"); }
			if (!file_exists("{$dir_root}u/wangeditor/img/" . date('Ymd'))) { mkdir("{$dir_root}u/wangeditor/img/" . date('Ymd')); }
			
			$target_file = "{$dir_root}u/wangeditor/img/" . date('Ymd') . "/{$file_name}.{$file_ext}";
			$info = move_uploaded_file($v['tmp_name'],$target_file);	
			if ($info) {
				$path = "/u/wangeditor/img/" . date('Ymd') . "/{$file_name}.{$file_ext}";
				
				/** 域名 **/
				$http = 'http://' . $_SERVER['HTTP_HOST'] . $dir_public;

				$img['url']  = $http . $path;	//图片地址
				$img['alt']  = $names;			//图片文字说明
				$img['href'] = '';				//跳转链接

				$data[] = $img;

				//return json_encode(['errno' => 0, 'msg' => '上传成功', 'data' => $data]);
			} else {
				//echo $file->getError();
				//return json_encode(['errno' => 2, 'msg' => '上传失败', 'data' => null]);
			}
		}

		return json_encode(['errno' => 0, 'msg' => '上传成功', 'data' => $data]);
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 视频上传     视频上传      视频上传      视频上传      视频上传      视频上传      视频上传      视频上传
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：罗傲傲@20211228 **/
	public function upload_video() {

		$request    = Request::instance();
		$dir_public = str_replace('/index.php', '', $request->root());

		$file = $_FILES['wangeditor-uploaded-video'];//得到传输的数据
		$name = $file['name'];
		/** 获取扩展名 */
		//$file_ext = pathinfo($name);
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];		//获取文件名
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['mp4', 'avi', 'mpeg', 'wmv', 'rmvb', 'rm', 'flv'];

		if (!in_array($file_ext, $conf_ext)) {
			//return  '对不起文件格式错误';
			return json_encode(['errno' => 1, 'msg' => '上传失败', 'data' => '对不起文件格式错误']);
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		/** 设定存储目录：u/wangeditor/video */
		$dir_root = str_replace('index.php', '', $_SERVER['SCRIPT_FILENAME']);
		
		if (!file_exists("{$dir_root}u")) { mkdir("{$dir_root}u"); }
		if (!file_exists("{$dir_root}u/wangeditor")) { mkdir("{$dir_root}u/wangeditor"); }
		if (!file_exists("{$dir_root}u/wangeditor/video")) { mkdir("{$dir_root}u/wangeditor/video"); }
		if (!file_exists("{$dir_root}u/wangeditor/video/" . date('Ymd'))) { mkdir("{$dir_root}u/wangeditor/video/" . date('Ymd')); }
		
		$target_file = "{$dir_root}u/wangeditor/video/" . date('Ymd') . "/{$file_name}.{$file_ext}";
		$info = move_uploaded_file($_FILES['wangeditor-uploaded-video']['tmp_name'],$target_file);	
		if ($info) {
			$path = "/u/wangeditor/video/" . date('Ymd') . "/{$file_name}.{$file_ext}";
			
			/** 域名 **/
			$http = 'http://' . $_SERVER['HTTP_HOST'] . $dir_public;

			$data['url']  = $http . $path;	//视频地址

			return json_encode(['errno' => 0, 'msg' => '上传成功', 'data' => $data]);
		} else {
			//echo $file->getError();
			return json_encode(['errno' => 2, 'msg' => '上传失败', 'data' => null]);
		}
	}
}