    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">新闻中心</a>
                </div>
                <ul class="tabBox right">
                    <li><a href="<?=url('web/news/news')?>">论坛动态</a></li>
                    <li class="on"><a href="<?=url('web/news/interview')?>">访谈栏目</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <li><a href="<?=url('web/news/interview')?>">访谈栏目</a></li>
            <li class="on"><a href="<?=url('web/news/video')?>">平行未来的N次元</a></li>
            <li><a href="<?=url('web/news/interview')?>">寻找青年的声音</a></li>
            <li><a href="<?=url('web/news/interview')?>">视频会客厅</a></li>
            <li><a href="<?=url('web/news/interview')?>">其他</a></li>
        </ul>
    </div>

    <!-- pageVideo -->
    <div class="pageVideo">
        <div class="wrap">
            <div class="videoBox between">
                <div class="swiper-container gallery-top">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <a href="#" class="img">
                                <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                                <div class="iconfont icon-bofang"></div>
                            </a>
                        </div>
                        <div class="swiper-slide">
                            <a href="#" class="img">
                                <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                                <div class="iconfont icon-bofang"></div>
                            </a>
                        </div>
                        <div class="swiper-slide">
                            <a href="#" class="img">
                                <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                                <div class="iconfont icon-bofang"></div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="swiper-container gallery-thumbs">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <p>平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</p>
                            <div class="iconfont icon-jiantou2"></div>
                        </div>
                        <div class="swiper-slide">
                            <p>平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</p>
                            <div class="iconfont icon-jiantou2"></div>
                        </div>
                        <div class="swiper-slide">
                            <p>平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</p>
                            <div class="iconfont icon-jiantou2"></div>
                        </div>
                    </div>
                </div>
            </div>

            <ul class="lists left">
                <li>
                    <a href="#">
                        <div class="img">
                            <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                            <div class="iconfont icon-bofang"></div>
                        </div>
                        <div class="tit">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                        <div class="t">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                    </a>
                </li>
                <li>
                    <a href="#">
                        <div class="img">
                            <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                            <div class="iconfont icon-bofang"></div>
                        </div>
                        <div class="tit">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                        <div class="t">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                    </a>
                </li>
                <li>
                    <a href="#">
                        <div class="img">
                            <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                            <div class="iconfont icon-bofang"></div>
                        </div>
                        <div class="tit">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                        <div class="t">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                    </a>
                </li>
                <li>
                    <a href="#">
                        <div class="img">
                            <img src="<?=$dir_public?>/web/images/liveImg.jpg" alt="">
                            <div class="iconfont icon-bofang"></div>
                        </div>
                        <div class="tit">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                        <div class="t">平行未来的N次元 | 跟随交大吕宝粮教授探寻“脑机结合”的未来</div>
                    </a>
                </li>
            </ul>

            <!-- 分页 -->
            <ul class="pagination">
                <li class="disabled"><span>上一页</span></li>
                <li class="active"><span>01</span></li>
                <li><a href="#">02</a></li>
                <li><a href="#">03</a></li>
                <li><a href="#">下一页</a></li>
            </ul>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="../../web/css/swiper.min.css" />
    <script type="text/javascript" src="../../web/js/swiper.min.js"></script>
    <script>
        var galleryThumbs = new Swiper('.gallery-thumbs', {
            spaceBetween: 0,
            slidesPerView: 3,
            // loop: true,
            freeMode: true,
            direction: "vertical",
            loopedSlides: 3, //looped slides should be the same
            watchSlidesVisibility: true,
            watchSlidesProgress: true,
        });
        var galleryTop = new Swiper('.gallery-top', {
            spaceBetween: 0,
            // loop:true,
            loopedSlides: 3, //looped slides should be the same
            // navigation: {
            //     nextEl: '.swiper-button-next',
            //     prevEl: '.swiper-button-prev',
            // },
            thumbs: {
                swiper: galleryThumbs,
            },
        });
    </script>