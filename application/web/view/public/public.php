<?php
	use \think\Cookie;
	use \think\Session;
	use \think\Request;
	use \think\Db;
	$request    = Request::instance();
	$dir_public = str_replace('/index.php', '', $request->root());
	$dir_view   = "../" . strtolower($request->controller()) . '/' . $request->action();

	/** 查询网站设置信息 **/
	// 注释掉数据库查询，使用控制器传递的变量
	/*
	try {
		$public_site = Db::table('rule_site')->where('language', 1)->where('genre', 1)->order('id desc')->find();
		if (!$public_site) {
			$public_site = [];
		}
	} catch (Exception $e) {
		$public_site = [];
	}
	*/
    
    // 设置默认值
    $public_site = isset($public_site) ? $public_site : [
        'guest_uid' => '#',
        'participation_uid' => '#',
        'sign_uid' => '#',
        'learning_contacts' => '待配置',
        'learning_phone' => '待配置',
        'learning_fax' => '待配置',
        'learning_mail' => '待配置',
        'learning_address' => '待配置',
        'business_contacts' => '待配置',
        'business_phone' => '待配置',
        'business_fax' => '待配置',
        'business_mail' => '待配置',
        'business_address' => '待配置',
        'service' => '/web/images/default-qr.png',
        'subscribe' => '/web/images/default-qr.png',
        'blog' => '/web/images/default-qr.png'
    ];

    // 查询合作伙伴分类
	/*
	try {
		$cooperate_sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();
		if (!$cooperate_sort) {
			$cooperate_sort = [];
		}
	} catch (Exception $e) {
		$cooperate_sort = [];
	}
	*/
    
    // 设置默认值
    $cooperate_sort = isset($cooperate_sort) ? $cooperate_sort : [];

    // 查询新闻分类
	/*
	try {
		$news_sort = Db::table('news_sort')->where('language', 1)->where('genre', 1)->where('pid', 0)->order('sort, id')->where('is_del', 1)->select();
		if (!$news_sort) {
			$news_sort = [];
		}
	} catch (Exception $e) {
		$news_sort = [];
	}
	*/
    
    // 设置默认值
    $news_sort = isset($news_sort) ? $news_sort : [];
    
    $web_public_url = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

	$web_public_url_index[0] = 'http://' . $_SERVER['HTTP_HOST'];
	$web_public_url_index[1] = 'http://' . $_SERVER['HTTP_HOST'] . '/';
	$web_public_url_index[2] = 'http://' . $_SERVER['HTTP_HOST'] . '/index/index';
	$web_public_url_index[3] = 'http://' . $_SERVER['HTTP_HOST'] . '/index/index.html';
	
	if (in_array($web_public_url, $web_public_url_index)) {
		$m_public_url = 'http://' . $_SERVER['HTTP_HOST'] . "/m/index/index";
	} else {
        $m_public_url = str_replace('/web/', "/m/", $web_public_url);
	}
?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<title><?=isset($public_title) ? $public_title : '';?></title>
	<meta name="keywords" content="<?=isset($public_key) ? $public_key : '';?>" />
    <meta name="description" content="<?=isset($public_desc) ? $public_desc : '';?>" />
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/common.css"/>
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/iconfont/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/web/css/style.css?v=' . date('YmdHis')?>">
	<script type="text/javascript" src="<?=$dir_public?>/web/js/jquery.min.js"></script>
	<script type="text/javascript" src="<?=$dir_public?>/web/js/jquery.SuperSlide.2.1.3.js"></script>
	<!-- <script type="text/javascript" src="<?=$dir_public?>/web/js/common.js"></script> -->
    <script type="text/javascript" src="<?=$dir_public?>/web/js/vue.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/axios.min.js"></script>
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/swiper.min.css" />
    <script type="text/javascript" src="<?=$dir_public?>/web/js/swiper.min.js"></script>
	<script type="text/javascript" src="<?=$dir_public?>/web/js/suncher.js"></script>
    <script>
		if(/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){
			window.location.href = "<?=$m_public_url?>";
		}
	</script>
    <script>
        var type=navigator.appName;
        if (type=="Netscape") {
            var lang = navigator.language;
        } else {
            var lang = navigator.userLanguage;
        }
        var lang = lang.substr(0,2);
        if (lang == "en") {
            window.location.href="http://pj.dsger.com/en/index/index.html";
        } else if (lang == "zh") {
            // window.location.href="http://pj.dsger.com";
        } else {
            window.location.href="http://pj.dsger.com";
        }
    </script>
</head>
<body>

    <!--headerBox-->
    <div class="headerBox headerBox1">
        <div class="wrap1 between">
            <a href="/" class="logo">
                <!-- <img src="<?=$dir_public?>/web/images/logo.png" /> -->
            </a>
            <div class="box right">
                <ul id="headerNav" class="navBox right">
                    <li class="nLi">
                        <h3><a href="/" data-href="/">首页</a></h3>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="about">论坛介绍</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('web/about/about')?>">论坛简介</a></li>
                                <li><a href="<?=url('web/about/organization')?>">组织机构</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="news">新闻中心</a></h3>
                        <div class="sub">
                            <ul>
                                <?php if (!count($news_sort)) { ?>
                                    
                                <?php } else { foreach ($news_sort as $k => $v) { ?>
                                    <li><a href="<?=url('web/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a></li>
                                <?php } } ?>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="<?=url('web/forum/forum')?>" data-href="forum">论坛日程</a></h3>
                    </li>
                    <?php /*
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="guest">演讲嘉宾</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('web/guest/guest')?>">人物风采</a></li>
                                <li><a href="<?=$public_site['guest_uid']?>" target="_blank">嘉宾注册</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="participation">报名参会</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('web/participation/participation')?>">参会说明</a></li>
                                <li><a href="<?=$public_site['participation_uid']?>" target="_blank">参会注册</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="information">会务信息</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('web/information/service')?>">班车信息</a></li>
                                <li><a href="<?=url('web/information/hotel')?>">推荐酒店</a></li>
                                <li><a href="<?=url('web/information/map')?>">会场地图</a></li>
                            </ul>
                        </div>
                    </li>
                    */ ?>
                    <li class="nLi">
                        <h3><a href="<?=url('web/live/live')?>" data-href="live">直播中心</a></h3>
                        <div class="sub">
                            <ul>
                                <li><a href="<?=url('web/live/review')?>">直播回顾</a></li>
                            </ul>
                        </div>
                    </li>
                    <li class="nLi">
                        <h3><a href="<?=url('web/review/review')?>" data-href="review">往届回顾</a></h3>
                    </li>
                    <li class="nLi">
                        <h3><a href="javascript:;" data-href="partner">合作伙伴</a></h3>
                        <div class="sub">
                            <ul>
                                <?php if (!count($cooperate_sort)) { ?>
                                    
                                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                                    <?php if ($v['type'] == 1) { ?>
                                        <li><a href="<?=url('web/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>   
                                    <?php } ?>
                                <?php } } ?>
                                <li><a href="<?=url('web/partner/medium')?>">合作媒体</a></li>    
                                <?php if (!count($cooperate_sort)) { ?>
                                    
                                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                                    <?php if ($v['type'] == 2) { ?>
                                        <li><a href="<?=url('web/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li> 
                                    <?php } ?>
                                <?php } } ?>
                            </ul>
                        </div>
                    </li>
                    <?php /*
                    <li class="nLi">
                        <h3><a href="<?=url('web/download/download')?>" data-href="download">文件下载</a></h3>
                    </li>
                    */ ?>
                </ul>
                <a href="<?=url('en/index/index')?>" class="language">EN</a>
                <a href="<?=$public_site['sign_uid']?>" target="_blank" class="login centerT">
                    <div class="iconfont icon-login"></div>
                    <p>注册/登录</p>
                </a>
            </div>
        </div>
    </div>

	<?php 
		$template_path = __DIR__ . '/../' . strtolower($request->controller()) . '/' . $request->action() . '.php';
		require_once($template_path);
	?>

    <!-- footer -->
    <div class="footer" id="footer">
        <div class="wrap between">
            <dl>
                <dt>学术联络</dt>
                <dd>联系人：<?=$public_site['learning_contacts']?></dd>
                <dd>电话：<?=$public_site['learning_phone']?></dd>
                <dd>传真：<?=$public_site['learning_fax']?></dd>
                <dd>邮箱：<?=$public_site['learning_mail']?></dd>
                <dd>地址：<?=$public_site['learning_address']?></dd>
            </dl>
            <dl>
                <dt>参会联络</dt>
                <dd>联系人：<?=$public_site['business_contacts']?></dd>
                <dd>电话：<?=$public_site['business_phone']?></dd>
                <dd>传真：<?=$public_site['business_fax']?></dd>
                <dd>邮箱：<?=$public_site['business_mail']?></dd>
                <dd>地址：<?=$public_site['business_address']?></dd>
            </dl>
            <div class="codeBox">
                <div class="p">扫一扫加关注</div>
                <div class="right">
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['service']?>" />
                        <p>官方微信服务号</p>
                    </div>
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['subscribe']?>" />
                        <p>官方微信订阅号</p>
                    </div>
                    <div class="code">
                        <img src="<?=$dir_public . $public_site['blog']?>" />
                        <p>官方微博</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="copyRight">
            <div class="wrap"><span>版权所有 上海浦江创新论坛中心</span> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备18034787号-1</a> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备05040256号-8</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402009841" target="_blank">沪公网安备 31010402009841号</a></div>
        </div>
    </div>

    <!-- sidebarBox -->
    <ul class="sidebarBox">
        <li>
            <a href="<?=url('web/partner/cooperation', ['sort_id' => 5])?>">
                <div class="iconfont icon-hezuo"></div>
                <div class="t">商务合作</div>
            </a>
        </li>
        <li>
            <a href="#footer">
                <div class="iconfont icon-dianhua"></div>
                <div class="t">联系我们</div>
            </a>
        </li>
        <!-- <li id="subscribe">
            <a href="javascript:;">
                <div class="iconfont icon-dingyue"></div>
                <div class="t">快讯订阅</div>
            </a>
        </li> -->
        <li>
            <a href="javascript:;">
                <div class="iconfont icon-weixin"></div>
                <div class="t">微信服务号</div>
                <div class="img"><img src="<?=$dir_public . $public_site['service']?>" alt=""></div>
            </a>
        </li>
        <li>
            <a href="javascript:;">
                <div class="iconfont icon-weixin"></div>
                <div class="t">微信订阅号</div>
                <div class="img"><img src="<?=$dir_public . $public_site['subscribe']?>" alt=""></div>
            </a>
        </li>
        <li class="goTop">
            <a href="javascript:;">
                <div class="iconfont icon-dingbu"></div>
            </a>
        </li>
    </ul>
    
    <!-- popupBox -->
    <div class="popupBox"></div>
    <div class="popupForm">
        <form autocomplete="off">
            <div class="close"></div>
            <div class="title">快讯订阅</div>
            <div class="tit">请填写系列信息以获得浦江创新论坛最新鲜的内容推送</div>
            <div class="row">
                <div class="p">姓名</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <div class="row">
                <div class="p">单位</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <div class="row">
                <div class="p">邮箱</div>
                <input type="text" name="title" placeholder="" class="txt" value="" />
            </div>
            <button type="button" class="ajaxformbtn">提交</button>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            // 检查元素是否存在再绑定事件
            if ($("#subscribe").length > 0) {
                $("#subscribe").click(function() {
                    $(".popupBox").show();
                    $(".popupForm").slideDown(300);
                });
            }

            if ($(".popupForm .close").length > 0) {
                $(".popupForm .close").click(function() {
                    $(".popupBox").hide();
                    $(".popupForm").slideUp(300);
                });
            }
        });
    </script>

</body>
</html>