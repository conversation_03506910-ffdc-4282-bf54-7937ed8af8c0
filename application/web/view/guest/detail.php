    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">演讲嘉宾</a>
                </div>
                <ul class="tabBox right">
                    <li class="on"><a href="<?=url('web/guest/guest')?>">人物风采</a></li>
                    <li><a href="<?=$public_site['guest_uid']?>" target="_blank">嘉宾注册</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- pageCharacterD -->
    <div class="pageCharacterD">
        <div class="wrap">
            <div class="swiper-container gallery-top">
                <div class="swiper-wrapper">
                    <?php if (!count($row)) { ?>

                    <?php } else { foreach ($row as $k => $v) { ?>
                        <div class="swiper-slide" data-hash="<?=$v['id']?>">
                            <div class="between">
                                <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                                <div class="cont">
                                    <div class="name"><?=$v['FullName']?></div>
                                    <div class="t"><?=$v['Position']?></div>
                                </div>
                            </div>
                        </div>
                    <?php } } ?>
                </div>
                <div class="arrow">
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
            <div class="swiper-container gallery-thumbs">
                <div class="swiper-wrapper">
                    <?php if (!count($row)) { ?>
                        
                    <?php } else { foreach ($row as $k => $v) { ?>
                        <div class="swiper-slide" data-hash="<?=$v['id']?>">
                            <img src="<?=$v['Avatar']?>" alt="">
                        </div>
                    <?php } } ?>
                </div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/web/css/swiper.min.css" />
    <script type="text/javascript" src="<?=$dir_public?>/web/js/swiper.min.js"></script>
    <script>
        var galleryThumbs = new Swiper('.gallery-thumbs', {
            spaceBetween: 20,
            slidesPerView: 6,
            loop: true,
            freeMode: true,
            loopedSlides: 7, //looped slides should be the same
            watchSlidesVisibility: true,
            watchSlidesProgress: true,
        });
        var galleryTop = new Swiper('.gallery-top', {
            hashNavigation: true,
            spaceBetween: 0,
            loop:true,
            loopedSlides: 7, //looped slides should be the same
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            thumbs: {
                swiper: galleryThumbs,
            },
        });
    </script>