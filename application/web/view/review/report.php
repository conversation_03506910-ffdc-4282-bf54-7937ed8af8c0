    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="between wrap">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">往届回顾</a>
                </div>
            </div>
        </div>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <li><a href="<?=url('web/review/theme', ['id' => $id])?>">论坛主题</a></li>
            <li><a href="<?=url('web/review/schedule', ['id' => $id])?>">论坛日程</a></li>
            <li><a href="<?=url('web/review/guest', ['id' => $id])?>">演讲嘉宾</a></li>
            <li><a href="<?=url('web/review/atlas', ['id' => $id])?>">图片集锦</a></li>
            <li class="on"><a href="<?=url('web/review/report', ['id' => $id])?>">专题报告集</a></li>
            <li><a href="<?=url('web/review/prints', ['id' => $id])?>">会后刊</a></li>
        </ul>
    </div>

    <!-- pageReport -->
    <div class="pageReport">
        <div class="wrap">
            <ul class="lists left">
                <?php if (!count($row)) { ?>
					<div class="noData">暂无数据</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <div class="tit"><?=$v['title']?></div>
                        <div class="time"><?=$v['time']?></div>
                        <a href="<?=$v['url']?>" download="<?=$v['url_name']?>" class="down">下载文件</a>
                    </li>
                <?php } } ?>
            </ul>
        </div>
    </div>