    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <!-- <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">合作伙伴</a>
                </div> -->
                <ul class="tabBox tabBox1 left">
                    <?php if (!count($sort)) { ?>
					
                    <?php } else { foreach ($sort as $k => $v) { ?>
                        <?php if ($v['type'] == 1) { ?>
                            <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                                <a href="<?=url('web/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                            </li>
                        <?php } ?>
                    <?php } } ?>
                    <li class="<?=$sort[$sort_id]['type'] == 3 ? 'on' : '';?> mon">
                        <a href="<?=url('web/partner/medium')?>">合作媒体</a>
                        <div class="ul">
                            <?php if (!count($sort)) { ?>
                        
                            <?php } else { foreach ($sort as $k => $v) { ?>
                                <?php if ($v['type'] == 3) { ?>
                                    <a href="<?=url('web/partner/partner', ['sort_id' => $v['id']])?>" class="li"><?=$v['name']?></a>
                                <?php } ?>
                            <?php } } ?>
                        </div>
                    </li>
                    <?php if (!count($sort)) { ?>
					
                    <?php } else { foreach ($sort as $k => $v) { ?>
                        <?php if ($v['type'] == 2) { ?>
                            <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                                <a href="<?=url('web/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                            </li>
                        <?php } ?>
                    <?php } } ?>
                </ul>
            </div>
        </div>
    </div>
    <script>
        $(".pageBanner .tabBox .mon").hover(function() {
            $(this).children('.ul').show();
        }, function() {
            $(this).children('.ul').hide();
        })
    </script>

    <!-- pagePartner -->
    <div class="pagePartner">
        <div class="wrap">
            <div class="title" style="display: <?=$sort[$sort_id]['type'] == 3 ? 'block' : 'none';?>"><?=$sort[$sort_id]['name']?></div>

            <ul class="left">
                <?php if (!count($row)) { ?>
                    <div class="noData">暂无数据</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <?php if ($v['url']) { ?>
                            <a href="<?=$v['url']?>" target="_blank">
                        <?php } else { ?>
                            <a href="javascrupt:;">
                        <?php } ?>
                            <img src="<?=$dir_public . $v['logo']?>" alt="">
                        </a>
                    </li>
                <?php } } ?>
            </ul>
        </div>
    </div>