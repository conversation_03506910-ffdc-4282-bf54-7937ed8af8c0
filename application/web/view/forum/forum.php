<div id="app">
    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">论坛日程</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- pageTab -->
    <div class="pageTab">
        <ul class="wrap centerT">
            <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07 00:00:00','2024-09-07 23:59:59', 1)">
                <a href="javascript:;">09-07</a>
            </li>
            <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08 00:00:00','2024-09-08 23:59:59', 2)">
                <a href="javascript:;">09-08</a>
            </li>
            <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09 00:00:00','2024-09-09 23:59:59', 3)">
                <a href="javascript:;">09-09</a>
            </li>
            <!-- <li data-start="2024-09-10 00:00:00" data-end="2024-09-10 23:59:59"><a href="javascript:;">09-10</a></li> -->
        </ul>
    </div>

    <!-- pageForum -->
    <div class="pageForum scheduleBox">
        <div class="wrap">
            <div v-for="(item,index) in lists" :key="index" @click="select(index)">
                <div class="liBox" v-if="item.lt.FC1I3KSV08QWDZVDU">
                    <div class="Box between" :class="sel === index ? 'on' : ''">
                        <div class="titBox">
                            <div class="left">
                                <div class="tit">{{item.lt.FC1I3KSV02JWDZVEX}}</div>
                                <!-- <div class="label label1 centerT" v-if="item.lt.forumtime_type == 1">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>未开始</p>
                                </div>
                                <div class="label label2 centerT" v-if="item.lt.forumtime_type == 2">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>直播中</p>
                                </div>
                                <div class="label label3 centerT" v-if="item.lt.forumtime_type == 3">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>已结束</p>
                                </div> -->
                            </div>
                            <div class="address left">
                                <div class="left" style="margin-right: 20px;">
                                    <div class="iconfont icon-weizhi1"></div>
                                    <p>会议室：{{item.lt.FC1I3KSV03VWDZVUO_name}}</p>
                                </div>
                                <div class="left">
                                    <div class="iconfont icon-shijian"></div>
                                    <p>会议时间：{{item.lt.start_time}} - {{item.lt.end_time}}</p>
                                </div>
                            </div>
                        </div>
                        <div class="btnBox right">
                            <!-- <a href="javascrupt:;" class="btn btn2" v-if="item.lt.forumtime_type == 1">敬请期待</a>
                            <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 2">去看直播</a>
                            <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 3">去看回放</a> -->
                            <div class="iconfont icon-jiantou"></div>
                        </div>
                    </div>
                    <div class="contBox" :class="sel === index ? 'sel' : ''">
                        <div class="cont">
                            <!-- <div class="t">论坛地点：中国•上海</div> -->
                            <div class="t" v-if="item.dw_data != ''">
                                <div class="between" v-if="item.dw_data.a">
                                    <p>承办单位：</p>
                                    <p><span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                </div>           
                                <div class="between" v-if="item.dw_data.b">
                                    <p>协办单位：</p>
                                    <p><span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                </div>        
                                <div class="between" v-if="item.dw_data.c">
                                    <p>支持单位：</p>
                                    <p><span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span></p>
                                </div>
                                <!-- <p v-if="item.dw_data.a">承办单位：
                                    <span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                </p>
                                <p v-if="item.dw_data.b">协办单位：
                                    <span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                </p>
                                <p v-if="item.dw_data.c">支持单位：
                                    <span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05B6WDZVDO}}<em>、</em></span>
                                </p> -->
                            </div>
                            <div class="t1" v-if="item.lt.FC1I3KSV03DWDZVUY">主题诠释</div>
                            <div class="t2">{{item.lt.FC1I3KSV03DWDZVUY}}</div>
                            <!-- <div class="show">
                                <p>点击展开</p>
                                <div class="iconfont icon-jiantou"></div>
                            </div> -->
                        </div>
                        <div class="dl" v-if="item.yc_row != ''">
                            <div v-for="(innerItem,innerIndex) in item.yc_row" :key="innerIndex">
                                <div class="dt between" v-if="!innerItem.FC1I3KT0NT6WDZVMF">
                                    <div class="time">{{innerItem.duration_sd}}</div>
                                    <div class="dd">
                                        <div class="t3">{{innerItem.topicCn}}</div>
                                        <div class="left" v-if="innerItem.FC1I3KT0NQ3WDZVIB != 'a' && innerItem.FC1I3KT0NQ3WDZVIB != 'e'">
                                            <div class="li left" v-for="(inItem,inIndex) in innerItem.associatedAttendee" :key="inIndex">
                                                <div class="img"><img :src="'https://fs.31huiyi.com/' + inItem.avatar" alt=""></div>
                                                <div class="t4">
                                                    <div class="name">{{inItem.fullName}}</div>
                                                    <div class="t5">{{inItem.company}}</div>
                                                    <div class="t5">{{inItem.position}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: {
            start_time: '2024-09-07 00:00:00',
            end_time: '2024-09-07 23:59:59',
            lists: [],
            sel: null, 
            sele: 1,
        },
        created() {
            this.forumList(this.start_time, this.end_time)

            let nowTime = new Date().getTime()
            let time = new Date('2024-09-07 23:59:59').getTime()
            if(nowTime > time) {
                this.start_time = '2024-09-08 00:00:00'
                this.end_time = '2024-09-08 23:59:59'
                this.sele = 2
                this.forumList(this.start_time, this.end_time)
            }

            let time1 = new Date('2024-09-08 23:59:59').getTime()
            if(nowTime > time1) {
                this.start_time = '2024-09-09 00:00:00'
                this.end_time = '2024-09-09 23:59:59'
                this.sele = 3
                this.forumList(this.start_time, this.end_time)
            }
        },
        methods: {
            changeTime(start, end, index) {
                this.forumList(start, end)
                this.sele = index
                this.sel = null
            },

            forumList(start, end){
                var that = this
                axios({
                    method: 'post',
                    url: '<?=url('api/schedule/schedule_zs')?>',
                    data: {
                        start_time: start,
                        end_time: end,
                    }
                }).then((res) => {
                    if (res.data.code == 200) {
                        let lists = res.data.data
                        lists.forEach((item, index) => {
                            let now = new Date().getTime()
                            let startTime = new Date(item.lt.forumtime[0]).getTime()
                            let endTime = new Date(item.lt.forumtime[1]).getTime()
                            item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                            item.lt.end_time = item.lt.forumtime[1].slice(10, 16)
                            if(now < startTime) {
                                item.lt.forumtime_type = 1  // 未开始
                            }
                            if(now > startTime && now < endTime) {
                                item.lt.forumtime_type = 2  // 进行中
                            }
                            if(now > endTime) {
                                item.lt.forumtime_type = 3  // 已结束
                            }
                        })
                        this.lists = lists
                    }
                })
            },

            select(index) {
                if (this.sel === index) {
                    this.sel = null; // 再次点击当前项时收起
                } else {
                    this.sel = index; // 点击其他项时当前项展开，其他项收起
                }
                // this.sel = index
            }
        }
    })
</script>
