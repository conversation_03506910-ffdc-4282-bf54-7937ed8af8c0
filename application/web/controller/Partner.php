<?php
namespace app\web\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：合作伙伴 **/
class Partner extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：合作伙伴 **/
	/** 作者：穠@20230328 **/
    public function partner() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');   //分类id

		/** 查询合作伙伴信息 **/
		$row = Db::table('cooperate')->where('language', 1)->where('genre', 1)->order('sort, id')->where('sort_id', $sort_id)->where('is_del', 1)->select();

		/** 查询分类 **/
		$sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,tdk_key,desc,detail,type');

		$this->assign('row', $row);
		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $sort[$sort_id]['name']);
		$this->assign('public_key', $sort[$sort_id]['tdk_key']);
		$this->assign('public_desc', $sort[$sort_id]['desc']);
        return $this->fetch('web@public/public');
	}

	/** 功能：商务合作 **/
	/** 作者：穠@20230328 **/
    public function cooperation() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');   //分类id

		/** 查询分类 **/
		$sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,tdk_key,desc,detail,type');

		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $sort[$sort_id]['name']);
		$this->assign('public_key', $sort[$sort_id]['tdk_key']);
		$this->assign('public_desc', $sort[$sort_id]['desc']);
        return $this->fetch('web@public/public');
	}

	/** 功能：合作媒体 **/
	/** 作者：穠@20230328 **/
    public function medium() {

		/** 查询分类 **/
		$sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,tdk_key,desc,detail,type');
		
		/** 查询合作媒体分类id **/
		$cooperate = Db::table('cooperate_sort')->where('type', 3)->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,type');

		/** 查询分类下合作伙伴 **/
		foreach ($cooperate as $k => $v) {
			$cooperate[$k]['data'] = Db::table('cooperate')->order('sort, id')->where('sort_id', $v['id'])->where('is_del', 1)->select();
		}

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 1)->where('id', 1)->find();

		$this->assign('sort', $sort);
		$this->assign('cooperate', $cooperate);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('web@public/public');
	}
}