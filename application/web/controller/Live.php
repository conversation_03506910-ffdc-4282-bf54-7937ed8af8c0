<?php
namespace app\web\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：直播中心 **/
class Live extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：直播中心 **/
	/** 作者：穠@20230328 **/
    public function live() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');			//分类id
		$num	 = input('num/d', 12);				//每页显示多少条

		/** 查询分类 **/
		$sort = Db::table('broadcast_sort')->where('language', 1)->where('genre', 1)->order('sort, id desc')->where('is_del', 1)->select();

		if(!$sort_id && $sort) {
			$sort_id = $sort[0]['id'];
		}

		/** 查询直播中心信息 **/
		$row  = Db::table('broadcast')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1);
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 1)->where('id', 5)->find();

		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('web@public/public');
	}

	/** 功能：直播回顾 **/
	/** 作者：穠@20230328 **/
    public function review() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');			//分类id
		$num	 = input('num/d', 6);				//每页显示多少条

		/** 查询分类 **/
		$sort = Db::table('broadcast_review_sort')->where('language', 1)->where('genre', 1)->order('sort, id desc')->where('is_del', 1)->select();

		if(!$sort_id && $sort) {
			$sort_id = $sort[0]['id'];
		}

		/** 查询直播回顾信息 **/
		$row  = Db::table('broadcast_review')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1);
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 1)->where('id', 29)->find();

		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('web@public/public');
	}

}