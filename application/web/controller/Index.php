<?php
namespace app\web\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

/** 功能：首页 **/
class Index extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：首页 **/
	/** 作者：穠@20230328 **/
    public function index() {
        
        // 检查PDO扩展
        if (!extension_loaded('pdo_mysql')) {
            echo "<h1>浦江论坛</h1>";
            echo "<p style='color: red;'>错误：PDO MySQL扩展未安装</p>";
            echo "<p>请安装并启用PHP的PDO MySQL扩展</p>";
            echo "<p><a href='/check_extensions.php'>检查PHP扩展</a></p>";
            echo "<p><a href='/home.php'>访问简单首页</a></p>";
            return;
        }
        
        // 初始化默认变量，避免数据库连接错误
        $banner = [];
        $news = [];
        $release = [];
        $report = [];
        $activity = [];
        $videos = [];
        $interview = [];
        $video = [];
        $medium = [];
        $cooperatesort = [];
        $rule_tdk = [
            'title' => '浦江论坛 - 首页',
            'key' => '浦江论坛,论坛,会议',
            'desc' => '浦江论坛官方网站'
        ];

        try {
            // 尝试连接数据库并查询数据
            /** 查询Banner信息 **/
            $banner = Db::table('banner')->where('language', 1)->where('genre', 1)->order('sort')->where('is_del', 1)->select();

            /** 查询新闻 **/
            $news = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->limit(7)->where('is_del', 1)->select();

            /** 查询新闻-浦江发布 **/
            $release = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 4)->limit(6)->where('is_del', 1)->select();

            /** 查询往届回顾2023年-专题报告集 **/
            $report = Db::table('review_report')->where('language', 1)->where('genre', 1)->order('sort, id')->where('review_id', 1)->limit(6)->where('is_del', 1)->select();

            /** 查询新闻-会议季活动 **/
            $activity = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 5)->limit(5)->where('is_del', 1)->select();

            /** 查询新闻-平行未来的N次元 **/
            $videos = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 8)->limit(5)->where('is_del', 1)->select();

            /** 查询新闻-新华访谈 **/
            $interview = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 23)->limit(5)->where('is_del', 1)->select();

            /** 查询新闻-视频会客厅 **/
            $video = Db::table('news')->where('language', 1)->where('genre', 1)->order('sort desc, release_time desc')->where('sort_id', 10)->limit(5)->where('is_del', 1)->select();

            /** 查询合作伙伴分类信息 **/
            $cooperatesort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('id', 'in', [25,23,1,2,3])->where('is_del', 1)->select();

            /** 查询合作伙伴信息 **/
            foreach ($cooperatesort as $k => $v) {
                $cooperatesort[$k]['cooperate'] = Db::table('cooperate')->order('sort, id')->where('language', 1)->where('genre', 1)->where('sort_id', $v['id'])->where('is_del', 1)->select();
            }

            /** 查询合作伙伴-合作媒体分类信息 **/
            $medium_id = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('type', 3)->where('is_del', 1)->column('id');

            $medium = Db::table('cooperate')->order('sort, id')->where('language', 1)->where('genre', 1)->where('sort_id', 'in', $medium_id)->where('is_del', 1)->select();

            /** 查询TDK设置 **/
            $rule_tdk_db = Db::table('rule_tdk')->where('language', 1)->where('id', 1)->find();
            if ($rule_tdk_db) {
                $rule_tdk = $rule_tdk_db;
            }
            
        } catch (\Exception $e) {
            // 数据库连接失败时使用默认值
            // 可以记录错误日志
            error_log('Database connection failed: ' . $e->getMessage());
        }

        // 尝试渲染模板，如果失败则直接输出
        try {
            $this->assign('banner', $banner);
            $this->assign('news', $news);
            $this->assign('release', $release);
            $this->assign('report', $report);
            $this->assign('activity', $activity);
            $this->assign('videos', $videos);
            $this->assign('interview', $interview);
            $this->assign('video', $video);
            $this->assign('medium', $medium);
            $this->assign('cooperatesort', $cooperatesort);
            $this->assign('public_title', $rule_tdk['title']);
            $this->assign('public_key', $rule_tdk['key']);
            $this->assign('public_desc', $rule_tdk['desc']);
            
            return $this->fetch('web@public/public');
        } catch (\Exception $e) {
            // 模板渲染失败，直接输出简单内容
            echo "<h1>浦江论坛</h1>";
            echo "<p>欢迎访问浦江论坛官方网站</p>";
            echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
            
            if (!empty($news)) {
                echo "<h2>最新新闻</h2>";
                foreach ($news as $item) {
                    echo "<p>" . $item['title'] . "</p>";
                }
            } else {
                echo "<p>暂无新闻数据</p>";
            }
            
            echo "<p><a href='/test'>测试页面</a></p>";
            echo "<p><a href='/check_extensions.php'>检查PHP扩展</a></p>";
        }
	}

}