<?php
namespace app\web\controller;
use think\Controller;

class Test extends Controller {
    
    public function index() {
        echo "<h1>浦江论坛 - 测试页面</h1>";
        echo "<p>如果您能看到这个页面，说明ThinkPHP框架工作正常！</p>";
        echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
        echo "<p>PHP版本: " . PHP_VERSION . "</p>";
        
        // 测试数据库连接
        try {
            $pdo = new \PDO('mysql:host=rm-cn-ioy4bnmu00003eeo.rwlb.rds.aliyuncs.com;dbname=pujiangforum;charset=utf8mb4', 'huiyi_31', 'huiyi_31@Ali4Launch');
            echo "<p style='color: green;'>✓ 数据库连接成功</p>";
        } catch (\PDOException $e) {
            echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='/'>返回首页</a></p>";
    }
} 