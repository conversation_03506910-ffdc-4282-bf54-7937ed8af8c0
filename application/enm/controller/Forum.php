<?php
namespace app\enm\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：论坛日程 **/
class Forum extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：论坛日程 **/
	/** 作者：穠@20230328 **/
    public function forum() {

		$this->assign('public_title', '论坛日程');
		$this->assign('public_key', '论坛日程');
		$this->assign('public_desc', '论坛日程');
        return $this->fetch('enm@public/public');
	}

}