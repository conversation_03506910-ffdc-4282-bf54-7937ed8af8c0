    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Partners</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <?php if (!count($sort)) { ?>
					
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <?php if ($v['type'] == 1) { ?>
                        <a href="<?=url('enm/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } else if ($v['type'] == 3) { ?>
                        <a href="<?=url('enm/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } else if ($v['type'] == 2) { ?>
                        <a href="<?=url('enm/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } ?>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageAbout -->
    <div class="pageAbout">
        <div class="pageTitle">Business Cooperation</div>
        <div class="text"><?=str_replace("&nbsp;", ' ', $sort[$sort_id]['detail'])?></div>
    </div>