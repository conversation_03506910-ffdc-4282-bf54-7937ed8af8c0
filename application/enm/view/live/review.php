    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Live Review</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <?php if (!count($sort)) { ?>
					
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('enm/live/review', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageVideo -->
    <div class="pageVideo pageVideo1">
        <ul class="lists left">
            <?php if (!count($row)) { ?>
                <div class="noData">No Data</div>
            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <a href="<?=$v['url']?>" target="_blank">
                        <div class="img"><img src="<?=$dir_public. $v['logo']?>" alt=""></div>
                        <div class="tit"><?=$v['title']?></div>
                        <div class="t">Time: <?=substr($v['start_time'], 5, 11)?>
                            <?php if ($v['end_time']) { ?>
                                -
                                <?=substr($v['end_time'], 11, 5)?>
                            <?php } ?>
                        </div>
                    </a>
                </li>
            <?php } } ?>
        </ul>

        <!-- 分页 -->
        <?php
            $page = json_encode($page);
            $page = str_replace('&laquo;', 'PREV', $page);
            $page = str_replace('&raquo;', 'NEXT', $page);
            $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
            $page = json_decode($page, true);
        ?>
        <?=$page?>
    </div>