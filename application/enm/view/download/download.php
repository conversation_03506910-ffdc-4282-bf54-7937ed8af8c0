    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">File Download</a>
        </div>
    </div>

    <!-- pageReport -->
    <div class="pageReport">
        <ul class="lists">
            <?php if (!count($row)) { ?>

            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <div class="tit"><?=$v['title']?></div>
                    <div class="time"><?=$v['time']?></div>
                    <a href="<?=$v['url']?>" download="<?=$v['url_name']?>" class="down">Download</a>
                </li>
            <?php } } ?>
        </ul>

        <!-- 分页 -->
        <?php
            $page = json_encode($page);
            $page = str_replace('&laquo;', 'PREV', $page);
            $page = str_replace('&raquo;', 'NEXT', $page);
            $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
            $page = json_decode($page, true);
        ?>
        <?=$page?>
    </div>