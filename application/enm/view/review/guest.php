    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Review</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <li><a href="<?=url('enm/review/theme', ['id' => $id])?>">Theme</a></li>
            <li><a href="<?=url('enm/review/schedule', ['id' => $id])?>">Agenda</a></li>
            <li class="on"><a href="<?=url('enm/review/guest', ['id' => $id])?>">Speakers</a></li>
            <li><a href="<?=url('enm/review/atlas', ['id' => $id])?>">Photo Gallery</a></li>
            <li><a href="<?=url('enm/review/report', ['id' => $id])?>">Forum Bulletin</a></li>
            <li><a href="<?=url('enm/review/prints', ['id' => $id])?>">Conference Review</a></li>
        </ul>
    </div>

    <!-- pageGuest -->
    <div class="pageGuest">
        <ul class="lists left">
            <?php if (!count($row)) { ?>
                <div class="noData">No Data</div>
            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <a href="javascript:;" class="between">
                        <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                        <div class="cont">
                            <div class="name"><?=$v['name']?></div>
                            <div class="t"><?=$v['identity']?></div>
                        </div>
                    </a>
                </li>         
            <?php } } ?>

            <!-- <li>
                <a href="javascript:;" class="between">
                    <div class="img"><img src="<?=$dir_public?>/m/images/guestImg.jpg" alt=""></div>
                    <div class="cont">
                        <div class="name">陈吉宁</div>
                        <div class="t">上海市委书记</div>
                    </div>
                </a>
            </li> -->
        </ul>

        <!-- 分页 -->
        <?php
            $page = json_encode($page);
            $page = str_replace('&laquo;', 'PREV', $page);
            $page = str_replace('&raquo;', 'NEXT', $page);
            $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
            $page = json_decode($page, true);
        ?>
        <?=$page?>
    </div>