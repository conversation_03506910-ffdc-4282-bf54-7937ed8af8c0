    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Review</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <li><a href="<?=url('enm/review/theme', ['id' => $id])?>">Theme</a></li>
            <li><a href="<?=url('enm/review/schedule', ['id' => $id])?>">Agenda</a></li>
            <li><a href="<?=url('enm/review/guest', ['id' => $id])?>">Speakers</a></li>
            <li><a href="<?=url('enm/review/atlas', ['id' => $id])?>">Photo Gallery</a></li>
            <li><a href="<?=url('enm/review/report', ['id' => $id])?>">Forum Bulletin</a></li>
            <li class="on"><a href="<?=url('enm/review/prints', ['id' => $id])?>">Conference Review</a></li>
        </ul>
    </div>

    <!-- pagePrint -->
    <div class="pagePrint">
        <ul>
            <?php if (!count($row)) { ?>

            <?php } else { foreach ($row as $k => $v) { ?>
                <li class="left">
                    <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                    <div class="cont">
                        <div class="t"><?=$v['title']?></div>
                        <div class="p">Conference Review</div>
                        <a href="<?=$v['url']?>" target="_blank" class="open">Open</a>
                    </div>
                </li>
            <?php } } ?>
        </ul>
    </div>