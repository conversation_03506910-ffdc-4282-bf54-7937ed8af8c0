    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">News</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <?php if (!count($news_sort)) { ?>
            
            <?php } else { foreach ($news_sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                    <a href="<?=url('enm/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>

            <!-- <li class="on"><a href="<?=url('enm/news/news')?>">News</a></li>
            <li><a href="<?=url('enm/news/interview')?>">Video</a></li> -->
        </ul>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <?php if (!count($sort)) { ?>
                                
        <?php } else { foreach ($sort as $k => $v) { ?>
            <div class="t <?=$v['id'] == $sort_id ? 'sel' : '';?>"><?=$v['name']?><p class="iconfont icon-jiantou"></p></div>
        <?php } } ?>
        <ul>
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('enm/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>
    <script>
        $(".pageTab .t").click(function() {
            $(".pageTab ul").slideToggle(300);
            $(this).toggleClass('on')
        })
    </script>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/css/swiper-bundle.min.css"/>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/swiper-bundle.min.js"></script>

    <?php if ($sort_id == 18) { ?>
        <!-- pageNews -->
        <div class="pageNews">
            <ul class="lists">
                <?php if (!count($row)) { ?>
                    <div class="noData">No Data</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>" class="between">
                            <div class="img"><img src="<?=$dir_public . $v['logo']?>?>" alt=""></div>
                            <div class="cont cont1">
                                <div class="tit cut"><?=$v['title']?></div>
                                <div class="txt cutTwo"><?=$v['desc']?></div>
                                <div class="left a">
                                    <p>More</p>
                                    <div class="iconfont icon-arrow"></div>
                                </div>
                            </div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', 'PREV', $page);
                $page = str_replace('&raquo;', 'NEXT', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } else if ($sort_id == 14 || $sort_id == 15 || $sort_id == 16 || $sort_id == 17) { ?>
        <!-- pageActivity -->
        <div class="pageActivity">
            <ul class="lists">
                <?php if (!count($row)) { ?>
                    <div class="noData">No Data</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>">
                            <div class="img">
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                                <div class="label">Conference Session Events</div>
                                <?php if ($sort_id == 14) { ?>
                                    <div class="label">News</div>
                                <?php } ?>
                                <?php if ($sort_id == 15) { ?>
                                    <div class="label">Annual Conference</div>
                                <?php } ?>
                                <?php if ($sort_id == 16) { ?>
                                    <div class="label">Conference Session Events</div>
                                <?php } ?>
                                <?php if ($sort_id == 17) { ?>
                                    <div class="label">Others</div>
                                <?php } ?>
                            </div>
                            <div class="cont">
                                <div class="tit cutTwo"><?=$v['title']?></div>
                                <!-- <?php if ($v['time']) { ?>
                                    <div class="time">
                                        <div class="p left">
                                            <div class="iconfont icon-shijian"></div>
                                            <p>Time: <?=$v['time']?></p>
                                        </div>
                                        <div class="p left">
                                            <div class="iconfont icon-weizhi1"></div>
                                            <p>Address: <?=$v['place']?></p>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <div class="txt cutTwo"><?=$v['desc']?></div>
                                <?php } ?> -->
                                <div class="txt cutTwo"><?=$v['desc']?></div>
                            </div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', 'PREV', $page);
                $page = str_replace('&raquo;', 'NEXT', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } else { ?>
        <!-- pageVideo -->
        <div class="pageVideo">
            <ul class="lists left">
                <?php if (!count($row)) { ?>
                    <div class="noData">No Data</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>">
                            <div class="img">
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                                <div class="iconfont icon-bofang"></div>
                            </div>
                            <div class="tit cut"><?=$v['title']?></div>
                            <div class="t cutTwo"><?=$v['desc']?></div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', 'PREV', $page);
                $page = str_replace('&raquo;', 'NEXT', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } ?>