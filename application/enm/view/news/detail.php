    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">News</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <?php if (!count($news_sort)) { ?>
            
            <?php } else { foreach ($news_sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                    <a href="<?=url('enm/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <?php if (!count($sort)) { ?>
                                
        <?php } else { foreach ($sort as $k => $v) { ?>
            <div class="t <?=$v['id'] == $sort_id ? 'sel' : '';?>"><?=$v['name']?><p class="iconfont icon-jiantou"></p></div>
        <?php } } ?>
        <ul>
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('enm/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>
    <script>
        $(".pageTab .t").click(function() {
            $(".pageTab ul").slideToggle(300);
            $(this).toggleClass('on')
        })
    </script>

    <!-- pageNewsD -->
    <div class="pageNewsD">
        <div class="title"><?=$data['title']?></div>
        <div class="date">
            <?php if ($data['source']) { ?>
                <span>Source: <?=$data['source']?></span> 
            <?php } ?>
            <?php if ($data['author']) { ?>
                <span>Author: <?=$data['author']?></span>
            <?php } ?>
            <span>Time: <?=substr($data['release_time'], 0, 10)?></span>
            <!-- <span>View: <?=$data['visit_num']?></span> -->
        </div>
        <div class="text"><?=str_replace("&nbsp;", ' ', $data['detail'])?></div>
        <div class="box">
            <?php if ($shang) { ?>
                <p><a href="<?=url('enm/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $shang['id']])?>">Prev: <?=$shang['title']?></a></p>
            <?php } else { ?>
                <p><a href="javascript:;">Prev: No More</a></p>
            <?php } ?>
            <?php if ($xiayt) { ?>
                <p><a href="<?=url('enm/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $xiayt['id']])?>">Next: <?=$xiayt['title']?></a></p>
            <?php } else { ?>
                <p><a href="javascript:;">Next: No More</a></p>
            <?php } ?>
        </div>
    </div>