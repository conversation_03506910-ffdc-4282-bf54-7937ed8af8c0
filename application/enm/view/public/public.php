<?php
	use \think\Cookie;
	use \think\Session;
	use \think\Request;
	use \think\Db;
	$request    = Request::instance();
	$dir_public = str_replace('/index.php', '', $request->root());
	$dir_view   = "../application/" . $request->module() . "/view/" . strtolower($request->controller()) . '/' . $request->action();

	/** 查询网站设置信息 **/
	$public_site = Db::table('rule_site')->where('language', 2)->where('genre', 1)->order('id desc')->find();

    /** 查询合作伙伴分类 **/
	$cooperate_sort = Db::table('cooperate_sort')->where('language', 2)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

    /** 查询新闻分类 **/
	$news_sort = Db::table('news_sort')->where('language', 2)->where('genre', 1)->where('pid', 0)->order('sort, id')->where('is_del', 1)->select();
    
	$m_public_url = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

	$web_public_url = str_replace("/enm/", '/en/', $m_public_url);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?=isset($public_title) ? $public_title : '';?></title>
	<meta name="keywords" content="<?=isset($public_key) ? $public_key : '';?>" />
    <meta name="description" content="<?=isset($public_desc) ? $public_desc : '';?>" />
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/iconfont/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/m/css/common.css?v=' . date('YmdHis')?>">
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/m/css/style.css?v=' . date('YmdHis')?>">
    <script type="text/javascript" src="<?=$dir_public?>/m/js/jquery-1.10.1.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/common.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/vue.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/axios.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/suncher.js"></script>
    <script>
		if(!(/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent))) {
			window.location.href = "<?=$web_public_url?>";
		}
	</script>
</head>
<body>

    <!--navBox-->
    <div class="navBox">
        <div class="nav">
            <h3><a href="<?=url('enm/index/index')?>">Home</a></h3>
            <h3><a href="javascript:;">About</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('enm/about/about')?>">About Us</a></li>
                <li><a href="<?=url('enm/about/organization')?>">Organization</a></li>
            </ul>
            <h3><a href="javascript:;">News</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">     
                <?php if (!count($news_sort)) { ?>
                                
                <?php } else { foreach ($news_sort as $k => $v) { ?>
                    <li><a href="<?=url('enm/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a></li>
                <?php } } ?>              
            </ul>
            <h3><a href="<?=url('enm/forum/forum')?>">Agenda</a></h3>
            <!-- <h3><a href="javascript:;">Speakers</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('enm/guest/guest')?>">Speakers</a></li>
                <li><a href="<?=$public_site['guest_uid']?>" target="_blank">Speaker Registration</a></li>
            </ul> -->
            <!-- <h3><a href="javascript:;">Registration</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('enm/participation/participation')?>">Guidelines</a></li>
                <li><a href="<?=$public_site['participation_uid']?>" target="_blank">Delegate Registration</a></li>
            </ul> -->
            <!-- <h3><a href="javascript:;">Information</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('enm/information/service')?>">Shuttle Bus</a></li>
                <li><a href="<?=url('enm/information/hotel')?>">Reservation</a></li>
                <li><a href="<?=url('enm/information/map')?>">Map</a></li>
            </ul> -->
            <!-- <h3><a href="#">名片墙</a></h3> -->
            <h3><a href="<?=url('enm/live/live')?>">Live</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('enm/live/review')?>">Live Review</a></li>
            </ul> 
            <h3><a href="<?=url('enm/review/review')?>">Review</a></h3>
            <h3><a href="javascript:;">Partners</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">   
                <?php if (!count($cooperate_sort)) { ?>
                                
                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                    <?php if ($v['type'] == 1) { ?>
                        <li><a href="<?=url('enm/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>     
                    <?php } else if ($v['type'] == 3) { ?>
                        <li><a href="<?=url('enm/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>  
                    <?php } else if ($v['type'] == 2) { ?>
                        <li><a href="<?=url('enm/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li> 
                    <?php } ?>
                <?php } } ?>
            </ul>
            <!-- <h3><a href="<?=url('enm/download/download')?>">File Download</a></h3> -->
        </div>
    </div>

    <!--headerBox-->
    <div class="headerBox between">
        <a href="<?=url('enm/index/index')?>" class="logo"><img src="<?=$dir_public?>/m/images/logo.png" /></a>
        <div class="box right">
            <div class="language centerT"><a href="<?=url('m/index/index')?>">CN</a><span>/</span><a href="<?=url('enm/index/index')?>" class="on">EN</a></div>
            <a href="<?=$public_site['sign_uid']?>" class="login iconfont icon-denglu"></a>
            <div class="menu">
                <div class="solid solid1"></div>
                <div class="solid solid2"></div>
                <div class="solid solid3"></div>
            </div>
        </div>
    </div>

	<?php require_once("{$dir_view}.php");?>

    <!-- indexFooter -->
    <div class="indexFooter">
        <div class="box">
            <div class="t">Scan to follow</div>
            <div class="between">
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['service']?>" />
                    <p>WeChat Service</p>
                </div>
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['subscribe']?>" />
                    <p>WeChat Subscription</p>
                </div>
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['blog']?>" />
                    <p>Weibo</p>
                </div>
            </div>
        </div>
        <div class="copy">All rights reserved. Pujiang Innovation Forum <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备18034787号-1</a> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备05040256号-8</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402009841" target="_blank">沪公网安备 31010402009841号</a></div>
    </div>

    <!--goTop-->
    <div class="goTop icon-dingbu iconfont"></div>

</body>
</html>