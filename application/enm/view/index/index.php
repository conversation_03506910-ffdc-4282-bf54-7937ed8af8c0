    <!-- indexBanner -->
    <div class="indexBanner swiper-container">
        <div class="swiper-wrapper">
            <?php if (!count($banner)) { ?>
            
            <?php } else { foreach ($banner as $k => $v) { ?>
                <div class="swiper-slide">
                    <a href="javascript:;">
                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                    </a>
                </div>
            <?php } } ?>
        </div>
        <div class="swiper-pagination"></div>
    </div>

    <!-- indexNews -->
    <div class="indexNews">
        <div class="indexTitle">
            <div class="title">NEWS</div>
            <!-- <div class="p">新闻中心</div> -->
        </div>

        <?php if (!count($news)) { ?>
                            
        <?php } else { foreach ($news as $k => $v) { if ($k < 1) { ?>
            <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>" class="contBox">
                <div class="img"><img src="<?=$dir_public . $v['logo']?>" alt=""></div>
                <div class="cont">
                    <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                    <div class="tit cut"><?=$v['title']?></div>
                    <div class="txt cutTwo"><?=$v['desc']?></div>
                </div>
            </a>
        <?php } } } ?>

        <div class="newsBox swiper-container">
            <div class="swiper-wrapper">
                <?php if (!count($news)) { ?>
                                
                <?php } else { foreach ($news as $k => $v) { if ($k >= 1) { ?>
                    <div class="swiper-slide">
                        <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>">
                            <div class="time"><?=substr($v['release_time'], 0, 10)?></div>
                            <div class="tit cutTwo"><?=$v['title']?></div>
                            <div class="txt cutTwo"><?=$v['desc']?></div>
                        </a>
                    </div>
                <?php } } } ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        
        <a href="<?=url('enm/news/news')?>" class="indexMore centerT">
            <p>More</p>
            <div class="iconfont icon-arrow"></div>
        </a>
    </div>

    <!-- indexAd -->
    <div class="indexAd">
        <div class="t">Sharing Innovation and Shaping the Future:<br />Towards an Open Environment for Scientific and Technological Innovation</div>
        <img src="<?=$dir_public?>/m/images/icon_arrow.png" alt="">
    </div>

    <!-- indexSchedule -->
    <div class="indexSchedule" id="app">
        <div class="indexTitle">
            <div class="title">FORUM AGENDA</div>
            <!-- <div class="p">论坛日程</div> -->
        </div>

        <ul class="timeBox centerT">
            <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07 00:00:00','2024-09-07 23:59:59', 1)">09-07</li>
            <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08 00:00:00','2024-09-08 23:59:59', 2)">09-08</li>
            <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09 00:00:00','2024-09-09 23:59:59', 3)">09-09</li>
        </ul>

        <ul class="scheduleBox">
            <li>
                <div v-for="(item,index) in lists" :key="index" @click="select(index)">
                    <div class="liBox" v-if="item.lt.FC1I3KSV08QWDZVDU">
                        <div class="Box" :class="sel === index ? 'on' : ''">
                            <div class="between">
                                <div class="tit">{{item.lt.FC1I3KSV02QWDZVUF}}</div>
                                <!-- <div class="label label1 centerT" v-if="item.lt.forumtime_type == 1">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>Not started</p>
                                </div>
                                <div class="label label2 centerT" v-if="item.lt.forumtime_type == 2">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>Live</p>
                                </div>
                                <div class="label label3 centerT" v-if="item.lt.forumtime_type == 3">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>Ended</p>
                                </div> -->
                            </div>
                            <div class="address">
                                <div class="left">
                                    <div class="iconfont icon-weizhi1"></div>
                                    <p>Room: {{item.lt.FC1I3KSV05GWDZVPB_name}}</p>
                                </div>
                                <div class="left">
                                    <div class="iconfont icon-shijian"></div>
                                    <p>Time: {{item.lt.start_time}} - {{item.lt.end_time}}</p>
                                </div>
                            </div>
                            <div class="btnBox between">
                                <!-- <a href="javascrupt:;" class="btn btn2" v-if="item.lt.forumtime_type == 1">Await</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 2">Live</a>
                                <a :href="item.lt.FC1I4JMDTNDWSUFPM" class="btn btn1" v-if="item.lt.forumtime_type == 3">Playback</a> -->
                                <div></div>
                                <div class="iconfont icon-jiantou"></div>
                            </div>
                        </div>
                        <div class="contBox" :class="sel === index ? 'sel' : ''">
                            <div class="cont">
                                <!-- <div class="t">论坛地点：中国•上海</div> -->
                                <div class="t" v-if="item.dw_data != ''">
                                    <div class="left" v-if="item.dw_data.a">
                                        <p class="p">Organizer: </p>
                                        <p class="p1"><span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div>
                                    <div class="left" v-if="item.dw_data.b">
                                        <p class="p">Co-Organizer: </p>
                                        <p class="p1"><span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div>
                                    <div class="left" v-if="item.dw_data.c">
                                        <p class="p">Supporter: </p>
                                        <p class="p1"><span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span></p>
                                    </div>
                                    <!-- <p v-if="item.dw_data.a">Organizer：
                                        <span v-for="(it,inx) in item.dw_data.a" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.b">Co-Organizer：
                                        <span v-for="(it,inx) in item.dw_data.b" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p>
                                    <p v-if="item.dw_data.c">Supporter：
                                        <span v-for="(it,inx) in item.dw_data.c" :key="inx">{{it.FC1I3KT05BDWDZVDN}}<em>、</em></span>
                                    </p> -->
                                </div>
                                <div class="between" v-if="item.lt.FC1I3KSV03JWDZVUB">
                                    <div class="t1">Theme</div>
                                    <!-- <div class="show right">
                                        <p>点击展开</p>
                                        <div class="iconfont icon-jiantou"></div>
                                    </div> -->
                                </div>
                                <div class="t2">{{item.lt.FC1I3KSV03JWDZVUB}}</div>
                            </div>
                            <div class="dl" v-if="item.yc_row != ''">
                                <div v-for="(innerItem,innerIndex) in item.yc_row" :key="innerIndex">
                                    <div class="dt between" v-if="!innerItem.FC1I3KT0NT6WDZVMF">
                                        <div class="time">{{innerItem.duration_sd}}</div>
                                        <div class="dd">
                                            <div class="t3">{{innerItem.topicEn}}</div>
                                            <div v-if="innerItem.FC1I3KT0NR7WDZVAE != 'a' && innerItem.FC1I3KT0NR7WDZVAE != 'e'">
                                                <div class="li left" v-for="(inItem,inIndex) in innerItem.associatedAttendee" :key="inIndex">
                                                    <div class="img"><img :src="'https://fs.31huiyi.com/' + inItem.avatar" alt=""></div>
                                                    <div class="t4">
                                                        <div class="name">{{inItem.stringField22}}</div>
                                                        <div class="t5">{{inItem.stringField18}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <a href="<?=url('enm/forum/forum')?>" class="indexMore centerT">
                    <p>More</p>
                    <div class="iconfont icon-arrow"></div>
                </a>
            </li>
        </ul>
    </div>
    <script>
        new Vue({
            el: '#app',
            data: {
                start_time: '2024-09-07 00:00:00',
                end_time: '2024-09-07 23:59:59',
                lists: [],
                sel: null, 
                sele: 1,
            },
            created() {
                this.forumList(this.start_time, this.end_time)
            },
            methods: {
                changeTime(start, end, index) {
                    this.forumList(start, end)
                    this.sele = index
                    this.sel = null
                },

                forumList(start, end){
                    var that = this
                    axios({
                        method: 'post',
                        url: '<?=url('api/schedule/schedule_zs')?>',
                        data: {
                            start_time: start,
                            end_time: end,
                        }
                    }).then((res) => {
                        // console.log(res.data.data);
                        if (res.data.code == 200) {
                            let lists = res.data.data
                            lists.forEach((item, index) => {
                                let now = new Date().getTime()
                                let startTime = new Date(item.lt.forumtime[0]).getTime()
                                let endTime = new Date(item.lt.forumtime[1]).getTime()
                                item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                                item.lt.end_time = item.lt.forumtime[1].slice(10, 16)
                                if(now < startTime) {
                                    item.lt.forumtime_type = 1  // 未开始
                                }
                                if(now > startTime && now < endTime) {
                                    item.lt.forumtime_type = 2  // 进行中
                                }
                                if(now > endTime) {
                                    item.lt.forumtime_type = 3  // 已结束
                                }
                            })
                            this.lists = lists
                        }
                    })
                },

                select(index) {
                    if (this.sel === index) {
                        this.sel = null; // 再次点击当前项时收起
                    } else {
                        this.sel = index; // 点击其他项时当前项展开，其他项收起
                    }
                    // this.sel = index
                }
            }
        })
    </script>

    <!-- indexVideo -->
    <div class="indexVideo">
        <div class="indexTitle">
            <div class="title">FORUM VIDEO</div>
            <!-- <div class="p">论坛视频</div> -->
        </div>
        <ul class="lists">
            <li>
                <div class="videoBox videoBox1 swiper-container">
                    <div class="swiper-wrapper">
                        <?php if (!count($videos)) { ?>
                        
                        <?php } else { foreach ($videos as $k => $v) { ?>
                            <div class="swiper-slide">
                                <a href="<?=url('enm/news/detail', ['id' => $v['id']])?>">
                                    <div class="img">
                                        <img src="<?=$dir_public . $v['logo']?>" alt="" class="pic">
                                        <img src="<?=$dir_public?>/m/images/icon_play.png" alt="" class="play">
                                    </div>
                                    <div class="tit"><p class="cutTwo"><?=$v['title']?></p></div>
                                </a>
                            </div>
                        <?php } } ?>
                    </div>
                    <div class="swiper-pagination"></div>
                    <a href="<?=url('enm/news/news', ['pid' => 13, 'sort_id' => 19])?>" class="indexMore centerT">
                        <p>More</p>
                        <div class="iconfont icon-arrow"></div>
                    </a>
                </div>
            </li>
        </ul>
    </div>

    <!-- indexPartner -->
    <div class="indexPartner">
        <?php if (!count($cooperatesort)) { ?>

        <?php } else { foreach ($cooperatesort as $k => $v) { ?>
            <div class="box">
                <div class="p"><?=$v['name']?></div>
                <ul class="left">
                    <?php if (!count($v['cooperate'])) { ?>

                    <?php } else { foreach ($v['cooperate'] as $kk => $vv) { ?>
                        <li>
                            <?php if ($vv['url']) { ?>
                                <a href="<?=$vv['url']?>" target="_blank">
                            <?php } else { ?>
                                <a href="javascrupt:;">
                            <?php } ?>
                                <img src="<?=$dir_public . $vv['logo']?>" alt="">
                            </a>
                        </li>
                    <?php } } ?>
                </ul>
            </div>
        <?php } } ?>

        <div class="box">
            <div class="p">Media Partners</div>
            <ul class="left">
                <?php if (!count($medium)) { ?>

                <?php } else { foreach ($medium as $k => $v) { ?>
                    <li>
                        <?php if ($v['url']) { ?>
                            <a href="<?=$v['url']?>" target="_blank">
                        <?php } else { ?>
                            <a href="javascrupt:;">
                        <?php } ?>
                            <img src="<?=$dir_public . $v['logo']?>" alt="">
                        </a>
                    </li>
                <?php } } ?>
            </ul>
        </div>
    </div>


    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/css/swiper-bundle.min.css"/>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/swiper-bundle.min.js"></script>
    <script>
        var indexBanner = new Swiper('.indexBanner', {
            loop: true,
            autoPlay:true,
            autoplay: {
                delay: 5000,
            },
            pagination: {
                el: '.indexBanner .swiper-pagination',
                clickable: true,
            },
        });

        var newsBox = new Swiper('.newsBox', {
            loop: true,
            autoPlay:true,
            autoplay: {
                delay: 5000,
            },
            pagination: {
                el: '.newsBox .swiper-pagination',
                clickable: true,
            },
        });

        
        var videoBox1 = new Swiper(".videoBox1", {
            slidesPerView: "auto",
            centeredSlides: true,
            // spaceBetween: 50,
            // slidesPerView: 2,
            observer: true, 
            observeParents: true,
            loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            // navigation: {
            //     nextEl: '.videoBox1 .swiper-button-next',
            //     prevEl: '.videoBox1 .swiper-button-prev',
            // },
            pagination: {
                el: '.videoBox1 .swiper-pagination',
                clickable: true,
            }
        });

        var videoBox2 = new Swiper(".videoBox2", {
            slidesPerView: "auto",
            centeredSlides: true,
            // spaceBetween: 50,
            // slidesPerView: 2,
            observer: true, 
            observeParents: true,
            loop: true,
            // autoplay: {
            //     delay: 5000,
            // },
            // navigation: {
            //     nextEl: '.videoBox2 .swiper-button-next',
            //     prevEl: '.videoBox2 .swiper-button-prev',
            // },
            pagination: {
                el: '.videoBox2 .swiper-pagination',
                clickable: true,
            }
        });
    </script>