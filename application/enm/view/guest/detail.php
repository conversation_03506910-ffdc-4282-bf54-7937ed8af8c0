    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Speakers</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <li class="on"><a href="<?=url('enm/guest/guest')?>">Speakers</a></li>
            <li><a href="<?=$public_site['guest_uid']?>">Speaker Registration</a></li>
        </ul>
    </div>

    <!-- pageCharacterD -->
    <div class="pageCharacterD">
        <div class="swiper-container gallery-top">
            <div class="swiper-wrapper">
                <?php if (!count($row)) { ?>

                <?php } else { foreach ($row as $k => $v) { ?>
                    <div class="swiper-slide" data-hash="<?=$v['id']?>">
                        <div class="between">
                            <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                            <div class="cont">
                                <?php if ($v['StringField22'] != '') { ?>
                                    <div class="name"><?=$v['StringField22']?></div>
                                <?php } else { ?>
                                    <div class="name"><?=$v['FullName']?></div>
                                <?php } ?>
                                <div class="t"><?=$v['Position']?></div>
                            </div>
                        </div>
                    </div>
                <?php } } ?>
            </div>
            <div class="arrow">
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/css/swiper-bundle.min.css"/>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/swiper-bundle.min.js"></script>
    <script>
        var galleryTop = new Swiper('.gallery-top', {
            hashNavigation: true,
            spaceBetween: 20,
            loop:true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            }
        });
    </script>