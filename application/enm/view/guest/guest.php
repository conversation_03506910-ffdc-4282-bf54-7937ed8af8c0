    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>Location: </p>
            <a href="<?=url('enm/index/index')?>">Home</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">Speakers</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <li class="on"><a href="<?=url('enm/guest/guest')?>">Speakers</a></li>
            <li><a href="<?=$public_site['guest_uid']?>">Speaker Registration</a></li>
        </ul>
    </div>

    <!-- pageCharacter -->
    <div class="pageCharacter">
        <ul class="lists left">
            <?php if (!count($row)) { ?>
                <div class="noData">No Data</div>
            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <a href="<?=url('enm/guest/detail')?>#<?=$v['id']?>" class="between">
                        <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                        <div class="cont">
                            <?php if ($v['StringField22'] != '') { ?>
                                <div class="name"><?=$v['StringField22']?></div>
                            <?php } else { ?>
                                <div class="name"><?=$v['FullName']?></div>
                            <?php } ?>
                            <div class="p"><?=$v['Position']?></div>
                            <div class="a left">
                                <p>More</p>
                                <div class="iconfont icon-arrow"></div>
                            </div>
                        </div>
                    </a>
                </li>
            <?php } } ?>
        </ul>

        <!-- 分页 -->
        <?php
            $page = json_encode($page);
            $page = str_replace('&laquo;', 'PREV', $page);
            $page = str_replace('&raquo;', 'NEXT', $page);
            $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>' . input('page', 1) . '<\/span>', $page);
            $page = json_decode($page, true);
        ?>
        <?=$page?>
    </div>