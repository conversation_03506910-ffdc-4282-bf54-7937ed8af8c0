    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">会务信息</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <li><a href="<?=url('m/information/service')?>">班车信息</a></li>
            <li class="on"><a href="<?=url('m/information/hotel')?>">推荐酒店</a></li>
            <li><a href="<?=url('m/information/map')?>">会场地图</a></li>
        </ul>
    </div>

    <!-- pageHotel -->
    <div class="pageHotel">
        <div class="noData">敬请期待</div>
        
        <!-- <div class="pageTitle">推荐酒店</div>
        <div class="t"><?=$data['title']?></div>
        <div class="cont">
            <div class="img"><img src="<?=$dir_public . $data['logo']?>" alt=""></div>
            <div class="txt"><?=str_replace("\n", '<br>', $data['hotel'])?></div>
        </div>
        <div class="t"><?=$data['desc']?></div>
        <div class="t1">酒店交通环境</div>
        <table>
            <tr>
                <th>起始地</th>
                <th>名称/位置</th>
                <th>距酒店公里数</th>
                <th>如何到达酒店</th>
            </tr>
            <?php if (!count($row)) { ?>

            <?php } else { foreach ($row as $k => $v) { ?>
                <tr>
                    <td><?=$v['start_address']?></td>
                    <td><?=$v['name']?></td>
                    <td><?=$v['distance']?></td>
                    <td><?=$v['method']?></td>
                </tr>
            <?php } } ?>
        </table>
        <div class="tips">* <?=$data['book_time']?></div>
        <div class="t1">住宿预订</div>
        <div class="t"><?=str_replace("\n", '<br>', $data['book_lodging'])?></div>
        <div class="p">预订单下载</div>
        <div class="box">
            <div class="t2"><?=$data['url_title']?></div>
            <a href="<?=$data['url']?>" download="<?=$data['url_name']?>">下载文件</a>
        </div>
        <div class="map"><?=$data['plat']?></div> -->
    </div>