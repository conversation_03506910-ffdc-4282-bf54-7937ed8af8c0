    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">演讲嘉宾</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <li class="on"><a href="<?=url('m/guest/guest')?>">人物风采</a></li>
            <li><a href="<?=$public_site['guest_uid']?>">嘉宾注册</a></li>
        </ul>
    </div>

    <!-- pageCharacterD -->
    <div class="pageCharacterD">
        <div class="swiper-container gallery-top">
            <div class="swiper-wrapper">
                <?php if (!count($row)) { ?>

                <?php } else { foreach ($row as $k => $v) { ?>
                    <div class="swiper-slide" data-hash="<?=$v['id']?>">
                        <div class="between">
                            <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                            <div class="cont">
                                <div class="name"><?=$v['FullName']?></div>
                                <div class="t"><?=$v['Position']?></div>
                            </div>
                        </div>
                    </div>
                <?php } } ?>
            </div>
            <div class="arrow">
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/css/swiper-bundle.min.css"/>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/swiper-bundle.min.js"></script>
    <script>
        var galleryTop = new Swiper('.gallery-top', {
            hashNavigation: true,
            spaceBetween: 20,
            loop:true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            }
        });
    </script>