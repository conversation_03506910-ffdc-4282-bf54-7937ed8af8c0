    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">演讲嘉宾</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <li class="on"><a href="<?=url('m/guest/guest')?>">人物风采</a></li>
            <li><a href="<?=$public_site['guest_uid']?>">嘉宾注册</a></li>
        </ul>
    </div>

    <!-- pageCharacter -->
    <div class="pageCharacter">
        <ul class="lists left">
            <?php if (!count($row)) { ?>
                <div class="noData">暂无数据</div>
            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <a href="<?=url('m/guest/detail')?>#<?=$v['id']?>" class="between">
                        <div class="img"><img src="<?=$v['Avatar']?>" alt=""></div>
                        <div class="cont">
                            <div class="name"><?=$v['FullName']?></div>
                            <div class="p"><?=$v['Position']?></div>
                            <div class="a left">
                                <p>查看更多</p>
                                <div class="iconfont icon-arrow"></div>
                            </div>
                        </div>
                    </a>
                </li>
            <?php } } ?>
        </ul>

        <!-- 分页 -->
        <?php
            $page = json_encode($page);
            $page = str_replace('&laquo;', '上一页', $page);
            $page = str_replace('&raquo;', '下一页', $page);
            $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>第' . input('page', 1) . '页<\/span>', $page);
            $page = json_decode($page, true);
        ?>
        <?=$page?>
    </div>