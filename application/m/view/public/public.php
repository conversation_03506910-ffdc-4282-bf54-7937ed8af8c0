<?php
	use \think\Cookie;
	use \think\Session;
	use \think\Request;
	use \think\Db;
	$request    = Request::instance();
	$dir_public = str_replace('/index.php', '', $request->root());
	$dir_view   = "../application/" . $request->module() . "/view/" . strtolower($request->controller()) . '/' . $request->action();

	/** 查询网站设置信息 **/
	$public_site = Db::table('rule_site')->where('language', 1)->where('genre', 1)->order('id desc')->find();

    /** 查询合作伙伴分类 **/
	$cooperate_sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->select();

    /** 查询新闻分类 **/
	$news_sort = Db::table('news_sort')->where('language', 1)->where('genre', 1)->where('pid', 0)->order('sort, id')->where('is_del', 1)->select();
    
	$m_public_url = 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

	$web_public_url = str_replace("/m/", '/web/', $m_public_url);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title><?=isset($public_title) ? $public_title : '';?></title>
	<meta name="keywords" content="<?=isset($public_key) ? $public_key : '';?>" />
    <meta name="description" content="<?=isset($public_desc) ? $public_desc : '';?>" />
    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/iconfont/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/m/css/common.css?v=' . date('YmdHis')?>">
	<link rel="stylesheet" type="text/css" href="<?=$dir_public.'/m/css/style.css?v=' . date('YmdHis')?>">
    <script type="text/javascript" src="<?=$dir_public?>/m/js/jquery-1.10.1.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/common.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/vue.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/web/js/axios.min.js"></script>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/suncher.js"></script>
    <script>
		if(!(/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent))) {
			window.location.href = "<?=$web_public_url?>";
		}
	</script>
</head>
<body>

    <!--navBox-->
    <div class="navBox">
        <div class="nav">
            <h3><a href="<?=url('m/index/index')?>">首页</a></h3>
            <h3><a href="javascript:;">论坛介绍</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('m/about/about')?>">论坛简介</a></li>
                <li><a href="<?=url('m/about/organization')?>">组织机构</a></li>
            </ul>
            <h3><a href="javascript:;">新闻中心</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">    
                <?php if (!count($news_sort)) { ?>
                
                <?php } else { foreach ($news_sort as $k => $v) { ?>
                    <li><a href="<?=url('m/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a></li>
                <?php } } ?>               
            </ul>
            <h3><a href="<?=url('m/forum/forum')?>">论坛日程</a></h3>
            <!-- <h3><a href="javascript:;">演讲嘉宾</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('m/guest/guest')?>">人物风采</a></li>
                <li><a href="<?=$public_site['guest_uid']?>">嘉宾注册</a></li>
            </ul> -->
            <!-- <h3><a href="javascript:;">报名参会</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('m/participation/participation')?>">参会说明</a></li>
                <li><a href="<?=$public_site['participation_uid']?>">参会注册</a></li>
            </ul> -->
            <!-- <h3><a href="javascript:;">会务信息</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('m/information/service')?>">班车信息</a></li>
                <li><a href="<?=url('m/information/hotel')?>">推荐酒店</a></li>
                <li><a href="<?=url('m/information/map')?>">会场地图</a></li>
            </ul> -->
            <!-- <h3><a href="<?=$public_site['business_uid']?>">名片墙</a></h3> -->
            <h3><a href="<?=url('m/live/live')?>">直播中心</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">                   
                <li><a href="<?=url('m/live/review')?>">直播回顾</a></li>
            </ul>
            <h3><a href="<?=url('m/review/review')?>">往届回顾</a></h3>
            <h3><a href="javascript:;">合作伙伴</a><div class="iconfont icon-jiantou"></div></h3>
            <ul class="sub">  
                <?php if (!count($cooperate_sort)) { ?>
                                
                <?php } else { foreach ($cooperate_sort as $k => $v) { ?>
                    <?php if ($v['type'] == 1) { ?>
                        <li><a href="<?=url('m/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>   
                    <?php } else if ($v['type'] == 3) { ?>
                        <li><a href="<?=url('m/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li>     
                    <?php } else if ($v['type'] == 2) { ?>
                        <li><a href="<?=url('m/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a></li> 
                    <?php } ?>
                <?php } } ?>
            </ul>
            <!-- <h3><a href="<?=url('m/download/download')?>">文件下载</a></h3> -->
        </div>
    </div>

    <!--headerBox-->
    <div class="headerBox between">
        <a href="<?=url('m/index/index')?>" class="logo"><img src="<?=$dir_public . $public_site['logo']?>" /></a>
        <div class="box right">
            <div class="language centerT"><a href="<?=url('m/index/index')?>" class="on">CN</a><span>/</span><a href="<?=url('enm/index/index')?>">EN</a></div>
            <a href="<?=$public_site['sign_uid']?>" class="login iconfont icon-denglu"></a>
            <div class="menu">
                <div class="solid solid1"></div>
                <div class="solid solid2"></div>
                <div class="solid solid3"></div>
            </div>
        </div>
    </div>

	<?php require_once("{$dir_view}.php");?>

    <!-- indexFooter -->
    <div class="indexFooter">
        <div class="box">
            <div class="t">扫一扫加关注</div>
            <div class="between">
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['service']?>" alt="">
                    <p>官方微信服务号</p>
                </div>
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['subscribe']?>" alt="">
                    <p>官方微信订阅号</p>
                </div>
                <div class="rwm">
                    <img src="<?=$dir_public . $public_site['blog']?>" alt="">
                    <p>官方微博</p>
                </div>
            </div>
        </div>
        <div class="copy">版权所有 上海浦江创新论坛中心 <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备18034787号-1</a> <a href="http://beian.miit.gov.cn/" target="_blank">沪ICP备05040256号-8</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402009841" target="_blank">沪公网安备 31010402009841号</a></div>
    </div>

    <!--goTop-->
    <div class="goTop icon-dingbu iconfont"></div>

</body>
</html>