    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">新闻中心</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <?php if (!count($news_sort)) { ?>
            
            <?php } else { foreach ($news_sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                    <a href="<?=url('m/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <?php if (!count($sort)) { ?>
                                
        <?php } else { foreach ($sort as $k => $v) { ?>
            <div class="t <?=$v['id'] == $sort_id ? 'sel' : '';?>"><?=$v['name']?><p class="iconfont icon-jiantou"></p></div>
        <?php } } ?>
        <ul>
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('m/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>
    <script>
        $(".pageTab .t").click(function() {
            $(".pageTab ul").slideToggle(300);
            $(this).toggleClass('on')
        })
    </script>

    <link rel="stylesheet" type="text/css" href="<?=$dir_public?>/m/css/swiper-bundle.min.css"/>
    <script type="text/javascript" src="<?=$dir_public?>/m/js/swiper-bundle.min.js"></script>


    <?php if ($sort_id == 7) { ?>
        <!-- pageNews -->
        <div class="pageNews">
            <ul class="lists">
                <?php if (!count($row)) { ?>
                    <div class="noData">暂无数据</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('m/news/detail', ['id' => $v['id']])?>" class="between">
                            <div class="img"><img src="<?=$dir_public . $v['logo']?>?>" alt=""></div>
                            <div class="cont cont1">
                                <div class="tit cut"><?=$v['title']?></div>
                                <div class="txt cutTwo"><?=$v['desc']?></div>
                                <div class="left a">
                                    <p>查看更多</p>
                                    <div class="iconfont icon-arrow"></div>
                                </div>
                            </div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', '上一页', $page);
                $page = str_replace('&raquo;', '下一页', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>第' . input('page', 1) . '页<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } else if ($sort_id == 3 || $sort_id == 4 || $sort_id == 5 || $sort_id == 6) { ?>
        <!-- pageActivity -->
        <div class="pageActivity">
            <ul class="lists">
                <?php if (!count($row)) { ?>
                    <div class="noData">暂无数据</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('m/news/detail', ['id' => $v['id']])?>">
                            <div class="img">
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                                <?php if ($sort_id == 3) { ?>
                                    <div class="label">论坛动态</div>
                                <?php } ?>
                                <?php if ($sort_id == 4) { ?>
                                    <div class="label">浦江发布</div>
                                <?php } ?>
                                <?php if ($sort_id == 5) { ?>
                                    <div class="label">会议季活动</div>
                                <?php } ?>
                                <?php if ($sort_id == 6) { ?>
                                    <div class="label">其他</div>
                                <?php } ?>
                            </div>
                            <div class="cont">
                                <div class="tit cutTwo"><?=$v['title']?></div>
                                <!-- <?php if ($v['time']) { ?>
                                    <div class="time">
                                        <div class="p left">
                                            <div class="iconfont icon-shijian"></div>
                                            <p>时间：<?=$v['time']?></p>
                                        </div>
                                        <div class="p left">
                                            <div class="iconfont icon-weizhi1"></div>
                                            <p>地点：<?=$v['place']?></p>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <div class="txt cutTwo"><?=$v['desc']?></div>
                                <?php } ?> -->
                                <div class="txt cutTwo"><?=$v['desc']?></div>
                            </div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', '上一页', $page);
                $page = str_replace('&raquo;', '下一页', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>第' . input('page', 1) . '页<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } else { ?>
        <!-- pageVideo -->
        <div class="pageVideo">
            <ul class="lists left">
                <?php if (!count($row)) { ?>
                    <div class="noData">暂无数据</div>
                <?php } else { foreach ($row as $k => $v) { ?>
                    <li>
                        <a href="<?=url('m/news/detail', ['id' => $v['id']])?>">
                            <div class="img">
                                <img src="<?=$dir_public . $v['logo']?>" alt="">
                                <div class="iconfont icon-bofang"></div>
                            </div>
                            <div class="tit cut"><?=$v['title']?></div>
                            <div class="t cutTwo"><?=$v['desc']?></div>
                        </a>
                    </li>
                <?php } } ?>
            </ul>

            <!-- 分页 -->
            <?php
                $page = json_encode($page);
                $page = str_replace('&laquo;', '上一页', $page);
                $page = str_replace('&raquo;', '下一页', $page);
                $page = str_replace('<span>' . input('page', 1) . '<\/span>', '<span>第' . input('page', 1) . '页<\/span>', $page);
                $page = json_decode($page, true);
            ?>
            <?=$page?>
        </div>
    <?php } ?>