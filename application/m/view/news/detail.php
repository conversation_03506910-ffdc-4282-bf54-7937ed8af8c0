    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">新闻中心</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav around">
            <?php if (!count($news_sort)) { ?>
            
            <?php } else { foreach ($news_sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $pid ? 'on' : '';?>">
                    <a href="<?=url('m/news/news', ['pid' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pageTab -->
    <div class="pageTab">
        <?php if (!count($sort)) { ?>
                                
        <?php } else { foreach ($sort as $k => $v) { ?>
            <div class="t <?=$v['id'] == $sort_id ? 'sel' : '';?>"><?=$v['name']?><p class="iconfont icon-jiantou"></p></div>
        <?php } } ?>
        <ul>
            <?php if (!count($sort)) { ?>
                                
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <a href="<?=url('m/news/news', ['pid' => $pid, 'sort_id' => $v['id']])?>"><?=$v['name']?></a>
                </li>
            <?php } } ?>
        </ul>
    </div>
    <script>
        $(".pageTab .t").click(function() {
            $(".pageTab ul").slideToggle(300);
            $(this).toggleClass('on')
        })
    </script>

    <!-- pageNewsD -->
    <div class="pageNewsD">
        <div class="title"><?=$data['title']?></div>
        <div class="date">
            <?php if ($data['source']) { ?>
                <span>来源：<?=$data['source']?></span> 
            <?php } ?>
            <?php if ($data['author']) { ?>
                <span>作者：<?=$data['author']?></span>
            <?php } ?>
            <span>发布时间：<?=substr($data['release_time'], 0, 10)?></span>
            <!-- <span>访问量：<?=$data['visit_num']?></span> -->
        </div>
        <div class="text"><?=$data['detail']?></div>
        <div class="box">
            <?php if ($shang) { ?>
                <p><a href="<?=url('m/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $shang['id']])?>">上一条：<?=$shang['title']?></a></p>
            <?php } else { ?>
                <p><a href="javascript:;">上一条：暂无更多</a></p>
            <?php } ?>
            <?php if ($xiayt) { ?>
                <p><a href="<?=url('m/news/detail', ['pid' => $pid, 'sort_id' => $sort_id, 'id' => $xiayt['id']])?>">下一条：<?=$xiayt['title']?></a></p>
            <?php } else { ?>
                <p><a href="javascript:;">下一条：暂无更多</a></p>
            <?php } ?>
        </div>
    </div>