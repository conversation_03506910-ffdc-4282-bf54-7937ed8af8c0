    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">合作伙伴</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <?php if (!count($sort)) { ?>
					
            <?php } else { foreach ($sort as $k => $v) { ?>
                <li class="<?=$v['id'] == $sort_id ? 'on' : '';?>">
                    <?php if ($v['type'] == 1) { ?>
                        <a href="<?=url('m/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } else if ($v['type'] == 3) { ?>
                        <a href="<?=url('m/partner/partner', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } else if ($v['type'] == 2) { ?>
                        <a href="<?=url('m/partner/cooperation', ['sort_id' => $v['id']])?>"><?=$v['name']?></a>
                    <?php } ?>
                </li>
            <?php } } ?>
        </ul>
    </div>

    <!-- pagePartner -->
    <div class="pagePartner">
        <ul class="left">
            <?php if (!count($row)) { ?>
                <div class="noData">暂无数据</div>
            <?php } else { foreach ($row as $k => $v) { ?>
                <li>
                    <?php if ($v['url']) { ?>
                        <a href="<?=$v['url']?>" target="_blank">
                    <?php } else { ?>
                        <a href="javascrupt:;">
                    <?php } ?>
                        <img src="<?=$dir_public . $v['logo']?>" alt="">
                    </a>
                </li>
            <?php } } ?>
        </ul>
    </div>