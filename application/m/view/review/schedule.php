    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/m/images/pageBg.jpg) center center no-repeat;">
        <div class="location left">
            <div class="iconfont icon-shouye sel"></div>
            <p>当前位置：</p>
            <a href="/">首页</a>
            <span class="iconfont icon-jiantou"></span>
            <a href="javascript:;" class="on">往届回顾</a>
        </div>
    </div>

    <!-- pageNavBox -->
    <div class="pageNavBox">
        <ul class="pageNav between">
            <li><a href="<?=url('m/review/theme', ['id' => $id])?>">论坛主题</a></li>
            <li class="on"><a href="<?=url('m/review/schedule', ['id' => $id])?>">论坛日程</a></li>
            <li><a href="<?=url('m/review/guest', ['id' => $id])?>">演讲嘉宾</a></li>
            <li><a href="<?=url('m/review/atlas', ['id' => $id])?>">图片集锦</a></li>
            <li><a href="<?=url('m/review/report', ['id' => $id])?>">专题报告集</a></li>
            <li><a href="<?=url('m/review/prints', ['id' => $id])?>">会后刊</a></li>
        </ul>
    </div>

    <!-- pageSchedule -->
    <div class="pageSchedule">
        <div class="title"><?=$data[$id]['schedule_title']?></div>
        <div class="p">
            <?php if ($data[$id]['schedule_day']) {?>
                年会会期：<?=$data[$id]['schedule_day']?><span>│</span>
            <?php } ?>
            会议主会场：<?=$data[$id]['schedule_main']?>
            <?php if ($data[$id]['schedule_exhibit']) {?>
                <span>│</span>展览主会场：<?=$data[$id]['schedule_exhibit']?>
            <?php } ?>
        </div>

        <?php if (!count($sort)) { ?>

        <?php } else { foreach ($sort as $k => $v) { ?>
            <?php if (strpos($v['title'], '月') !== false && strpos($v['title'], '日') !== false) {?>
                <div class="time"><?=$v['title']?></div>
            <?php } else { ?>
                <div class="time time1"><?=$v['title']?></div>
            <?php } ?>
            <table>
                <tr>
                    <th>会议时间</th>
                    <th>会议内容</th>
                    <th>会议地点</th>
                </tr>
                <?php if (!count($v['row'])) { ?>

                <?php } else { foreach ($v['row'] as $kk => $vv) { ?>
                    <tr>
                        <?php if ($vv['time']) {?>
                            <?php if (isset($vv['time_merge'])) { ?>
                                <td rowspan="<?=$vv['time_merge']?>"><?=$vv['time']?></td>
                            <?php } else { ?>
                                <td><?=$vv['time']?></td>
                            <?php } ?>
                        <?php } ?>
                        <td><?=$vv['content']?></td>
                        <?php if ($vv['place']) {?>
                            <?php if (isset($vv['place_merge'])) { ?>
                                <td rowspan="<?=$vv['place_merge']?>"><?=$vv['place']?></td>
                            <?php } else { ?>
                                <td><?=$vv['place']?></td>
                            <?php } ?>
                        <?php } ?>
                    </tr>
                <?php } } ?>
            </table>
        <?php } } ?>
        <div class="tips">* 论坛议程以现场实际为准，最终调整和解释权归论坛秘书处所有</div>
    </div>