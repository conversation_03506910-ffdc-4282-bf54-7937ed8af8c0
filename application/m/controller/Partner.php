<?php
namespace app\m\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：合作伙伴 **/
class Partner extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：合作伙伴 **/
	/** 作者：穠@20230328 **/
    public function partner() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');   //分类id

		/** 查询合作伙伴信息 **/
		$row = Db::table('cooperate')->where('language', 1)->where('genre', 1)->order('sort, id')->where('sort_id', $sort_id)->where('is_del', 1)->select();

		/** 查询分类 **/
		$sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,tdk_key,desc,detail,type');

		$this->assign('row', $row);
		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $sort[$sort_id]['name']);
		$this->assign('public_key', $sort[$sort_id]['tdk_key']);
		$this->assign('public_desc', $sort[$sort_id]['desc']);
        return $this->fetch('m@public/public');
	}

	/** 功能：商务合作 **/
	/** 作者：穠@20230328 **/
    public function cooperation() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');   //分类id

		/** 查询分类 **/
		$sort = Db::table('cooperate_sort')->where('language', 1)->where('genre', 1)->order('sort, id')->where('is_del', 1)->column('id,name,tdk_key,desc,detail,type');

		$this->assign('sort', $sort);
		$this->assign('sort_id', $sort_id);
		$this->assign('public_title', $sort[$sort_id]['name']);
		$this->assign('public_key', $sort[$sort_id]['tdk_key']);
		$this->assign('public_desc', $sort[$sort_id]['desc']);
        return $this->fetch('m@public/public');
	}

	/** 功能：合作媒体 **/
	/** 作者：穠@20230328 **/
    public function medium() {

		$this->redirect('m/index/index');
	}
}