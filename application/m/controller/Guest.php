<?php
namespace app\m\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;
/** 功能：演讲嘉宾 **/
class Guest extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20230328 **/
	public function _initialize() {
		
    }

	/** 功能：人物风采 **/
	/** 作者：穠@20230328 **/
    public function guest() {

		/** 对外接收参数 **/
		$num = input('num/d', 10);				//每页显示多少条

		/** 查询嘉宾信息 **/
		$row  = Db::table('forum_applys');
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 1)->where('id', 27)->find();

		$this->assign('num', $num);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('m@public/public');
	}

	/** 功能：人物风采详情 **/
	/** 作者：穠@20230328 **/
    public function detail() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	  //id

		$row  = Db::table('forum_applys')->select();

		/** 查询TDK设置 **/
		$rule_tdk = Db::table('rule_tdk')->where('language', 1)->where('id', 27)->find();

		$this->assign('id', $id);
		$this->assign('row', $row);
		$this->assign('public_title', $rule_tdk['title']);
		$this->assign('public_key', $rule_tdk['key']);
		$this->assign('public_desc', $rule_tdk['desc']);
        return $this->fetch('m@public/public');
	}
}