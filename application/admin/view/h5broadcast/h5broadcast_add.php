<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="3">
		<input type="hidden" name="language" id="language" value="1">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
        <div class="edit_wrap">
			<div class="title">直播中心</div>
			<div class="layui-form-item">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">地点</label>
                <div class="layui-input-block">
                    <input type="text" name="address" class="layui-input" value="<?=$data['address']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">直播链接</label>
                <div class="layui-input-block">
                    <input type="text" name="url" lay-verify="url" class="layui-input" value="<?=$data['url']?>">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">日期</label>
				<div class="layui-input-block">
					<select name="sort_id">
						<option value=""></option>
						<?php foreach ($sort as $k => $v) { ?>
							<option value="<?=$v['id']?>" <?=$v['id'] == $data['sort_id'] ? 'selected' : '';?>><?=$v['name']?></option>
						<?php } ?>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">开始时间</label>
				<div class="layui-input-block">
					<input type="text" name="start_time" class="layui-input" id="start_time" value="<?=$data['start_time'] ? $data['start_time'] : '';?>" readonly>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">结束时间</label>
				<div class="layui-input-block">
					<input type="text" name="end_time" class="layui-input" id="end_time" value="<?=$data['end_time'] ? $data['end_time'] : '';?>" readonly>
				</div>
			</div>
			<div class="layui-form-item">
                <label class="layui-form-label">直播中</label>
                <div class="layui-input-block left">
                    <input type="radio" name="is_afoot" value="1" title="是" <?=$data['is_afoot'] == 1 ? 'checked' : '';?>>
                    <input type="radio" name="is_afoot" value="2" title="否" <?=$data['is_afoot'] != 1 || !$id ? 'checked' : '';?>>
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">缩略图</label>
                <div class="layui-form-upload banner">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadLogo"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 690*400</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		//日期时间选择器
		laydate.render({
			elem: '#start_time',
			type: 'datetime',
			min: '2024-09-07',
			max: '2024-09-09 23:59:59'
		});

		//日期时间选择器
		laydate.render({
			elem: '#end_time',
			type: 'datetime',
			min: '2024-09-07',
			max: '2024-09-09 23:59:59'
		});

		var dir_public = "<?=$dir_public?>";

		/* 上传缩略图 */
        upload.render({
            elem: "#uploadLogo",
            url: "<?=url('admin/image/upload_broadcast')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + dir_public + res.data + '">').css('background', 'none');
            }
        });

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/h5broadcast/h5broadcast_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>