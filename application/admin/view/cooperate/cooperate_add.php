<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="1">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
        <div class="edit_wrap">
			<div class="title">合作伙伴</div>
			<div class="layui-form-item">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">链接地址</label>
                <div class="layui-input-block">
                    <input type="text" name="url" class="layui-input" value="<?=$data['url']?>">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">分类</label>
				<div class="layui-input-block">
					<select name="sort_id">
						<option value=""></option>
						<?php foreach ($sort as $k => $v) { ?>
							<option value="<?=$v['id']?>" <?=$v['id'] == $data['sort_id'] ? 'selected' : '';?>><?=$v['name']?></option>
						<?php } ?>
					</select>
				</div>
			</div>
            <div class="layui-form-item">
                <label class="layui-form-label">缩略图</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadLogo"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 264*110</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		var dir_public = "<?=$dir_public?>";

		/* 上传缩略图 */
        upload.render({
            elem: "#uploadLogo",
            url: "<?=url('admin/image/upload_cooperate')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + dir_public + res.data + '">').css('background', 'none');
            }
        });

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/cooperate/cooperate_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>