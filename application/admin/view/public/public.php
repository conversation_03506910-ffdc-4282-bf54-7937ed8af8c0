<?php
	use \think\Cookie;
	use \think\Session;
	use \think\Request;
	use \think\Db;
	$request    = Request::instance();
	$dir_public = str_replace('/index.php', '', $request->root());
	//$dir_public = $dir_public . '/public';
	$dir_view   = "../application/" . $request->module() . "/view/" . strtolower($request->controller()) . '/' . $request->action();

	/** 获取文件名 **/
	$url_v = array_slice(explode('/', $dir_view), -2, 1)[0];
	$url_u = array_slice(explode('/', $dir_view), -1, 1)[0];

	/************************************* 轮播图 ***************************************/
	
	$url_z[8]['name']			  = '轮播图';
	$url_z[8]['url']			  = 'banner';
	$url_z[8]['icon']			  = 'banner';
	$url_z[8]['active']			  = 'banner';
	$url_z[8]['way'][1]['name']   = 'PC轮播图';
	$url_z[8]['way'][1]['url']	  = 'banner';
	$url_z[8]['way'][1]['active'] = 'banner,banner_add';
	$url_z[8]['way'][1]['type']   = '2';
	$url_z[8]['way'][2]['name']   = 'H5轮播图';
	$url_z[8]['way'][2]['url']	  = 'h5_banner';
	$url_z[8]['way'][2]['active'] = 'h5_banner,h5_banner_add';
	$url_z[8]['way'][2]['type']   = '2';
	$url_z[8]['way'][3]['name']   = '独立H5轮播图';
	$url_z[8]['way'][3]['url']	  = 'dl_h5_banner';
	$url_z[8]['way'][3]['active'] = 'dl_h5_banner,dl_h5_banner_add';
	$url_z[8]['way'][3]['type']   = '2';

	/************************************* 论坛介绍 ***************************************/
	
	$url_z[0]['name']			  = '论坛介绍';
	$url_z[0]['url']			  = 'forum';
	$url_z[0]['icon']			  = 'luntanjieshao';
	$url_z[0]['active']			  = 'forum';
	$url_z[0]['way'][1]['name']   = '论坛简介';
	$url_z[0]['way'][1]['url']	  = 'blurb';
	$url_z[0]['way'][1]['active'] = 'blurb';
	$url_z[0]['way'][1]['type']   = '2';
	$url_z[0]['way'][2]['name']   = '组织机构';
	$url_z[0]['way'][2]['url']	  = 'agency';
	$url_z[0]['way'][2]['active'] = 'agency,agency_add';
	$url_z[0]['way'][2]['type']   = '2';
	$url_z[0]['way'][3]['name']   = '组织机构分类';
	$url_z[0]['way'][3]['url']	  = 'agencysort';
	$url_z[0]['way'][3]['active'] = 'agencysort,agencysort_add';
	$url_z[0]['way'][3]['type']   = '2';

	/************************************* 新闻中心 ***************************************/
	
	$url_z[1]['name']			  = '新闻中心';
	$url_z[1]['url']			  = 'news';
	$url_z[1]['icon']			  = 'xinwen';
	$url_z[1]['active']			  = 'news';
	$url_z[1]['way'][1]['name']   = '新闻中心';
	$url_z[1]['way'][1]['url']	  = 'news';
	$url_z[1]['way'][1]['active'] = 'news,news_add';
	$url_z[1]['way'][1]['type']   = '2';
	$url_z[1]['way'][2]['name']   = '新闻中心分类';
	$url_z[1]['way'][2]['url']	  = 'newssort';
	$url_z[1]['way'][2]['active'] = 'newssort,newssort_add';
	$url_z[1]['way'][2]['type']   = '2';

	/************************************* 报名参会 ***************************************/
	
	$url_z[2]['name']			  = '报名参会';
	$url_z[2]['url']			  = 'participation';
	$url_z[2]['icon']			  = 'baomingcanhui';
	$url_z[2]['active']			  = 'participation';
	$url_z[2]['way'][1]['name']   = '参会说明';
	$url_z[2]['way'][1]['url']	  = 'explain';
	$url_z[2]['way'][1]['active'] = 'explain';
	$url_z[2]['way'][1]['type']   = '2';

	/************************************* 会务信息 ***************************************/
	
	$url_z[3]['name']			  = '会务信息';
	$url_z[3]['url']			  = 'meeting';
	$url_z[3]['icon']			  = 'huiwuxinxi';
	$url_z[3]['active']			  = 'meeting';
	$url_z[3]['way'][1]['name']   = '班车信息';
	$url_z[3]['way'][1]['url']	  = 'vehicle';
	$url_z[3]['way'][1]['active'] = 'vehicle';
	$url_z[3]['way'][1]['type']   = '2';
	$url_z[3]['way'][2]['name']   = '推荐酒店';
	$url_z[3]['way'][2]['url']	  = 'hotel';
	$url_z[3]['way'][2]['active'] = 'hotel';
	$url_z[3]['way'][2]['type']   = '2';
	$url_z[3]['way'][3]['name']   = '交通环境';
	$url_z[3]['way'][3]['url']	  = 'traffic';
	$url_z[3]['way'][3]['active'] = 'traffic,traffic_add';
	$url_z[3]['way'][3]['type']   = '2';

	/************************************* 直播中心 ***************************************/
	
	$url_z[7]['name']			  = '直播中心';
	$url_z[7]['url']			  = 'broadcast';
	$url_z[7]['icon']			  = 'zhibo';
	$url_z[7]['active']			  = 'broadcast';
	$url_z[7]['way'][1]['name']   = '直播中心';
	$url_z[7]['way'][1]['url']	  = 'broadcast';
	$url_z[7]['way'][1]['active'] = 'broadcast,broadcast_add';
	$url_z[7]['way'][1]['type']   = '2';
	$url_z[7]['way'][2]['name']   = '直播中心分类';
	$url_z[7]['way'][2]['url']	  = 'broadcastsort';
	$url_z[7]['way'][2]['active'] = 'broadcastsort,broadcastsort_add';
	$url_z[7]['way'][2]['type']   = '2';
	$url_z[7]['way'][3]['name']   = '直播回顾';
	$url_z[7]['way'][3]['url']	  = 'review';
	$url_z[7]['way'][3]['active'] = 'review,review_add';
	$url_z[7]['way'][3]['type']   = '2';
	$url_z[7]['way'][4]['name']   = '直播回顾分类';
	$url_z[7]['way'][4]['url']	  = 'reviewsort';
	$url_z[7]['way'][4]['active'] = 'reviewsort,reviewsort_add';
	$url_z[7]['way'][4]['type']   = '2';

	/************************************* 往届回顾 ***************************************/
	
	$url_z[4]['name']			  = '往届回顾';
	$url_z[4]['url']			  = 'review';
	$url_z[4]['icon']			  = 'wangqihuigu';
	$url_z[4]['active']			  = 'review';
	$url_z[4]['way'][1]['name']   = '往届回顾';
	$url_z[4]['way'][1]['url']	  = 'review';
	$url_z[4]['way'][1]['active'] = 'review,review_add';
	$url_z[4]['way'][1]['type']   = '2';
	$url_z[4]['way'][2]['name']   = '论坛日程';
	$url_z[4]['way'][2]['url']	  = 'schedule';
	$url_z[4]['way'][2]['active'] = 'schedule,schedule_add,scheduletime,scheduletime_add';
	$url_z[4]['way'][2]['type']   = '2';
	$url_z[4]['way'][3]['name']   = '演讲嘉宾';
	$url_z[4]['way'][3]['url']	  = 'guest';
	$url_z[4]['way'][3]['active'] = 'guest,guest_add';
	$url_z[4]['way'][3]['type']   = '2';
	$url_z[4]['way'][4]['name']   = '图片集锦';
	$url_z[4]['way'][4]['url']	  = 'picture';
	$url_z[4]['way'][4]['active'] = 'picture,picture_add';
	$url_z[4]['way'][4]['type']   = '2';
	$url_z[4]['way'][5]['name']   = '专题报告集';
	$url_z[4]['way'][5]['url']	  = 'report';
	$url_z[4]['way'][5]['active'] = 'report,report_add';
	$url_z[4]['way'][5]['type']   = '2';
	$url_z[4]['way'][6]['name']   = '会后刊';
	$url_z[4]['way'][6]['url']	  = 'prints';
	$url_z[4]['way'][6]['active'] = 'prints,prints_add';
	$url_z[4]['way'][6]['type']   = '2';

	/************************************* 合作伙伴 ***************************************/
	
	$url_z[5]['name']			  = '合作伙伴';
	$url_z[5]['url']			  = 'cooperate';
	$url_z[5]['icon']			  = 'hezuohuoban';
	$url_z[5]['active']			  = 'cooperate';
	$url_z[5]['way'][1]['name']   = '合作伙伴';
	$url_z[5]['way'][1]['url']	  = 'cooperate';
	$url_z[5]['way'][1]['active'] = 'cooperate,cooperate_add';
	$url_z[5]['way'][1]['type']   = '2';
	$url_z[5]['way'][2]['name']   = '合作伙伴分类';
	$url_z[5]['way'][2]['url']	  = 'cooperatesort';
	$url_z[5]['way'][2]['active'] = 'cooperatesort,cooperatesort_add';
	$url_z[5]['way'][2]['type']   = '2';

	/************************************* 文件下载 ***************************************/
	
	$url_z[6]['name']			  = '文件下载';
	$url_z[6]['url']			  = 'download';
	$url_z[6]['icon']			  = 'wenjianxiazai';
	$url_z[6]['active']			  = 'download';
	$url_z[6]['way'][1]['name']   = '文件下载';
	$url_z[6]['way'][1]['url']	  = 'download';
	$url_z[6]['way'][1]['active'] = 'download,download_add';
	$url_z[6]['way'][1]['type']   = '2';

	/************************************* H5图片直播 ***************************************/
	
	$url_z[9]['name']			  = 'H5图片直播';
	$url_z[9]['url']			  = 'picture';
	$url_z[9]['icon']			  = 'wenjianxiazai';
	$url_z[9]['active']			  = 'picture';
	$url_z[9]['way'][1]['name']   = '图片直播';
	$url_z[9]['way'][1]['url']	  = 'picture';
	$url_z[9]['way'][1]['active'] = 'picture,picture_add';
	$url_z[9]['way'][1]['type']   = '2';

	/************************************* H5会务 ***************************************/
	
	$url_z[10]['name']			   = 'H5会务';
	$url_z[10]['url']			   = 'h5meeting';
	$url_z[10]['icon']			   = 'wenjianxiazai';
	$url_z[10]['active']		   = 'h5meeting';
	$url_z[10]['way'][1]['name']   = '论坛基本信息';
	$url_z[10]['way'][1]['url']	   = 'basic';
	$url_z[10]['way'][1]['active'] = 'basic';
	$url_z[10]['way'][1]['type']   = '2';
	$url_z[10]['way'][2]['name']   = '会场交通';
	$url_z[10]['way'][2]['url']	   = 'traffic';
	$url_z[10]['way'][2]['active'] = 'traffic';
	$url_z[10]['way'][2]['type']   = '2';
	$url_z[10]['way'][3]['name']   = '推荐酒店';
	$url_z[10]['way'][3]['url']	   = 'hotel';
	$url_z[10]['way'][3]['active'] = 'hotel';
	$url_z[10]['way'][3]['type']   = '2';
	$url_z[10]['way'][4]['name']   = '班车信息';
	$url_z[10]['way'][4]['url']	   = 'vehicle';
	$url_z[10]['way'][4]['active'] = 'vehicle';
	$url_z[10]['way'][4]['type']   = '2';

	/************************************* H5直播中心 ***************************************/
	
	$url_z[11]['name']			   = 'H5直播中心';
	$url_z[11]['url']			   = 'h5broadcast';
	$url_z[11]['icon']			   = 'zhibo';
	$url_z[11]['active']		   = 'h5broadcast';
	$url_z[11]['way'][1]['name']   = 'H5直播中心';
	$url_z[11]['way'][1]['url']	   = 'h5broadcast';
	$url_z[11]['way'][1]['active'] = 'h5broadcast,h5broadcast_add';
	$url_z[11]['way'][1]['type']   = '2';
	$url_z[11]['way'][2]['name']   = 'H5直播中心日期';
	$url_z[11]['way'][2]['url']	   = 'h5broadcastsort';
	$url_z[11]['way'][2]['active'] = 'h5broadcastsort,h5broadcastsort_add';
	$url_z[11]['way'][2]['type']   = '2';

	/************************************* 系统设置 ***************************************/

	$url_z[13]['name']			   = '网站设置';
	$url_z[13]['url']			   = 'rule';
	$url_z[13]['icon']			   = 'setup';
	$url_z[13]['way'][1]['name']   = '系统设置';
	$url_z[13]['way'][1]['url']	   = 'site';
	$url_z[13]['way'][1]['active'] = 'site';
	$url_z[13]['way'][1]['type']   = '2';
	$url_z[13]['way'][2]['name']   = 'H5 联系我们';
	$url_z[13]['way'][2]['url']	   = 'h5_about';
	$url_z[13]['way'][2]['active'] = 'h5_about';
	$url_z[13]['way'][2]['type']   = '2';
	$url_z[13]['way'][3]['name']   = 'H5 外链设置';
	$url_z[13]['way'][3]['url']	   = 'h5_likes';
	$url_z[13]['way'][3]['active'] = 'h5_likes';
	$url_z[13]['way'][3]['type']   = '2';
	$url_z[13]['way'][4]['name']   = 'TDK设置';
	$url_z[13]['way'][4]['url']	   = 'tdk';
	$url_z[13]['way'][4]['active'] = 'tdk,tdk_add';
	$url_z[13]['way'][4]['type']   = '2';
	$url_z[13]['way'][9]['name']   = '修改密码';
	$url_z[13]['way'][9]['url']    = 'pwd';
	$url_z[13]['way'][9]['type']   = '2';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>浦江创新论坛后台管理系统</title>
	<link href="<?=$dir_public.'/common/css/common.css?v=' . date('YmdHis')?>" rel="stylesheet">
	<!-- <link href="<?=$dir_public.'/common/css/admin.css?v=' . date('YmdHis')?>" rel="stylesheet">
	<link href="<?=$dir_public.'/common/css/style.css?v=' . date('YmdHis')?>" rel="stylesheet"> -->
	<link href="<?=$dir_public.'/common/css/css.css?v=' . date('YmdHis')?>" rel="stylesheet">
	<link href="<?=$dir_public.'/layui/css/layui.css?v=' . date('YmdHis')?>" rel="stylesheet">
	<link href="<?=$dir_public.'/common/css/iconfont.css?v=' . date('YmdHis')?>" rel="stylesheet">
	<script src="<?=$dir_public?>/common/js/jquery.min.js"></script>
	<script src="<?=$dir_public?>/layui/layui.js"></script>
	<script src="<?=$dir_public?>/common/js/common.js"></script>
	<script src="<?=$dir_public?>/common/js/wangEditor.min.js"></script>
	<script>
		var http = 'http://';
		if('https:' == document.location.protocol) http = 'https://';
		var host = http + '<?=$_SERVER['HTTP_HOST'] . $dir_public?>';
	</script>
</head>
<?php if (Session::has('pjcxlt_shrink') && (Session::get('pjcxlt_shrink') == 2)) { ?>
	<body class="shrink">
<?php } else { ?>
	<body>
<?php } ?>
	<div class="header between">
        <!--<a class="logo" href="javascript:;">-->
		<a class="logo" href="<?=url("admin/index/index")?>">
            <div class="text">后台管理系统</div>
            <span class="iconfont icon-home"></span>
		</a>
		<div class="navbar between">
			<div class="left">
			    <div class="shrinkBtn"><span class="iconfont icon-menu"></span></div>
				<?php if (Session::get('pjcxlt_type') == 100) { ?>
					<a href="<?='http://' . $_SERVER['HTTP_HOST'] . '/adminen'?>" target="_blank" class="web">英文管理</a>
				<?php }?>
			</div>
			<div class="user">
				<a class="username" href="javascript:;">
					<span class="iconfont icon-user"></span>
					<span>管理员</span>
					<span class="iconfont icon-caret"></span>
				</a>
				<div class="bar"></div>
				<ul class="sub">
					<li><a href="<?=url("admin/login/logout")?>">退出登录</a></li>
					<li><a href="<?=url("admin/rule/pwd")?>">修改密码</a></li>
				</ul>
			</div>
        </div>
    </div>
    <div class="sidenav">
        <ul class="nav">
			<?php foreach ($url_z as $k => $v) { ?>
				<li class="item<?=$url_v == $v['url'] ? ' show' : '';?>">
					<a href="javascript:;">
						<span class="icon iconfont icon-<?=$v['icon']?>"></span>
						<span class="text"><?=$v['name']?></span>
						<span class="iconfont icon-caret"></span>
						<?php if (isset($v['num']) && $v['num'] > 0) { ?>
							<i class="verify-num"><?=$v['num']?></i>
						<?php } ?>
					</a>
					<ul>
						<?php foreach ($v['way'] as $kk => $vv) { if ($vv['type'] == 1) { ?>
							<li class="item<?=in_array($url_u, explode(',', implode(',', array_column($vv['a'], 'active')))) ? ' show' : '';?>">
								<a href="javascript:;">
									<span class="text <?=in_array($url_u, explode(',', implode(',', array_column($vv['a'], 'active')))) ? '' : '';?>"><?=$vv['name']?></span>
									<span class="iconfont icon-caret"></span>
								</a>
								<ul>
									<?php foreach ($vv['a'] as $kkk => $vvv) { ?>
										<?php if (isset($vvv['active'])) { ?>
											<li><a class="<?=($url_v == $v['url'] && in_array($url_u, explode(',', $vvv['active']))) ? 'active' : '';?>" href="<?=url("admin/{$v['url']}/{$vvv['url']}")?>"><span class="text"><?=$vvv['name']?></span></a></li>
										<?php } else { ?>
											<li><a class="<?=($url_v == $v['url'] && $url_u == $vvv['url']) ? 'active' : '';?>" href="<?=url("admin/{$v['url']}/{$vvv['url']}")?>"><span class="text"><?=$vvv['name']?></span></a></li>
										<?php } ?>
									<?php } ?>
								</ul>
							</li>
						<?php } else { ?>
							<li>
								<?php if (isset($vv['active'])) { ?>
									<a class="<?=($url_v == $v['url'] && in_array($url_u, explode(',', $vv['active']))) ? 'active' : '';?>" href="<?=url("admin/{$v['url']}/{$vv['url']}")?>">
										<span class="text"><?=$vv['name']?></span>
										<?php if (isset($vv['num']) && $vv['num'] > 0) { ?>
											<i class="verify-num"><?=$vv['num']?></i>
										<?php } ?>
									</a>
								<?php } else { ?>
									<a class="<?=($url_v == $v['url'] && $url_u == $vv['url']) ? 'active' : '';?>" href="<?=url("admin/{$v['url']}/{$vv['url']}")?>">
										<span class="text"><?=$vv['name']?></span>
										<?php if (isset($vv['num']) && $vv['num'] > 0) { ?>
											<i class="verify-num"><?=$vv['num']?></i>
										<?php } ?>
									</a>
								<?php } ?>
							</li>
						<?php } } ?>
					</ul>
				</li>
			<?php } ?>
		</ul>
	</div>
	<?php require_once("{$dir_view}.php");?>
</body>
</html>