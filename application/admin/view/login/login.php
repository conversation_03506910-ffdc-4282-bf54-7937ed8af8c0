<?php
	use think\Cookie;
	$dir_public = str_replace('/index.php', '', $_SERVER['SCRIPT_NAME']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>浦江创新论坛后台管理系统</title>
	<link href="<?=$dir_public?>/common/css/common.css" rel="stylesheet">
	<link href="<?=$dir_public?>/layui/css/layui.css" rel="stylesheet">
	<link href="<?=$dir_public?>/common/css/iconfont.css" rel="stylesheet">
	<script src="<?=$dir_public?>/common/js/jquery.min.js"></script>
	<script src="<?=$dir_public?>/layui/layui.js"></script>
</head>
<body>
	<div class="loginWrap">
		<div class="login">
			<form class="layui-form">
				<div class="name">浦江创新论坛<br>后台管理系统</div>
				<div class="layui-form-item">
					<div class="icon"><span class="iconfont icon-admin"></span></div>
					<input type="text" name="username" placeholder="请输入用户名" autocomplete="off" class="layui-input">
				</div>
				<div class="layui-form-item">
					<div class="icon"><span class="iconfont icon-lock"></span></div>
					<input type="password" name="password" placeholder="请输入密码" autocomplete="off" class="layui-input">
				</div>
				<button class="layui-btn" lay-submit="" lay-filter="submit">登录</button>
			</form>
		</div>
	</div>
	<script>
		layui.use(['form', 'layer'], function () {
			var form = layui.form, layer = layui.layer;
			submit();
			$("body").keydown(function () {
				if (event.keyCode == 13) {
					submit();
				}
			});
			function submit() {
				form.on('submit(submit)', function (data) {
					$.ajax({
						type: 'post',
						url: "<?=url('admin/login/login_pwd')?>",
						data: data.field,
						success: function (res) {
							if (res.code == 200) {
								layer.msg('登录成功', { icon: 1, shade: 0.3, time: 1000 }, function () {
									window.location.href = "<?=url('admin/index/index')?>";
								});
							} else {
								layer.msg(res.msg, { icon: 2, shade: 0.3, time: 2800 });
							}
						}
					});
					return false;
				});
			}
		})
	</script>
</body>
</html>