<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="3">
		<input type="hidden" name="language" id="language" value="1">
        <div class="edit_wrap">
			<div class="title">H5 外链设置</div>
			<div class="layui-form-item">
                <label class="layui-form-label">全球创业投资会</label>
                <div class="layui-input-block">
                    <input type="text" name="invest_meeting" class="layui-input" value="<?=$data['invest_meeting']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">全球技术转移会</label>
                <div class="layui-input-block">
                    <input type="text" name="transfer_meeting" class="layui-input" value="<?=$data['transfer_meeting']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">直播中心</label>
                <div class="layui-input-block">
                    <input type="text" name="broadcast" class="layui-input" value="<?=$data['broadcast']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">图片直播</label>
                <div class="layui-input-block">
                    <input type="text" name="photo" class="layui-input" value="<?=$data['photo']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">焦点荟萃</label>
                <div class="layui-input-block">
                    <input type="text" name="focus" class="layui-input" value="<?=$data['focus']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">互动</label>
                <div class="layui-input-block">
                    <input type="text" name="interact" class="layui-input" value="<?=$data['interact']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">名片夹</label>
                <div class="layui-input-block">
                    <input type="text" name="card" class="layui-input" value="<?=$data['card']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">问卷调查</label>
                <div class="layui-input-block">
                    <input type="text" name="survey" class="layui-input" value="<?=$data['survey']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">客服</label>
                <div class="layui-input-block">
                    <input type="text" name="kefu" class="layui-input" value="<?=$data['kefu']?>">
                </div>
            </div>
            
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload'], function () {
        var form = layui.form, upload = layui.upload;

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/rule/h5_likes_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							location.reload();
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>