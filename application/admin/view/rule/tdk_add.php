<div class="content">
	<form class="layui-form">
		<input type="hidden" name="id" value="<?=$id?>">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="1">
		<div class="edit_wrap">
			<div class="title"><?=$id ? '编辑' : '添加';?>TDK设置</div>
			<div class="layui-form-item">
				<label class="layui-form-label">说明</label>
				<div class="layui-input-block">
					<input type="text" name="remarks" class="layui-input" value="<?=$data['remarks']?>" placeholder="请填写说明" <?=$id ? 'readonly' : '';?>>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">标题</label>
				<div class="layui-input-block">
					<input type="text" name="title" class="layui-input" value="<?=$data['title']?>" placeholder="请填写标题">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">关键词</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="key" rows="8"><?=$data['key']?></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">简介</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="desc" rows="8"><?=$data['desc']?></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">排序</label>
				<div class="layui-input-block">
					<input type="text" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" placeholder="请填写排序" onkeyup="this.value=this.value.toString().match(/^\d+(?:\d)?/)">
				</div>
			</div>
		</div>
		<div class="layui-form-btns left">
			<button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
			<button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
		</div>
	</form>
</div>
<script>
	layui.use(['form', 'upload', 'laydate'], function () {
		var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		//提交数据
		form.on('submit(submit)', function (data) {
			$(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/rule/tdk_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
		});
	})
</script>