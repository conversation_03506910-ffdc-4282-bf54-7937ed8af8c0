<div class="content">
	<form class="layui-form">
		<div class="edit_wrap">
			<div class="title">修改密码</div>
			<div class="layui-form-item">
				<label class="layui-form-label">原密码</label>
				<div class="layui-input-block">
					<input type="text" name="raw_password" class="layui-input" placeholder="请填写原密码">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">密码</label>
				<div class="layui-input-block">
					<input type="text" name="password" class="layui-input" placeholder="请填写密码">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">确认密码</label>
				<div class="layui-input-block">
					<input type="text" name="repassword" class="layui-input" placeholder="请再次填写密码">
				</div>
			</div>
		</div>
		<div class="layui-form-btns left">
			<button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
			<button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
		</div>
	</form>
</div>
<script>
	layui.use(['form'], function () {
		var form = layui.form;

		//提交数据
		form.on('submit(submit)', function (data) {
			$(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/login/update_pwd')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							location.reload();
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
		});
	})
</script>