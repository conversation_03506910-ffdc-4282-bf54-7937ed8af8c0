<div class="content">
    <div class="tools">
        <form class="layui-form">
            <div class="left add">
				<a class="layui-btn centerT" href="<?=url("admin/banner/h5_banner_add")?>">新增轮播图</a>
            </div>
        </form>
    </div>
    <div class="wrap">
        <table class="table layui-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>缩略图</th>
                    <th>排序</th>
                    <th>发布时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
				<?php if (!count($row)) { ?>
					<tr class="noData"><td colspan="20">暂无数据</td></tr>
				<?php } else {$i = 0; foreach ($row as $k => $v) { $i++;?>
					<tr>
						<td><?=$i?></td>
						<td class="banner"><img src="<?=$dir_public . $v['logo']?>"></td>
						<td><input class="sequence" type="number" data-id="<?=$v['id']?>" value="<?=$v['sort']?>" onchange="sort(this, <?=$v['id']?>)"></td>
						<td><?=$v['addtime']?></td>
						<td>
							<div class="operate centerT">
								<a href="<?=url("admin/banner/h5_banner_add", ['id' => $v['id']])?>" class="blue">编辑</a>
								<a href="javascript:;" class="red" onclick="deleteItem(this, <?=$v['id']?>)">删除</a>
							</div>
						</td>
					</tr>
				<?php } } ?>
            </tbody>
        </table>
    </div>
</div>
<script>
	layui.use(['form'], function () {
        var form = layui.form;

        /** 修改排序 **/
		sort = function(that,id) {
			sortChange(id, parseInt($(that).val()), "<?=url('admin/banner/banner_sort')?>")
        }

        /** 单个删除 **/
		deleteItem = function(that,id) {
			var json = {
				id: id
			}
			singleOperate(json, '确定要删除该轮播图吗？', "<?=url('admin/banner/banner_del')?>")
        }
	});
</script>