<?php
	$type[1] = '展示';
	$type[2] = '富文本';
?>
<div class="content">
    <form class="layui-form">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
		<input type="hidden" name="language" id="language" value="1">
		<input type="hidden" name="genre" id="genre" value="1">
        <div class="edit_wrap">
            <div class="title">轮播图管理</div>
            <div class="layui-form-item">
                <label class="layui-form-label">缩略图</label>
                <div class="layui-form-upload banner">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadBanner"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 1920*960</div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
            <!--<div class="layui-form-item left">
    			<label class="layui-form-label">选择类型</label>
    			<div class="layui-input-block">
    				<select name="type" lay-filter="bannerType">
    					<?php foreach ($type as $k => $v) { if ($k == $data['type']) { ?>
    						<option value="<?=$k?>" selected><?=$v?></option>
    					<?php } else { ?>
    						<option value="<?=$k?>"><?=$v?></option>
    					<?php } } ?>
    				</select>
    			</div>
    		</div>-->
			<!--<div class="layui-form-item layui-editor">
				<label class="layui-form-label">详情</label>
				<div class="editor">
				    <div id="toolbar-editor"></div>
				    <div id="content-editor"></div>
				    <textarea id="editorText" name="detail" style="display:none"><?=$data['detail']?></textarea>
				</div>
			</div>-->
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script src="<?=$dir_public?>/common/js/editor.js"></script>
<script>
	layui.use(['form', 'upload'], function () {
        var form = layui.form, upload = layui.upload;

		var dir_public = "<?=$dir_public?>";
		
		/*var selectType = $(".edit_wrap select[name='type']").val();
		if (selectType == 1) {
			$(".layui-editor").hide();
		}  else {
			$(".layui-editor").show();
		}
		
		form.on('select(bannerType)', function (data) {
            if (data.value == 1) {
                $(".layui-editor").hide();
            } else {
                $(".layui-editor").show();
            }
        })*/

		/*上传缩略图*/
        upload.render({
            elem: "#uploadBanner",
            url: "<?=url('admin/image/upload_banner')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + res.data + '">').css('background', 'none');
            }
        });

        form.on('submit(submit)', function (data) {
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/banner/banner_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>