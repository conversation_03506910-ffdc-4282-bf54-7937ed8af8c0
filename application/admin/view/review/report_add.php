<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="1">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
        <div class="edit_wrap">
			<div class="title">专题报告集</div>
			<div class="layui-form-item">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">发布日期</label>
				<div class="layui-input-block">
					<input type="text" name="time" class="layui-input" id="time" value="<?=$data['time'] ? $data['time'] : '';?>" readonly>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">文件</label>
				<input type="hidden" name="url_name" id="url_name" value="<?=$data['url_name']?>">
				<input type="hidden" name="url" id="url" value="<?=$data['url']?>">
				<input type="hidden" name="suffix" id="suffix" value="<?=$data['suffix']?>">
				<div class="layui-input-block fileA">
					<a id="fileA" href="<?=$dir_public . $data['url']?>" target="_blank" style="line-height:36px;color:#3385ff"><?=$data['url_name']?></a>
				</div>
				<button type="button" class="layui-btn" id="uploadUrl"><span class="iconfont icon-upload"></span>上传</button>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">往届回顾</label>
				<div class="layui-input-block">
					<select name="review_id">
						<option value=""></option>
						<?php foreach ($review as $k => $v) { ?>
							<option value="<?=$v['id']?>" <?=$v['id'] == $data['review_id'] ? 'selected' : '';?>><?=$v['title']?></option>
						<?php } ?>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		//日期时间选择器
		laydate.render({
			elem: '#time',
			type: 'date'
		});

		if ($('#url').val()) {
			$(".fileA").show();
		} else {
			$(".fileA").hide();
		}

		//上传文档
		upload.render({
			elem: '#uploadUrl',
			url: '<?=url("admin/image/upload_download")?>',
			accept: 'file',
			done: function (res) {
				if (res.code == 200) {
					var item = this.item;
					$("#fileA").text(res.name);
					$("#fileA").attr('href','<?=$dir_public?>' + res.data);
					$('#url').val(res.data);
					$('#url_name').val(res.name);
					$('#suffix').val(res.suffix);
					$(".fileA").show();
				} else {
					layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
				}
				
			}
		});

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/review/report_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>