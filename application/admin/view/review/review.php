<div class="content">
    <div class="tools">
        <form class="layui-form between">
            <div class="left add">
                <a class="layui-btn centerT" href="<?=url("admin/review/review_add")?>">新增往届回顾</a>
            </div>
            <div class="right filter">
                <div class="layui-form-item">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-block input">
                        <input type="text" name="keyword" placeholder="请填写标题" autocomplete="off" class="layui-input" value="<?=$keyword?>">
                    </div>
                </div>              
                <button type="submit" class="layui-btn">搜索</button>
            </div>
        </form>
    </div>
    <div class="wrap">
        <table class="table layui-table">
            <thead>
                <tr>
                    <th>序号</th>
					<th>缩略图</th>
                    <th>标题</th>
                    <th>排序</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
				<?php if (!count($row)) { ?>
					<tr class="noData"><td colspan="20">暂无数据</td></tr>
				<?php } else {$i = 0; foreach ($row as $k => $v) { $i++;?>
					<tr>
						<td><?=$i?></td>
						<td class="logo"><img src="<?=$v['logo']?>"></td>
						<td><?=$v['title']?></td>
						<td><input class="sequence" type="number" data-id="<?=$v['id']?>" value="<?=$v['sort']?>" onchange="sort(this, <?=$v['id']?>)"></td>
						<td>
							<div class="operate centerT">
								<a href="<?=url("admin/review/review_add", ['id' => $v['id']])?>" class="blue">编辑</a>
								<a href="javascript:;" class="red" onclick="deleteItem(this, <?=$v['id']?>)">删除</a>
							</div>
						</td>
					</tr>
				<?php } } ?>
            </tbody>
        </table>
		<?php include('../application/admin/view/public/page.php');?>
    </div>
</div>
<script>
	layui.use(['form'], function () {
        var form = layui.form;

        /** 修改排序 **/
		sort = function(that,id) {
			sortChange(id, parseInt($(that).val()), "<?=url('admin/review/review_sort')?>")
        }

        /** 单个删除 **/
		deleteItem = function(that,id) {
			var json = {
				id: id
			}
			singleOperate(json, '确定要删除该信息吗？', "<?=url('admin/review/review_del')?>")
        }
	});
</script>