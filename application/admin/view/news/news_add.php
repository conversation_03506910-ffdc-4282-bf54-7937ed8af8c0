<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="1">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
        <div class="edit_wrap">
			<div class="title">新闻中心</div>
			<div class="layui-form-item">
				<label class="layui-form-label">标题</label>
				<div class="layui-input-block">
					<input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">发布时间</label>
				<div class="layui-input-block">
					<input type="text" name="release_time" class="layui-input" id="release_time" value="<?=$data['release_time'] ? $data['release_time'] : '';?>" readonly>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">分类</label>
				<div class="layui-input-block">
					<input type="hidden" name="sort_id" value="<?=$data['sort_id']?>">
					<cascader id="newssort"></cascader>
				</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">来源</label>
				<div class="layui-input-block">
					<input type="text" name="source" class="layui-input" value="<?=$data['source']?>">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">作者</label>
				<div class="layui-input-block">
					<input type="text" name="author" class="layui-input" value="<?=$data['author']?>">
				</div>
			</div>

			<div class="layui-form-item sort5">
				<label class="layui-form-label">时间</label>
				<div class="layui-input-block">
					<input type="text" name="time" class="layui-input" value="<?=$data['time']?>">
				</div>
			</div>
			<div class="layui-form-item sort5">
				<label class="layui-form-label">地点</label>
				<div class="layui-input-block">
					<input type="text" name="place" class="layui-input" value="<?=$data['place']?>">
				</div>
			</div>

			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">简介</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="desc" rows="5"><?=$data['desc']?></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">关键词</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="tdk_key" rows="5"><?=$data['tdk_key']?></textarea>
				</div>
			</div>
            <div class="layui-form-item">
                <label class="layui-form-label">缩略图</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadLogo"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 500*350</div>
			<div class="layui-form-item">
				<label class="layui-form-label">详情</label>
				<div class="editor">
				    <div id="toolbar-editor"></div>
				    <div id="content-editor"></div>
				    <textarea id="editorText" name="detail" style="display:none"><?=$data['detail']?></textarea>
				</div>
			</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script src="<?=$dir_public?>/common/js/editor.js"></script>
<script>
	layui.config({ base: '<?=$dir_public?>/layui/lay/extend/' });
	layui.use(['form', 'upload', 'laydate', 'ajaxCascader'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate, sortTree = layui.ajaxCascader;

		//日期时间选择器
		laydate.render({
			elem: '#release_time',
			type: 'datetime'
		});

		var selectSortId = "<?=$data['sort_id']?>";
		if (selectSortId == 5) {
			$(".sort5").show();
		}  else {
			$(".sort5").hide();
		}

		var dir_public = "<?=$dir_public?>";

		/* 上传头像 */
        upload.render({
            elem: "#uploadLogo",
            url: "<?=url('admin/image/upload_news')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + dir_public + res.data + '">').css('background', 'none');
            }
        });

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('admin/news/news_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })

		/** 二级单选 **/
		var roomArr = <?=json_encode($news_sort)?>;
		var sort_id = "<?=$data['sort_id']?>";
		var sort_pid = "<?=$sort['pid']?>";
		var chooseData = [];
        if (sort_id) {
			 chooseData.push(sort_pid);
		    chooseData.push(sort_id);
        }
        sortTree.load({
            elem:'#newssort',
            width: 388,
            height: 36,
            prop: { value: 'id', label: 'name', children: 'list'},
            clicklast: true,
            data: roomArr,
            chooseData: chooseData
        });
        sortTree.on('click', '#newssort', function(){
            var arr = sortTree.getChooseData(); //数组的最后一个元素

			if (arr.length == 1) {
				$("input[name='sort_id']").val('');
			}
			if (arr.length == 2) {
				$("input[name='sort_id']").val(arr[1]);

				if (arr[1] == 5) {
					$(".sort5").show();
				}  else {
					$(".sort5").hide();
				}
			}
        });
    })
</script>