<div class="content">
    <div class="tools">
        <form class="layui-form between">
            <div class="left add">
                <a class="layui-btn centerT" href="<?=url("admin/news/news_add")?>">新增新闻中心</a>
            </div>
            <div class="right filter">
				<div class="layui-form-item">
                    <label class="layui-form-label">分类</label>
                    <div class="layui-input-block classify">
                        <input type="hidden" name="sort_pid" value="<?=$sort_pid?>">
                        <input type="hidden" name="sort_id" value="<?=$sort_id?>">
                        <cascader id="newssort"></cascader>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-block input">
                        <input type="text" name="keyword" placeholder="请填写标题" autocomplete="off" class="layui-input" value="<?=$keyword?>">
                    </div>
                </div>              
                <button type="submit" class="layui-btn">搜索</button>
            </div>
        </form>
    </div>
    <div class="wrap">
        <table class="table layui-table">
            <thead>
                <tr>
                    <th>序号</th>
					<th>缩略图</th>
                    <th>标题</th>
                    <th>来源</th>
                    <th>作者</th>
                    <th>发布时间</th>
                    <!-- <th>访问量</th> -->
                    <th>排序</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
				<?php if (!count($row)) { ?>
					<tr class="noData"><td colspan="20">暂无数据</td></tr>
				<?php } else {$i = 0; foreach ($row as $k => $v) { $i++;?>
					<tr>
						<td><?=$i?></td>
						<td class="logo"><img src="<?=$v['logo']?>"></td>
						<td><?=$v['title']?></td>
						<td><?=$v['source']?></td>
						<td><?=$v['author']?></td>
						<td><?=$v['release_time']?></td>
						<!-- <td><?=$v['visit_num']?></td> -->
						<td><input class="sequence" type="number" data-id="<?=$v['id']?>" value="<?=$v['sort']?>" onchange="sort(this, <?=$v['id']?>)"></td>
						<td>
							<div class="operate centerT">
								<a href="<?=url("admin/news/news_add", ['id' => $v['id']])?>" class="blue">编辑</a>
								<a href="javascript:;" class="red" onclick="deleteItem(this, <?=$v['id']?>)">删除</a>
							</div>
						</td>
					</tr>
				<?php } } ?>
            </tbody>
        </table>
		<?php include('../application/admin/view/public/page.php');?>
    </div>
</div>
<script>
	layui.config({ base: '<?=$dir_public?>/layui/lay/extend/' });
	layui.use(['form', 'ajaxCascader'], function () {
        var form = layui.form, sortTree = layui.ajaxCascader;

        /** 修改排序 **/
		sort = function(that,id) {
			sortChange(id, parseInt($(that).val()), "<?=url('admin/news/news_sort')?>")
        }

        /** 单个删除 **/
		deleteItem = function(that,id) {
			var json = {
				id: id
			}
			singleOperate(json, '确定要删除该信息吗？', "<?=url('admin/news/news_del')?>")
        }

		/** 二级单选 **/
		var roomArr = <?=json_encode($news_sort)?>;
		var sort_pid = '<?=$sort_pid?>';
		var sort_id = '<?=$sort_id?>';
		var chooseData = [];
		if (sort_pid) {
		    chooseData.push(sort_pid);
        }
        if (sort_id) {
		    chooseData.push(sort_id);
        }
        sortTree.load({
            elem:'#newssort',
            width: 218,
            height: 32,
            prop: { value: 'id', label: 'name', children: 'list'},
            clicklast: true,
            data: roomArr,
            chooseData: chooseData
        });
        sortTree.on('click', '#newssort', function(){
            var arr = sortTree.getChooseData(); //数组的最后一个元素

			console.log(arr);
			$(".filter input[name='sort_pid']").val(arr[0]);
			if (arr.length == 1) {
				$(".filter input[name='sort_id']").val('');
			}
			if (arr.length == 2) {
				$(".filter input[name='sort_id']").val(arr[1]);
			}
        });
	});
</script>