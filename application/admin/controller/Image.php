<?php
namespace app\admin\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Image extends \think\Controller {

	public function my_path_info($file) {
		$path_parts = array();
		$path_parts ['dirname'] = rtrim(substr($file, 0, strrpos($file, '/')),"/")."/";
		$path_parts ['basename'] = ltrim(substr($file, strrpos($file, '/')),"/");
		$path_parts ['extension'] = substr(strrchr($file, '.'), 1);
		$path_parts ['filename'] = ltrim(substr($path_parts ['basename'], 0, strrpos($path_parts ['basename'], '.')),"/");
		return $path_parts;
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 轮播图     轮播图     轮播图     轮播图     轮播图     轮播图     轮播图     轮播图     轮播图     轮播图     轮播图
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：@20221031 **/
	public function upload_banner() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：@20221031 **/
	public function upload_news() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：@20221031 **/
	public function upload_hotel() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：@20221031 **/
	public function upload_review() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 合作伙伴     合作伙伴     合作伙伴     合作伙伴     合作伙伴     合作伙伴     合作伙伴     合作伙伴     合作伙伴
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片上传 **/
	/** 作者：@20221031 **/
	public function upload_cooperate() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：直播中心图片上传 **/
	/** 作者：@20221031 **/
	public function upload_broadcast() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：系统设置图片上传 **/
	/** 作者：@20221031 **/
	public function upload_site() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['gif', 'jpg', 'jpeg', 'png'];
		//$conf_ext = ['mp4', 'mov', 'avi', 'mpeg'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }
		
		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location']];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 上传文件     上传文件     上传文件     上传文件     上传文件     上传文件     上传文件     上传文件     上传文件
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：文档上传 **/
	/** 作者：@20221031 **/
	public function upload_download() {	

		/** 引入sdk **/
		vendor('qcloud.cos-sdk-v5.vendor.autoload');
		//vendor('qcloud.stssdk.src.Sts');

		$file = $_FILES['file'];//得到传输的数据

		$name = $file['name'];

		/** 获取扩展名 */
		$file_ext = $this->my_path_info($name);
		$names	  = $file_ext['filename'];	
		$file_ext = $file_ext['extension'];
		$file_ext = strtolower($file_ext); // 统一将文件扩展名转成小写

		$conf_ext = ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'PDF', 'ppt', 'pptx'];
		//$conf_ext = ['gif', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'zip', 'rar', '7z', 'xls', 'xlsx', 'pdf', 'PDF', 'mp4', 'mov', 'avi', 'mpeg', 'ppt', 'pptx'];

		if (!in_array($file_ext, $conf_ext)) {
			return ['code' => 1, 'msg' => '对不起文件格式错误'];
		}
		
		/** 获取新的文件名 */
		$str       = '123456789abcdefhijkmnpqrstuvwxyz'; //去掉容易混淆的l,o,g字母(共32个)
		$file_name =  '';
		for ($i = 0; $i < 20; $i++) {
			$file_name .= $str[mt_rand(0,strlen($str) - 1)];
		}

		$file_url = 'shangcheng/pjcxlt/' . $file_name . '.' . $file_ext;

		//上传秘钥等信息
		$secretId = 'AKID53qPkwzsY3QcihXUGzeFxdcZO8pTH3lS';
		$secretKey = 'd3Sq8ScdTBlDUZdXFE3gBXnpmZDgw2yn';

		//地域节点
		$region = 'ap-shanghai';

		//实例化链接
		$cosClient = new \Qcloud\Cos\Client(
			array(
				'region' => $region,
				'schema' => 'https', //协议头部，默认为http
				'credentials'=> array(
					'secretId'  => $secretId ,
					'secretKey' => $secretKey
				)
			)
		);
	
		//存储桶名称 格式：BucketName-APPID
        $bucket = "partner-cos-1304859415";

        //上传文件的临时路径
        $srcPath = $file['tmp_name'];
        $file = fopen($srcPath, 'rb');
        if ($file) {
            $result = $cosClient->Upload(
                $bucket = $bucket,
                $key = $file_url,
                $body = $file);
        }

		return ['code' => 200, 'msg' => '上传成功', 'data' => 'https://' . $result['Location'], 'name' => $name, 'suffix' => $file_ext];
	}
}