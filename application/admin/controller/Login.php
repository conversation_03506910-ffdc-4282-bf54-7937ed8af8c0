<?php
namespace app\admin\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Login extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20220413 **/
	public function _initialize() {

    }

	/** 功能：登录 **/
	/** 作者：穠@20220413 **/
	public function login() {

		return $this->fetch();
	}

	/** 功能：密码登录 **/
	/** 作者：穠@20220413 **/
	public function login_pwd() {
		
		/** 对外接收参数 **/
		$username = htmlspecialchars(input('username', ''));	//获取帐号
		$password = htmlspecialchars(input('password', ''));	//获取密码
		$rember	  = input('rember', '');						//记住密码
		
		/** 查询账号信息 **/
		$row = Db::table('admin')	//【用户账号表】
			   ->field('id,uid,username,password,type,is_del')
			   ->where('username', $username)
			   ->find();
		
		/** 判断此账号是否存在，不存在则返回提示信息 **/
		/** 判断密码是否正确，密码是以uid拼接密码进行md5的加密字符串 **/
		/** 分开判断是为了返回信息提示中的准确性，提高用户的应用体验 **/
		if (!$row) {
			return ['code' => 1, 'msg' => '对不起，用户名或密码有误', 'data' => ''];
		}

		if ($row['is_del'] == 2) {
			return ['code' => 1, 'msg' => '对不起，该用户已被删除', 'data' => ''];
		}

		if (md5($row['uid'] . $password) == $row['password']) {
			Session::set('pjcxlt_username', $username);
			Session::set('pjcxlt_uid', $row['uid']);
			Session::set('pjcxlt_type', $row['type']);

			if ($rember) {
				Cookie::set("pjcxlt_username", $username, 3600*24);
				Cookie::set("pjcxlt_password", $password, 3600*24);
				Cookie::set("pjcxlt_type", $row['type'], 3600*24);
				Cookie::set("pjcxlt_uid", $row['uid'], 3600*24);
			} else {
				Cookie::has('pjcxlt_username') ? Cookie::delete("pjcxlt_username") : '';
				Cookie::has('pjcxlt_password') ? Cookie::delete("pjcxlt_password") : '';
				Cookie::has('pjcxlt_type') ? Cookie::delete("pjcxlt_type") : '';
				Cookie::has('pjcxlt_uid') ? Cookie::delete("pjcxlt_uid") : '';
			}

			Db::table('admin')->where('id', $row['id'])->setField('login_time', date('Y-m-d H:i:s'));
			return ['code' => 200, 'msg' => '登录成功', 'data' => $row];
		} else {
			return ['code' => 1, 'msg' => '对不起，用户名或密码有误', 'data' => ''];
		}		
	}

	/** 功能：修改密码页 **/
	/** 作者：穠@20220413 **/
	public function change() {
		return $this->fetch();
	}

	/** 功能：修改自己账户密码 **/
	/** 作者：穠@20220413 **/
	public function update_pwd() {
		
		/** 对外接收参数 **/
        $admin_uid	  = session('pjcxlt_uid');
		$raw_password = htmlspecialchars(trim(input('raw_password')));		//原密码
        $password	  = htmlspecialchars(trim(input('password', '')));		//现密码
        $repassword	  = htmlspecialchars(trim(input('repassword', '')));	//再次输入的密码

        $result = $this->validate(
            [
                'raw_password' => $raw_password,
				'password'	   => $password,
                'repassword'   => $repassword
            ],[
				'raw_password' => 'require',
                'password'	   => 'require|min:6',
                'repassword'   => 'require|confirm:password'
            ],[
                'raw_password.require' => '请输入原密码',
                'password.require'     => '请输入密码',
                'repassword.require'   => '请再次输入密码',
                'password.min'         => '密码不能低于6位',
                'repassword.confirm'   => '两次输入的密码不一致'
            ]   
        );
        if($result !== true) {
            return ['code' => 1, 'msg' => $result];
        } 

		/** 查询原密码信息 **/
		$admin = db('admin')->where('uid', $admin_uid)->value('password');

		if (md5($admin_uid . $raw_password) != $admin) {
			return ['code' => 1, 'msg' => '原密码不正确'];
		}

		Db::table('admin')->where('uid', $admin_uid)->update(['password' => md5($admin_uid . $password), 'passwords' => $password]);

		/* 注销当前账号，重定向到来源页 */
		Session::delete('pjcxlt_username');
		Session::delete('pjcxlt_uid');
		Session::delete('pjcxlt_type');
		Cookie::has('pjcxlt_username') ? Cookie::delete("pjcxlt_username") : '';
		Cookie::has('pjcxlt_password') ? Cookie::delete("pjcxlt_password") : '';
		Cookie::has('pjcxlt_type') ? Cookie::delete("pjcxlt_type") : '';
		Cookie::has('pjcxlt_uid') ? Cookie::delete("pjcxlt_uid") : '';

		return ['code' => 200, 'msg' => '重置密码成功'];
    }
	
	/** 功能：退出登录 **/
	/** 作者：穠@20220413 **/
	public function logout() {

		/* 注销当前账号，重定向到来源页 */
		Session::delete('pjcxlt_username');
		Session::delete('pjcxlt_uid');
		Session::delete('pjcxlt_type');
		Cookie::has('pjcxlt_username') ? Cookie::delete("pjcxlt_username") : '';
		Cookie::has('pjcxlt_password') ? Cookie::delete("pjcxlt_password") : '';
		Cookie::has('pjcxlt_type') ? Cookie::delete("pjcxlt_type") : '';
		Cookie::has('pjcxlt_uid') ? Cookie::delete("pjcxlt_uid") : '';
		$this->redirect($_SERVER['HTTP_REFERER']);
	}
}
