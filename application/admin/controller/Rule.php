<?php
namespace app\admin\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Rule extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20221013 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (<PERSON>ie::has('pjcxlt_uid') && <PERSON><PERSON>::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('admin/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置     系统设置
	// +---------------------------------------------------------------------------------------------------------------------
    
	/** 功能：系统设置 **/
	/** 作者：锂@20221013 **/
	public function site() {
        
		/** 查询系统设置信息 **/
		$data = Db::table('rule_site')->where('language', 1)->where('genre', 1)->order('id desc')->find();

		$this->assign('data', $data);
        return $this->fetch('admin@public/public');
	}

	/** 功能：提交系统设置 **/
	/** 作者：锂@20221013 **/
	public function site_edit() {

		/** 对外接受参数 **/
		$data['genre']				= input('genre', 1);					//类型 1：pc 2：移动
		$data['language']			= input('language', 1);					//语言 1：中文 2：英文
		$data['name']				= input('name', '');					//网站名称
		$data['logo']				= input('logo', '');					//网站LOGO
		$data['service']			= input('service', '');					//官方微信服务号
		$data['subscribe']			= input('subscribe', '');				//官方微信订阅号
		$data['blog']				= input('blog', '');					//官方微博
		$data['record']				= input('record', '');					//备案号
		$data['guest_uid']			= input('guest_uid', '');				//嘉宾注册
		$data['participation_uid']	= input('participation_uid', '');		//参会注册
		$data['sign_uid']			= input('sign_uid', '');				//报名入口
		$data['business_uid']		= input('business_uid', '');			//名片墙
		$data['learning_contacts']	= input('learning_contacts', '');		//学术联络 联系人
		$data['learning_phone']		= input('learning_phone', '');			//学术联络 电话
		$data['learning_fax']		= input('learning_fax', '');			//学术联络 传真
		$data['learning_mail']		= input('learning_mail', '');			//学术联络 邮箱
		$data['learning_address']	= input('learning_address', '');		//学术联络 地址
		$data['business_contacts']	= input('business_contacts', '');		//商务合作 联系人
		$data['business_phone']		= input('business_phone', '');			//商务合作 电话
		$data['business_fax']		= input('business_fax', '');			//商务合作 传真
		$data['business_mail']		= input('business_mail', '');			//商务合作 邮箱
		$data['business_address']	= input('business_address', '');		//商务合作 地址
		$data['addtime']			= date('Y-m-d H:i:s');					//添加时间

		//验证参数
		$result = $this->validate(
					[
						'name' => $data['name'],
						'logo' => $data['logo']
					],[
						'name' => 'require',
						'logo' => 'require'
					],[
						'name.require' => '请填写网站名称',
						'logo.require' => '请上传网站LOGO'
					]);

		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}
		
		/** 添加新的系统设置 **/
		Db::table('rule_site')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | H5 联系我们     H5 联系我们     H5 联系我们     H5 联系我们     H5 联系我们     H5 联系我们     H5 联系我们
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：H5 联系我们 **/
	/** 作者：锂@20240131 **/
	public function h5_about() {
        
		/** 查询H5 联系我们信息 **/
		$data = Db::table('h5_about')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('admin@public/public');
	}

	/** 功能：提交H5 联系我们 **/
	/** 作者：锂@20240131 **/
	public function h5_about_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 1);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的H5 联系我们 **/
		Db::table('h5_about')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | H5 外链设置     H5 外链设置     H5 外链设置     H5 外链设置     H5 外链设置     H5 外链设置     H5 外链设置
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：H5 外链设置 **/
	/** 作者：锂@20240131 **/
	public function h5_likes() {
        
		/** 查询H5 外链设置信息 **/
		$data = Db::table('h5_likes')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('admin@public/public');
	}

	/** 功能：提交H5 外链设置 **/
	/** 作者：锂@20240131 **/
	public function h5_likes_edit() {

		/** 对外接受参数 **/
		$data['genre']		        = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language']	        = input('language', 1);			//语言 1：中文 2：英文
		$data['invest_meeting']	    = input('invest_meeting', '');  //直播中心外链
		$data['transfer_meeting']	= input('transfer_meeting', '');//直播中心外链
		$data['broadcast']	        = input('broadcast', '');		//直播中心外链
		$data['photo']	            = input('photo', '');		    //直播中心外链
		$data['focus']	            = input('focus', '');		    //直播中心外链
		$data['interact']	        = input('interact', '');		//直播中心外链
		$data['card']	            = input('card', '');		    //直播中心外链
		$data['survey']	            = input('survey', '');		    //直播中心外链
		$data['kefu']	            = input('kefu', '');		    //直播中心外链
		$data['addtime']	        = date('Y-m-d H:i:s');			//添加时间
		
		/** 添加新的H5 外链设置 **/
		Db::table('h5_likes')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | TDK设置     TDK设置     TDK设置     TDK设置     TDK设置     TDK设置     TDK设置     TDK设置     TDK设置     TDK设置
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：TDK设置列表页 **/
	/** 作者：锂@20221229 **/
	public function tdk() {
		
		/** 对外接收参数 **/
		$num	 = input('num', 10);			//每页显示几条
		$keyword = trim(input('keyword', ''));	//检索条件
		
		/** 查询TDK设置信息 **/
		$row  = Db::table('rule_tdk')->order('sort, id desc')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('remarks|title', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		$this->assign('num', $num);
        $this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('keyword', $keyword);
		return $this->fetch('admin@public/public');
	}

	/** 功能：新增TDK设置 **/
	/** 作者：锂@20221229 **/
	public function tdk_add() {
		
		/** 对外接收参数 **/
		$id = input('id/d', '');	//TDK设置id

		/** 查询宣传报道信息 **/
		$data = Db::table('rule_tdk')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('admin@public/public');
	}

	/** 功能：TDK设置编辑 **/
	/** 作者：锂@20221229 **/
	public function tdk_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id/d', '');			//TDK设置id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 1);		//语言 1：中文 2：英文
		$data['remarks']  = input('remarks', '');		//说明
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['key']	  = input('key', '');			//关键词
		$data['sort']	  = input('sort', 1);			//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title'	=> $data['title']
									],[
										'title'	=> 'require'
									],[
										'title.require' => '请填写标题'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 写入数据 **/
		if ($id) {
			Db::table('rule_tdk')->where('id', $id)->update($data);
		} else {
			Db::table('rule_tdk')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：TDK设置删除 **/
	/** 作者：锂@20221229 **/
	public function tdk_del() {
		
		/** 对外接受参数 **/
		$id = input('id/d', '');	//TDK设置id

		Db::table('rule_tdk')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}

	/** 功能：排序 **/
	/** 作者：锂@20221229 **/
	public function tdk_sort() {
		
		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('rule_tdk')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '操作成功'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 修改密码     修改密码     修改密码     修改密码     修改密码     修改密码     修改密码     修改密码     修改密码
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：修改密码 **/
	/** 作者：锂@20221013 **/
	public function pwd() {
		return $this->fetch('admin@public/public');
	}
}