<?php
namespace app\admin\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Review extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (<PERSON>ie::has('pjcxlt_uid') && <PERSON><PERSON>::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('admin/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾     往届回顾
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：往届回顾 **/
	/** 作者：锂@20240131 **/
	public function review() {
        
		/** 对外接收参数 **/
		$keyword = trim(input('keyword', ''));		//检索条件
		$num	 = input('num/d', 10);				//每页显示多少条
		
		/** 查询论坛日程 **/
		$row  = Db::table('review')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
		$this->assign('keyword', $keyword);
		return $this->fetch('admin@public/public');
	}

	/** 功能：论坛日程信息 **/
	/** 作者：@20210729 **/
	public function review_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//往届回顾id

		/** 查询往届回顾信息 **/
		$data = Db::table('review')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('admin@public/public');
	}

	/** 功能：提交往届回顾 **/
	/** 作者：锂@20240131 **/
	public function review_edit() {

		/** 对外接受参数 **/
		$id						  = input('id', '');					//往届回顾id
		$data['genre']			  = input('genre', 1);					//类型 1：pc 2：移动
		$data['language']		  = input('language', 1);				//语言 1：中文 2：英文
		$data['title']			  = input('title', '');					//标题
		$data['logo']			  = input('logo', '');					//缩略图
		$data['desc']			  = input('desc', '');					//简介
		$data['tdk_key']		  = input('tdk_key', '');				//关键词
		$data['theme_detail']	  = input('theme_detail', '');			//论坛主题
		$data['schedule_title']	  = input('schedule_title', '');		//论坛日程标题
		$data['schedule_day']	  = input('schedule_day', '');			//论坛日程会期
		$data['schedule_main']	  = input('schedule_main', '');			//论坛日程主会场
		$data['schedule_exhibit'] = input('schedule_exhibit', '');		//展览主会场
		$data['addtime']		  = date('Y-m-d H:i:s');				//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title' => $data['title'],
										'logo'	=> $data['logo']
									],[
										'title' => 'require',
										'logo'	=> 'require'
									],[
										'title.require' => '请填写标题',
										'logo.require'	=> '请上传缩略图'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review')->where('id', $id)->update($data);
		} else {
			Db::table('review')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	/** 功能：删除往届回顾 **/
	/** 作者：@20230216 **/
	public function review_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//往届回顾id

		Db::table('review')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：往届回顾排序 **/
	/** 作者：@20230216 **/
	public function review_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 论坛日程     论坛日程     论坛日程     论坛日程     论坛日程     论坛日程     论坛日程     论坛日程     论坛日程
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：论坛日程 **/
	/** 作者：@20210729 **/
	public function schedule() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$review_id	= input('review_id', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条
		
		/** 查询论坛日程 **/
		$row  = Db::table('review_schedule')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $review_id ? $row->where('review_id', $review_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();

		/** 查询往届回顾标题 **/
		$review_name = Db::table('review')->where('language', 1)->where('genre', 1)->column('id,title');

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('review', $review);
		$this->assign('keyword', $keyword);
		$this->assign('review_id', $review_id);
		$this->assign('review_name', $review_name);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：论坛日程信息 **/
	/** 作者：@20210729 **/
	public function schedule_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//论坛日程id

		/** 查询论坛日程信息 **/
		$data = Db::table('review_schedule')->where('id', $id)->find();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('review', $review);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function schedule_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//论坛日程id
		$data['genre']		= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']	= input('language', 1);			//语言 1：中文 2：英文
		$data['review_id']	= input('review_id', '');		//往届回顾id
		$data['title']		= input('title', '');			//标题
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'review_id' => $data['review_id'],
										'title'		=> $data['title']
									],[
										'review_id' => 'require',
										'title'		=> 'require'
									],[
										'review_id.require' => '请选择往届回顾',
										'title.require'		=> '请填写标题'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_schedule')->where('id', $id)->update($data);
		} else {
			Db::table('review_schedule')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除论坛日程 **/
	/** 作者：@20230216 **/
	public function schedule_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//论坛日程id

		Db::table('review_schedule')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：论坛日程排序 **/
	/** 作者：@20230216 **/
	public function schedule_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_schedule')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 时段     时段     时段     时段     时段     时段     时段     时段     时段     时段     时段     时段     时段
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：时段 **/
	/** 作者：@20210729 **/
	public function scheduletime() {

		/** 对外接收参数 **/
		$keyword	 = trim(input('keyword', ''));		//检索条件
		$schedule_id = input('schedule_id', '');		//论坛日程id
		$num		 = input('num/d', 10);				//每页显示多少条
		
		/** 查询时段 **/
		$row  = Db::table('review_schedule_time')->order('sort, id')->where('schedule_id', $schedule_id)->where('is_del', 1);
		$row  = $keyword ? $row->where('content', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询论坛日程 **/
		$schedule = Db::table('review_schedule')->where('id', $schedule_id)->find();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
		$this->assign('keyword', $keyword);
        $this->assign('schedule', $schedule);
		$this->assign('schedule_id', $schedule_id);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：时段信息 **/
	/** 作者：@20210729 **/
	public function scheduletime_add() {
		
		/** 对外接收参数 **/
		$id			 = input('id', '');				//时段id
		$schedule_id = input('schedule_id', '');	//论坛日程id

		/** 查询时段信息 **/
		$data = Db::table('review_schedule_time')->where('id', $id)->find();

		if ($data) {
			$schedule_id = $data['schedule_id'];
		}

		/** 查询论坛日程 **/
		$schedule = Db::table('review_schedule')->where('id', $schedule_id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('schedule', $schedule);
		$this->assign('schedule_id', $schedule_id);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function scheduletime_edit() {
		
		/** 对外接受参数 **/
		$id					 = input('id', '');					//时段id
		$data['review_id']	 = input('review_id', '');			//往届回顾id
		$data['schedule_id'] = input('schedule_id', '');		//论坛日程id
		$data['time']		 = input('time', '');				//会议时间
		$data['content']	 = input('content', '');			//会议内容
		$data['place']		 = input('place', '');				//会议地点
		$data['sort']		 = input('sort', 1);				//排序
		$data['addtime']	 = date('Y-m-d H:i:s');				//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'content' => $data['content']
									],[
										'content' => 'require'
									],[
										'content.require' => '请填写会议内容'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_schedule_time')->where('id', $id)->update($data);
		} else {
			Db::table('review_schedule_time')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除时段 **/
	/** 作者：@20230216 **/
	public function scheduletime_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//时段id

		Db::table('review_schedule_time')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：时段排序 **/
	/** 作者：@20230216 **/
	public function scheduletime_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_schedule_time')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾     演讲嘉宾
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：演讲嘉宾 **/
	/** 作者：@20210729 **/
	public function guest() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$review_id	= input('review_id', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条
		
		/** 查询演讲嘉宾 **/
		$row  = Db::table('review_guest')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('name|identity', 'like', "%$keyword%") : $row;
		$row  = $review_id ? $row->where('review_id', $review_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();

		/** 查询往届回顾标题 **/
		$review_name = Db::table('review')->where('language', 1)->where('genre', 1)->column('id,title');

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('review', $review);
		$this->assign('keyword', $keyword);
		$this->assign('review_id', $review_id);
		$this->assign('review_name', $review_name);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：演讲嘉宾信息 **/
	/** 作者：@20210729 **/
	public function guest_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//演讲嘉宾id

		/** 查询演讲嘉宾信息 **/
		$data = Db::table('review_guest')->where('id', $id)->find();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('review', $review);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑演讲嘉宾编辑 **/
	/** 作者：@20210729 **/
	public function guest_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//演讲嘉宾id
		$data['genre']		= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']	= input('language', 1);			//语言 1：中文 2：英文
		$data['review_id']	= input('review_id', '');		//往届回顾id
		$data['name']		= input('name', '');			//姓名
		$data['logo']		= input('logo', '');			//头像
		$data['identity']	= input('identity', '');		//身份
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'review_id' => $data['review_id'],
										'name'		=> $data['name'],
										'logo'		=> $data['logo'],
										'identity'	=> $data['identity']
									],[
										'review_id' => 'require',
										'name'		=> 'require',
										'logo'		=> 'require',
										'identity'	=> 'require'
									],[
										'review_id.require' => '请选择往届回顾',
										'name.require'		=> '请填写姓名',
										'logo.require'		=> '请上传头像',
										'identity.require'	=> '请填写身份'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_guest')->where('id', $id)->update($data);
		} else {
			Db::table('review_guest')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除演讲嘉宾 **/
	/** 作者：@20230216 **/
	public function guest_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//演讲嘉宾id

		Db::table('review_guest')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：演讲嘉宾排序 **/
	/** 作者：@20230216 **/
	public function guest_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_guest')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 图片集锦     图片集锦     图片集锦     图片集锦     图片集锦     图片集锦     图片集锦     图片集锦     图片集锦
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片集锦 **/
	/** 作者：@20210729 **/
	public function picture() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$review_id	= input('review_id', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条
		
		/** 查询图片集锦 **/
		$row  = Db::table('review_picture')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $review_id ? $row->where('review_id', $review_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();

		/** 查询往届回顾标题 **/
		$review_name = Db::table('review')->where('language', 1)->where('genre', 1)->column('id,title');

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('review', $review);
		$this->assign('keyword', $keyword);
		$this->assign('review_id', $review_id);
		$this->assign('review_name', $review_name);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：图片集锦信息 **/
	/** 作者：@20210729 **/
	public function picture_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//图片集锦id

		/** 查询图片集锦信息 **/
		$data = Db::table('review_picture')->where('id', $id)->find();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('review', $review);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑图片集锦编辑 **/
	/** 作者：@20210729 **/
	public function picture_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//图片集锦id
		$data['genre']		= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']	= input('language', 1);			//语言 1：中文 2：英文
		$data['review_id']	= input('review_id', '');		//往届回顾id
		$data['title']		= input('title', '');			//标题
		$data['logo']		= input('logo', '');			//图片
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'review_id' => $data['review_id'],
										'title'		=> $data['title'],
										'logo'		=> $data['logo'],
									],[
										'review_id' => 'require',
										'title'		=> 'require',
										'logo'		=> 'require'
									],[
										'review_id.require' => '请选择往届回顾',
										'title.require'		=> '请填写标题',
										'logo.require'		=> '请上传图片'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_picture')->where('id', $id)->update($data);
		} else {
			Db::table('review_picture')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除图片集锦 **/
	/** 作者：@20230216 **/
	public function picture_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//图片集锦id

		Db::table('review_picture')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：图片集锦排序 **/
	/** 作者：@20230216 **/
	public function picture_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_picture')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 专题报告集     专题报告集     专题报告集     专题报告集     专题报告集     专题报告集     专题报告集     专题报告集
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：专题报告集 **/
	/** 作者：@20210729 **/
	public function report() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$review_id	= input('review_id', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条
		
		/** 查询专题报告集 **/
		$row  = Db::table('review_report')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $review_id ? $row->where('review_id', $review_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();

		/** 查询往届回顾标题 **/
		$review_name = Db::table('review')->where('language', 1)->where('genre', 1)->column('id,title');

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('review', $review);
		$this->assign('keyword', $keyword);
		$this->assign('review_id', $review_id);
		$this->assign('review_name', $review_name);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：专题报告集信息 **/
	/** 作者：@20210729 **/
	public function report_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//专题报告集id

		/** 查询专题报告集信息 **/
		$data = Db::table('review_report')->where('id', $id)->find();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('review', $review);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑专题报告集编辑 **/
	/** 作者：@20210729 **/
	public function report_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//专题报告集id
		$data['genre']		= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']	= input('language', 1);			//语言 1：中文 2：英文
		$data['review_id']	= input('review_id', '');		//往届回顾id
		$data['title']		= input('title', '');			//标题
		$data['time']		= input('time', '');			//发布日期
		$data['url']		= input('url', '');				//文件地址
		$data['url_name']	= input('url_name', '');		//文件名称
		$data['suffix']		= input('suffix', '');			//后缀名
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'review_id' => $data['review_id'],
										'title'		=> $data['title'],
										'url'		=> $data['url'],
									],[
										'review_id' => 'require',
										'title'		=> 'require',
										'url'		=> 'require'
									],[
										'review_id.require' => '请选择往届回顾',
										'title.require'		=> '请填写标题',
										'url.require'		=> '请上传文件'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_report')->where('id', $id)->update($data);
		} else {
			Db::table('review_report')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除专题报告集 **/
	/** 作者：@20230216 **/
	public function report_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//专题报告集id

		Db::table('review_report')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：专题报告集排序 **/
	/** 作者：@20230216 **/
	public function report_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_report')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 会后刊     会后刊     会后刊     会后刊     会后刊     会后刊     会后刊     会后刊     会后刊     会后刊     会后刊
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：会后刊 **/
	/** 作者：@20210729 **/
	public function prints() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$review_id	= input('review_id', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条
		
		/** 查询会后刊 **/
		$row  = Db::table('review_prints')->order('sort, id')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $review_id ? $row->where('review_id', $review_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();

		/** 查询往届回顾标题 **/
		$review_name = Db::table('review')->where('language', 1)->where('genre', 1)->column('id,title');

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('review', $review);
		$this->assign('keyword', $keyword);
		$this->assign('review_id', $review_id);
		$this->assign('review_name', $review_name);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：会后刊信息 **/
	/** 作者：@20210729 **/
	public function prints_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//会后刊id

		/** 查询会后刊信息 **/
		$data = Db::table('review_prints')->where('id', $id)->find();

		/** 查询往届回顾 **/
		$review = Db::table('review')->where('language', 1)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('review', $review);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑会后刊编辑 **/
	/** 作者：@20210729 **/
	public function prints_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//会后刊id
		$data['genre']		= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']	= input('language', 1);			//语言 1：中文 2：英文
		$data['review_id']	= input('review_id', '');		//往届回顾id
		$data['title']		= input('title', '');			//标题
		$data['logo']		= input('logo', '');			//图片
		$data['url']		= input('url', '');				//文件地址
		$data['url_name']	= input('url_name', '');		//文件名称
		$data['suffix']		= input('suffix', '');			//后缀名
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'review_id' => $data['review_id'],
										'title'		=> $data['title'],
										'url'		=> $data['url'],
										'logo'		=> $data['logo']
									],[
										'review_id' => 'require',
										'title'		=> 'require',
										'url'		=> 'require',
										'logo'		=> 'require'
									],[
										'review_id.require' => '请选择往届回顾',
										'title.require'		=> '请填写标题',
										'url.require'		=> '请上传文件',
										'logo.require'		=> '请上传图片'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('review_prints')->where('id', $id)->update($data);
		} else {
			Db::table('review_prints')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除会后刊 **/
	/** 作者：@20230216 **/
	public function prints_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//会后刊id

		Db::table('review_prints')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：会后刊排序 **/
	/** 作者：@20230216 **/
	public function prints_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('review_prints')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}