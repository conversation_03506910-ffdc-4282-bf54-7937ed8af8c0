<?php
namespace app\admin\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Picture extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (<PERSON>ie::has('pjcxlt_uid') && <PERSON><PERSON>::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('admin/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 图片直播     图片直播     图片直播     图片直播     图片直播     图片直播     图片直播     图片直播     图片直播
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：图片直播 **/
	/** 作者：@20210729 **/
	public function picture() {

		/** 对外接收参数 **/
		$keyword  = trim(input('keyword', ''));		//检索条件
		$num	  = input('num/d', 10);				//每页显示多少条

		$row  = Db::table('h5_broadcast_picture')->order('sort, id')->where('language', 1)->where('genre', 3)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
		$this->assign('keyword', $keyword);
		return $this->fetch('admin@public/public');		
	}

	/** 功能：图片直播信息 **/
	/** 作者：@20210729 **/
	public function picture_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//图片直播id

		/** 查询图片直播信息 **/
		$data = Db::table('h5_broadcast_picture')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('admin@public/public');
	}

	/** 功能：编辑图片直播编辑 **/
	/** 作者：@20210729 **/
	public function picture_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id', '');			//图片直播id
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 1);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['logo']	  = input('logo', '');			//缩略图
		$data['url']	  = input('url', '');			//链接地址
		$data['sort']	  = input('sort', 1);			//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title' => $data['title'],
										'logo'	=> $data['logo']
									],[
										'title' => 'require',
										'logo'	=> 'require'
									],[
										'title.require' => '请填写标题',
										'logo.require'	=> '请上传图片'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('h5_broadcast_picture')->where('id', $id)->update($data);
		} else {
			Db::table('h5_broadcast_picture')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除图片直播 **/
	/** 作者：@20230216 **/
	public function picture_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//图片直播id

		Db::table('h5_broadcast_picture')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：图片直播排序 **/
	/** 作者：@20230216 **/
	public function picture_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('h5_broadcast_picture')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}