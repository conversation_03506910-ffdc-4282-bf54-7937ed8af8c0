<?php
// 简化的数据库配置，不依赖PDO扩展
return [
  // 数据库类型
  'type'            => 'mysql',
  // 服务器地址 - 修改为本地开发环境
  'hostname'        => 'rm-cn-ioy4bnmu00003eeo.rwlb.rds.aliyuncs.com',
  // 数据库名 - 可以保持原名或修改为本地数据库名
  'database'        => 'pujiangforum',
  // 用户名 - 修改为本地数据库用户名
  'username'        => 'huiyi_31',
  // 密码 - 修改为本地数据库密码（如果没有密码则留空）
  'password'        => 'huiyi_31@Ali4Launch',
  // 端口
  'hostport'        => '3306',
  // 数据库编码
  'charset'         => 'utf8mb4',
  // 数据库表前缀
  'prefix'          => '',
  // 数据库调试模式
  'debug'           => true,
  // 数据库部署方式
  'deploy'          => 0,
  // 数据库读写是否分离
  'rw_separate'     => false,
  // 读写分离后 主服务器数量
  'master_num'      => 1,
  // 指定从服务器序号
  'slave_no'        => '',
  // 是否严格检查字段是否存在
  'fields_strict'   => true,
  // 数据集返回类型
  'resultset_type'  => 'array',
  // 自动写入时间戳字段
  'auto_timestamp'  => false,
  // 时间字段取出后的默认时间格式
  'datetime_format' => 'Y-m-d H:i:s',
  // 是否需要进行SQL性能分析
  'sql_explain'     => false,
]; 