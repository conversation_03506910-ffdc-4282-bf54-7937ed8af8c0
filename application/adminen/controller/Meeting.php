<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Meeting extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：班车信息 **/
	/** 作者：锂@20240131 **/
	public function vehicle() {
        
		/** 查询班车信息信息 **/
		$data = Db::table('meeting_vehicle')->order('id desc')->where('language', 2)->where('genre', 1)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交班车信息 **/
	/** 作者：锂@20240131 **/
	public function vehicle_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的班车信息 **/
		Db::table('meeting_vehicle')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：推荐酒店 **/
	/** 作者：锂@20240131 **/
	public function hotel() {
        
		/** 查询推荐酒店信息 **/
		$data = Db::table('meeting_hotel')->order('id desc')->where('language', 2)->where('genre', 1)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交推荐酒店 **/
	/** 作者：锂@20240131 **/
	public function hotel_edit() {

		/** 对外接受参数 **/
		$data['genre']		  = input('genre', 1);				//类型 1：pc 2：移动
		$data['language']	  = input('language', 2);			//语言 1：中文 2：英文
		$data['title']		  = input('title', '');				//标题
		$data['logo']		  = input('logo', '');				//酒店图片
		$data['desc']		  = input('desc', '');				//简介
		$data['tdk_key']	  = input('tdk_key', '');			//关键词
		$data['hotel']		  = input('hotel', '');				//酒店信息
		$data['book_time']	  = input('book_time', '');			//预订时间
		$data['book_lodging'] = input('book_lodging', '');		//住宿预订
		$data['plat']		  = input('plat', '');				//会场地图
		$data['url_title']	  = input('url_title', '');			//预订单标题
		$data['url']		  = input('url', '');				//文件地址
		$data['url_name']	  = input('url_name', '');			//文件名称
		$data['suffix']		  = input('suffix', '');			//后缀名
		$data['addtime']	  = date('Y-m-d H:i:s');			//添加时间
		
		/** 添加新的推荐酒店 **/
		Db::table('meeting_hotel')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 交通环境     交通环境     交通环境     交通环境     交通环境     交通环境     交通环境     交通环境     交通环境
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：交通环境 **/
	/** 作者：@20210729 **/
	public function traffic() {

		/** 对外接收参数 **/
		$keyword  = trim(input('keyword', ''));		//检索条件
		$num	  = input('num/d', 10);				//每页显示多少条

		$row  = Db::table('meeting_hotel_traffic')->order('sort, id')->where('language', 2)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('start_address|name', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
		$this->assign('keyword', $keyword);
		return $this->fetch('adminen@public/public');		
	}

	/** 功能：交通环境信息 **/
	/** 作者：@20210729 **/
	public function traffic_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//交通环境id

		/** 查询交通环境信息 **/
		$data = Db::table('meeting_hotel_traffic')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function traffic_edit() {
		
		/** 对外接受参数 **/
		$id						= input('id', '');				//交通环境id
		$data['genre']			= input('genre', 1);			//类型 1：pc 2：移动
		$data['language']		= input('language', 2);			//语言 1：中文 2：英文
		$data['start_address']	= input('start_address', '');	//起始地
		$data['name']			= input('name', '');			//名称/位置
		$data['distance']		= input('distance', '');		//距酒店公里数
		$data['method']			= input('method', '');			//如何到达酒店
		$data['sort']			= input('sort', 1);				//排序
		$data['addtime']		= date('Y-m-d H:i:s');			//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'start_address' => $data['start_address'],
										'name'			=> $data['name'],
										'distance'		=> $data['distance'],
										'method'		=> $data['method']
									],[
										'start_address' => 'require',
										'name'			=> 'require',
										'distance'		=> 'require',
										'method'		=> 'require'
									],[
										'start_address.require' => '请填写起始地',
										'name.require'			=> '请填写名称/位置',
										'distance.require'		=> '请填写距酒店公里数',
										'method.require'		=> '请填写如何到达酒店'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('meeting_hotel_traffic')->where('id', $id)->update($data);
		} else {
			Db::table('meeting_hotel_traffic')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除交通环境 **/
	/** 作者：@20230216 **/
	public function traffic_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//交通环境id

		Db::table('meeting_hotel_traffic')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：交通环境排序 **/
	/** 作者：@20230216 **/
	public function traffic_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('meeting_hotel_traffic')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}