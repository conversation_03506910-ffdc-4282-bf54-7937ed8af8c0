<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Participation extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 参会说明     参会说明     参会说明     参会说明     参会说明     参会说明     参会说明     参会说明     参会说明
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：参会说明 **/
	/** 作者：锂@20240131 **/
	public function explain() {
        
		/** 查询参会说明信息 **/
		$data = Db::table('participation_explain')->order('id desc')->where('language', 2)->where('genre', 1)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交参会说明 **/
	/** 作者：锂@20240131 **/
	public function explain_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的参会说明 **/
		Db::table('participation_explain')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }
}