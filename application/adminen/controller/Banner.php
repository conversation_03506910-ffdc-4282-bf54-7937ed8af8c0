<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Banner extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | PC轮播图     PC轮播图     PC轮播图     PC轮播图     PC轮播图     PC轮播图     PC轮播图     PC轮播图     PC轮播图
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：轮播图管理 **/
	/** 作者：@20210406 **/
	public function banner() {
		
		/** 查询Banner信息 **/
		$row = Db::table('banner')->order('sort')->where('language', 2)->where('genre', 1)->where('is_del', 1)->select();

        $this->assign('row', $row);
		$this->assign('page_title', '轮播图管理');
		return $this->fetch('adminen@public/public');
	}
	
	/** 功能：轮播图编辑 **/
	/** 作者：@20210406 **/
	public function banner_add() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//Banner id

		/** 查询轮播图信息 **/
		$data = Db::table('banner')->where('id', $id)->find();;
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('page_title', '轮播图编辑');
		return $this->fetch('adminen@public/public');
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | H5轮播图     H5轮播图     H5轮播图     H5轮播图     H5轮播图     H5轮播图     H5轮播图     H5轮播图     H5轮播图
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：轮播图管理 **/
	/** 作者：@20210406 **/
	public function h5_banner() {
		
		/** 查询Banner信息 **/
		$row = Db::table('banner')->order('sort')->where('language', 2)->where('genre', 2)->where('is_del', 1)->select();

        $this->assign('row', $row);
		$this->assign('page_title', '轮播图管理');
		return $this->fetch('adminen@public/public');
	}
	
	/** 功能：轮播图编辑 **/
	/** 作者：@20210406 **/
	public function h5_banner_add() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//Banner id

		/** 查询轮播图信息 **/
		$data = Db::table('banner')->where('id', $id)->find();;
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('page_title', '轮播图编辑');
		return $this->fetch('adminen@public/public');
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 独立H5轮播图     独立H5轮播图     独立H5轮播图     独立H5轮播图     独立H5轮播图     独立H5轮播图     独立H5轮播图
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：轮播图管理 **/
	/** 作者：@20210406 **/
	public function dl_h5_banner() {
		
		/** 查询Banner信息 **/
		$row = Db::table('banner')->order('sort')->where('language', 2)->where('genre', 3)->where('is_del', 1)->select();

        $this->assign('row', $row);
		$this->assign('page_title', '轮播图管理');
		return $this->fetch('adminen@public/public');
	}
	
	/** 功能：轮播图编辑 **/
	/** 作者：@20210406 **/
	public function dl_h5_banner_add() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//Banner id

		/** 查询轮播图信息 **/
		$data = Db::table('banner')->where('id', $id)->find();;
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('page_title', '轮播图编辑');
		return $this->fetch('adminen@public/public');
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 轮播图操作     轮播图操作     轮播图操作     轮播图操作     轮播图操作     轮播图操作     轮播图操作     轮播图操作
	// +---------------------------------------------------------------------------------------------------------------------
	
	/** 功能：轮播图编辑提交 **/
	/** 作者：@20210406 **/
	public function banner_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id/d', '');			//Banner id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动 3：独立H5
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['logo']	  = input('logo', '');			//图片
		$data['type']	  = input('type', 1);			//类型 1：展示 2：富文本 3：外链
		$data['url']	  = input('url', '');			//外链
		$data['sort']	  = input('sort', '');			//排序
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//更新时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'logo' => $data['logo']
									],[
										'logo' => 'require'
									],[
										'logo.require' => '请上传图片'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 写入数据 **/
		if ($id) {
			Db::table('banner')->where('id', $id)->update($data);
		} else {
			Db::table('banner')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除轮播图 **/
	/** 作者：@20210406 **/
	public function banner_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//轮播图id

		Db::table('banner')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：轮播图排序 **/
	/** 作者：@20210406 **/
	public function banner_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('banner')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}