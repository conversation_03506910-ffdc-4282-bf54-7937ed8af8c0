<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class H5meeting extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20221013 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 论坛基本信息     论坛基本信息     论坛基本信息     论坛基本信息     论坛基本信息     论坛基本信息     论坛基本信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：论坛基本信息 **/
	/** 作者：锂@20240131 **/
	public function basic() {
        
		/** 查询论坛基本信息信息 **/
		$data = Db::table('h5_meeting_basic')->order('id desc')->where('language', 2)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交论坛基本信息 **/
	/** 作者：锂@20240131 **/
	public function basic_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的论坛基本信息 **/
		Db::table('h5_meeting_basic')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 会场交通     会场交通     会场交通     会场交通     会场交通     会场交通     会场交通     会场交通     会场交通
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：会场交通 **/
	/** 作者：锂@20240131 **/
	public function traffic() {
        
		/** 查询会场交通信息 **/
		$data = Db::table('h5_meeting_traffic')->order('id desc')->where('language', 2)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交会场交通 **/
	/** 作者：锂@20240131 **/
	public function traffic_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的会场交通 **/
		Db::table('h5_meeting_traffic')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店     推荐酒店
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：推荐酒店 **/
	/** 作者：锂@20240131 **/
	public function hotel() {
        
		/** 查询推荐酒店信息 **/
		$data = Db::table('h5_meeting_hotel')->order('id desc')->where('language', 2)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交推荐酒店 **/
	/** 作者：锂@20240131 **/
	public function hotel_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的推荐酒店 **/
		Db::table('h5_meeting_hotel')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息     班车信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：班车信息 **/
	/** 作者：锂@20240131 **/
	public function vehicle() {
        
		/** 查询班车信息信息 **/
		$data = Db::table('h5_meeting_vehicle')->order('id desc')->where('language', 2)->where('genre', 3)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交班车信息 **/
	/** 作者：锂@20240131 **/
	public function vehicle_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 3);			//类型 1：pc 2：移动 3：H5
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的班车信息 **/
		Db::table('h5_meeting_vehicle')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }
}