<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Index extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20220413 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }
	
	/** 功能：数据统计 **/
	/** 作者：穠@20220413 **/
	public function index() {
		
		$this->redirect('adminen/forum/blurb');
	}

	/** 功能：后台收缩 **/
	/** 作者：穠@20220413 **/
	public function shrink() {

		/** 对外接收参数 **/
		$shrink = input('shrink', 1);

		Session::set('pjcxlt_shrink', $shrink);

		return ['code' => 200, 'msg' => '状态已修改', 'msg' => Session::get('pjcxlt_shrink')];
	}
}
