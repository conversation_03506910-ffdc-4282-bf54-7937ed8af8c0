<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class H5broadcast extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心     直播中心
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：直播中心 **/
	/** 作者：@20210729 **/
	public function h5broadcast() {

		/** 对外接收参数 **/
		$keyword  = trim(input('keyword', ''));		//检索条件
		$num	  = input('num/d', 10);				//每页显示多少条
		$sort_id = input('sort_id', '');			//分类id

		$row = Db::table('h5_broadcast')->order('sort, id')->where('language', 2)->where('genre', 3)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询分类名称 **/
		$sort_name = Db::table('h5_broadcast_sort')->order('sort, id')->where('language', 2)->where('genre', 3)->column('id,name');

		/** 查询分类信息 **/
		$sort = Db::table('h5_broadcast_sort')->order('sort, id')->where('language', 2)->where('genre', 3)->where('is_del', 1)->select();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('sort', $sort);
        $this->assign('sort_id', $sort_id);
		$this->assign('keyword', $keyword);
        $this->assign('sort_name', $sort_name);
		return $this->fetch('adminen@public/public');		
	}

	/** 功能：直播中心信息 **/
	/** 作者：@20210729 **/
	public function h5broadcast_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//直播中心id

		/** 查询直播中心信息 **/
		$data = Db::table('h5_broadcast')->where('id', $id)->find();

		/** 查询分类 **/
		$sort = Db::table('h5_broadcast_sort')->order('sort, id')->where('language', 2)->where('genre', 3)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('sort', $sort);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function h5broadcast_edit() {
		
		/** 对外接受参数 **/
		$id					= input('id', '');				//直播中心id
		$data['genre']		= input('genre', 3);			//类型 1：pc 2：移动
		$data['language']	= input('language', 2);			//语言 1：中文 2：英文
		$data['sort_id']	= input('sort_id', '');			//分类id
		$data['title']		= input('title', '');			//标题
		$data['address']	= input('address', '');			//地点
		$data['logo']		= input('logo', '');			//缩略图
		$data['start_time'] = input('start_time', '');		//开始时间
		$data['end_time']	= input('end_time', '');		//结束时间
		$data['url']		= input('url', '');				//链接地址
		$data['sort']		= input('sort', 1);				//排序
		$data['addtime']	= date('Y-m-d H:i:s');			//添加时间

		if (!$data['end_time']) {
			$data['end_time'] = null;
		}

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title'		 => $data['title'],
										'address'	=> $data['address'],
										'sort_id'	 => $data['sort_id'],
										'logo'		 => $data['logo'],
										'start_time' => $data['start_time'],
										'end_time'	 => $data['end_time']
									],[
										'title'		 => 'require',
										'address'	=> 'require',
										'sort_id'	 => 'require',
										'logo'		 => 'require',
										'start_time' => 'require',
										'end_time'	 => 'require'
									],[
										'title.require'		 => '请填写标题',
										'address.require'	 => '请填写地点',
										'sort_id.require'	 => '请选择分类',
										'logo.require'		 => '请上传图片',
										'start_time.require' => '请选择开始时间',
										'end_time.require'	 => '请选择结束时间'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('h5_broadcast')->where('id', $id)->update($data);
		} else {
			Db::table('h5_broadcast')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除直播中心 **/
	/** 作者：@20230216 **/
	public function h5broadcast_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//直播中心id

		Db::table('h5_broadcast')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：直播中心排序 **/
	/** 作者：@20230216 **/
	public function h5broadcast_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('h5_broadcast')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 直播中心分类     直播中心分类     直播中心分类     直播中心分类     直播中心分类     直播中心分类     直播中心分类
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：直播中心分类 **/
	/** 作者：@20230905 **/
	public function h5broadcastsort() {

		/** 对外接收参数 **/
		$num	 = input('num/d', 10);				//每页显示多少条
		$keyword = trim(input('keyword', ''));		//检索条件
		
		/** 查询分类信息 **/
		$row  = Db::table('h5_broadcast_sort')->order('sort, id')->where('language', 2)->where('genre', 3)->where('is_del', 1);
		$row  = $keyword ? $row->where('name', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

        $this->assign('row', $row);
        $this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('keyword', $keyword);
		return $this->fetch('adminen@public/public');
	}
	
	/** 功能：分类编辑 **/
	/** 作者：@20230905 **/
	public function h5broadcastsort_add() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//分类id

		/** 查询分类信息 **/
		$data = Db::table('h5_broadcast_sort')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：分类编辑提交 **/
	/** 作者：@20230905 **/
	public function h5broadcastsort_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id/d', '');			//分类id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['name']	  = input('name', '');			//分类名称(日期)
		$data['sort']	  = input('sort', '');			//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//更新时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'name' => $data['name']
									],[
										'name' => 'require'
									],[
										'name.require' => '请选择日期'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 写入数据 **/
		if ($id) {
			Db::table('h5_broadcast_sort')->where('id', $id)->update($data);
		} else {
			Db::table('h5_broadcast_sort')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除分类 **/
	/** 作者：@20230905 **/
	public function h5broadcastsort_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//分类id

		Db::table('h5_broadcast_sort')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：分类排序 **/
	/** 作者：@20230905 **/
	public function h5broadcastsort_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('h5_broadcast_sort')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}