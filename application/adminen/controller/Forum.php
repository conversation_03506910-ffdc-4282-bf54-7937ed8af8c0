<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class Forum extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：锂@20240131 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && Cookie::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 论坛简介     论坛简介     论坛简介     论坛简介     论坛简介     论坛简介     论坛简介     论坛简介     论坛简介
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：论坛简介 **/
	/** 作者：锂@20240131 **/
	public function blurb() {
        
		/** 查询论坛简介信息 **/
		$data = Db::table('forum_blurb')->order('id desc')->where('language', 2)->where('genre', 1)->find();

		$this->assign('data', $data);
        return $this->fetch('adminen@public/public');
	}

	/** 功能：提交论坛简介 **/
	/** 作者：锂@20240131 **/
	public function blurb_edit() {

		/** 对外接受参数 **/
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['title']	  = input('title', '');			//标题
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['detail']	  = input('detail', '');		//详情
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间
		
		/** 添加新的论坛简介 **/
		Db::table('forum_blurb')->insert($data);
		
		return ['code' => 200, 'msg' => '已保存'];
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 组织机构     组织机构     组织机构     组织机构     组织机构     组织机构     组织机构     组织机构     组织机构
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：组织机构 **/
	/** 作者：@20210729 **/
	public function agency() {

		/** 对外接收参数 **/
		$keyword  = trim(input('keyword', ''));		//检索条件
		$num	  = input('num/d', 10);				//每页显示多少条
		$sort_id = input('sort_id', '');			//分类id

		$row = Db::table('forum_agency')->order('sort, id')->where('language', 2)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询分类名称 **/
		$sort_name = Db::table('forum_agency_sort')->order('sort, id')->where('language', 2)->where('genre', 1)->column('id,name');

		/** 查询分类信息 **/
		$sort = Db::table('forum_agency_sort')->order('sort, id')->where('language', 2)->where('genre', 1)->where('is_del', 1)->select();

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('sort', $sort);
        $this->assign('sort_id', $sort_id);
		$this->assign('keyword', $keyword);
        $this->assign('sort_name', $sort_name);
		return $this->fetch('adminen@public/public');		
	}

	/** 功能：组织机构信息 **/
	/** 作者：@20210729 **/
	public function agency_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//组织机构id

		/** 查询组织机构信息 **/
		$data = Db::table('forum_agency')->where('id', $id)->find();

		/** 查询分类 **/
		$sort = Db::table('forum_agency_sort')->order('sort, id')->where('language', 2)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('sort', $sort);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function agency_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id', '');			//组织机构id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['sort_id']  = input('sort_id', '');		//分类id
		$data['title']	  = input('title', '');			//标题
		$data['sort']	  = input('sort', 1);			//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title'   => $data['title'],
										'sort_id' => $data['sort_id']
									],[
										'title'   => 'require',
										'sort_id' => 'require'
									],[
										'title.require'   => '请填写标题',
										'sort_id.require' => '请选择分类'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('forum_agency')->where('id', $id)->update($data);
		} else {
			Db::table('forum_agency')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除组织机构 **/
	/** 作者：@20230216 **/
	public function agency_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//组织机构id

		Db::table('forum_agency')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：组织机构排序 **/
	/** 作者：@20230216 **/
	public function agency_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('forum_agency')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 组织机构分类     组织机构分类     组织机构分类     组织机构分类     组织机构分类     组织机构分类     组织机构分类
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：组织机构分类 **/
	/** 作者：@20230905 **/
	public function agencysort() {

		/** 对外接收参数 **/
		$num	 = input('num/d', 10);				//每页显示多少条
		$keyword = trim(input('keyword', ''));		//检索条件
		
		/** 查询分类信息 **/
		$row  = Db::table('forum_agency_sort')->order('sort, id')->where('language', 2)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('name', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

        $this->assign('row', $row);
        $this->assign('num', $num);
        $this->assign('page', $page);
        $this->assign('keyword', $keyword);
		return $this->fetch('adminen@public/public');
	}
	
	/** 功能：分类编辑 **/
	/** 作者：@20230905 **/
	public function agencysort_add() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//分类id

		/** 查询分类信息 **/
		$data = Db::table('forum_agency_sort')->where('id', $id)->find();
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：分类编辑提交 **/
	/** 作者：@20230905 **/
	public function agencysort_edit() {
		
		/** 对外接受参数 **/
		$id				  = input('id/d', '');			//分类id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['name']	  = input('name', '');			//名称
		$data['sort']	  = input('sort', '');			//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//更新时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'name' => $data['name']
									],[
										'name' => 'require'
									],[
										'name.require' => '请填写名称'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 写入数据 **/
		if ($id) {
			Db::table('forum_agency_sort')->where('id', $id)->update($data);
		} else {
			Db::table('forum_agency_sort')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除分类 **/
	/** 作者：@20230905 **/
	public function agencysort_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//分类id

		Db::table('forum_agency_sort')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：分类排序 **/
	/** 作者：@20230905 **/
	public function agencysort_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('forum_agency_sort')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}
}