<?php
namespace app\adminen\controller;
use think\Session;
use think\Cookie;
use think\Request;
use think\Db;

class News extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：穠@20220413 **/
	public function _initialize() {
		if (!Session::has('pjcxlt_uid')) {
			if (Cookie::has('pjcxlt_uid') && <PERSON><PERSON>::get('pjcxlt_uid')) {
				Session::set('pjcxlt_username', Cookie::get('pjcxlt_username'));
				Session::set('pjcxlt_type', Cookie::get('pjcxlt_type'));
				Session::set('pjcxlt_uid', Cookie::get('pjcxlt_uid'));
			} else {
				$this->redirect('adminen/login/login');
			}
		}
    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心     新闻中心
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：新闻中心 **/
	/** 作者：@20210729 **/
	public function news() {

		/** 对外接收参数 **/
		$keyword	= trim(input('keyword', ''));		//检索条件
		$sort_id	= input('sort_id', '');				//分类id
		$sort_pid	= input('sort_pid', '');			//分类id
		$num		= input('num/d', 10);				//每页显示多少条

		$row = Db::table('news')->order('sort desc, release_time desc')->where('language', 2)->where('genre', 1)->where('is_del', 1);
		$row  = $sort_id ? $row->where('sort_id', $sort_id) : $row;
		$row  = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 获取新闻中心二级分类 **/
		$news_sort = news_sort(2, 1);

        $this->assign('row', $row);
		$this->assign('num', $num);
        $this->assign('page', $page);
		$this->assign('keyword', $keyword);
		$this->assign('sort_id', $sort_id);
		$this->assign('sort_pid', $sort_pid);
		$this->assign('news_sort', $news_sort);
		return $this->fetch('adminen@public/public');		
	}

	/** 功能：新闻中心信息 **/
	/** 作者：@20210729 **/
	public function news_add() {
		
		/** 对外接收参数 **/
		$id = input('id', '');		//新闻中心id

		/** 查询新闻中心信息 **/
		$data = Db::table('news')->where('id', $id)->find();

		/** 查询选择的分类信息 **/
		$sort = Db::table('news_sort')->where('id', $data['sort_id'])->find();

		/** 获取新闻中心二级分类 **/
		$news_sort = news_sort(2, 1);
		
		$this->assign('id', $id);
		$this->assign('data', $data);
		$this->assign('sort', $sort);
		$this->assign('news_sort', $news_sort);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：编辑来客编辑 **/
	/** 作者：@20210729 **/
	public function news_edit() {
		
		/** 对外接受参数 **/
		$id						= input('id', '');					//新闻中心id
		$data['genre']			= input('genre', 1);				//类型 1：pc 2：移动
		$data['language']		= input('language', 2);				//语言 1：中文 2：英文
		$data['sort_id']		= input('sort_id', '');				//分类id
		$data['title']			= input('title', '');				//标题
		$data['logo']			= input('logo', '');				//缩略图
		$data['desc']			= input('desc', '');				//简介
		$data['tdk_key']		= input('tdk_key', '');				//关键词
		$data['detail']			= input('detail', '');				//详情
		$data['source']			= input('source', '');				//来源
		$data['author']			= input('author', '');				//作者
		$data['time']			= input('time', '');				//时间
		$data['place']			= input('place', '');				//地点
		$data['sort']			= input('sort', 1);					//排序
		$data['release_time']	= input('release_time', '');		//发布时间
		$data['addtime']		= date('Y-m-d H:i:s');				//添加时间

		if (!$data['release_time']) {
			$data['release_time'] = $data['addtime'];
		} else {
			if (strpos($data['release_time'], '00:00:00') !== false) {
				$data['release_time'] = str_replace("00:00:00", date('H:i:s'), $data['release_time']);
			}
		}

		/** 验证参数 **/
		$result = $this->validate(
									[
										'title'	  => $data['title'],
										'logo'	  => $data['logo'],
										'sort_id' => $data['sort_id']
									],[
										'title'	  => 'require',
										'logo'	  => 'require',
										'sort_id' => 'require'
									],[
										'title.require'	  => '请填写标题',
										'logo.require'	  => '请上传缩略图',
										'sort_id.require' => '请选择分类'
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 修改信息 **/
		if ($id) {
			Db::table('news')->where('id', $id)->update($data);
		} else {
			Db::table('news')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：删除新闻中心 **/
	/** 作者：@20230216 **/
	public function news_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//新闻中心id

		Db::table('news')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
	
	/** 功能：新闻中心排序 **/
	/** 作者：@20230216 **/
	public function news_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('news')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 新闻中心分类     新闻中心分类     新闻中心分类     新闻中心分类     新闻中心分类     新闻中心分类     新闻中心分类
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：新闻中心分类 **/
	/** 作者：@20210406 **/
	public function newssort() {

		/** 对外接收参数 **/
		$num	 = input('num/d', 10);				//每页显示多少条
		$pid	 = input('pid', 0);					//上级id
		$keyword = trim(input('keyword', ''));		//检索条件

		if (!$pid) {
			$pid = 0;
		}

		/** 查询分类信息 **/
		$row  = Db::table('news_sort')->order('sort, id')->where('pid', $pid)->where('language', 2)->where('genre', 1)->where('is_del', 1);
		$row  = $keyword ? $row->where('name', 'like', "%$keyword%") : $row;
		$row  = $row->paginate($num, false, ['query' => $this->request->param()]);
        $page = $row->render();

		/** 查询父级分类信息 **/
		$pdata = Db::name('news_sort')->where('id', $pid)->find();

		$this->assign('num', $num);
		$this->assign('pid', $pid);
		$this->assign('row', $row);
		$this->assign('page', $page);
		$this->assign('pdata', $pdata);
		$this->assign('keyword', $keyword);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：添加分类 **/
	/** 作者：@20210223 **/
	public function newssort_add() {
		
		/** 对外接收参数 **/
		$id	 = input('id/d', '');	//分类id
		$pid = input('pid/d', '');	//上级分类id

		/** 查询分类信息 **/
		$data = Db::name('news_sort')->where('id', $id)->find();

		/** 查询父级分类信息 **/
		if ($pid) {
			$pdata = Db::name('news_sort')->where('id', $pid)->find();
		} else {
			$pid = $data['pid'];

			$pdata = Db::name('news_sort')->where('id', $data['pid'])->find();
		}

		/** 查询父级分类组 **/
		$pdata_arr = Db::name('news_sort')->where('pid', $pdata['pid'])->where('language', 2)->where('genre', 1)->where('is_del', 1)->select();
		
		$this->assign('id', $id);
		$this->assign('pid', $pid);
		$this->assign('data', $data);
		$this->assign('pdata', $pdata);
		$this->assign('pdata_arr', $pdata_arr);
		return $this->fetch('adminen@public/public');
	}

	/** 功能：编辑分类 **/
	/** 作者：@20210223 **/
	public function newssort_edit() {

		/** 对外接受参数 **/
		$id				  = input('id', '');			//分类id
		$data['genre']	  = input('genre', 1);			//类型 1：pc 2：移动
		$data['language'] = input('language', 2);		//语言 1：中文 2：英文
		$data['pid']	  = input('pid/d', '');			//上级id
		$data['name']	  = input('name', '');			//名称
		$data['desc']	  = input('desc', '');			//简介
		$data['tdk_key']  = input('tdk_key', '');		//关键词
		$data['sort']	  = input('sort/d', '');		//排序
		$data['addtime']  = date('Y-m-d H:i:s');		//添加时间

		/** 验证参数 **/
		$result = $this->validate(
									[
										'name' => $data['name'],
									],[
										'name' => 'require',
									],[
										'name.require' => '请填写分类名称',
									]
								);
		
		/** 验证未通过返回提示信息 **/
		if (true !== $result) {
			return ['code' => 1, 'msg' => $result];
		}

		/** 写入数据 **/
		if ($id) {
			Db::table('news_sort')->where('id', $id)->update($data);
		} else {
			Db::table('news_sort')->insert($data);
		}
		
		return ['code' => 200, 'msg' => '已保存'];
	}

	/** 功能：修改分类排序 **/
	/** 作者：@20210223 **/
	public function newssort_sort() {

		/** 对外接收参数 **/
		$id	  = input('id/d', '');		//id
		$sort = input('sort/d', 1);		//排序
		
		Db::table('news_sort')->where('id', $id)->update(['sort' => $sort]);

		return ['code' => 200, 'msg' => '已修改'];
	}

	/** 功能：删除分类 **/
	/** 作者：@20210223 **/
	public function newssort_del() {

		/** 对外接受参数 **/
		$id = input('id', '');	//分类id
		
		/** 查询下级分类 **/
		$is = Db::table('news_sort')->where('pid', $id)->where('is_del', 1)->find();

		if ($is) {
			return ['code' => 1, 'msg' => '该分类下有下级分类，不可删除！'];
		}
		
		/** 删除分类 **/
		Db::table('news_sort')->where('id', $id)->setField('is_del', 2);

		return ['code' => 200, 'msg' => '已删除'];
	}
}