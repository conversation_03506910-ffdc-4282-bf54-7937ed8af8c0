<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="3">
		<input type="hidden" name="language" id="language" value="2">
        <div class="edit_wrap">
			<div class="title">推荐酒店</div>
			<div class="layui-form-item">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">简介</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="desc" rows="5"><?=$data['desc']?></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">关键词</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="tdk_key" rows="5"><?=$data['tdk_key']?></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">详情</label>
				<div class="editor">
				    <div id="toolbar-editor"></div>
				    <div id="content-editor"></div>
				    <textarea id="editorText" name="detail" style="display:none"><?=$data['detail']?></textarea>
				</div>
			</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script src="<?=$dir_public?>/common/js/editor.js"></script>
<script>
	layui.use(['form', 'upload'], function () {
        var form = layui.form, upload = layui.upload;

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/h5meeting/hotel_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							location.reload();
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>