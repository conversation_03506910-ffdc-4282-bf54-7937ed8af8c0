<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="2">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
        <div class="edit_wrap">
			<div class="title">交通环境</div>
			<div class="layui-form-item">
                <label class="layui-form-label">起始地</label>
                <div class="layui-input-block">
                    <input type="text" name="start_address" class="layui-input" value="<?=$data['start_address']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">名称/位置</label>
                <div class="layui-input-block">
                    <input type="text" name="name" class="layui-input" value="<?=$data['name']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">距酒店公里数</label>
                <div class="layui-input-block">
                    <input type="text" name="distance" class="layui-input" value="<?=$data['distance']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">如何到达酒店</label>
                <div class="layui-input-block">
                    <input type="text" name="method" class="layui-input" value="<?=$data['method']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/meeting/traffic_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>