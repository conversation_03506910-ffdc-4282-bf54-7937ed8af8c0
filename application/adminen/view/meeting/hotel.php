<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="2">
        <div class="edit_wrap">
			<div class="title">推荐酒店</div>
			<div class="layui-form-item">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" class="layui-input" value="<?=$data['title']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">预订时间</label>
                <div class="layui-input-block">
                    <input type="text" name="book_time" class="layui-input" value="<?=$data['book_time']?>">
                </div>
            </div>

			<div class="layui-form-item">
				<label class="layui-form-label">简介</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="desc" rows="5"><?=$data['desc']?></textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">酒店信息</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="hotel" rows="5"><?=$data['hotel']?></textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">关键词</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="tdk_key" rows="5"><?=$data['tdk_key']?></textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">住宿预订</label>
				<div class="layui-input-block">
					<textarea class="layui-textarea" name="book_lodging" rows="5"><?=$data['book_lodging']?></textarea>
				</div>
			</div>
			
			<div class="layui-form-item">
                <label class="layui-form-label">预订单标题</label>
                <div class="layui-input-block">
                    <input type="text" name="url_title" class="layui-input" value="<?=$data['url_title']?>">
                </div>
            </div>
			<div class="layui-form-item">
				<label class="layui-form-label">预订单文件</label>
				<input type="hidden" name="url_name" id="url_name" value="<?=$data['url_name']?>">
				<input type="hidden" name="url" id="url" value="<?=$data['url']?>">
				<input type="hidden" name="suffix" id="suffix" value="<?=$data['suffix']?>">
				<div class="layui-input-block fileA">
					<a id="fileA" href="<?=$dir_public . $data['url']?>" target="_blank" style="line-height:36px;color:#3385ff"><?=$data['url_name']?></a>
				</div>
				<button type="button" class="layui-btn" id="uploadUrl"><span class="iconfont icon-upload"></span>上传</button>
			</div>

            <div class="layui-form-item">
                <label class="layui-form-label">酒店图片</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadLogo"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 720*320</div>

			<div class="layui-form-item">
				<label class="layui-form-label">会场地图</label>
				<div class="editor">
				    <div id="toolbar-editor"></div>
				    <div id="content-editor"></div>
				    <textarea id="editorText" name="plat" style="display:none"><?=$data['plat']?></textarea>
				</div>
			</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script src="<?=$dir_public?>/common/js/editor.js"></script>
<script>
	layui.use(['form', 'upload'], function () {
        var form = layui.form, upload = layui.upload;

		var dir_public = "<?=$dir_public?>";

		/* 酒店图片 */
        upload.render({
            elem: "#uploadLogo",
            url: "<?=url('adminen/image/upload_hotel')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + dir_public + res.data + '">').css('background', 'none');
            }
        });

		if ($('#url').val()) {
			$(".fileA").show();
		} else {
			$(".fileA").hide();
		}

		//上传文档
		upload.render({
			elem: '#uploadUrl',
			url: '<?=url("adminen/image/upload_download")?>',
			accept: 'file',
			done: function (res) {
				if (res.code == 200) {
					var item = this.item;
					$("#fileA").text(res.name);
					$("#fileA").attr('href','<?=$dir_public?>' + res.data);
					$('#url').val(res.data);
					$('#url_name').val(res.name);
					$('#suffix').val(res.suffix);
					$(".fileA").show();
				} else {
					layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
				}
				
			}
		});

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/meeting/hotel_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							location.reload();
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>