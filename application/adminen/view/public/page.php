<div class="pages centerT">
	<span class="total">共 <?=$row->total()?> 条</span>
	<?php if ($page) { ?>
		<?=$page?>
	<?php } else { ?>
		<ul class="pagination">
			<li class="disabled"><span>«</span></li>
			<li class="active"><span>1</span></li>
			<li class="disabled"><span>»</span></li>
		</ul>
	<?php } ?>
	<form class="layui-form page" lay-filter="page">
		<div class="layui-input-block">
			<select name="num" lay-filter="pagenum">
				<option value="10">10 条/页</option>
				<option value="20">20 条/页</option>
				<option value="50">50 条/页</option>
			</select>
		</div>
	</form>
</div>
<script>
	layui.use(['form'], function () {
        var form = layui.form;
        //页数赋值
        form.val("page", {
            num: "<?=$num?>"
        });
        //选择页面跳转
        form.on('select(pagenum)', function(data){
            var form = $(".pages .page");
            form.submit();
        });
    })
</script>