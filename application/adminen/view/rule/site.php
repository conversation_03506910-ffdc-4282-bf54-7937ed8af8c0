<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="2">
        <div class="edit_wrap">
            <div class="title">网站设置</div>
			<div class="layui-form-item">
                <label class="layui-form-label">网站名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name"  class="layui-input" value="<?=$data['name']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">备案号</label>
                <div class="layui-input-block">
                    <input type="text" name="record" class="layui-input" value="<?=$data['record']?>">
                </div>
            </div>

			<div class="title">学术联络</div>
			<div class="layui-form-item">
                <label class="layui-form-label">联系人</label>
                <div class="layui-input-block">
                    <input type="text" name="learning_contacts" class="layui-input" value="<?=$data['learning_contacts']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">电话</label>
                <div class="layui-input-block">
                    <input type="text" name="learning_phone" class="layui-input" value="<?=$data['learning_phone']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">传真</label>
                <div class="layui-input-block">
                    <input type="text" name="learning_fax" class="layui-input" value="<?=$data['learning_fax']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">邮箱</label>
                <div class="layui-input-block">
                    <input type="text" name="learning_mail" class="layui-input" value="<?=$data['learning_mail']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">地址</label>
                <div class="layui-input-block">
                    <input type="text" name="learning_address" class="layui-input" value="<?=$data['learning_address']?>">
                </div>
            </div>
			
			<div class="title">参会联络/商务合作</div>
			<div class="layui-form-item">
                <label class="layui-form-label">联系人</label>
                <div class="layui-input-block">
                    <input type="text" name="business_contacts" class="layui-input" value="<?=$data['business_contacts']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">电话</label>
                <div class="layui-input-block">
                    <input type="text" name="business_phone" class="layui-input" value="<?=$data['business_phone']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">传真</label>
                <div class="layui-input-block">
                    <input type="text" name="business_fax" class="layui-input" value="<?=$data['business_fax']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">邮箱</label>
                <div class="layui-input-block">
                    <input type="text" name="business_mail" class="layui-input" value="<?=$data['business_mail']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">地址</label>
                <div class="layui-input-block">
                    <input type="text" name="business_address" class="layui-input" value="<?=$data['business_address']?>">
                </div>
            </div>

			<div class="title">链接地址</div>
			<div class="layui-form-item">
                <label class="layui-form-label">嘉宾注册</label>
                <div class="layui-input-block">
                    <input type="text" name="guest_uid" class="layui-input" value="<?=$data['guest_uid']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">参会注册</label>
                <div class="layui-input-block">
                    <input type="text" name="participation_uid" class="layui-input" value="<?=$data['participation_uid']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">报名入口</label>
                <div class="layui-input-block">
                    <input type="text" name="sign_uid" class="layui-input" value="<?=$data['sign_uid']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">名片墙</label>
                <div class="layui-input-block">
                    <input type="text" name="business_uid" class="layui-input" value="<?=$data['business_uid']?>">
                </div>
            </div>

			<div class="title">网站图片</div>
            <div class="layui-form-item">
                <label class="layui-form-label">网站LOGO</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="logo" id="logo" value="<?=$data['logo']?>">
                    <div class="uploadBtn" id="uploadLogo"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 高：70</div>
            <div class="layui-form-item">
                <label class="layui-form-label">官方微信服务号</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="service" id="service" value="<?=$data['service']?>">
                    <div class="uploadBtn" id="uploadService"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 200*200</div>
            <div class="layui-form-item">
                <label class="layui-form-label">官方微信订阅号</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="subscribe" id="subscribe" value="<?=$data['subscribe']?>">
                    <div class="uploadBtn" id="uploadSubscribe"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 200*200</div>
            <div class="layui-form-item">
                <label class="layui-form-label">官方微博</label>
                <div class="layui-form-upload logo">
                    <input type="hidden" name="blog" id="blog" value="<?=$data['blog']?>">
                    <div class="uploadBtn" id="uploadBlog"></div>
                </div>
            </div>
            <div class="tip">建议尺寸: 200*200</div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>
<script>
	layui.config({ base: '<?=$dir_public?>/layui/lay/extend/' });
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		var dir_public = "<?=$dir_public?>";

		//日期时间选择器
		laydate.render({
			elem: '#start_time',
			type: 'datetime'
		});

		/*上传网站LOGO*/
        upload.render({
            elem: "#uploadLogo",
            url: "<?=url('adminen/image/upload_site')?>",
            done: function (res) {
                var that = this.item;
                $("#logo").val(res.data);
                $(that).html('').append('<img src="' + dir_public + res.data + '">').css('background', 'none');
            }
        });

		/*官方微信服务号*/
        upload.render({
            elem: "#uploadService",
            url: "<?=url('adminen/image/upload_site')?>",
            done: function (res) {
                var that = this.item;
                $("#service").val(res.data);
                $(that).html('').append('<img src="' + res.data + '">').css('background', 'none');
            }
        });

		/*官方微信订阅号*/
        upload.render({
            elem: "#uploadSubscribe",
            url: "<?=url('adminen/image/upload_site')?>",
            done: function (res) {
                var that = this.item;
                $("#subscribe").val(res.data);
                $(that).html('').append('<img src="' + res.data + '">').css('background', 'none');
            }
        });

		/*官方微博*/
        upload.render({
            elem: "#uploadBlog",
            url: "<?=url('adminen/image/upload_site')?>",
            done: function (res) {
                var that = this.item;
                $("#blog").val(res.data);
                $(that).html('').append('<img src="' + res.data + '">').css('background', 'none');
            }
        });

		pic('#service');
		pic('#subscribe');
		pic('#blog');

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/rule/site_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							location.reload();
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>