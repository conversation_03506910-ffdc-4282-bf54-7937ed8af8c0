<div class="content">
	<form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="2">
		<input type="hidden" name="id" value="<?=$id?>">
		<div class="edit_wrap">
			<div class="title"><?=$id ? '编辑' : '添加';?>分类</div>
			<div class="layui-form-item">
				<label class="layui-form-label">名称</label>
				<div class="layui-input-block">
					<input type="text" name="name" class="layui-input" value="<?=$data['name']?>" lay-verify="required" lay-reqtext="请填写分类名称">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">上级分类</label>
				<div class="layui-input-block">
					<select name="pid">
						<?php if (!count($pdata_arr)) { ?>
							
						<?php } else { foreach ($pdata_arr as $k => $v) { ?>
							<?php if (!$pid) { ?>
								<option value="<?=$v['id']?>" <?=($data['pid'] == $v['id']) ? 'selected' : '';?>><?=$v['name']?></option>
							<?php } else { ?>
								<option value="<?=$v['id']?>" <?=($pid == $v['id']) ? 'selected' : '';?>><?=$v['name']?></option>
							<?php } ?>
						<?php } } ?>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$data['sort'] ? $data['sort'] : 1?>">
                </div>
            </div>
			<?php if ($pid > 0) { ?>
				<div class="layui-form-item">
					<label class="layui-form-label">简介</label>
					<div class="layui-input-block">
						<textarea class="layui-textarea" name="desc" rows="5"><?=$data['desc']?></textarea>
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">关键词</label>
					<div class="layui-input-block">
						<textarea class="layui-textarea" name="tdk_key" rows="5"><?=$data['tdk_key']?></textarea>
					</div>
				</div>
			<?php } ?>
		</div>
		<div class="layui-form-btns left">
			<button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
			<button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
		</div>
	</form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
		var form = layui.form, upload = layui.upload, laydate = layui.laydate;

		//提交数据
		form.on('submit(submit)', function (data) {
			$(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/news/newssort_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
		});
	})
</script>