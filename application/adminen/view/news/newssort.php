<div class="content">
	<div class="tools">
		<form class="layui-form between">
			<div class="left add">
                <a class="layui-btn centerT" href="<?=url("adminen/news/newssort_add", ['pid' => $pid])?>">添加分类</a>
            </div>
			<div class="right filter">
                <div class="layui-form-item">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-block input">
                        <input type="text" name="keyword" placeholder="请填写分类名称" autocomplete="off" class="layui-input" value="<?=$keyword?>">
                    </div>
                </div>              
                <button type="submit" class="layui-btn">搜索</button>
            </div>
		</form>
	</div>
	<div class="wrap">
		<table class="table layui-table">
			<thead>
				<tr>
					<th>序号</th>
					<th>名称</th>
					<th>上级分类</th>
					<th>排序</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<?php if (!count($row)) { ?>
					<tr class="noData"><td colspan="20">暂无数据</td></tr>
				<?php } else {$i = 0; foreach ($row as $k => $v) { $i++;?>
					<tr>
						<td><?=$i?></td>
						<td><?=$v['name']?></td>
						<td>
							<?php if ($pdata) { ?>
								<?=$pdata['name']?>
							<?php } else { ?>
								无
							<?php } ?>
						</td>
						<td><input class="sequence" type="number" data-id="<?=$v['id']?>" value="<?=$v['sort']?>" onchange="sort(this, <?=$v['id']?>)"></td>
						<td>
							<div class="operate centerT">
								<a href="<?=url("adminen/news/newssort_add", ['id' => $v['id']])?>" class="blue">编辑</a>
								<?php if ($v['pid'] == 0) { ?>
									<a href="<?=url("adminen/news/newssort", ['pid' => $v['id']])?>" class="blue">下级分类</a>
								<?php } ?>
								<a href="javascript:;" class="red" onclick="deleteItem(this, <?=$v['id']?>)">删除</a>
							</div>
						</td>
					</tr>
				<?php } } ?>
			</tbody>
		</table>
		<?php include('../application/adminen/view/public/page.php');?>
	</div>
</div>

<script>
	layui.use(['form', 'laydate'], function () {
		var form = layui.form, laydate = layui.laydate;

        /** 修改排序 **/
		sort = function(that,id) {
			sortChange(id, parseInt($(that).val()), "<?=url('adminen/news/newssort_sort')?>")
        }

		/** 删除分类 **/
		deleteItem = function(that,id) {
			var json = {
				id: id
			};

			singleOperate(json, '确定要删除该分类吗？', "<?=url('adminen/news/newssort_del')?>");
        }
	});
</script>