<div class="content">
    <form class="layui-form">
		<input type="hidden" name="genre" id="genre" value="1">
		<input type="hidden" name="language" id="language" value="2">
		<input type="hidden" name="id" id="id" value="<?=$id?>">
		<input type="hidden" name="schedule_id" id="schedule_id" value="<?=$schedule_id?>">
		<input type="hidden" name="review_id" id="review_id" value="<?=$schedule['review_id']?>">
        <div class="edit_wrap">
			<div class="title">时段</div>
			<div class="layui-form-item">
                <label class="layui-form-label">论坛日程</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="<?=$schedule['title']?>" disabled>
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">会议时间</label>
                <div class="layui-input-block">
                    <input type="text" name="time" class="layui-input" value="<?=$data['time']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">会议内容</label>
                <div class="layui-input-block">
                    <input type="text" name="content" class="layui-input" value="<?=$data['content']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">会议地点</label>
                <div class="layui-input-block">
                    <input type="text" name="place" class="layui-input" value="<?=$data['place']?>">
                </div>
            </div>
			<div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" class="layui-input" value="<?=$id ? $data['sort'] : 1;?>" lay-verify="integer">
                </div>
            </div>
        </div>
        <div class="layui-form-btns left">
            <button type="button" class="layui-btn layui-btn-normal" onclick="javascript:history.back(-1);">返回</button>
            <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">保存</button>
        </div>
    </form>
</div>

<script>
	layui.use(['form', 'upload', 'laydate'], function () {
        var form = layui.form, upload = layui.upload, laydate = layui.laydate;

        form.on('submit(submit)', function (data) {
			//console.log(data.field);
            $(data.elem).attr('disabled', true);
			$.ajax({
				type: 'post',
				url: "<?=url('adminen/review/scheduletime_edit')?>",
				data: data.field,
				success: function (res) {
					if (res.code == 200) {
						layer.msg(res.msg, {icon: 1, shade: 0.3, time: 1500}, function(){
							window.location.href = document.referrer;
						});
					} else {
						$(data.elem).attr('disabled', false);
						layer.msg(res.msg, {icon: 2, shade: 0.3, time: 1500});
					}
				}
			});
			return false;
        })
    })
</script>