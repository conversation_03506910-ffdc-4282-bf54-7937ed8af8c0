<?php
namespace app\h5api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Rule extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240722 **/
	public function _initialize() {

    }

	/** 功能：联系我们 **/
	/** 作者：@20230825 **/
	public function about() {

		/** 查询联系我们信息 **/
		$data = Db::table('h5_about')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：论坛基本信息 **/
	/** 作者：@20230825 **/
	public function basic() {

		/** 查询联系我们信息 **/
		$data = Db::table('h5_meeting_basic')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：会场交通 **/
	/** 作者：@20230825 **/
	public function traffic() {

		/** 查询联系我们信息 **/
		$data = Db::table('h5_meeting_traffic')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：推荐酒店 **/
	/** 作者：@20230825 **/
	public function hotel() {

		/** 查询联系我们信息 **/
		$data = Db::table('h5_meeting_hotel')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：班车信息 **/
	/** 作者：@20230825 **/
	public function vehicle() {

		/** 查询联系我们信息 **/
		$data = Db::table('h5_meeting_vehicle')->order('id desc')->where('language', 1)->where('genre', 3)->find();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}
}