<?php
namespace app\h5api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Trip extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240820 **/
	public function _initialize() {

    }

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息     获取参会用户信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会用户信息 **/
	/** 作者：@20240820 **/
	public function user() {

		/** 对外接收参数 **/
		$id		 = input('id', '');								//账号id
		$token	 = input('token', '');							//token
		$bventId = '67000000-f0b5-6a75-d4d5-08dc731b5d6e';		//会议id

		$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/GetPersonalAttendeesPages/' . $id;

		/** 封装参数 **/
		$row['pageNo']		 = 1;
		$row['pageSize']	 = 10;
		$row['pageSizeOpts'] = [10,20,50,100];
		$row['totalCount']	 = 1;
		$row['totalPage']	 = 1;

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		if (isset($result['list']) && isset($result['list'][0]) && isset($result['list'][0]['id'])) {
			return ['code' => 200, 'msg' => '查询成功', 'data' => $result['list'][0]['id']];
		}

		return ['code' => 1, 'msg' => '查询失败', 'data' => $result];
	}

	// +---------------------------------------------------------------------------------------------------------------------
	// | 获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息     获取参会信息
	// +---------------------------------------------------------------------------------------------------------------------

	/** 功能：获取参会信息 **/
	/** 作者：@20240820 **/
	public function participation() {

		/** 对外接收参数 **/
		$id				= input('id', '');												//参会人id
		$token			= input('token', '');											//token
		$attendeeTypeId = input('attendeeTypeId', '');									//列别id
		$bventId		= '67000000-f0b5-6a75-d4d5-08dc731b5d6e';						//会议id
		//$attendeeTypeId = '28330000-1b2e-bacb-8af5-08dbab8bfb8b';						//列别id

		$url = 'https://gateway.pujiangforum.cn/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;

		//$url = 'https://gateway.31huiyi.com/api/ClientAggregator/Attendee/' . $bventId . '/PortalDetail/' . $id;


		/** 封装参数 **/
		$row['attendeeTypeId'] = $attendeeTypeId;

		$postdata = json_encode($row);

		$http_header = array(
			'Content-Type: application/json','Authorization:Bearer ' . $token
		);

		$ch = curl_init();

		curl_setopt ($ch, CURLOPT_URL, $url);					//请求url地址
		curl_setopt ($ch, CURLOPT_POST, 1);						//POST
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $postdata);		//参数
		curl_setopt ($ch, CURLOPT_HEADER, false );				//是否返回响应头信息
		curl_setopt ($ch, CURLOPT_HTTPHEADER,$http_header);
		//curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER,false);		//处理http证书问题
		curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT, 5000);
		curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);

		$result = curl_exec($ch);

		if (false === $result) {
			$result = curl_errno($ch);
		} else {
			//return 1;
		}

		curl_close($ch);

		$result = json_decode($result, true);

		return ['code' => 200, 'msg' => '查询成功', 'data' => $result];
	}
}