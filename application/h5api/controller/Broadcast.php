<?php
namespace app\h5api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Broadcast extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240722 **/
	public function _initialize() {

    }

	/** 功能：直播中心 **/
	/** 作者：@20230825 **/
	public function broadcast() {

		/** 对外接收参数 **/
		$sort_id = input('sort_id', '');				//分类id

		/** 查询直播 **/
		$data = Db::table('h5_broadcast')->order('sort')->where('language', 1)->where('genre', 3)->where('is_del', 1);
		$data = $sort_id ? $data->where('sort_id', $sort_id) : $data;
		$data = $data->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：直播中心分类 **/
	/** 作者：@20230825 **/
	public function broadcast_sort() {

		/** 查询直播分类 **/
		$data = Db::table('h5_broadcast_sort')->order('sort')->where('language', 1)->where('genre', 3)->where('is_del', 1)->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}

	/** 功能：图片直播 **/
	/** 作者：@20230825 **/
	public function picture() {

		/** 查询图片直播 **/
		$data = Db::table('h5_broadcast_picture')->order('sort')->where('language', 1)->where('genre', 3)->where('is_del', 1)->select();

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}
}