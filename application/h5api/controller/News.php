<?php
namespace app\h5api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class News extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240722 **/
	public function _initialize() {

    }

	/** 功能：新闻列表 **/
	/** 作者：@20230825 **/
	public function news() {

		/** 对外接收参数 **/
		$keyword = trim(input('keyword', ''));		//检索条件
		$page	 = input('page', 1);				//第几页
		
		/** 查询新闻信息 **/
		$row = Db::table('news')->order('release_time desc')->where('language', 1)->where('genre', 1)->where('is_del', 1);
		$row = $keyword ? $row->where('title', 'like', "%$keyword%") : $row;
		$row = $row->page($page, 10);
        $row = $row->select();

		if (!$row) {
			return json_encode(['code' => 1, 'msg' => '没找到', 'data' => $row]);
		}

		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $row]);
	}

	/** 功能：新闻详情 **/
	/** 作者：@20230825 **/
	public function show() {

		/** 对外接收参数 **/
		$id = input('id/d', '');	//新闻id

		/** 查询新闻信息 **/
		$data = Db::table('news')->where('id', $id)->find();
		
		return json_encode(['code' => 200, 'msg' => '查询成功', 'data' => $data]);
	}
}