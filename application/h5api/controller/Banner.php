<?php
namespace app\h5api\controller;
use think\Validate;
use think\Session;
use think\Cookie;
use think\Request;
use think\Cache;
use think\Db;

class Banner extends \think\Controller {

	/** 功能：初始化控制器 **/
	/** 作者：@20240820 **/
	public function _initialize() {

    }
	
	/** 功能：轮播图 **/
	/** 作者：@20240820 **/
	public function banner() {
		
		/** 查询轮播图 **/
		$data = Db::table('banner')->order('sort')->where('language', 1)->where('genre', 3)->where('is_del', 1)->select();

		return ['code' => 200, 'msg' => '查询成功', 'data' => $data];
	}
}